module.exports = {
  parser: 'babel-eslint',
  parserOptions: {
    ecmaFeatures: { legacyDecorators: true },
  },
  extends: [
    'airbnb',
    'prettier',
    'plugin:compat/recommended',
  ],
  env: {
    browser: true,
    node: true,
    es6: true,
    mocha: true,
    jest: true,
    jasmine: true,
  },
  globals: {
    APP_TYPE: true,
  },
  rules: {
    'react/jsx-filename-extension': [1, { extensions: ['.js'] }],
    'import/extensions': 0,
    'react/jsx-wrap-multilines': 0,
    'react/prop-types': 0,
    'react/forbid-prop-types': 0,
    'react/jsx-one-expression-per-line': 0,
    'react/jsx-props-no-spreading': 'off',
    'react/jsx-newline': ['error', { 'prevent': true, 'allowMultilines': true }],
    'react/jsx-fragments': ['error', 'syntax'],
    'import/no-unresolved': [
      2,
      {
        ignore: [
          '^@/',
          '^umi/',
          '.(scss|less|css)$',
          'config',
          'components/',
          'stores/',
          'actions/',
          'sources/',
          'styles/',
          'images/',
        ],
      },
    ],
    'import/no-extraneous-dependencies': [2, { optionalDependencies: true }],
    'jsx-a11y/no-noninteractive-element-interactions': 0,
    'jsx-a11y/click-events-have-key-events': 0,
    'jsx-a11y/no-static-element-interactions': 0,
    'jsx-a11y/anchor-is-valid': 0,
    'jsx-quotes': ['error', 'prefer-single'],
    'linebreak-style': 0,
    'no-unused-expressions': 0,
    'no-underscore-dangle': ['warn', { 'allow': ['foo_', '_bar'] }],
    'quotes': ['error', 'single', { 'avoidEscape': true }],
    'semi': ['error', 'always'],
    'comma-spacing': ['error', { 'before': false, 'after': true }],
    'object-curly-spacing': ['error', 'always'],
    'indent': ['error', 2, { 'SwitchCase': 1 }],
    'no-multi-spaces': ['error'],
    'no-multiple-empty-lines': ['error', { 'max': 1 }],
    'keyword-spacing': ['error', { 'before': true, 'after': true }],
    'space-before-function-paren': ['error', 'never'],
    'space-before-blocks': 'error',
    'block-spacing': ['error', 'always'],
    'space-infix-ops': 'error',
    'no-extra-semi': 'error',
    'key-spacing': ['error', { 'beforeColon': false, 'afterColon': true }],
    'padding-line-between-statements': [
      'error',
      { blankLine: 'always', prev: ['const', 'let', 'var'], next: '*'},
      { blankLine: 'always', prev: '*', next: ['return', 'block-like', 'multiline-expression']},
      { blankLine: 'always', prev: '*', next: ['const', 'let', 'var']},
      { blankLine: 'any', prev: ['const', 'let', 'var'], next: ['const', 'let', 'var']},
    ],
    'operator-linebreak': ['error', 'before'],
    'no-unneeded-ternary': 'error',
    'no-var': 'error',
    'arrow-body-style': ['error', 'as-needed']
  },
  settings: {
    polyfills: ['fetch', 'promises', 'url'],
    react: {
      version: 'detect',
    },
  },
};
