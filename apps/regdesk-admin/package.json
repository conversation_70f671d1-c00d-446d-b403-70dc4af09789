{"name": "regdesk-admin", "private": true, "version": "4.2.0", "description": "", "main": "src/index.js", "scripts": {"build": "node_modules/.bin/webpack --progress --bail -p --mode production", "dev": "webpack-dev-server --open --hot --mode development"}, "repository": "", "keywords": [], "author": "", "devDependencies": {"@babel/core": "^7.2.2", "@babel/plugin-proposal-class-properties": "^7.3.0", "@babel/plugin-proposal-decorators": "^7.3.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.3.2", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "@babel/preset-react": "^7.0.0", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "babel-plugin-import": "^1.11.0", "clean-webpack-plugin": "^3.0.0", "css-loader": "^2.1.0", "eslint": "^5.13.0", "eslint-config-airbnb": "^17.1.0", "eslint-config-prettier": "^4.0.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-loader": "^2.1.2", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-compat": "^2.7.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-markdown": "^1.0.0", "eslint-plugin-react": "^7.12.4", "file-loader": "^3.0.1", "html-webpack-plugin": "^3.2.0", "less": "3.9.0", "less-loader": "^4.1.0", "react-hot-loader": "^4.6.5", "style-loader": "^0.23.1", "webpack": "^4.29.3", "webpack-cli": "^3.2.3", "webpack-dev-server": "^3.1.14"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "antd": "^3.13.2", "axios": "^0.18.0", "classnames": "^2.2.6", "events": "^3.3.0", "flag-icon-css": "^3.5.0", "history": "^4.7.2", "i18next": "^23.11.2", "i18next-browser-languagedetector": "^7.2.1", "immutability-helper": "^3.0.1", "json-diff": "^1.0.6", "json-diff-react": "^1.0.1", "lodash": "^4.17.11", "markdown-it": "^12.0.3", "markdown-it-ins": "^3.0.0", "markdown-it-link-attributes": "^3.0.0", "moment": "^2.24.0", "moment-timezone": "^0.5.40", "react": "^16.14.0", "react-dnd": "^9.3.4", "react-dnd-html5-backend": "^9.3.4", "react-dom": "^16.14.0", "react-i18next": "^13.5.0", "react-json-editor-ajrm": "^2.5.14", "react-markdown-editor-lite": "^1.3.4", "react-redux": "^6.0.0", "react-router-dom": "^4.3.1", "redux": "^4.0.1", "redux-thunk": "^2.3.0"}}