const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');

module.exports = (_env, argv) => {
  const env = argv.mode || 'development';
  const srcPath = path.resolve('./src');

  return {
    context: srcPath,
    devtool: env === 'development' ? 'cheap-module-source-map' : 'none',
    devServer: {
      contentBase: './src/',
      publicPath: '/',
      historyApiFallback: true,
      hot: env === 'development',
      inline: true,
      host: env === 'development' ? 'localhost' : '0.0.0.0',
      port: env === 'development' ? 8001 : 80,
      open: true,
      openPage: '',
    },
    entry: './index.js',
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          exclude: /node_modules/,
          use: ['babel-loader'],
        },
        {
          test: /\.(png|jpg|gif|mp4|ogg|svg|woff|woff2)$/,
          loader: 'file-loader',
          options: {
            outputPath: 'assets',
          },
        },
        {
          test: /^.((?!cssmodule).)*\.less$/,
          use: [
            {
              loader: 'style-loader',
            },
            {
              loader: 'css-loader',
              options: {
                sourceMap: true,
                modules: true,
                localIdentName: '[local]___[hash:base64:5]',
              },
            },
            {
              loader: 'less-loader',
            },
          ],
        },
        {
          test: /^.((?!cssmodule).)*\.css$/,
          loaders: [{ loader: 'style-loader' }, { loader: 'css-loader' }],
        },
      ],
    },
    output: {
      path: path.resolve(__dirname, '../regdesk-server/public/admin/'),
      filename: 'app.js',
      publicPath: env === 'development' ? '/' : '/rd-admin/',
    },
    plugins: [
      new CleanWebpackPlugin(),
      new HtmlWebpackPlugin({
        template: 'index.html',
      }),
    ],
    resolve: {
      alias: {
        config: `${srcPath}/../config/${env}.js`,
      },
      extensions: ['.js', '.jsx', '.json'],
      modules: [srcPath, 'node_modules'],
    },
  };
};
