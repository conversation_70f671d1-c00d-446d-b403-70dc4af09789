{"presets": ["@babel/env", "@babel/react"], "plugins": [["import", {"libraryName": "antd", "libraryDirectory": "es", "style": "css"}], "@babel/plugin-proposal-object-rest-spread", ["@babel/plugin-proposal-decorators", {"legacy": true}], ["@babel/plugin-proposal-class-properties", {"loose": true}], ["@babel/plugin-transform-private-property-in-object", {"loose": true}], ["@babel/plugin-transform-private-methods", {"loose": true}], "@babel/plugin-proposal-optional-chaining", "@babel/plugin-proposal-nullish-coalescing-operator", "react-hot-loader/babel"]}