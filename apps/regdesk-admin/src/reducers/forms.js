import * as constants from '../const';

const initialState = {
  list: {},
  loading: false,
  currentFilters: {},
  currentTemplate: null,
  chapters: [],
  currentChapter: null,
  currentSection: null,
  currentRequirement: null,
  currentSubRequirement: null,
  filtersData: null,
  addChapter: false,
  pos: '',
};

export default (state = initialState, action) => {
  switch (action.type) {
    case constants.FORMS_CHANGE:
      return {
        ...state,
        ...action.payload,
      };

    case constants.RESET_STORE:
      return {
        ...initialState,
      };

    default:
      return state;
  }
};
