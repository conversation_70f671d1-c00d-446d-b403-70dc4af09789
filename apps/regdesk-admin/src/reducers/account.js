import * as constants from '../const';

const defaultState = {
  _id: null,
  name: '',
  avatar: '',
  email: '',
  role: '',
  contact: {},
  adminPermissions: [],
  adminCountriesForRegulation: [],
  adminLimitCountriesForRegulation: null,
  adminCountriesForPharmaRegulation: [],
  adminLimitCountriesForPharmaRegulation: null,
  adminCountriesForCCP: [],
  adminLimitCountriesForCCP: null,
  adminCountriesForAlert: [],
  adminLimitCountriesForAlert: null,
  adminCountriesForPharmaAlert: [],
  adminLimitCountriesForPharmaAlert: null,
  adminCountriesForLegislation: [],
  adminLimitCountriesForLegislation: null,
  adminCountriesForForm: [],
  adminLimitCountriesForForm: null,
  adminCountriesForWizards: [],
  adminLimitCountriesForWizards: null,
  loaded: false,
  su: false,
  loading: false,
  message: '',
  phone2FA: '',
  is2FAEnable: false,
  session2FA: null,
  loading2FA: false,
  config2FA: {},
};

export default (state = defaultState, action) => {
  switch (action.type) {
    case constants.ACCOUNT_CHANGE:
      return {
        ...state,
        ...action.payload,
      };

    case constants.RESET_STORE:
      return {
        ...defaultState,
        loaded: state.loaded,
        config2FA: state.config2FA,
      };

    default:
      return state;
  }
};
