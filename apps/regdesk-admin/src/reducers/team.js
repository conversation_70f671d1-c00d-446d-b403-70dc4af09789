import * as constants from '../const';

const defaultState = {
  list: [],
  loading: false,
  modalLoading: false,
  showModal: '',
  idu: null,
  user: {},
};

export default (state = defaultState, action) => {
  switch (action.type) {
    case constants.TEAM_CHANGE:
      return {
        ...state,
        ...action.payload,
      };

    case constants.RESET_STORE:
      return {
        ...defaultState,
      };

    default:
      return state;
  }
};
