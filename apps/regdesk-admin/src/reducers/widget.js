import * as constants from '../const';

const initialState = {
  timer: null,
  loadingTimer: false,
  migrations: [],
  filtersMigrations: {},
  paginationMigrations: {},
  loadingMigrations: false,
  services: [],
  loadingServices: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case constants.WIDGET_CHANGE:
      return { ...state, ...action.payload };
    case constants.RESET_STORE:
      return { ...initialState };
    default:
      return state;
  }
};
