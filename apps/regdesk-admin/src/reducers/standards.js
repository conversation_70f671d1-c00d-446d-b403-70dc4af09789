import * as constants from '../const';

const initialState = {
  list: [],
  pagination: {},
  searchType: 'name',
  searchValue: '',
  groups: [],
  organizations: [],
};

export default (state = initialState, action) => {
  switch (action.type) {
    case constants.DIC_TYPE_CHANGE:
      return {
        ...state,
        ...action.payload,
      };

    case constants.RESET_STORE:
      return {
        ...initialState,
      };

    default:
      return state;
  }
};
