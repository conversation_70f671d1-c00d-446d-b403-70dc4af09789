import * as constants from '../const';

const initialState = {
  list: [],
  pagination: {},
  filters: {},
  sorter: {},
  loading: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case constants.LOG_CHANGE:
      return {
        ...state,
        ...action.payload,
      };

    case constants.RESET_STORE:
      return {
        ...initialState,
      };

    default:
      return state;
  }
};
