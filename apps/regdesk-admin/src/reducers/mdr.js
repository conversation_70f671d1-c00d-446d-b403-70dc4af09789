import * as constants from '../const';

const initialState = {
  data: [],
  loading: false,
  item: {},
  released: false,
  buffer: [],
};

export default (state = initialState, action) => {
  switch (action.type) {
    case constants.MDR_CHANGE:
      return {
        ...state,
        ...action.payload,
      };

    case constants.RESET_STORE:
      return {
        ...initialState,
        buffer: [],
      };

    default:
      return state;
  }
};
