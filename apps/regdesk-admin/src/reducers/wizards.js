import * as constants from '../const';

const initialState = {
  list: [],
  loading: false,
  filters: {},
  pagination: {},
  classifications: [],
  applicationTypes: [],
  productTypes: [],
  notifiedBodies: [],
  identifiers: [],
  tags: [],
  ids: [],
  currentWizard: null,
  currentSection: null,
  currentQuestion: null,
  currentSubSection: null,
  sections: [],
  questions: [],
  sectionId: null,
  subSectionId: null,
  questionId: null,
  showSection: false,
  showQuestion: false,
  showSubSection: false,
  addSection: false,
  filtersData: null,
  questionsIds: [],
  position: [],
  buffer: [],
  bufferTypes: [],
  autofillQuestions: [],
  standardQuestions: [],
  classificationQuestions: []
};

export default (state = initialState, action) => {
  switch (action.type) {
    case constants.WIZARDS_CHANGE:
      return {
        ...state,
        ...action.payload,
      };

    case constants.RESET_STORE:
      return {
        ...initialState,
      };

    default:
      return state;
  }
};
