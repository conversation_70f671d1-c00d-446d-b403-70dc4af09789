import * as constants from '../const';

const initialState = {
  list: [],
  loading: false,
  filters: {},
  pagination: {},
  existsCountries: [],
};

export default (state = initialState, action) => {
  switch (action.type) {
    case constants.PHARMA_ALERTS_CHANGE:
      return { ...state, ...action.payload };
    case constants.RESET_STORE:
      return { ...initialState };
    default:
      return state;
  }
};
