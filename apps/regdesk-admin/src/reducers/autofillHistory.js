import * as constants from '../const';

const initialState = {
  list: [],
  pagination: {},
  loading: false,
  item: {},
};

export default (state = initialState, action) => {
  switch (action.type) {
    case constants.AUTOFILL_HISTORY_CHANGE:
      return {
        ...state,
        ...action.payload,
      };

    case constants.RESET_STORE:
      return {
        ...initialState,
      };

    default:
      return state;
  }
};
