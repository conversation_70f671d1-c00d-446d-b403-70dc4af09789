import * as constants from '../const';

const initialState = {
  loading: false,
  item: {},
  released: false,
  data: [],
  buffer: [],
};

export default (state = initialState, action) => {
  switch (action.type) {
    case constants.CCP_CHANGE:
      return {
        ...state,
        ...action.payload,
      };

    case constants.RESET_STORE:
      return {
        ...initialState,
      };

    default:
      return state;
  }
};
