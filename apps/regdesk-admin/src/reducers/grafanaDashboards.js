import * as constants from '../const';

const initialState = {
  list: [],
  sorter: {},
  pagination: {},
  filters: {},
  total: 0,
  existingCountries: []
};

export default (state = initialState, action) => {
  switch (action.type) {
    case constants.GRAFANA_DASHBOARD_CHANGE:
      return {
        ...state,
        ...action.payload,
      };
    case constants.RESET_STORE:
      return {
        ...initialState,
      };
    default:
      return state;
  }
};