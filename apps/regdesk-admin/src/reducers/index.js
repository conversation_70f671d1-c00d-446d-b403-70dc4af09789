import { combineReducers } from 'redux';
import logs from './logs';
import doc from './doc';
import users from './users';
import account from './account';
import standards from './standards';
import wizards from './wizards';
import forms from './forms';
import autofillHistory from './autofillHistory';
import notifications from './notifications';
import team from './team';
import regPlan from './regPlan';
import alerts from './alerts';
import pharmaAlerts from './pharmaAlerts';
import mdr from './mdr';
import mdrDocTypes from './mdrDocTypes';
import phr from './phr';
import phrDocTypes from './phrDocTypes';
import ccp from './ccp';
import grafanaDashboards from './grafanaDashboards';
import ccpChanges from './ccpChanges';
import widget from './widget';

export default combineReducers({
  doc,
  logs,
  users,
  account,
  standards,
  wizards,
  forms,
  autofillHistory,
  notifications,
  team,
  regPlan,
  alerts,
  pharmaAlerts,
  mdr,
  mdrDocTypes,
  phr,
  phrDocTypes,
  ccp,
  grafanaDashboards,
  ccpChanges,
  widget,
});
