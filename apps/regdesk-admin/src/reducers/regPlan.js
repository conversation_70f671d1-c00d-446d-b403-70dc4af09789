import * as constants from '../const';

const initialState = {
  loading: false,
  wizardId: null,
  sectionId: null,
  questionId: null,
  subSectionId: null,
  currentSection: null,
  currentQuestion: null,
  currentSubSection: null,
  sections: [],
  questionIds: [],
  questions: [],
  showSection: false,
  showQuestion: false,
  showSubSection: false,
  showModal: null,
  modalLoading: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case constants.REG_PLAN_CHANGE:
      return {
        ...state,
        ...action.payload,
      };

    case constants.RESET_STORE:
      return {
        ...initialState,
      };

    default:
      return state;
  }
};
