export const LOG_CHANGE = 'LOG_CHANGE';
export const DOC_CHANGE = 'DOC_CHANGE';
export const USER_CHANGE = 'USER_CHANGE';
export const RESET_STORE = 'RESET_STORE';
export const ACCOUNT_CHANGE = 'ACCOUNT_CHANGE';
export const DIC_TYPE_CHANGE = 'DIC_TYPE_CHANGE';
export const ALERTS_CHANGE = 'ALERTS_CHANGE';
export const PHARMA_ALERTS_CHANGE = 'PHARMA_ALERTS_CHANGE';
export const WIZARDS_CHANGE = 'WIZARDS_CHANGE';
export const GRAFANA_DASHBOARD_CHANGE = 'GRAFANA_DASHBOARD_CHANGE';
export const FORMS_CHANGE = 'FORMS_CHANGE';
export const AUTOFILL_HISTORY_CHANGE = 'AUTOFILL_HISTORY_CHANGE';
export const TEAM_CHANGE = 'TEAM_CHANGE';
export const REG_PLAN_CHANGE = 'REG_PLAN_CHANGE';
export const MDR_CHANGE = 'MDR_CHANGE';
export const MDR_DOC_TYPE_CHANGE = 'MDR_DOC_TYPE_CHANGE';
export const PHR_CHANGE = 'PHR_CHANGE';
export const PHR_DOC_TYPE_CHANGE = 'PHR_DOC_TYPE_CHANGE';
export const WIDGET_CHANGE = 'WIDGET_CHANGE';
export const CCP_CHANGE = 'CCP_CHANGE';
export const CCP_CHANGES_CHANGE = 'CCP_CHANGES_CHANGE';
export const NOTIFICATIONS_CHANGE = 'NOTIFICATIONS_CHANGE';

export const ADMIN_PERMISSIONS = [
  { key: 'users', name: 'Users' },
  { key: 'regulations', name: 'Device Regs' },
  { key: 'pharmaRegulations', name: 'Pharma Regs' },
  { key: 'control', name: 'Change Control Projects' },
  { key: 'dataAlerts', name: 'Data Alerts' },
  { key: 'pharmaAlerts', name: 'Pharma Alerts' },
  { key: 'updates', name: 'Notification Center' },
  { key: 'standards', name: 'Standards' },
  { key: 'regPlan', name: 'Regulatory Classification Plan' },
  { key: 'wizards', name: 'Wizards' },
  { key: 'appUsage', name: 'Application Usage' },
  { key: 'forms', name: 'Forms' },
  { key: 'autofillHistory', name: 'Autofill History' },
  { key: 'logs', name: 'Logs' },
];

export const PERMISSIONS = [
  { key: 'applications', name: 'Applications' },
  { key: 'checklists', name: 'Distributor Collaboration Tool' },
  { key: 'tracking', name: 'Product Tracking' },
  { key: 'regulations', name: 'Device Regs' },
  { key: 'pharmaRegulations', name: 'Pharma Regs' },
  { key: 'regPlan', name: 'Regulatory Classification Plan' },
  { key: 'control', name: 'Change Control Projects' },
  { key: 'reg-cmp', name: 'Planning Tool' },
  { key: 'data-alerts', name: 'Alerts' },
  { key: 'pharma-alerts', name: 'Pharma Alerts' },
  { key: 'chat', name: 'Chat' },
  { key: 'forms', name: 'Forms' },
  { key: 'appUsage', name: 'Application Usage' },
];

export const ACTIONS = [
  { key: 'login', name: 'Login' },
  { key: 'logout', name: 'Logout' },
];
