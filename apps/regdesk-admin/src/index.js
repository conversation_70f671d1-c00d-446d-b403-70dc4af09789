import '@babel/polyfill';
import React from 'react';
import ReactDOM from 'react-dom';
import moment from 'moment-timezone';
import { Provider } from 'react-redux';
import App from './containers/App';
import store from './stores';
import './utils/i18n';

moment.tz.setDefault('Europe/London');

ReactDOM.render(
  <Provider store={store}>
    <App />
  </Provider>,
  document.getElementById('app')
);

if (module.hot) {
  module.hot.accept();
}
