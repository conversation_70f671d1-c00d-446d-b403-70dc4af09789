import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   */
  get() {
    Actions.change({ loading: true });

    return api.ccpChange.get().then(data => Actions.change({ loading: false, ...data }));
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.ccpChange.add(data);
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  update(data) {
    return api.ccpChange.update(data);
  },

  /**
   * Remove
   * @param id
   * @returns {*}
   */
  remove(id) {
    return api.ccpChange.remove(id);
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}){
    store.dispatch({ type: constants.CCP_CHANGES_CHANGE, payload });
  },
};

export default Actions;

