import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   */
  get() {
    return api.phrDocType.get().then(data => Actions.change({ ...data }));
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.phrDocType.add(data);
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  update(data) {
    return api.phrDocType.update(data);
  },

  /**
   * Remove
   * @param id
   * @returns {*}
   */
  remove(id) {
    return api.phrDocType.remove(id);
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}){
    store.dispatch({ type: constants.PHR_DOC_TYPE_CHANGE, payload });
  },
};

export default Actions;

