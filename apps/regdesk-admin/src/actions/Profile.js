import { message } from 'antd';
import api from '../utils/api';
import * as constants from '../const';
import store from '../stores';
import Session from './Session';
import Auth from './Auth';

const ProfileActions = {
  /**
   * Update profile
   */
  get() {
    api.session
      .check()
      .then(data => ProfileActions.change({ ...data }))
      .catch(err => ProfileActions.change({ loaded: true, loading: false, sessionExpires: 0, message: (err && err.message) || '' }));
  },

  /**
   * Change password
   * @param data
   * @returns {*}
   */
  changePassword(data) {
    ProfileActions.change({ loading: true });

    return api.profile
      .changePassword(data)
      .then(() => {
        ProfileActions.change({ loading: false });
        message.success('Password changed successfully');
      })
      .catch(err => {
        ProfileActions.change({ loading: false });

        if (err && err.message) message.error(err.message);
      });
  },

  /**
   * Update profile
   * @param data
   */
  update(data) {
    ProfileActions.change({ loading: true });

    return api.profile
      .update(data)
      .then(() => {
        ProfileActions.change({ loading: false });
        message.success('Sent successfully');
        Session.check();
      })
      .catch(() => ProfileActions.change({ loading: false }));
  },

  /**
   * Destroy account
   * @param data
   */
  destroy(data) {
    return api.profile
      .destroy(data)
      .then(() => {
        message.success('Sent successfully');
        Auth.logout();
      });
  },

  /**
   * Change auth reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.ACCOUNT_CHANGE, payload });
  },
};

export default ProfileActions;
