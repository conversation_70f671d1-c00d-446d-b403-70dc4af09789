import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get list
   */
  get() {
    Actions.change({ loading: true });

    return api.ccpRelease.get().then(data => Actions.change({ loading: false, ...data }));
  },

  /**
   * Get by country Id
   * @param id
   */
  getById(id) {
    Actions.change({ loading: true });

    api.ccpContainer.getByCountryId(id).then(data => Actions.change({ loading: false, ...data }));
  },

  /**
   * Update
   * @param body
   */
  update(body) {
    Actions.change({ loading: true });

    return api.ccpContainer.update(body).then(data => Actions.change({ loading: false, ...data }));
  },

  /**
   * Release
   * @param idp
   */
  release(idp) {
    Actions.change({ loading: true });

    return api.ccpContainer.release({ idp }).then(() => Actions.change({ loading: false }));
  },

  /**
   * UnRelease
   * @param countryId
   */
  unRelease(countryId) {
    Actions.change({ loading: true });

    return api.ccpContainer.unRelease({ countryId }).then(Actions.get);
  },

  /**
   * Push buffer
   * @param key
   * @param type
   * @param data
   */
  pushBuffer(key, type, data) {
    const { buffer = [] } = store.getState().ccp;
    const index = buffer.findIndex((item) => item.key === key);
    const newData = JSON.parse(JSON.stringify(data));

    const newItem = { key, type, data: newData };

    if (index !== -1) {
      buffer[index] = newItem;
      message.success('Buffer updated');
    } else {
      buffer.push(newItem);
      message.success('Added new data in buffer');
    }

    Actions.change({ buffer });
  },

  /**
   * Change controle reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.CCP_CHANGE, payload });
  },
};

export default Actions;
