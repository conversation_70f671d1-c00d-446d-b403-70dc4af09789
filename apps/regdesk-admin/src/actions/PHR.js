import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import { generateId } from '../components/Form/ItemId';

const Actions = {
  /**
   * Get list
   */
  get() {
    Actions.change({ loading: true });

    return api.phrRelease
      .get()
      .then((data) => Actions.change({ loading: false, ...data }));
  },

  /**
   * Get by country Id
   * @param id
   */
  getById(id) {
    Actions.change({ loading: true });

    api.phrContainer
      .getByCountry(id)
      .then(data => Actions.change({ loading: false, ...data }));
  },

  /**
   * Update
   * @param body
   */
  update(body) {
    Actions.change({ loading: true });

    return api.phrContainer
      .update(body)
      .then(data => Actions.change({ loading: false, ...data }));
  },

  /**
   * Release
   * @param id
   */
  release(id) {
    Actions.change({ loading: true });

    return api.phrContainer
      .release({ id })
      .then(() => Actions.change({ loading: false }));
  },

  /**
   * UnRelease
   * @param idc
   */
  unRelease(idc) {
    Actions.change({ loading: true });

    return api.phrRelease
      .unRelease({ idc })
      .then(Actions.get);
  },

  /**
   * Push buffer
   * @param key
   * @param type
   * @param data
   */
  pushBuffer(key, type, data) {
    const { buffer = [] } = store.getState().phr;
    const index = buffer.findIndex((item) => item.key === key);
    const newData = JSON.parse(JSON.stringify(data));
    const newItem = { key, type, data: newData };

    if (index !== -1) {
      buffer[index] = newItem;
      message.success('Buffer updated');
    } else {
      buffer.push(newItem);
      message.success('Added new data in buffer');
    }

    Actions.change({ buffer });
  },

  /**
   * Update ids for classifications and
   * @param data
   * @returns {{}[]}
   */
  updateIds(data = []) {
    return data.map((item = {}) => {
      const id = generateId();

      return { ...item, id };
    });
  },

  /**
   * Change ecig regulations reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.PHR_CHANGE, payload });
  },
};

export default Actions;
