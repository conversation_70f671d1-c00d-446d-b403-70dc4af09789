import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get wizard
   */
  get() {
    Actions.change({ loading: true });

    return api.regPlan
      .getWizard()
      .then(({ wizard }) => {
        Actions.change({
          wizardId: wizard._id,
          sections: wizard.sections,
          loading: false,
        });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update wizard
   * @param props
   * @returns {Promise<any>}
   */
  updateWizard(props) {
    Actions.change({ loading: true });

    return api.regPlan
      .updateWizard(props)
      .then(this.get)
      .then(() => message.success('Wizard updated successfully'));
  },

  /**
   * Add section
   * @param props
   * @returns {Promise<any>}
   */
  addSection(props) {
    Actions.change({ loading: true });

    return api.regPlan
      .addSection(props)
      .then(this.clean)
      .then(this.get)
      .then(() => message.success('Section added successfully'));
  },

  /**
   * Update section
   * @param props
   * @returns {Promise<any>}
   */
  updateSection(props) {
    Actions.change({ loading: true });

    return api.regPlan
      .updateSection(props)
      .then(this.get)
      .then(() => message.success('Section updated successfully'));
  },

  /**
   * Update section
   * @param sectionId
   * @returns {Promise<any>}
   */
  deleteSection(sectionId) {
    Actions.change({ loading: true });

    return api.regPlan
      .deleteSection(sectionId)
      .then(this.clean)
      .then(this.get)
      .then(() => message.success('Section deleted successfully'));
  },

  /**
   * Add subSection
   * @param props
   * @returns {Promise<any>}
   */
  addSubSection(props) {
    Actions.change({ loading: true });

    return api.regPlan
      .addSubSection(props)
      .then(this.clean)
      .then(this.get)
      .then(() => message.success('SubSection added successfully'));
  },

  /**
   * Update subSection
   * @param sectionId
   * @returns {Promise<any>}
   */
  deleteSubSection(sectionId) {
    Actions.change({ loading: true });

    return api.regPlan
      .deleteSubSection(sectionId)
      .then(this.clean)
      .then(this.get)
      .then(() => message.success('SubSection deleted successfully'));
  },

  /**
   * Get question Ids
   */
  getQuestionIds() {
    api.regPlan.getQuestionIds().then((data) => Actions.change({ ...data }));
  },

  /**
   * Get questions
   */
  getQuestions() {
    api.regPlan.getQuestions().then((data) => Actions.change({ ...data }));
  },

  /**
   * Add question
   * @param props
   * @returns {Promise<any>}
   */
  addQuestion(props) {
    Actions.change({ loading: true });

    return api.regPlan
      .addQuestion(props)
      .then(this.clean)
      .then(this.get)
      .then(() => message.success('Question added successfully'));
  },

  /**
   * Update question
   * @param props
   * @returns {Promise<any>}
   */
  updateQuestion(props) {
    Actions.change({ loading: true });

    return api.regPlan
      .updateQuestion(props)
      .then(this.get)
      .then(() => message.success('Question updated successfully'));
  },

  /**
   * Delete question
   * @param questionId
   * @returns {Promise<any>}
   */
  deleteQuestion(questionId) {
    Actions.change({ loading: true });

    return api.regPlan
      .deleteQuestion(questionId)
      .then(this.clean)
      .then(this.get)
      .then(() => message.success('Question deleted successfully'));
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: null, modalLoading: false });
  },

  /**
   * Clean
   */
  clean() {
    Actions.change({
      currentSection: null,
      currentQuestion: null,
      currentSubSection: null,
      sectionId: null,
      questionId: null,
      subSectionId: null,
      showSection: false,
      showSubSection: false,
      showQuestion: false,
      showModal: null,
      modalLoading: false,
    });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.REG_PLAN_CHANGE, payload });
  },
};

export default Actions;
