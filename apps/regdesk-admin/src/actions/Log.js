import * as constants from '../const';
import store from '../stores';
import api from '../utils/api';

const LogActions = {
  /**
   * Load logs
   */
  load(params) {
    LogActions.change({ loading: true });

    api.log.get(params)
      .then(data => LogActions.change({ ...data, loading: false }))
      .catch(() => LogActions.change({ loading: false }));
  },

  /**
   * Change logs reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.LOG_CHANGE, payload });
  },
};

export default LogActions;
