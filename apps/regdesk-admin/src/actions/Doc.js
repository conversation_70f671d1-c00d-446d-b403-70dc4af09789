import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Upload
   * @param data
   * @returns {*}
   */
  upload(data) {
    return api.doc.upload(data);
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}){
    store.dispatch({ type: constants.DOC_CHANGE, payload });
  },
};

export default Actions;

