import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   * @param props
   */
  get(props = {}) {
    Actions.change({ loading: true });

    return api.wizards
      .get(props)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get wizard by id
   * @param wizardId
   */
  getById(wizardId) {
    Actions.change({ loading: true });

    return api.wizards
      .getById(wizardId)
      .then(({ wizard }) => {
        const { _id, sections } = wizard;

        Actions.change({
          wizardId: _id,
          sections,
          currentWizard: wizard,
          loading: false,
        });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get classifications
   */
  getClassifications() {
    Actions.change({ loading: true });

    return api.wizards
      .getClassifications()
      .then(({ classifications }) => Actions.change({ classifications, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get classifications by country
   * @param countryId
   */
  getClassificationsByCountry(countryId) {
    return api.wizards.getClassificationsByCountry(countryId).then(data => Actions.change(data));
  },

  /**
   * Get filters data
   */
  getFiltersData() {
    return api.wizards.getFiltersData().then(filtersData => Actions.change({ filtersData }));
  },

  /**
   * Get suggestions data
   */
  getSuggestions() {
    return api.wizards.getSuggestions().then(data => Actions.change(data));
  },

  /**
   * Get questions
   */
  getQuestions() {
    const { currentWizard } = store.getState().wizards;
    const { _id: wizardId } = currentWizard;

    return api.wizards.getQuestions(wizardId).then(data => Actions.change(data));
  },

  /**
   * Get autofill questions
   */
  getAutofillQuestions() {
    Actions.change({ loading: true });

    return api.wizards
      .getAutofillQuestions()
      .then(({ questions }) => Actions.change({ autofillQuestions: questions, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get standard questions
   * @param props
   */
  getStandardQuestions(props) {
    Actions.change({ loading: true });

    return api.wizards
      .getStandardQuestions(props)
      .then(({ list, ...data }) => Actions.change({ ...data, standardQuestions: list, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get Wizard List
   * @param id
   */
  getWizardDataStandardQuestions(id) {
    Actions.change({ loading: true });

    return api.wizards
      .getWizardDataStandardQuestions(id)
      .then(({ list, filtersData }) => {
        Actions.change({ loading: false });

        return { list, filtersData };
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get Wizard List
   * @param id
   */
  getWizardDataClassificationQuestions(id) {
    Actions.change({ loading: true });

    return api.wizards
      .getWizardDataClassificationQuestions(id)
      .then(({ list, filtersData }) => {
        Actions.change({ loading: false });

        return { list, filtersData };
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get classification questions
   * @param props
   */
  getClassificationQuestions(props) {
    Actions.change({ loading: true });

    return api.wizards
      .getClassificationQuestions(props)
      .then(({ list, ...data }) => Actions.change({ ...data, classificationQuestions: list, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add wizard
   * @param props
   */
  add(props) {
    Actions.change({ loading: true });

    return api.wizards
      .add(props)
      .then(data => {
        message.success('Wizard create successfully');
        Actions.change({ loading: false });

        return data;
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update version
   * @param id
   * @param props
   */
  updateVersion(id, props) {
    Actions.change({ loading: true });

    return api.wizards
      .updateVersion(id, props)
      .then(() => {
        message.success('Wizard updated successfully');
        Actions.getById(id);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update wizard
   * @param props
   */
  updateWizard(props) {
    Actions.change({ loading: true });

    const { currentWizard } = store.getState().wizards;
    const { _id: wizardId } = currentWizard;

    return api.wizards
      .updateWizard({ ...props, wizardId })
      .then(this.clean)
      .then(() => {
        message.success('Wizard updated successfully');
        Actions.getById(wizardId);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add Classification
   * @param props
   */
  addClassification(props) {
    Actions.change({ loading: true });

    return api.wizards
      .addClassification(props)
      .then(() => {
        Actions.getClassifications();
        message.success('Classification created successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update Classification
   * @param id
   * @param props
   */
  updateClassification(id, props) {
    Actions.change({ loading: true });

    return api.wizards
      .updateClassification(id, props)
      .then(() => {
        Actions.getClassifications();
        message.success('Classification updated successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update Classifications
   * @param id
   * @param props
   */
  updateClassifications(id, props) {
    Actions.change({ loading: true });

    return api.wizards
      .updateClassifications(id, props)
      .then(() => {
        Actions.getClassifications();
        message.success('Classifications updated successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add section
   * @param props
   */
  addSection(props) {
    Actions.change({ loading: true });

    const { currentWizard } = store.getState().wizards;
    const { _id: wizardId } = currentWizard;

    return api.wizards
      .addSection({ ...props, wizardId })
      .then(this.clean)
      .then(() => {
        message.success('Section added successfully');
        Actions.getById(wizardId);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update section
   * @param sectionId
   * @param props
   */
  updateSection(sectionId, props) {
    Actions.change({ loading: true });

    const { currentWizard } = store.getState().wizards;
    const { _id: wizardId } = currentWizard;

    return api.wizards
      .updateSection(sectionId, props)
      .then(() => {
        message.success('Section updated successfully');
        Actions.getById(wizardId);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add question
   * @param props
   */
  addQuestion(props) {
    Actions.change({ loading: true });

    const { currentWizard } = store.getState().wizards;
    const { _id: wizardId } = currentWizard;

    return api.wizards
      .addQuestion({ ...props, wizardId })
      .then(this.clean)
      .then(() => {
        message.success('Question added successfully');
        Actions.getById(wizardId);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update question
   * @param questionId
   * @param props
   */
  updateQuestion(questionId, props) {
    Actions.change({ loading: true });

    const { currentWizard } = store.getState().wizards;
    const { _id: wizardId } = currentWizard;

    return api.wizards
      .updateQuestion(questionId, props)
      .then(() => {
        message.success('Question updated successfully');
        Actions.getById(wizardId);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add autofill question
   * @param props
   */
  addAutofillQuestion(props) {
    Actions.change({ loading: true });

    return api.wizards
      .addAutofillQuestion(props)
      .then(() => {
        Actions.getAutofillQuestions();
        message.success('Question added successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update autofill question
   * @param questionId
   * @param props
   */
  updateAutofillQuestion(questionId, props) {
    Actions.change({ loading: true });

    return api.wizards
      .updateAutofillQuestion(questionId, props)
      .then(() => {
        Actions.getAutofillQuestions();
        message.success('Question updated successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add standard question
   * @param props
   */
  addStandardQuestion(props) {
    Actions.change({ loading: true });

    return api.wizards
      .addStandardQuestion(props)
      .then(() => {
        Actions.getStandardQuestions();
        message.success('Question added successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update standard question
   * @param questionId
   * @param props
   */
  updateStandardQuestion(questionId, props) {
    Actions.change({ loading: true });

    return api.wizards
      .updateStandardQuestion(questionId, props)
      .then(() => {
        Actions.getStandardQuestions();
        message.success('Question updated successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add classification question
   * @param props
   */
  addClassificationQuestion(props) {
    Actions.change({ loading: true });

    return api.wizards
      .addClassificationQuestion(props)
      .then(() => {
        Actions.getClassificationQuestions();
        message.success('Question added successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update classification question
   * @param questionId
   * @param props
   */
  updateClassificationQuestion(questionId, props) {
    Actions.change({ loading: true });

    return api.wizards
      .updateClassificationQuestion(questionId, props)
      .then(() => {
        Actions.getClassificationQuestions();
        message.success('Question updated successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add sub section
   * @param props
   */
  addSubSection(props) {
    Actions.change({ loading: true });

    const { currentWizard } = store.getState().wizards;
    const { _id: wizardId } = currentWizard;

    return api.wizards
      .addSubSection({ ...props, wizardId })
      .then(this.clean)
      .then(() => {
        message.success('Subsection added successfully');
        Actions.getById(wizardId);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
  * Add suggestion
   * @param props
  */
  addSuggestion(props) {
    return api.wizards.addSuggestion(props).then(() => Actions.getSuggestions());
  },

  /**
  * Update suggestion
   * @param id
   * @param props
  */
  updateSuggestion(id, props) {
    return api.wizards.updateSuggestion(id, props).then(() => Actions.getSuggestions());
  },

  /**
   * Delete
   * @param wizardId
   */
  delete(wizardId) {
    Actions.change({ loading: true });

    return api.wizards
      .delete(wizardId)
      .then(() => {
        const { filters, pagination } = store.getState().wizards;
        const { current, pageSize = 10, total = 0 } = pagination;
        let newPagination = pagination;

        if (total % pageSize === 1) newPagination = { current: current > 1 ? current - 1 : 1, pageSize };

        Actions.get({ pagination: newPagination, filters });
        message.success('Deleted successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Delete section
   * @param sectionId
   */
  deleteSection(sectionId) {
    Actions.change({ loading: true });

    const { currentWizard } = store.getState().wizards;
    const { _id: wizardId } = currentWizard;

    return api.wizards
      .deleteSection(sectionId)
      .then(this.clean)
      .then(() => {
        message.success('Section deleted successfully');
        Actions.getById(wizardId);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Delete sub section
   * @param sectionId
   */
  deleteSubSection(subSectionId) {
    Actions.change({ loading: true });

    const { currentWizard } = store.getState().wizards;
    const { _id: wizardId } = currentWizard;

    return api.wizards
      .deleteSubSection(subSectionId)
      .then(this.clean)
      .then(() => {
        message.success('Subsection deleted successfully');
        Actions.getById(wizardId);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Delete question
   * @param questionId
   */
  deleteQuestion(questionId) {
    Actions.change({ loading: true });

    const { currentWizard } = store.getState().wizards;
    const { _id: wizardId } = currentWizard;

    return api.wizards
      .deleteQuestion(questionId)
      .then(this.clean)
      .then(() => {
        message.success('Question deleted successfully');
        Actions.getById(wizardId);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Delete question
   * @param questionId
   */
  deleteAutofillQuestion(questionId) {
    Actions.change({ loading: true });

    return api.wizards
      .deleteAutofillQuestion(questionId)
      .then(() => {
        Actions.getAutofillQuestions();
        message.success('Question deleted successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Delete question
   * @param questionId
   */
  deleteStandardQuestion(questionId) {
    Actions.change({ loading: true });

    return api.wizards
      .deleteStandardQuestion(questionId)
      .then(() => {
        Actions.getStandardQuestions();
        message.success('Question deleted successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Delete question
   * @param questionId
   */
  deleteClassificationQuestion(questionId) {
    Actions.change({ loading: true });

    return api.wizards
      .deleteClassificationQuestion(questionId)
      .then(() => {
        Actions.getClassificationQuestions();
        message.success('Question deleted successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Clone wizard
   * @param props
   */
  clone(props) {
    Actions.change({ loading: true });

    return api.wizards
      .clone(props)
      .then(data => {
        message.success('Wizard cloned successfully');
        Actions.change({ loading: false });

        return data;
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Release
   * @param id
   * @param released
   */
  updateRelease(id, released) {
    Actions.change({ loading: true });

    return api.wizards
      .updateRelease(id, { released: !released })
      .then(() => {
        const { filters, pagination } = store.getState().wizards;

        Actions.get({ pagination, filters });
        message.success('Updated successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Clean
   */
  clean() {
    Actions.change({
      currentSection: null,
      currentQuestion: null,
      currentSubSection: null,
      sectionId: null,
      questionId: null,
      subSectionId: null,
      showSection: false,
      showQuestion: false,
      showSubSection: false,
    });
  },

  /**
   * Get system tags
   * @param params
   */
  getTags(params) {
    Actions.change({ loading: true });

    return api.wizards
      .getSystemTags(params)
      .then(({ list, pagination }) => Actions.change({ loading: false, tags: list, pagination }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get all system tags
   */
  getAllTags() {
    return api.wizards.getSystemTags({ fullList: true }).then(tags => Actions.change({ tags }));
  },

  /**
   * Add system tag
   * @param props
   */
  addTag(props) {
    Actions.change({ loading: true });

    return api.wizards
      .addSystemTag(props)
      .then(() => Actions.getTags())
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update system tag
   * @param id
   * @param props
   */
  updateTag(id, props) {
    Actions.change({ loading: true });

    return api.wizards
      .updateSystemTag(id, props)
      .then(() => Actions.getTags())
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Release system tag
   * @param id
   */
  releaseTag(id) {
    Actions.change({ loading: true });

    return api.wizards
      .releaseSystemTag(id)
      .then(() => Actions.getTags())
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Delete system tag
   * @param id
   */
  deleteTag(id) {
    Actions.change({ loading: true });

    return api.wizards
      .deleteSystemTag(id)
      .then(() => Actions.getTags())
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get Identifiers
   * @param props
   */
  getIdentifiers(props) {
    Actions.change({ loading: true });

    return api.wizards
      .getIdentifiers(props)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add identifier
   * @param props
   */
  addIdentifier(props) {
    Actions.change({ loading: true });

    return api.wizards
      .addIdentifier(props)
      .then(() => Actions.getIdentifiers())
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update identifier
   * @param id
   * @param props
   */
  updateIdentifier(id, props) {
    Actions.change({ loading: true });

    return api.wizards
      .updateIdentifier(id, props)
      .then(() => Actions.getIdentifiers())
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Disable identifier
   * @param id
   */
  disableIdentifier(id) {
    Actions.change({ loading: true });

    return api.wizards
      .disableIdentifier(id)
      .then(() => Actions.getIdentifiers())
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add identifier
   * @param id
   */
  deleteIdentifier(id) {
    Actions.change({ loading: true });

    return api.wizards
      .deleteIdentifier(id)
      .then(() => Actions.getIdentifiers())
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get Wizard List
   * @param params
   */
  getWizardData(id, params) {
    Actions.change({ loading: true });

    return api.wizards
      .getWizardData(id, params)
      .then(({ list, filtersData }) => {
        Actions.change({ loading: false });

        return { list, filtersData };
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get all IDs
   */
  getIds() {
    return api.wizards.getIdentifiers({ fullList: true }).then(ids => Actions.change({ ids }));
  },

  /**
   * Push buffer
   * @param key
   * @param type
   * @param data
   */
  pushBuffer(key, type, data) {
    const { buffer = [] } = store.getState().wizards;
    const index = buffer.findIndex(item => item.key === key);
    const newItem = { key, type, data: JSON.parse(JSON.stringify(data)) };

    if (index !== -1) {
      buffer[index] = newItem;
      message.success('Buffer updated');
    } else {
      buffer.push(newItem);
      message.success('Added new data in buffer');
    }

    Actions.change({ buffer });
  },

  /**
   * Update data from buffer
   * @param props
   */
  updateFromBuffer(props) {
    Actions.change({ loading: true });

    const { currentWizard } = store.getState().wizards;
    const { _id: wizardId } = currentWizard;

    return api.wizards
      .updateFromBuffer({ ...props, wizardId })
      .then(() => {
        message.success('Buffer data added successfully');
        Actions.getById(wizardId);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.WIZARDS_CHANGE, payload });
  },
};

export default Actions;
