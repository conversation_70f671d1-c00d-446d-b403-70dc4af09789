import config from 'config';
import api from '../utils/api';
import * as constants from '../const';
import store from '../stores';
import Session from './Session';

const AuthActions = {
  /**
   * Logout
   * @returns {Function}
   */
  logout() {
    api.auth
      .logout()
      .then(() => {
        localStorage.removeItem('token');
        Session.stop();
        store.dispatch({ type: constants.RESET_STORE });
      });
  },

  /**
   * Login
   * @param data
   * @returns {*}
   */
  login(data) {
    AuthActions.change({ loading: true });

    api.auth
      .login(data)
      .then(payload => {
        Session.init(payload);
        AuthActions.change({ ...payload, loading: false });
      })
      .catch(err => AuthActions.change({ loading: false, message: (err && err.message) || '' }));
  },

  /**
   * Login As
   * @param email
   * @returns {*}
   */
  loginAs(email) {
    api.auth
      .loginAs({ email })
      .then(() => AuthActions.openClient());
  },

  /**
   * Change auth reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.ACCOUNT_CHANGE, payload });
  },

  /**
   * Open site
   */
  openClient(){
    window.location.href = config.site;
  },
};

export default AuthActions;
