import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   */
  get(props = {}) {
    const { sorter, filters, pagination } = store.getState().grafanaDashboards;

    return api.grafanaDashboards
      .get({ sorter, filters, pagination, ...props })
      .then(data => Actions.change({ ...data }));
  },
  
  filter(newFilter = {}) {
    const { filters = {}, pagination } = store.getState().grafanaDashboards;
    const nextFilters = { ...filters, ...newFilter };

    Actions.get({ filters: nextFilters, pagination: { ...pagination, current: 1 } });
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.grafanaDashboards.add(data);
  },

  /**
   * Remove
   * @param id
   */
  delete(id) {
    return api.grafanaDashboards.delete(id);
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  update(data) {
    return api.grafanaDashboards.update(data);
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.GRAFANA_DASHBOARD_CHANGE, payload });
  },
};

export default Actions;