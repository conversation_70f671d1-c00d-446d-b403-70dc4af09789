import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Create template
   */
  create(body) {
    Actions.change({ loading: true });

    return api.forms
      .create(body)
      .then((newTemplate) => {
        message.success('Created successfully');

        Actions.change({
          loading: false,
          currentTemplate: newTemplate,
          addChapter: true,
        });
      })
      .catch((err) => {
        Actions.change({ loading: false });

        return err;
      });
  },

  /**
   * Get data
   */
  getPage({ page = 1, filters = {} }) {
    Actions.change({ loading: true });

    return api.forms
      .getPage({ page, filters })
      .then(({ list }) => Actions.change({ list, currentFilters: filters, loading: false }))
      .catch(() => Actions.change({ loading: false, currentFilters: filters }));
  },

  /**
   * Get form template by id
   */
  getById(templateId) {
    Actions.change({ loading: true });

    return api.forms
      .getById(templateId)
      .then(({ template }) => {
        Actions.change({
          currentTemplate: template,
          chapters: template.chapters,
          loading: false,
        });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get filters data
   */
  getFiltersData() {
    return api.forms.getFiltersData().then((filtersData) => Actions.change({ filtersData }));
  },

  /**
   * Update version
   * @param item
   */
  updateVersion(id, item) {
    Actions.change({ loading: true });

    return api.forms
      .updateVersion(id, item)
      .then(({ formTemplate }) => {
        message.success('Updated successfully');

        Actions.change({
          loading: false,
          currentTemplate: formTemplate,
        });
      })
      .catch((err) => {
        Actions.change({ loading: false });

        return err;
      });
  },

  /**
   * Add chapter
   */
  addChapter({ name }) {
    const { currentTemplate, chapters } = store.getState().forms;

    return api.forms
      .addChapter({ templateId: currentTemplate._id, name })
      .then(({ template, chapter }) => {
        message.success('Chapter added successfully');

        Actions.change({
          currentTemplate: template,
          currentChapter: chapter,
          chapters: [...chapters, chapter],
          addChapter: false,
        });

        return chapter;
      });
  },

  /**
   * Update chapter
   */
  updateChapter({ name }) {
    const { currentChapter, chapters } = store.getState().forms;

    return api.forms
      .updateChapter({ chapterId: currentChapter._id, name })
      .then(() => {
        message.success('Chapter updated successfully');

        const curChapIndex = chapters.findIndex(s => s._id === currentChapter._id);
        const newChapter = { ...currentChapter, name };

        chapters.splice(curChapIndex, 1, newChapter);

        Actions.change({
          currentChapter: newChapter,
          chapters: [...chapters],
        });
      });
  },

  /**
   * Delete chapter
   */
  deleteChapter() {
    Actions.change({ loading: true });

    const { currentChapter, currentTemplate } = store.getState().forms;

    return api.forms
      .deleteChapter({ chapterId: currentChapter._id, templateId: currentTemplate._id })
      .then(() => {
        message.success('Chapter deleted successfully');
        Actions.getById(currentTemplate._id);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add section
   */
  addSection({ name }) {
    const { currentTemplate, currentChapter } = store.getState().forms;

    return api.forms
      .addSection({ chapterId: currentChapter._id, name })
      .then(({ section }) => {
        Actions.getById(currentTemplate._id);
        message.success('Section added successfully');

        return section;
      });
  },

  /**
   * Update section
   */
  updateSection({ name }) {
    const { currentTemplate, currentSection } = store.getState().forms;

    return api.forms
      .updateSection({ sectionId: currentSection._id, name })
      .then(() => {
        Actions.getById(currentTemplate._id);
        message.success('Section updated successfully');
      });
  },

  /**
   * Delete section
   */
  deleteSection() {
    Actions.change({ loading: true });

    const { currentSection, currentTemplate } = store.getState().forms;

    return api.forms
      .deleteSection({ sectionId: currentSection._id })
      .then(() => {
        message.success('Section deleted successfully');
        Actions.getById(currentTemplate._id);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add requirement
   */
  addRequirement(body) {
    const { currentTemplate, currentSection } = store.getState().forms;

    return api.forms
      .addRequirement({ sectionId: currentSection._id, body })
      .then(() => {
        Actions.getById(currentTemplate._id);
        message.success('Requirement added successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update requirement
   */
  updateRequirement(body) {
    const { currentTemplate, currentRequirement }
      = store.getState().forms;

    return api.forms
      .updateRequirement({ requirementId: currentRequirement._id, body })
      .then(() => {
        Actions.getById(currentTemplate._id);
        message.success('Requirement updated successfully');
      });
  },

  /**
   * Delete requirement
   */
  deleteRequirement() {
    Actions.change({ loading: true });

    const { currentRequirement, currentTemplate } = store.getState().forms;

    return api.forms
      .deleteRequirement({ requirementId: currentRequirement._id })
      .then(() => {
        message.success('Requirement deleted successfully');
        Actions.getById(currentTemplate._id);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add subrequirement
   */
  addSubRequirement(body) {
    const { currentTemplate, currentRequirement } = store.getState().forms;

    return api.forms
      .addSubRequirement({ requirementId: currentRequirement._id, body })
      .then(() => {
        Actions.getById(currentTemplate._id);
        message.success('Sub Requirement added successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update subrequirement
   */
  updateSubRequirement(body) {
    const { currentTemplate, currentSubRequirement } = store.getState().forms;

    return api.forms
      .updateSubRequirement({ subRequirementId: currentSubRequirement._id, body })
      .then(() => {
        Actions.getById(currentTemplate._id);
        message.success('Sub Requirement updated successfully');
      });
  },

  /**
   * Delete subrequirement
   */
  deleteSubRequirement() {
    Actions.change({ loading: true });

    const { currentSubRequirement, currentTemplate } = store.getState().forms;

    return api.forms
      .deleteSubRequirement({ subRequirementId: currentSubRequirement._id })
      .then(() => {
        message.success('Sub Requirement deleted successfully');
        Actions.getById(currentTemplate._id);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Delete
   */
  delete(templateId) {
    Actions.change({ loading: true });

    return api.forms
      .delete(templateId)
      .then(() => {
        const { list, currentFilters } = store.getState().forms;
        const { docs, page } = list;

        Actions.getPage({
          page: docs.length === 1 && page !== 1 ? page - 1 : page,
          filters: currentFilters,
        });
        message.success('Deleted successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Clone template
   */
  clone(newTemplateParams) {
    Actions.change({ loading: true });

    const { currentTemplate } = store.getState().forms;

    return api.forms
      .clone({ templateId: currentTemplate._id, newTemplateParams })
      .then(() => {
        message.success('Form Template cloned successfully');
        Actions.change({ loading: false });
      })
      .catch((err) => {
        Actions.change({ loading: false });

        return err;
      });
  },

  /**
   * Release
   * @param item
   */
  updateRelease(item) {
    const id = item?._id;
    const is_released = item?.is_released === 1 ? 0 : 1;

    Actions.change({ loading: true });

    return api.forms
      .updateRelease(id, { is_released })
      .then(() => {
        Actions.change({ loading: false });

        const { list, currentFilters } = store.getState().forms;
        const { docs, page } = list;

        Actions.getPage({
          page: docs.length === 1 && page !== 1 ? page - 1 : page,
          filters: currentFilters,
        });
        message.success('Updated successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.FORMS_CHANGE, payload });
  },
};

export default Actions;
