import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(params) {
    Actions.change({ loading: true });

    api.team
      .get(params)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get user
   * @returns {Function}
   */
  getById(idu) {
    Actions.change({ modalLoading: true });

    return api.team
      .getById(idu)
      .then(data => {
        Actions.change({ ...data, modalLoading: false });

        return data;
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Remove by ID
   * @param id
   * @param assignee
   */
  remove(id, assignee) {
    return api.team.remove(id, assignee);
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.team.add(data);
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  update(data) {
    return api.team.update(data);
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: '', modalLoading: false, idu: null, user: {} });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}){
    store.dispatch({ type: constants.TEAM_CHANGE, payload });
  },
};

export default Actions;
