import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   * @param props
   */
  get(props = {}) {
    Actions.change({ loading: true });

    return api.alerts
      .get(props)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get countries
   */
  getCountries() {
    return api.alerts.getCountries().then(data => Actions.change(data));
  },

  /**
   * Filter data
   * @param newFilter
   */
  filter(newFilter = {}) {
    const { filters = {} } = store.getState().alerts;
    const nextFilters = { ...filters, ...newFilter };

    Actions.get({ filters: nextFilters, pagination: { current: 1 } });
  },

  /**
   * Add alert
   * @param props
   */
  add(props) {
    Actions.change({ loading: true });

    return api.alerts
      .add(props)
      .then(() => {
        const { filters, pagination } = store.getState().alerts;

        Actions.get({ pagination, filters });
        message.success('Alert created successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update alert
   * @param id
   * @param props
   */
  update(id, props) {
    Actions.change({ loading: true });

    return api.alerts
      .update(id, props)
      .then(() => {
        const { filters, pagination } = store.getState().alerts;

        Actions.get({ pagination, filters });
        message.success('Alert updated successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Release
   * @param id
   * @param released
   */
  updateRelease(id, released) {
    Actions.change({ loading: true });

    return api.alerts
      .updateRelease(id, { released: !released })
      .then(() => {
        const { filters, pagination } = store.getState().alerts;

        Actions.get({ pagination, filters });
        message.success('Alert updated successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Delete
   * @param id
   */
  delete(id) {
    Actions.change({ loading: true });

    return api.alerts
      .delete(id)
      .then(() => {
        const { filters, pagination } = store.getState().alerts;
        const { current, pageSize = 10, total = 0 } = pagination;
        let newPagination = pagination;

        if (total % pageSize === 1) newPagination = { current: current > 1 ? current - 1 : 1, pageSize };

        Actions.get({ pagination: newPagination, filters });
        message.success('Alert deleted successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.ALERTS_CHANGE, payload });
  },
};

export default Actions;
