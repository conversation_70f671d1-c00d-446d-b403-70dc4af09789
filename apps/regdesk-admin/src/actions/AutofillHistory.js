import * as constants from '../const';
import store from '../stores';
import api from '../utils/api';

const AutofillHistoryActions = {
  /**
   * Load autofill history
   */
  load(params) {
    AutofillHistoryActions.change({ loading: true });

    api.autofillHistory
      .get(params)
      .then(data => AutofillHistoryActions.change({ ...data, loading: false }))
      .catch(() => AutofillHistoryActions.change({ loading: false }));
  },

  /**
   * Get autofill history by id
   */
  getById(id) {
    AutofillHistoryActions.change({ loading: true });

    return api.autofillHistory
      .getById(id)
      .then(({ history }) => {
        AutofillHistoryActions.change({
          item: history,
          loading: false,
        });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Change logs reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.AUTOFILL_HISTORY_CHANGE, payload });
  },
};

export default AutofillHistoryActions;
