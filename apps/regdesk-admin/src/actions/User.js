import * as constants from '../const';
import store from '../stores';
import api from '../utils/api';

const UserActions = {
  /**
   * Load All users
   */
  load(params) {
    UserActions.change({ loading: true });

    api.user.get(params)
      .then(data => UserActions.change({ ...data, loading: false }))
      .catch(() => UserActions.change({ loading: false }));
  },

  /**
   * Remove user by userId
   * @param userId
   * @returns {AxiosPromise}
   */
  remove(userId, assignee = null) {
    return api.user.remove({ userId, assignee });
  },

  /**
   * Get user data by userId
   * @param userId
   */
  getById(userId) {
    api.user.getById(userId).then(data => UserActions.change(data));
  },

  /**
   * Create new user
   * @param data
   * @returns {AxiosPromise}
   */
  add(data) {
    return api.user.add(data);
  },

  /**
   * Update user by userId
   * @param userId
   * @param body
   * @returns {AxiosPromise}
   */
  update(userId, body) {
    return api.user.update({ userId, body });
  },

  /**
   * Update user's application usage control
   * @param userId
   * @param body
   * @returns {AxiosPromise}
   */
  applicationUsageUpdate(userId, body) {
    return api.user.applicationUsageUpdate({ userId, body });
  },

  /**
   * Change password users
   * @param data
   * @returns {*}
   */
  changePassword(data) {
    return api.user.changePassword(data);
  },

  /**
   * Force sub-users to change their passwords
   * @returns {AxiosPromise}
   */
  forceChangePassword() {
    return api.user.forceChangePassword();
  },

  /**
   * Change users reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.USER_CHANGE, payload });
  },
};

export default UserActions;
