import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   * @param props
   */
  get(props = {}) {
    Actions.change({ loading: true });

    return api.notifications
      .get(props)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Filter data
   * @param newFilter
   */
  filter(newFilter = {}) {
    const { filters = {} } = store.getState().notifications;
    const nextFilters = { ...filters, ...newFilter };

    Actions.get({ filters: nextFilters, pagination: { current: 1 } });
  },

  /**
   * Add notification
   * @param props
   */
  add(props) {
    Actions.change({ loading: true });

    return api.notifications
      .add(props)
      .then(() => {
        const { filters, pagination } = store.getState().notifications;

        Actions.get({ pagination, filters });
        message.success('Notification created successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update notification
   * @param id
   * @param props
   */
  update(id, props) {
    Actions.change({ loading: true });

    return api.notifications
      .update(id, props)
      .then(() => {
        const { filters, pagination } = store.getState().notifications;

        Actions.get({ pagination, filters });
        message.success('Notification updated successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Push notification
   * @param id
   */
  push(id) {
    Actions.change({ loading: true });

    return api.notifications
      .push(id)
      .then(() => {
        const { filters, pagination } = store.getState().notifications;

        Actions.get({ pagination, filters });
        message.success('Push send successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Test notification
   * @param id
   */
  test(id) {
    Actions.change({ loading: true });

    return api.notifications
      .test(id)
      .then(() => {
        const { filters, pagination } = store.getState().notifications;

        Actions.get({ pagination, filters });
        message.success('Test send successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Test banner notification
   * @param id
   */
  testBanner(id) {
    Actions.change({ loading: true });

    return api.notifications
      .testBanner(id)
      .then(() => {
        const { filters, pagination } = store.getState().notifications;

        Actions.get({ pagination, filters });
        message.success('Test send successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Release notification
   * @param id
   * @param released
   */
  updateRelease(id, released) {
    Actions.change({ loading: true });

    return api.notifications
      .updateRelease(id, { released: !released })
      .then(() => {
        const { filters, pagination } = store.getState().notifications;

        Actions.get({ pagination, filters });
        message.success('Notification updated successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Delete notification
   * @param id
   */
  delete(id) {
    Actions.change({ loading: true });

    return api.notifications
      .delete(id)
      .then(() => {
        const { filters, pagination } = store.getState().notifications;
        const { current, pageSize = 10, total = 0 } = pagination;
        let newPagination = pagination;

        if (total % pageSize === 1) newPagination = { current: current > 1 ? current - 1 : 1, pageSize };

        Actions.get({ pagination: newPagination, filters });
        message.success('Notification deleted successfully');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.NOTIFICATIONS_CHANGE, payload });
  },
};

export default Actions;
