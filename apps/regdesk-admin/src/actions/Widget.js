import * as constants from '../const';
import store from '../stores';
import api from '../utils/api';

const Actions = {
  /**
   * Get timer
   */
  getTimer() {
    Actions.change({ loadingTimer: true });

    return api.widget
      .getTimer()
      .then(payload => Actions.change({ ...payload, loadingTimer: false }))
      .catch(() => Actions.change({ loadingTimer: false }));
  },

  /**
   * Run timer
   */
  runTimer(data) {
    Actions.change({ loadingTimer: true });

    return api.widget
      .runTimer(data)
      .then(() => Actions.getTimer())
      .catch(() => Actions.change({ loadingTimer: false }));
  },

  /**
   * Get migrations
   */
  getMigrations(data) {
    Actions.change({ loadingMigrations: true });

    return api.widget
      .getMigrations(data)
      .then(({ list, filters, pagination }) => (
        Actions.change({ migrations: list, filtersMigrations: filters, paginationMigrations: pagination, loadingMigrations: false })
      ))
      .catch(() => Actions.change({ loadingMigrations: false }));
  },

  /**
   * Get services
   */
  getServices() {
    Actions.change({ loadingServices: true });

    return api.widget
      .getServices()
      .then(({ list }) => Actions.change({ services: list, loadingServices: false }))
      .catch(() => Actions.change({ loadingServices: false }));
  },

  /**
   * Change widget reducers
   */
  change(payload) {
    store.dispatch({ type: constants.WIDGET_CHANGE, payload });
  },
};

export default Actions;
