import i18n from './i18n';

const { t } = i18n;

const PASSWORD_RULES = [
  {
    pattern: /^[a-zA-Z0-9_!@#$%^&*()~-]{1,1000}$/,
    warning: 'onlyLatinNumberSpecial',
  },
  {
    pattern: /^.{8,}$/,
    warning: 'passwordNotValid',
  },
  {
    pattern: /^(?=.*[A-Z]).*$/,
    warning: 'oneUppercase',
    isDynamic: true,
  },
  {
    pattern: /^(?=.*[a-z]).*$/,
    warning: 'oneLowercase',
    isDynamic: true,
  },
  {
    pattern: /^(?=.*[0-9]).*$/,
    warning: 'oneNumeric',
    isDynamic: true,
  },
  {
    pattern: /^(?=.*[_!@#$%^&*()~-]).*$/,
    warning: 'oneSpecial',
    isDynamic: true,
  },
];

// Default rules are not enough, use this validator with dynamic message.
const validator = (_, password, callback) => {
  let fullWarning; // should be undefined, not ''
  let isPrefixAdded = false;

  const warnings = [];
  const dynamicWarnings = [];

  if (!password?.length) return callback();

  const addWarning = (text, isDynamic, isPrefix) => {
    let targetArray = warnings;

    if (isDynamic) targetArray = dynamicWarnings;

    targetArray.push(t(`ProfileInfo.ChangePass.${text}`), (isPrefix || !isDynamic) ? ' ' : ', ');
  };

  const applyWarningsByRules = ({ pattern, warning, isDynamic }) => {
    if (pattern.test(password)) return;

    if (isDynamic && !isPrefixAdded) {
      addWarning('dynamicPrefix', true, true);
      isPrefixAdded = true;
    }

    addWarning(warning, isDynamic);
  };

  PASSWORD_RULES.forEach(applyWarningsByRules);

  if (warnings.length) fullWarning = warnings.join('');

  const dynamicSize = dynamicWarnings.length;

  if (dynamicSize) {
    dynamicWarnings[dynamicSize - 1] = '.'; // replace the last ', ' with '.'
    fullWarning = `${fullWarning || ''}${dynamicWarnings.join('')}`;
  }

  return callback(fullWarning);
};

const totalPasswordRules = isNewPassword => {
  const rules = [{
    required: true,
    message: t('ProfileInfo.ChangePass.passwordIsEmpty'),
  }];

  if (isNewPassword) rules.push({ validator });

  return ({ initialValue: '', rules });
};

const generatePassword = () => {
  const length = 32;
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_!@#$%^&*()~-';
  const n = charset.length;
  let retVal = '';

  for (let i = 0; i < length; i += 1) {
    retVal += charset.charAt(Math.floor(Math.random() * n));
  }

  return retVal;
};

/**
 * Check session for 2FA
 * @param config2FA
 * @param session2FA
 * @param step
 * @returns alert
 */
const checkSession2FA = ({ config2FA, session2FA, step = 1 }) => {
  const { countFailCheck, countResend, expireCode } = session2FA || {};
  const { BLOCK_TIME = 0, BLOCK_RESEND = 0, BLOCK_CHECK_CODE = 0 } = config2FA || {};

  let alert = null;

  if (step === 1 && expireCode < Date.now()) {
    alert = 'The verification code is expired. Please request a new code';
  }

  if (countFailCheck >= BLOCK_CHECK_CODE && BLOCK_TIME) {
    alert = `Too many failed attempts. You have been temporarily blocked. Please wait ${BLOCK_TIME/60} minutes before trying again.`;
  }

  if (countResend >= BLOCK_RESEND && BLOCK_TIME) {
    alert = `You have reached the maximum number of resend attempts. Please wait ${BLOCK_TIME/60} minutes before requesting a new code.`;
  }

  return alert;
};

export {
  totalPasswordRules,
  generatePassword,
  checkSession2FA,
};
