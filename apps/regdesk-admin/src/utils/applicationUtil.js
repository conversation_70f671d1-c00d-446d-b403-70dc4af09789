const tags = [
  {
    name: 'Draft Dossier',
    ids: [],
  },
  {
    name: 'Manufacturer Declaration',
    scope: 'Country',
    fileType: 'PDF',
    description: 'Includes any declarations required to market the product in the country ',
    ids: ['manufacturer_declaration']
  },
  {
    name: 'Power of Attorney/Local Authorization/Business License',
    scope: 'Country',
    fileType: 'PDF',
    description: 'Includes any country specific licenses, authorizations and permissions that the local entity needs in order to market the product ',
    ids: [
      'power_of_attorney',
      'signed_statement',
      'summarizing_letter',
      'notice_of_operation_of_health_supplies_establishment',
      'commercial_record',
      'commercial_record',
      'ar_details',
      'affidavit',
      'power_of_attorney_letter',
      'letter_of_authorization',
      'business_license',
      'marketing_authorization_letter',
      'business_registration_certificate',
      'importer_license',
      'ar_license',
      'establishment_license'
    ]
  },
  {
    name: 'Proof of Payment',
    scope: 'Country',
    fileType: 'PDF',
    description: 'Includes any payment receipts, acknowledgements, bank drafts etc. ',
    ids: ['proof_of_payment', 'fee_receipt', 'israel_fee_receipt']
  },
  {
    name: '510k',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Documents submitted to the USFDA for 510k approval',
    ids: [
      '510k_certificate',
      'truthful_and_accuracy_statement',
      'rta_special_checklist',
      'rta_traditional_checklist',
      'financial_certification',
      'form_3514',
      'form_3601',
      'form_3674',
      'form_fda',
      'coversheet',
      'class_iii_summary_and_certification',
      '510k_summary'
    ]
  },
  {
    name: 'Accessories Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents related to accessories that are associated with the product ',
    ids: ['accessories', 'list_of_accessories', 'accessories_not_included']
  },
  {
    name: 'Adverse Event Reports',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents related to adverse events or their summaries ',
    ids: ['potential_adverse_effects']
  },
  {
    name: 'Animal Studies',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents relating to animal studies conducted for the product ',
    ids: [
      'animal_study_summaries_combination',
      'animal_studies',
      'animal_study_conclusions_combination',
      'performance_testing_animal',
      'performance_testing_animal_upload',
      'animal_studies_conclusions_combination'
    ]
  },
  {
    name: 'Bench Testing',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents on bench testing',
    ids: ['performance_testing_bench', 'performance_testing_bench_upload', 'non_clinical_combination', 'introduction_non_clinical_combination']
  },
  {
    name: 'Biocompatibility Information & Reports',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents on biocompatibility studies, summaries etc. ',
    ids: [
      'biocompatibility_report_upload',
      'biocompatibility_and_materials_upload',
      'biocompatibility_risk_evaluation',
      'biocompatibility_test_reports',
      'biocompatibility',
      'biocompatibility_upload',
      'biocompatibility_conclusion',
      'biocompatibility_and_materials_upload',
      'biocompatibility_testing_plan'
    ]
  },
  {
    name: 'Catalog/Brochure',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes catalogs, brochures and any sales/promotional material ',
    ids: ['brochure', 'promotional_material']
  },
  {
    name: 'CE Certificate',
    scope: 'Product',
    fileType: 'PDF',
    description: 'CE certificate obtained after Notified Body approval in the EU',
    ids: ['ec_certificate', 'ce_certificate']
  },
  {
    name: 'Certificate of Free Sale/CFG',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any Certificates of Free Sale for the product as well as the CFG from the US',
    ids: ['free_sales_certificate', 'free_sale_certificate']
  },
  {
    name: 'Change Control Documentation ',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents related to changes made or being planned to the product ',
    ids: [
      'description_of_change',
      'comparative_table',
      'comparison_of_the_modified_device_upload',
      'description_of_change_upload',
      'changes_and_additions',
      'significant_changes',
      'changes_to_design_or_label_upload',
      'identification_of_device_change(s)'
    ]
  },
  {
    name: 'Clinical Data & Clinical Evaluation Reports',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any clinical testing and evaluation related documents ',
    ids: [
      'clinical_combination',
      'clinical_studies_combination',
      'clinical_investigation',
      'clinical_data',
      'clinical_evaluation_plan',
      'summary_of_safety_and_clinical_performance',
      'clinical_evaluation_report',
      'clinical_evaluation_upload',
      'clinical_data_conclusions_combination',
      'clinical_evaluation',
      'performance_testing_clinical_upload',
      'clinical_evaluator(s)',
      'clinical_statement_combination',
      'clinical_studies'
    ]
  },
  {
    name: 'Connected Devices Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes documents related to any accessories or devices that are connected to the product ',
    ids: ['connected_devices_upload']
  },
  {
    name: 'Cybersecurity Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents related to any cybersecurity requirements for the product ',
    ids: ['cybersecurity_report']
  },
  {
    name: 'Declaration of Conformity',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any and all Declarations of Conformity for the product ',
    ids: [
      'declaration_of_conformity',
      'australian_declaration_of_conformity',
      'eu_declaration_of_conformity',
      'closure_declaration_of_conformity'
    ]
  },
  {
    name: 'Design Examination Certificate',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes the Design Examination Certificate for the product ',
    ids: ['design_examination_certificate']
  },
  {
    name: 'Design Verification & Validation ',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents related to the verification and validation for the product ',
    ids: [
      'design_verification',
      'user_requirements_upload',
      'target_group_consultation',
      'design_verification_upload',
      'design_validation_upload',
      'design_validation'
    ]
  },
  {
    name: 'Device Disposal Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents related to the information about disposing the product ',
    ids: ['device_repair_and_servicing']
  },
  {
    name: 'Device Repair & Maintenance Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents related to the repair and maintenance of the product ',
    ids: ['device_repair_and_servicing']
  },
  {
    name: 'Device Safety Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents related to the safety information of the product',
    ids: ['certificate_of_safety', 'performance_and_safety_tests']
  },
  {
    name: 'Dispersed/Absorbed Products Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes any documents about dispersed/absorbed products in the product ',
    ids: ['dispersed_products_upload']
  },
  {
    name: 'Drug Consult File',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to drug consult performed by a health agency for a combination product ',
    ids: [
      'bibliography_combination',
      'regional_information_combination',
      'similarity_combination',
      'specification_combination',
      'specifications_control_drug_combination',
      'stability_data_combination',
      'stability_data_drug_combination',
      'stability_summary_combination',
      'stability_summary_conclusion_drug_combination',
      'structure_elucidation_combination',
      'overages_combination',
      'pharmaceutical_development_combination',
      'long_balloons_combination',
      'market_exclusivity',
      'market_exclusivity_orphan',
      'member_states_products_info',
      'microbiological_attributes_combination',
      'novel_excipients_combination',
      'exceptional_combination',
      'excipients_combination',
      'formulation_development_combination',
      'impurities_combination',
      'justification_of_specification_combination',
      'justification_specifications_drug_control_combination',
      'drug_substance_combination',
      'compatibility_combination',
      'container_closure_system_combination',
      'container_closure_system_drug_combination',
      'appendix_3_combination',
      'appendix_7_combination',
      'appendix_combination_device'
    ]
  },
  {
    name: 'EMC Test Reports',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related electromagnetic and electrical safety information',
    ids: [
      'emc',
      'emc_upload',
      'electric_safety_and_electromagnetic_compatibility',
      'electrical_testing_upload'
    ]
  },
  {
    name: 'Endocrine Disruptors Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related endocrine disrupting substances found in the product ',
    ids: []
  },
  {
    name: 'Full Quality Assurance',
    scope: 'Product',
    fileType: 'PDF',
    description: 'The FQA certificate issues under MDD',
    ids: ['full_quality_assurance']
  },
  {
    name: 'GSPR/Essential Principles Checklist',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all GSPRs and Essential Principles Checklists for the product ',
    ids: [
      'gspr',
      'australian_essential_principles_checklist',
      'essential_requirements_checklist',
      'australian_essential_requirements_checklist',
      'essential_principles',
      'essential_principles_conformity_upload',
      'cmr_products_upload'
    ]
  },
  {
    name: 'Human/Animal Tissue Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to any human or animal derieved parts of the product ',
    ids: [
      'human_blood_derivatives',
      'medical_devices_containing_biological_material_upload',
      'human_animal_tissue_upload',
      'animal_tissues',
      'animal_derieved_substances',
      'excipients_human_animal_combination'
    ]
  },
  {
    name: 'IFU',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Product IFU',
    ids: ['ifu', 'instructions_for_use']
  },
  {
    name: 'Implant Card Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Implant card needed for implantable products',
    ids: ['implant_card']
  },
  {
    name: 'Implantable/Active Devices Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related implantable and active products',
    ids: ['implantable_and_active_devices_upload']
  },
  {
    name: 'Inner Label/Leaflet/Insert',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Inner labels, leaflets and packaging inserts for the product ',
    ids: []
  },
  {
    name: 'Intended Use',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the Intended Use of the product ',
    ids: [
      'intended_use',
      'indication_for_use_upload',
      'device_description',
      'device_description_upload',
      'intended_conditions_of_use'
    ]
  },
  {
    name: 'Literature Review',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the literature review of the product ',
    ids: [
      'literature_references_combination',
      'use_of_existing_bibliography',
      'bibliography'
    ]
  },
  {
    name: 'Manufacturing Documentation',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the manufacturing process for the product ',
    ids: [
      'manufacturing_information_upload',
      'site_audit_report',
      'process_validation',
      'process_validation_upload',
      'manufacturing_contract',
      'environmental_assessment',
      'environmental_factors',
      'manufacturing_flowchart',
      'manufacturing_site_inspection_report',
      'manufacturing_site_information_upload',
      'manufacturing_process',
      'manufacturing_process_flow',
      'manufacturing_process_combination',
      'manufacturing_process_development_combination',
      'manufacturing_process_process_controls_combination',
      'controls_critical_steps_intermediates_combination',
      'controls_intermediates_combination'
    ]
  },
  {
    name: 'Materials Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the materials related to the product ',
    ids: [
      'device_material_specifications',
      'materials_table',
      'material_characterization',
      'raw_materials',
      'raw_material_certification',
      'raw_material_process',
      'origin_of_material',
      'quantity_of_material',
      'materials'
    ]
  },
  {
    name: 'MDSAP Documentation',
    scope: 'Product',
    fileType: 'PDF',
    description: 'MDSAP documents',
    ids: []
  },
  {
    name: 'Measuring/Diagnostic Products Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to measuring or diagnostic products ',
    ids: [
      'measuring_devices_upload',
      'measuring_features',
      'measurement_validation_report'
    ]
  },
  {
    name: 'Medicinal Substance Information ',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the medicinal or drug substance of the product ',
    ids: ['medicinal_substances']
  },
  {
    name: 'MRI Safety Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the MRI safety of the product ',
    ids: ['mri_upload', 'mri_compatibility_evidence']
  },
  {
    name: 'Outer Label',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Outer label',
    ids: [
      'sample_label',
      'samples_of_labels_on_the_medical_device_and_its_packaging',
      'mock_up',
      'changes_to_labeling_or_design_upload',
      'labels',
      'labeling',
      'sample_label_upload',
      'sample_labels',
      'braille'
    ]
  },
  {
    name: 'Packaging Data & Reports',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the packaging of the product ',
    ids: [
      'description_of_packaging_system',
      'packaging_materials_specifications',
      'packaging_process_validation',
      'packaging_integrity_and_transporatation_testing_upload',
      'packaging_testing_upload',
      'packaging_shelf_life_testing',
      'darwings_of_packaging_system',
      'packaging_ship_testing',
      'packaging_validation'
    ]
  },
  {
    name: 'Performance Testing Results & Reports',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the performance testing of the product ',
    ids: [
      'performance_and_safety_upload',
      'toxicology_report',
      'software_studies',
      'summary_of_studies_upload',
      'lab_equipment',
      'lab_testing_report',
      'mechanical_tests_upload',
      'notice_of_examination_of_results',
      'performance_testing_clinical',
      'performance_and_safety'
    ]
  },
  {
    name: 'Plant Master File',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Plant Master File',
    ids: ['plant_master_file', 'sanitation', 'appendix_1_combination', 'appendix_6_combination']
  },
  {
    name: 'Post Market Activities Information ',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to post market activities associated with the product ',
    ids: [
      'post_market_clinical_follow_up',
      'post market surveillance report',
      'pms_plan',
      'pms_report',
      'pms_upload',
      'post market surveillance report',
      'post_market_clinical_follow_up_plan',
      'post_market_clinical_follow_up_upload',
      'post_market_report',
      'post_market_clinical_follow_up_report',
      'post_market_plan',
      'post_market_surveillance_report',
      'post_market_monitoring_data',
      'post_market_surveillance'
    ]
  },
  {
    name: 'Preclinical Studies',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to preclinical studies of the product ',
    ids: ['preclinical_studies', 'human_study_combination']
  },
  {
    name: 'Product & Design Specifications',
    scope: 'Product',
    fileType: 'PDF',
    description: "Includes all documents related the product's specifications",
    ids: ['product_design_specifications', 'product_and_design_specifications_upload', 'design_stages_upload']
  },
  {
    name: 'Product Photos & Drawings',
    scope: 'Product',
    fileType: 'PDF, JPEG, PNG',
    description: 'Includes all documents related to photos and drawings of the product ',
    ids: [
      'photograph',
      'device_drawings_and_specifications_upload',
      'device_drawings_and_specifications',
      'key_functional_elements_upload'
    ]
  },
  {
    name: 'PSUR',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Periodic Safety Update Report',
    ids: ['psur']
  },
  {
    name: 'QMS Documentation & Certificates',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the quality management system for the product  ',
    ids: [
      'qms_certificate',
      'qrd',
      'documentation_policy',
      'certificate_of_analysis',
      'certificate_of_conformity',
      'quality_management_system',
      'quality_control_operations',
      'quality_monitoring_activities',
      'quality_system',
      'personnel',
    ]
  },
  {
    name: 'References',
    scope: 'Product',
    fileType: 'PDF',
    description: 'References',
    ids: ['references_upload']
  },
  {
    name: 'Reusable/Surgical Products Information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the description of reusable or surgical aspects of the product',
    ids: ['reusbale_surgical_instruments_upload']
  },
  {
    name: 'Risk Assessment/Management Data & Reports',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to risk management or analysis for the product ',
    ids: [
      'risk_analysis',
      'results_of_risk_analysis',
      'risk_management_plan',
      'risk_assessment',
      'risk_control_measures',
      'risk_management_upload',
      'risk_conclusions',
      'risk_benefit_statement',
      'risk_evaluation',
      'risk_justification',
      'risk_management_system',
      'risk_management_conclusion',
      'risk_assessment_and_control',
      'benefit_risk_analysis',
      'risk_management_report'
    ]
  },
  {
    name: 'Shelf Life Reports',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the shelf life for the product ',
    ids: [
      'shelf_life_report_upload',
      'shelf_life',
      'product_shelf_life_and_stability_testing_upload',
      'lifetime_and_shelf_life_of_product_upload',
      'shelf_life_claims'
    ]
  },
  {
    name: 'Software Verification & Validation',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to the software verification and validation of the product ',
    ids: [
      'software_requirement_specifications',
      'software_verification_and _validation_studies_upload',
      'software_verification_or_validation_plan',
      'software_verfification_or_validation_plan',
      'software_verification_and_validation_studies_upload',
      'software',
      'software_upload',
      'software_risk_assessment'
    ]
  },
  {
    name: 'Stability Data & Reports',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents relatedto the stability of the product ',
    ids: [
      'stability_upload',
      'product_shelf_life_and_stability_testing_text',
      'packaging_integrity_and_transportation_testing_upload',
      'pharmacovigilance_system',
      'post_approval_stability_combination',
      'postapproval_stability_protocol_stability_commitment_combination'
    ]
  },
  {
    name: 'Sterilization Data & Reports',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes all documents related to teh sterilization of the product ',
    ids: [
      'sterilization',
      'sterilization_method_validation',
      'sterlization_report_upload',
      'sterilization_upload',
      'sterilization_method',
      'sterilization_data',
      'sterilization_report_upload',
      'appendix_10_combination'
    ]
  },
  {
    name: 'Technical File',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Technical File of the product ',
    ids: ['technical_file', 'technical_specifications', 'device_master_file']
  },
  {
    name: 'User Manual',
    scope: 'Product',
    fileType: 'PDF',
    description: 'User Manual of the product ',
    ids: ['user_manual']
  },
  {
    name: 'PMA',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Documents submitted to the USFDA for PMA approval',
    ids: ['pma_review_checklist_upload', 'pma_supplement']
  },
  {
    name: 'Application Form',
    scope: '',
    fileType: 'PDF',
    description: 'Includes all application forms submitted to the agency',
    ids: ['ANSM_Application_Form', 'Application_Form', 'Application_Form_India', 'CYMDA_Application_Form', 'FIMEA_Application_Form']
  },
  {
    name: 'Standards',
    scope: '',
    fileType: 'PDF',
    description: 'Includes all standards related information',
    ids: ['Applicable_Standards', 'List_Of_Standards']
  },
  {
    name: 'Cover Letter',
    scope: '',
    fileType: 'PDF',
    description: 'Includes all cover letters included with submissions to the agency',
    ids: ['cover_letter', 'module1_ctd_cover_letter', 'BSI_IVDR_Cover_Letter', 'Cover_Letter_India', 'Egypt_Cover_Letter']
  },
  {
    name: 'Table of Contents',
    scope: '',
    fileType: 'PDF',
    description: 'Includes all ToCs used to describe dossiers',
    ids: ['ctd_table_of_contents', 'Table_Of_Contents']
  },
  {
    name: 'Subcontractors Information',
    scope: 'Product',
    fileType: '',
    description: "Details subcontractor's information as contact information, qualifications or certificates for example",
    ids: ['location_of_subcontractors_upload']
  },
  {
    name: 'Analytical Procedures',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Describes applicable procedures intended for analysis of products or processes.',
    ids: ['validation_analytical_procedures_combination', 'validation_analytical_procedures_drug_control_combination', 'analytical_procedures_combination', 'analytical_procedures_drug_control_combination', 'appendix_9_combination']
  },
  {
    name: 'Product Quality Documentation',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes documents regarding evaluation or testing of product quality',
    ids: ['reference_standards_materials_combination', 'quality_combination', 'materials_standards_combination', 'characterization_impurities_drug_control_combination', 'batch_analyses_combination', 'batch_analyses_drug_control_combination', 'batch_formula_combination', 'appendix_11_combination', 'appendix_2_combination']
  },
  {
    name: 'User safety information',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Details information to be presented to users regarding safety when product is used',
    ids: ['pediatrics_combination']
  },
  {
    name: 'Process Validation Report',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes documented reports as result of a validation of manufacturing or control processes',
    ids: ['process_validation_combination', 'process_validation_evaluation_combination', 'appendix_8_combination']
  },
  {
    name: 'Marketing Authorization',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes a type of document that evidence the authorization for marketing of a product',
    ids: ['conditional_marketing_authorization_combination']
  },
  {
    name: 'GMP certificate',
    scope: 'Product',
    fileType: 'PDF',
    description: 'It is a Certificate or equivalent document that certifies the compliance with the good manufacturing practices',
    ids: ['gmp_certificate', 'appendix_4_combination']
  },
  {
    name: 'Companion Diagnostics',
    scope: 'Product',
    fileType: 'PDF',
    description: 'Includes diagnostic tests used as a companion to a therapeutic drug to determine its applicability to a specific person',
    ids: ['']
  },
  {
    name: 'Payment Form',
    scope: 'Country',
    fileType: 'PDF',
    description: 'Includes Information for Payment Request Forms',
    ids: ['Fee_Form', 'Submission_and_Fees']
  }
];

const applicationUtil = {
  getTags() {
    return tags;
  },
};

module.exports = applicationUtil;
