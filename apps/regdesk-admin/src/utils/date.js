import moment from 'moment';

export const FULL_DATE_FORMAT = 'DD MMM YYYY';
export const FULL_TIME_FORMAT = 'HH:mm:ss DD MMM YYYY';
export const LEGACY_DATE_FORMAT = 'YYYY-MM-DD';

/**
 * Get date in UTC
 * @param date
 * @param format
 * @returns {string}
 */
const getUTC = (date = new Date(), format = FULL_TIME_FORMAT) => moment(date).utc().format(format).toUpperCase();

/**
 * Init
 */
const init = () => {
  window.Date.prototype.getUTC = function () { return getUTC(new Date(this.valueOf()), FULL_DATE_FORMAT); };

  window.Date.prototype.getFullUTC = function () { return getUTC(new Date(this.valueOf())); };
  window.Date.getUTC = (date) => getUTC(date, FULL_DATE_FORMAT);
  window.Date.getFullUTC = getUTC;

  moment.prototype.getUTC = function () { return this.utc().format(FULL_DATE_FORMAT).toUpperCase(); };

  moment.prototype.getFullUTC = function () { return this.utc().format(FULL_TIME_FORMAT).toUpperCase(); };

  moment.prototype.getTimeUTC = function () { return this.utc().format('HH:mm').toUpperCase(); };
};

const inputDateFormats = [
  'DD.MM.YYYY',
  'DD/MM/YYYY',
  'DD MMM YYYY',
];

// Regex check for inputDateFormats compliance
const inputDateRegex = /(0[1-9]|[12][0-9]|3[01])((\/|\.)(0[1-9]|1[1,2])(\/|\.)| (jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec) )(19|20)\d{2}/i;

/**
  * Is valid date
  * @param {any} value
  * @param {string|string[]} format
  * @returns {boolean}
  */
function isValidDate(value, format = FULL_DATE_FORMAT) {
  return moment(value, format, true).isValid();
};

/**
  * Is valid input date
  * @param {any} value
  * @returns {boolean}
  */
function isValidInputDate(value) {
  return moment(value, inputDateFormats, true).isValid();
};

/**
  * Does the string contains the valid date?
  * @param string
  * @returns {boolean}
  */
function hasValidInputDate(string) {
  const extractedDate = string.match(inputDateRegex)?.[0];

  return extractedDate && isValidInputDate(extractedDate);
};

/**
  * Extracts valid date and returns moment object or null
  * @param string
  * @returns {moment|null}
  */
function extractValidInputDateMoment(string) {
  const extractedDate = string.match(inputDateRegex)?.[0];

  if (!extractedDate || !isValidInputDate(extractedDate)) {
    return null;
  }

  return moment(extractedDate, inputDateFormats, true);
};

export default {
  init,
  getUTC,
  isValidDate,
  isValidInputDate,
  hasValidInputDate,
  extractValidInputDateMoment,
  inputDateFormats,
  inputDateRegex,
};
