import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import en from '../locales/en';
import ru from '../locales/ru';
import de from '../locales/de';
import es from '../locales/es';

const resources = {
  en: {
    translation: en,
  },
  cn: {
    translation: en,
  },
  de: {
    translation: de,
  },
  es: {
    translation: es,
  },
  jp: {
    translation: en,
  },
  pt: {
    translation: en,
  },
  fr: {
    translation: en,
  },
  kr: {
    translation: en,
  },
  ru: {
    translation: ru,
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en',
    interpolation: {
      escapeValue: false,
    },
    fallbackLng: 'en',
    react: {
      wait: true,
    },
  });

export default i18n;
