import axios from 'axios';
import config from 'config';
import actions from '../actions';
import ErrorNotification from '../components/ErrorNotification';

const URL_ROOT = config.apiServer;
const options = { withCredentials: true };

axios.interceptors.request.use(conf => ({
  ...conf,
  url: `${conf.url}${conf.url.includes('?') ? '&' : '?'}r=${Math.random()}`
}));

const request = promise =>
  Promise.resolve()
    .then(() => promise)
    .then(res => {
      const { error } = res.data || {};

      if (error) {
        if (['100', '103', '104', '107', '132'].includes(error.code)) error.hide = true;

        if (error.code === '103') actions.auth.logout();

        throw error;
      }

      actions.session.updateTimer();

      return res.data || {};
    })
    .catch(error => {
      if (error && 'message' in error && !error.hide) {
        const messageLines = [
          'Oops',
          error.code ? `Error code: ${error.code}` : 'Request failed:',
          error.message,
        ];

        ErrorNotification(messageLines.join('\n'));
      }

      throw error;
    });

const requests = {
  get: ({ url, params = {} }) => request(axios.get(`${URL_ROOT}${url}`, { ...options, params })),
  delete: ({ url, data = {} }) => request(axios.delete(`${URL_ROOT}${url}`, { ...options, data })),
  put: ({ url, body = {} }) => request(axios.put(`${URL_ROOT}${url}`, body, options)),
  post: ({ url, body = {}, opt = {} }) => request(axios.post(`${URL_ROOT}${url}`, body, { ...options, ...opt })),
};

export default {
  session: {
    update: () => requests.get({ url: '/api/session/update' }),
    check: () => requests.get({ url: '/api/session/check' }),
  },
  auth: {
    login: body => requests.post({ url: '/api/auth/', body }),
    logout: () => requests.get({ url: '/api/auth/logout' }),
    loginAs: body => requests.post({ url: '/api/auth/email', body }),
  },
  twoStep: {
    get: () => requests.get({ url: '/api/auth/twoStep' }),
    getById: (id) => requests.get({ url: `/api/auth/twoStep/${id}` }),
    resend: (body) => requests.post({ url: '/api/auth/twoStep/resend', body }),
    verify: (body) => requests.post({ url: '/api/auth/twoStep/verify', body }),
    enable: (body) => requests.post({ url: '/api/auth/twoStep/enable', body }),
    disable: (body) => requests.post({ url: '/api/auth/twoStep/disable', body }),
  },
  user: {
    get: params => requests.get({ url: '/api/user', params }),
    getById: userId => requests.get({ url: `/api/user/${userId}` }),
    getPackageLogs: ({ userId, params }) => requests.get({ url: `/api/user/packageLogs/${userId}`, params }),
    add: body => requests.post({ url: '/api/user/', body }),
    update: ({ userId, body }) => requests.put({ url: `/api/user/${userId}`, body }),
    applicationUsageUpdate: ({ userId, body }) => requests.put({ url: `/api/user/applicationUsage/${userId}`, body }),
    remove: ({ userId, assignee }) => requests.delete({ url: `/api/user/${userId}`, data: { assignee } }),
    changePassword: body => requests.put({ url: '/api/user/change/password', body }),
    forceChangePassword: body => requests.post({ url: '/api/user/change/password/force', body }),
  },
  log: {
    get: params => requests.get({ url: '/api/log', params }),
  },
  profile: {
    changePassword: body => requests.put({ url: '/api/profile/change/password', body }),
    update: body => requests.put({ url: '/api/profile', body }),
    destroy: body => requests.post({ url: '/api/profile/destroy', body }),
  },
  doc: {
    upload: body => requests.post({ url: '/api/doc/', body }),
    getCommon: params => requests.get({ url: '/api/doc/common/', params }),
  },
  notifications: {
    get: params => requests.get({ url: '/api/updates/admin/get', params }),
    add: body => requests.post({ url: '/api/updates/admin/add', body }),
    update: (id, body) => requests.put({ url: `/api/updates/admin/update/${id}`, body }),
    updateRelease: (id, body) => requests.put({ url: `/api/updates/admin/updateRelease/${id}`, body }),
    push: id => requests.put({ url: `/api/updates/admin/push/${id}` }),
    test: id => requests.put({ url: `/api/updates/admin/test/${id}` }),
    testBanner: id => requests.put({ url: `/api/updates/admin/testBanner/${id}` }),
    delete: id => requests.delete({ url: `/api/updates/admin/delete/${id}` }),
  },
  standards: {
    get: params => requests.get({ url: '/api/standards/admin/get', params }),
    getCompare: params => requests.get({ url: '/api/standards/admin/compare', params }),
    compare: body => requests.post({ url: '/api/standards/admin/compare', body }),
    release: params => requests.get({ url: '/api/standards/admin/release', params }),
    update: body => requests.post({ url: '/api/standards/admin/edit', body }),
    add: body => requests.post({ url: '/api/standards/admin/add', body }),
    remove: body => requests.post({ url: '/api/standards/admin/delete', body }),
  },
  alerts: {
    get: params => requests.get({ url: '/api/alerts/admin/get', params }),
    getCountries: () => requests.get({ url: '/api/alerts/admin/getCountries' }),
    add: body => requests.post({ url: '/api/alerts/admin/add', body }),
    update: (id, body) => requests.put({ url: `/api/alerts/admin/update/${id}`, body }),
    updateRelease: (id, body) => requests.put({ url: `/api/alerts/admin/updateRelease/${id}`, body }),
    delete: id => requests.delete({ url: `/api/alerts/admin/delete/${id}` }),
  },
  pharmaAlerts: {
    get: params => requests.get({ url: '/api/pharmaAlerts/admin/get', params }),
    getCountries: () => requests.get({ url: '/api/pharmaAlerts/admin/getCountries' }),
    add: body => requests.post({ url: '/api/pharmaAlerts/admin/add', body }),
    update: (id, body) => requests.put({ url: `/api/pharmaAlerts/admin/update/${id}`, body }),
    updateRelease: (id, body) => requests.put({ url: `/api/pharmaAlerts/admin/updateRelease/${id}`, body }),
    delete: id => requests.delete({ url: `/api/pharmaAlerts/admin/delete/${id}` }),
  },
  phrContainer: {
    getByCountry: (id) => requests.get({ url: `/api/phr/container/${id}` }),
    release: (body) => requests.post({ url: '/api/phr/container/release', body }),
    update: (body) => requests.put({ url: '/api/phr/container', body }),
  },
  phrRelease: {
    get: () => requests.get({ url: '/api/phr/release/admin' }),
    unRelease: (body) => requests.put({ url: '/api/phr/unrelease', body }),
  },
  phrDocType: {
    get: () => requests.get({ url: '/api/phr/docType' }),
    add: body => requests.post({ url: '/api/phr/docType', body }),
    update: body => requests.put({ url: '/api/phr/docType', body }),
    remove: id => requests.delete({ url: `/api/phr/docType/${id}` }),
  },
  phrReport: {
    get: params => requests.get({ url: '/api/phr/report', params }),
    update: (id, body) => requests.put(({ url: `/api/phr/report/${id}`, body })),
    remove: (id) => requests.delete({ url: `/api/phr/report/${id}` }),
    add: (body) => requests.post({ url: '/api/phr/report', body })
  },
  mdrContainer: {
    getByCountry: (id) => requests.get({ url: `/api/mdr/container/${id}` }),
    release: (body) => requests.post({ url: '/api/mdr/container/release', body }),
    update: (body) => requests.put({ url: '/api/mdr/container', body }),
  },
  mdrRelease: {
    get: () => requests.get({ url: '/api/mdr/release/admin' }),
    unRelease: (body) => requests.put({ url: '/api/mdr/unrelease', body }),
  },
  mdrDocType: {
    get: () => requests.get({ url: '/api/mdr/docType' }),
    add: body => requests.post({ url: '/api/mdr/docType', body }),
    update: body => requests.put({ url: '/api/mdr/docType', body }),
    remove: id => requests.delete({ url: `/api/mdr/docType/${id}` }),
  },
  mdrReport: {
    get: params => requests.get({ url: '/api/mdr/report', params }),
    update: (id, body) => requests.put(({ url: `/api/mdr/report/${id}`, body })),
    remove: (id) => requests.delete({ url: `/api/mdr/report/${id}` }),
    add: (body) => requests.post({ url: '/api/mdr/report', body })
  },
  ccpChange: {
    get: () => requests.get({ url: '/api/ccp/admin/changes' }),
    update: (body) => requests.put({ url: '/api/ccp/admin/changes', body }),
    add: (body) => requests.post({ url: '/api/ccp/admin/changes', body }),
    remove: (id) => requests.delete({ url: `/api/ccp/admin/changes/${id}` }),
  },
  ccpCache: {
    get: (params) => requests.get({ url: '/api/ccp/admin/cache', params }),
    update: (body) => requests.put({ url: '/api/ccp/admin/cache', body }),
    add: (body) => requests.post({ url: '/api/ccp/admin/cache', body }),
    remove: (id) => requests.delete({ url: `/api/ccp/admin/cache/${id}` }),
  },
  ccpContainer: {
    getByCountryId: idc => requests.get({ url: `/api/ccp/admin/container/${idc}` }),
    update: (body) => requests.put({ url: '/api/ccp/admin/container', body }),
    release: (body) => requests.post({ url: '/api/ccp/admin/container/release', body }),
    unRelease: (body) => requests.put({ url: '/api/ccp/admin/container/unRelease', body }),
  },
  ccpRelease: {
    get: () => requests.get({ url: '/api/ccp/admin/release' }),
  },
  ccpTask: {
    updateLow: (body) => requests.post({ url: '/api/ccp/admin/task/updateLow', body }),
  },
  wizards: {
    get: (params) => requests.get({ url: '/api/wizards/admin/get', params }),
    getById: (wizardId) => requests.get({ url: `/api/wizards/admin/${wizardId}` }),
    getFiltersData: () => requests.get({ url: '/api/wizards/admin/filtersData' }),
    getSuggestions: () => requests.get({ url: '/api/wizards/admin/suggestions' }),
    getClassifications: () => requests.get({ url: '/api/wizards/admin/classifications' }),
    getClassificationsByCountry: (countryId) => requests.get({ url: `/api/wizards/admin/classifications/${countryId}` }),
    getQuestions: (wizardId) => requests.get({ url: `/api/wizards/admin/questions/${wizardId}` }),
    getAutofillQuestions: () => requests.get({ url: '/api/wizards/admin/autofillQuestions' }),
    getStandardQuestions: (params) => requests.get({ url: '/api/wizards/admin/standardQuestions', params }),
    getClassificationQuestions: (params) => requests.get({ url: '/api/wizards/admin/classificationQuestions', params }),
    getWizardDataStandardQuestions: (id) => requests.get({ url: `/api/wizards/admin/standardQuestions/wizardList/${id}` }),
    getWizardDataClassificationQuestions: (id) => requests.get({ url: `/api/wizards/admin/classificationQuestions/wizardList/${id}` }),
    getStandardQuestionsData: (body) => requests.post({ url: '/api/wizards/admin/standardQuestions/filtersData', body }),
    getClassificationQuestionsData: (body) => requests.post({ url: '/api/wizards/admin/classificationQuestions/filtersData', body }),
    delete: (wizardId) => requests.delete({ url: `/api/wizards/admin/${wizardId}` }),
    add: (body) => requests.post({ url: '/api/wizards/admin/add', body }),
    clone: (body) => requests.post({ url: '/api/wizards/admin/clone', body }),
    updateWizard: (body) => requests.put({ url: '/api/wizards/admin/update', body }),
    updateRelease: (wizardId, body) => requests.put({ url: `/api/wizards/admin/updateRelease/${wizardId}`, body }),
    updateVersion: (wizardId, body) => requests.put({ url: `/api/wizards/admin/updateVersion/${wizardId}`, body }),
    addSection: (body) => requests.post({ url: '/api/wizards/admin/sections', body }),
    updateSection: (sectionId, body) => requests.put({ url: `/api/wizards/admin/sections/${sectionId}`, body }),
    deleteSection: (sectionId) => requests.delete({ url: `/api/wizards/admin/sections/${sectionId}` }),
    addQuestion: (body) => requests.post({ url: '/api/wizards/admin/questions', body }),
    updateQuestion: (questionId, body) => requests.put({ url: `/api/wizards/admin/questions/${questionId}`, body }),
    deleteQuestion: (questionId) => requests.delete({ url: `/api/wizards/admin/questions/${questionId}` }),
    addAutofillQuestion: (body) => requests.post({ url: '/api/wizards/admin/autofillQuestions', body }),
    updateAutofillQuestion: (questionId, body) => requests.put({ url: `/api/wizards/admin/autofillQuestions/${questionId}`, body }),
    deleteAutofillQuestion: (questionId) => requests.delete({ url: `/api/wizards/admin/autofillQuestions/${questionId}` }),
    addStandardQuestion: (body) => requests.post({ url: '/api/wizards/admin/standardQuestions', body }),
    updateStandardQuestion: (questionId, body) => requests.put({ url: `/api/wizards/admin/standardQuestions/${questionId}`, body }),
    deleteStandardQuestion: (questionId) => requests.delete({ url: `/api/wizards/admin/standardQuestions/${questionId}` }),
    addClassificationQuestion: (body) => requests.post({ url: '/api/wizards/admin/classificationQuestions', body }),
    updateClassificationQuestion: (questionId, body) => requests.put({ url: `/api/wizards/admin/classificationQuestions/${questionId}`, body }),
    deleteClassificationQuestion: (questionId) => requests.delete({ url: `/api/wizards/admin/classificationQuestions/${questionId}` }),
    addSubSection: (body) => requests.post({ url: '/api/wizards/admin/subsections', body }),
    deleteSubSection: (subSectionId) => requests.delete({ url: `/api/wizards/admin/subsections/${subSectionId}` }),
    addClassification: (body) => requests.post({ url: '/api/wizards/admin/classifications', body }),
    updateClassification: (classificationId, body) => requests.put({ url: `/api/wizards/admin/classifications/update/${classificationId}`, body }),
    updateClassifications: (countryId, body) => requests.put({ url: `/api/wizards/admin/classifications/updateMany/${countryId}`, body }),
    addSuggestion: (body) => requests.post({ url: '/api/wizards/admin/suggestions', body }),
    updateSuggestion: (suggestionId, body) => requests.put({ url: `/api/wizards/admin/suggestions/update/${suggestionId}`, body }),
    getSystemTags: (params) => requests.get({ url: '/api/dms/admin/tags', params }),
    addSystemTag: (body) => requests.post({ url: '/api/dms/admin/tags', body }),
    archiveSystemTag: (tagId) => requests.delete({ url: `/api/dms/admin/${tagId}` }),
    releaseSystemTag: (tagId) => requests.post({ url: `/api/dms/admin/tags/release/${tagId}` }),
    updateSystemTag: (tagId, body) => requests.put({ url: `/api/dms/admin/tags/${tagId}`, body }),
    deleteSystemTag: (tagId) => requests.delete({ url: `/api/dms/admin/tags/${tagId}` }),
    getIdentifiers: (params) => requests.get({ url: '/api/wizards/admin/identifiers', params }),
    disableIdentifier: (id) => requests.post({ url: `/api/wizards/admin/identifiers/disable/${id}` }),
    addIdentifier: (body) => requests.post({ url: '/api/wizards/admin/identifiers', body }),
    deleteIdentifier: (id) => requests.delete({ url: `/api/wizards/admin/identifiers/${id}` }),
    updateIdentifier: (id, body) => requests.put({ url: `/api/wizards/admin/identifiers/${id}`, body }),
    getWizardData: (id, params) => requests.get({ url: `/api/wizards/admin/identifiers/wizardList/${id}`, params }),
    updateFromBuffer: (body) => requests.put({ url: '/api/wizards/admin/updateFromBuffer', body }),
  },
  forms: {
    getFiltersData: () => requests.get({ url: '/api/forms/admin/filtersData' }),
    create: (body) => requests.post({ url: '/api/forms/admin/create', body }),
    updateVersion: (templateId, body) => requests.put({ url: `/api/forms/admin/updateVersion/${templateId}`, body }),
    clone: (body) => requests.post({ url: '/api/forms/admin/clone', body }),
    getPage: ({ page, filters }) => requests.get({ url: '/api/forms/admin/page', params: { page, filters } }),
    getById: (templateId) => requests.get({ url: `/api/forms/admin/${templateId}` }),
    delete: (templateId) => requests.delete({ url: `/api/forms/admin/${templateId}` }),
    addChapter: ({ templateId, name }) => requests.post({ url: `/api/forms/chapters/admin/${templateId}`, body: { name } }),
    updateChapter: ({ chapterId, name }) => requests.put({ url: `/api/forms/chapters/admin/${chapterId}`, body: { name } }),
    deleteChapter: ({ templateId, chapterId }) => requests.delete({ url: `/api/forms/chapters/admin/${templateId}/${chapterId}` }),
    addSection: ({ chapterId, name }) => requests.post({ url: `/api/forms/sections/admin/${chapterId}`, body: { name } }),
    updateSection: ({ sectionId, name }) => requests.put({ url: `/api/forms/sections/admin/${sectionId}`, body: { name } }),
    deleteSection: ({ sectionId }) => requests.delete({ url: `/api/forms/sections/admin/${sectionId}` }),
    addRequirement: ({ sectionId, body }) => requests.post({ url: `/api/forms/requirements/admin/${sectionId}`, body }),
    updateRequirement: ({ requirementId, body }) => requests.put({ url: `/api/forms/requirements/admin/${requirementId}`, body }),
    deleteRequirement: ({ requirementId }) => requests.delete({ url: `/api/forms/requirements/admin/${requirementId}` }),
    addSubRequirement: ({ requirementId, body }) => requests.post({ url: `/api/forms/subrequirements/admin/${requirementId}`, body }),
    updateSubRequirement: ({ subRequirementId, body }) => requests.put({ url: `/api/forms/subrequirements/admin/${subRequirementId}`, body }),
    deleteSubRequirement: ({ subRequirementId }) => requests.delete({ url: `/api/forms/subrequirements/admin/${subRequirementId}` }),
    updateRelease: (templateId, body) => requests.put({ url: `/api/forms/admin/updateRelease/${templateId}`, body }),
  },
  regPlan: {
    getWizard: () => requests.get({ url: '/api/regplan/wizards' }),
    updateWizard: (body) => requests.put({ url: '/api/regplan/wizards', body }),
    getQuestionIds: () => requests.get({ url: '/api/regplan/questions/ids' }),
    getQuestions: () => requests.get({ url: '/api/regplan/questions' }),
    addSection: (body) => requests.post({ url: '/api/regplan/sections', body }),
    updateSection: (body) => requests.put({ url: '/api/regplan/sections', body }),
    deleteSection: (sectionId) => requests.delete({ url: `/api/regplan/sections/${sectionId}` }),
    addQuestion: (body) => requests.post({ url: '/api/regplan/questions', body }),
    updateQuestion: (body) => requests.put({ url: '/api/regplan/questions', body }),
    deleteQuestion: (questionId) => requests.delete({ url: `/api/regplan/questions/${questionId}` }),
    addSubSection: (body) => requests.post({ url: '/api/regplan/subsections', body }),
    deleteSubSection: (sectionId) => requests.delete({ url: `/api/regplan/subsections/${sectionId}` }),
  },
  autofillHistory: {
    getById: (id) => requests.get({ url: `/api/autofillhistory/get/${id}` }),
    get: params => requests.get({ url: '/api/autofillhistory/get', params }),
  },
  team: {
    get: params => requests.get({ url: '/api/team', params })
  },
  guide: {
    get: () => requests.get({ url: '/api/guide' }),
    update: (body) => requests.put({ url: '/api/guide', body }),
  },
  grafanaDashboards: {
    get: params => requests.get({ url: '/api/grafanaDashboards', params }),
    add: body => requests.post({ url: '/api/grafanaDashboards', body }),
    update: body => requests.put({ url: '/api/grafanaDashboards', body }),
    delete: id => requests.delete({ url: `/api/grafanaDashboards/${id}` }),
  },
  versions: {
    get: (params) => requests.get({ url: '/api/versions/get', params }),
    getById: (id, params) => requests.get({ url: `/api/versions/get/${id}`, params }),
    getDiff: (params) => requests.get({ url: '/api/versions/diff', params }),
  },
  legislations: {
    get: (params) => requests.get({ url: '/api/legislation/admin/get', params }),
    getById: (id) => requests.get({ url: `/api/legislation/admin/get/${id}` }),
    getByIds: (params) => requests.get({ url: '/api/legislation/admin/byIds', params }),
    getUsage: (params) => requests.get({ url: '/api/legislation/admin/getUsage', params }),
    add: (body) => requests.post({ url: '/api/legislation/admin/add', body }),
    update: (body) => requests.post({ url: '/api/legislation/admin/update', body }),
  },
  cron: {
    sync: (body) => requests.post({ url: '/api/cron/sync', body }),
  },
  dbMode: {
    change: (body) => requests.post({ url: '/api/dbmode/change', body }),
  },
  widget: {
    getTimer: () => requests.get({ url: '/api/admin/widget/timer' }),
    runTimer: (body) => requests.post({ url: '/api/admin/widget/timer', body }),
    getMigrations: (params) => requests.get({ url: '/api/admin/widget/migrations', params }),
    getServices: () => requests.get({ url: '/api/admin/widget/services' }),
  },
};
