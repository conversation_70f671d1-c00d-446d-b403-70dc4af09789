<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <title>RegDesk Admin v2</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="description" content="">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <link rel="shortcut icon" href="https://www.regdesk.co/wp-content/uploads/2018/11/favicon.ico">
</head>
<body>
  <div id="app" style="height: 100%;">
    <style>
      #loader {position: absolute;left: 0;top: 0;right: 0;bottom: 0;display: flex;flex-direction: column;align-items: center;justify-content: center;}
      .ant-spin{font-size:14px;font-variant:tabular-nums;-webkit-font-feature-settings:'tnum';font-feature-settings:'tnum';line-height:1.5;color:rgba(0,0,0,0.65);-webkit-box-sizing:border-box;box-sizing:border-box;margin:0;padding:0;list-style:none;color:#1890ff;vertical-align:middle;text-align:center;opacity:0;position:absolute;-webkit-transition:-webkit-transform .3s cubic-bezier(.78, .14, .15, .86);transition:-webkit-transform .3s cubic-bezier(.78, .14, .15, .86);transition:transform .3s cubic-bezier(.78, .14, .15, .86);transition:transform .3s cubic-bezier(.78, .14, .15, .86), -webkit-transform .3s cubic-bezier(.78, .14, .15, .86);display:none}.ant-spin-spinning{opacity:1;position:static;display:inline-block}.ant-spin-nested-loading{position:relative}.ant-spin-nested-loading>div>.ant-spin{display:block;position:absolute;height:100%;max-height:400px;width:100%;z-index:4}.ant-spin-nested-loading>div>.ant-spin .ant-spin-dot{position:absolute;top:50%;left:50%;margin:-10px}.ant-spin-nested-loading>div>.ant-spin .ant-spin-text{position:absolute;top:50%;width:100%;padding-top:5px;text-shadow:0 1px 2px #fff}.ant-spin-nested-loading>div>.ant-spin.ant-spin-show-text .ant-spin-dot{margin-top:-20px}.ant-spin-nested-loading>div>.ant-spin-sm .ant-spin-dot{margin:-7px}.ant-spin-nested-loading>div>.ant-spin-sm .ant-spin-text{padding-top:2px}.ant-spin-nested-loading>div>.ant-spin-sm.ant-spin-show-text .ant-spin-dot{margin-top:-17px}.ant-spin-nested-loading>div>.ant-spin-lg .ant-spin-dot{margin:-16px}.ant-spin-nested-loading>div>.ant-spin-lg .ant-spin-text{padding-top:11px}.ant-spin-nested-loading>div>.ant-spin-lg.ant-spin-show-text .ant-spin-dot{margin-top:-26px}.ant-spin-container{position:relative;-webkit-transition:opacity .3s;transition:opacity .3s}.ant-spin-container:after{content:'';position:absolute;left:0;right:0;top:0;bottom:0;background:#fff;opacity:0;pointer-events:none;-webkit-transition:all .3s;transition:all .3s;height:100%;width:100%;z-index:10;display:none \9}.ant-spin-blur{pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;opacity:.5}.ant-spin-blur:after{opacity:.4;pointer-events:auto}.ant-spin-tip{color:rgba(0,0,0,0.45)}.ant-spin-dot{position:relative;display:inline-block;font-size:20px;width:20px;height:20px}.ant-spin-dot i{width:9px;height:9px;border-radius:100%;background-color:#1890ff;-webkit-transform:scale(.75);-ms-transform:scale(.75);transform:scale(.75);display:block;position:absolute;opacity:.3;-webkit-animation:antSpinMove 1s infinite linear alternate;animation:antSpinMove 1s infinite linear alternate;-webkit-transform-origin:50% 50%;-ms-transform-origin:50% 50%;transform-origin:50% 50%}.ant-spin-dot i:nth-child(1){left:0;top:0}.ant-spin-dot i:nth-child(2){right:0;top:0;-webkit-animation-delay:.4s;animation-delay:.4s}.ant-spin-dot i:nth-child(3){right:0;bottom:0;-webkit-animation-delay:.8s;animation-delay:.8s}.ant-spin-dot i:nth-child(4){left:0;bottom:0;-webkit-animation-delay:1.2s;animation-delay:1.2s}.ant-spin-dot-spin{-webkit-transform:rotate(45deg);-ms-transform:rotate(45deg);transform:rotate(45deg);-webkit-animation:antRotate 1.2s infinite linear;animation:antRotate 1.2s infinite linear}.ant-spin-sm .ant-spin-dot{font-size:14px;width:14px;height:14px}.ant-spin-sm .ant-spin-dot i{width:6px;height:6px}.ant-spin-lg .ant-spin-dot{font-size:32px;width:32px;height:32px}.ant-spin-lg .ant-spin-dot i{width:14px;height:14px}.ant-spin.ant-spin-show-text .ant-spin-text{display:block}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active){.ant-spin-blur{background:#fff;opacity:.5}}@-webkit-keyframes antSpinMove{to{opacity:1}}@keyframes antSpinMove{to{opacity:1}}@-webkit-keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}@keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}
    </style>
    <div id="loader">
      <div class="ant-spin ant-spin-lg ant-spin-spinning">
        <span class="ant-spin-dot ant-spin-dot-spin"><i></i><i></i><i></i><i></i></span>
      </div>
    </div>
  </div>
</body>
</html>
