import React from 'react';
import MarkdownIt from 'markdown-it';
import MdEditor from 'react-markdown-editor-lite';
import insert from 'markdown-it-ins';
import mila from 'markdown-it-link-attributes';
import 'react-markdown-editor-lite/lib/index.css';

const mdParser = new MarkdownIt().use(insert);

mdParser.use(mila, { attrs: { target: '_blank', rel: 'noopener' } });

export default (props) => {
  const { label, setValue, value, extra, height } = props;

  const handleEditorChange = ({ text }) => setValue(text);

  return (
    <div style={{ display: 'flex', margin: '0 0 20px' }}>
      <div
        style={{
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: '#f1f1f1',
          padding: '17px',
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            whiteSpace: 'pre',
            fontWeight: 'bold',
            backgroundColor: '#f1f1f1',
            fontSize: '115%',
            padding: '0 0 8px',
          }}
        >
          {label}
          {extra}
        </div>

        <MdEditor
          style={{ height: height || 500 }}
          renderHTML={(text) => mdParser.render(text)}
          onChange={handleEditorChange}
          value={value}
        />
      </div>
    </div>
  );
};
