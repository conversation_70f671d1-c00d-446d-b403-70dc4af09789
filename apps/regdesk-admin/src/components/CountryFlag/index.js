import React from 'react';
import { Tooltip } from 'antd';
import classNames from 'classnames';
import { alpha3ToAlpha2, alpha3ToName } from '../../utils/countries';
import styles from './index.less';

export default function CountryFlag({ countryId, className, name, fullName, ...props }) {
  if (name) {
    return (
      <span {...props}>
        <span
          className={classNames(
            `flag-icon flag-icon-squared flag-icon-${alpha3ToAlpha2(countryId).toLowerCase()}`,
            styles.flag,
            className
          )}
        />

        {name}
      </span>
    );
  }

  return (
    <span {...props}>
      <Tooltip placement='bottom' title={alpha3ToName(countryId)}>
        <span
          className={classNames(
            `flag-icon flag-icon-squared flag-icon-${alpha3ToAlpha2(countryId).toLowerCase()}`,
            styles.flag,
            className
          )}
        />

        {fullName === false ? <span>{alpha3ToName(countryId)}</span> : <span>{countryId}</span>}
      </Tooltip>
    </span>
  );
}
