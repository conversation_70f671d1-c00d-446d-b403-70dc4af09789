import React, { PureComponent } from 'react';
import { Button, Modal, Row, Col, Card, Input, Icon, Table, Select, message } from 'antd';
import Uploader from './index';
import api from '../../utils/api';
import { allCountriesWithEU } from '../../utils/countries';

const { Option } = Select;
const ButtonGroup = Button.Group;

export default class UploadModal extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      showModal: false,
      menu: 'upload',
      data: [],
      loading: false,
      filters: {},
      pagination: {},
    };

    this.timerSearch = null;

    this.columns = [
      {
        title: 'Name',
        dataIndex: 'name',
        render: (name, record) => (
          <a
            target='_blank'
            href={`/api/doc/${record._id}/${name}`}
            rel='noreferrer'
            style={{
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              display: 'inline-block',
              maxWidth: 400,
              marginBottom: '-8px',
            }}
          >
            {name}
          </a>
        )
      },
      {
        title: 'Upload date',
        dataIndex: 'createdAt',
        render: createdAt => createdAt && new Date(createdAt).getFullUTC(),
      },
      {
        title: 'Action',
        dataIndex: '_id',
        render: (id, record) => (
          <a
            onClick={() => {
              this.props.onUpload && this.props.onUpload(`/api/doc/${id}/${record.name}`);
              this.setState({ showModal: false });
            }}
          >
            Select
          </a>
        )
      },
    ];
  }

  componentDidMount() {
    this.load();
  }

  /**
   * Load common files
   * @returns {Promise<void>}
   */
  load = async({ newPagination = {}, newFilters = {} } = {}) => {
    const { filters, pagination: pg } = this.state;

    this.setState({ loading: true });

    const { list: data = [], pagination } = await api.doc.getCommon({
      filters: { ...filters, ...newFilters },
      pagination: { ...pg, ...newPagination }
    });

    this.setState({ data, pagination, loading: false });
  }

  /**
   * Show modal
   * @param state
   */
  showModal = (state = false) => this.setState({ showModal: state });

  /**
   * Handle table change
   * @param pagination
   */
  handleTableChange = (pagination) => this.load({ newPagination: pagination });

  render() {
    const { info } = this.props;
    const { menu, showModal, loading, data, pagination, filters = {} } = this.state;
    const uploadData = { ...info, common: true };

    return (
      <Modal
        title='Uploader'
        visible={showModal}
        footer={null}
        centered
        destroyOnClose
        maskClosable={false}
        onCancel={() => this.showModal(false)}
        onClose={() => this.showModal(false)}
        width={1000}
        zIndex={1001}
      >
        {showModal && (
          <div>
            <div style={{ display: 'flex', justifyContent: 'center', marginBottom: 20 }}>
              <ButtonGroup>
                <Button type={menu === 'upload' ? 'primary' : ''} onClick={() => this.setState({ menu: 'upload' })}>
                  Upload to AWS S3
                </Button>

                <Button type={menu === 'select' ? 'primary' : ''} onClick={() => this.setState({ menu: 'select' })}>
                  Select from existing files
                </Button>
              </ButtonGroup>
            </div>

            {menu === 'upload' && (
              <div style={{ height: 450, padding: '0 0 30px 0' }}>
                <Uploader
                  showUploadList={false}
                  data={uploadData}
                  uploadType='dragger'
                  onChange={(res) => {
                    if (this.props.onUpload && res[0] && res[0].link) {
                      this.props.onUpload(res[0].link);
                      this.setState({ showModal: false });
                      this.load();
                    } else {
                      message.error('Sorry, problems with downloading file');
                    }
                  }}
                />
              </div>
            )}

            {menu === 'select' && (
              <div style={{ height: 450 }}>
                <Row gutter={24}>
                  <Col xl={6} lg={6} md={24} sm={24} xs={24}>
                    <Card
                      size='small'
                      title='Filters'
                      bodyStyle={{ display: 'flex', flexDirection: 'column' }}
                      extra={Object.keys(filters).length !== 0 && (
                        <a
                          onClick={() => {
                            this.setState({ filters: {} });
                            this.state.filters = {};
                            this.load({ newFilters: {} });
                          }}
                        >
                          Clean all
                        </a>
                      )}
                    >
                      <Input
                        addonAfter={<Icon type='search' />}
                        placeholder='Search by Name'
                        style={{ marginBottom: 12 }}
                        value={filters.name || ''}
                        onChange={e => {
                          const val = e.target.value;

                          if (!val) {
                            const { name, ...newFilters } = filters;

                            this.setState({ filters: { ...newFilters } });
                          } else {
                            this.setState({ filters: { ...filters, name: val } });
                          }

                          clearTimeout(this.timerSearch);

                          this.timerSearch = setTimeout(() => {
                            const newPagination = { ...this.state.pagination, current: 1 };

                            this.load({ newPagination });
                          }, 600);
                        }}
                      />

                      <Select
                        showSearch
                        value={filters.module}
                        placeholder='Search by Module'
                        style={{ marginBottom: 12 }}
                        onChange={module => {
                          const newFilters = { ...this.state.filters, module };
                          const newPagination = { ...this.state.pagination, current: 1 };

                          this.setState({ filters: newFilters });
                          this.load({ newFilters, newPagination });
                        }}
                      >
                        <Option value='pharma'>Pharma Reg</Option>
                        <Option value='md'>Device Reg</Option>
                        <Option value='ccp'>CCP</Option>
                        <Option value='guide'>Guide</Option>
                        <Option value='wizard'>Wizard</Option>
                      </Select>

                      <Select
                        showSearch
                        value={filters.countryId}
                        placeholder='Search by Country'
                        onChange={countryId => {
                          const newFilters = { ...this.state.filters, countryId };
                          const newPagination = { ...this.state.pagination, current: 1 };

                          this.setState({ filters: newFilters });
                          this.load({ newFilters, newPagination });
                        }}
                      >
                        {allCountriesWithEU.map(({ name, alpha3code }) => <Option key={name} value={alpha3code}>{name}</Option>)}
                      </Select>
                    </Card>
                  </Col>

                  <Col xl={18} lg={18} md={24} sm={24} xs={24}>
                    <Table
                      bordered
                      rowKey='_id'
                      size='small'
                      loading={loading}
                      columns={this.columns}
                      dataSource={data}
                      pagination={{
                        defaultPageSize: 10,
                        ...pagination,
                      }}
                      onChange={this.handleTableChange}
                    />
                  </Col>
                </Row>
              </div>
            )}
          </div>
        )}
      </Modal>
    );
  }
}
