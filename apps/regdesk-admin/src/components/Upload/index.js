import React, { PureComponent } from 'react';
import config from 'config';
import { Icon, message, Upload } from 'antd';
import pdfImage from './doc/pdf.png';
import docImage from './doc/doc.png';

const { Dragger } = Upload;

export default class Uploader extends PureComponent {
  constructor(props) {
    super(props);

    this.url = `${config.apiServer}/api/doc/`;

    this.state = {
      docs: this.fill(this.props.defaultDocs),
    };
  }

  clearFiles = () => this.setState({ docs: [] });

  /**
   * Event change upload
   * @param file
   * @param fileList
   */
  onChange = ({ file, fileList }) => {
    if (!file.status) return;

    this.setState({
      docs: fileList.map(item => {
        if (item.response) {
          return { ...item, ...this.getUrl({ ...item.response, type: item.type, idf: item.response.idf }) };
        }

        return item;
      }),
    });

    if (file.status === 'done') {
      message.success(`${file.name} file successfully uploaded`);
      this.ping(fileList);
    }

    if (file.status === 'error') message.error(`${file.name} file upload error`);
  };

  getFileList = () => this.state.docs;

  /**
   * Remove file
   * @param file
   */
  onRemove = file => {
    const { docs } = this.state;
    const newDocs = docs.filter(({ uid }) => uid !== file.uid);

    this.setState({ docs: newDocs });
    this.ping(newDocs);
  };

  /**
   * Check upload file
   * @param file
   * @param fileList
   * @returns {boolean}
   */
  onBeforeUpload = (file, fileList) => {
    const { fileType, maxFiles, maxFileSize, beforeUpload } = this.props;

    let error = '';

    if (fileList.length > maxFiles) {
      error = `Maximum ${maxFiles} file(s)`;
    }

    if (fileType === 'image' && file.type !== 'image/jpeg' && file.type !== 'image/png' && file.type !== 'image/jpg') {
      error = `Loading file ${file.name} is stopped. Invalid picture format`;
    }

    if (fileType === 'pdf' && file.type !== 'application/pdf') {
      error = `Loading file ${file.name} is stopped. Invalid document format`;
    }

    if (file.size / 1024 / 1024 > maxFileSize) {
      error = `Loading file ${file.name} is stopped. File more than ${maxFileSize} Mb`;
    }

    if (error) {
      message.error(error);

      return false;
    }

    if (beforeUpload) return beforeUpload(file, fileList);

    return true;
  };

  /**
   * Show popup for image and open new tab for any files
   * @param file
   */
  onPreview = file => {
    if (file.status !== 'done') return;

    window.open(file.url || file.thumbUrl, '_blank');
  };

  /**
   * Get url for file
   * @param type
   * @param idf
   * @param common
   * @param name
   * @returns {{thumbUrl: *, url: (string|*)}}
   */
  getUrl = ({ type, idf, common, name }) => {
    const url = common ? `${this.url}${idf}/${name}` : this.url + idf;
    const props = { url, thumbUrl: docImage };

    if (type === 'image/jpeg' || type === 'image/png' || type === 'image/jpg') {
      props.thumbUrl = url;
    }

    if (type === 'application/pdf') {
      props.thumbUrl = pdfImage;
      props.icon = 'file-pdf';
    }

    return props;
  };

  /**
   * Get icon
   * @returns {string}
   */
  getIcon = () => {
    const { fileType } = this.props;

    if (fileType === 'image') return 'picture';
    if (fileType === 'pdf') return 'file-pdf';

    return 'plus';
  };

  /**
   * Fill data for uploader
   * @param defaultValue
   * @returns {*}
   */
  fill = defaultValue => {
    const docs = defaultValue || [];

    return docs.map(item => {
      const { _id: idf, type } = item;
      const doc = { ...this.getUrl({ ...item, type, idf }) };

      doc.error = undefined;
      doc.lastModified = new Date();
      doc.name = idf;
      doc.status = 'done';
      doc.type = type;
      doc.uid = idf;
      doc.idf = idf;

      return doc;
    });
  };

  /**
   * Ping to parent component
   * @param files
   */
  ping = files => {
    const { onChange, shouldResetList } = this.props;

    if (onChange) {
      onChange(
        files.map(file => ({
          _id: file.idf || file.response.idf,
          link: file.link || (file.response && file.response.link),
          type: file.type,
        }))
      );
    }

    if (shouldResetList) this.setState({ docs: [] });
  };

  render() {
    const { docs } = this.state;
    const {
      data,
      defaultDocs,
      fileType,
      maxFileSize,
      shouldResetList,
      beforeUpload,
      maxFiles,
      disabled,
      listType,
      showUploadList,
      uploadType,
      name,
      multiple,
      textBtn,
      children,
      ...props
    } = this.props;

    if (uploadType === 'dragger') {
      return (
        <Dragger
          {...props}
          withCredentials
          name={name}
          multiple={multiple}
          action={this.url}
          onChange={this.onChange}
          onRemove={this.onRemove}
          beforeUpload={this.onBeforeUpload}
          fileList={docs}
          data={data}
          disabled={disabled}
        >
          <p className='ant-upload-drag-icon'>
            <Icon type='upload' style={{ fontSize: '32px' }} />
          </p>

          <p className='ant-upload-hint'>Click or drag file to this area to upload</p>
        </Dragger>
      );
    }

    let btn = (
      <a>
        <Icon type='upload' style={{ marginRight: 5 }} />
        {textBtn}
      </a>
    );

    if (listType === 'picture-card') {
      btn = (
        <div>
          <Icon type={this.getIcon()} style={{ fontSize: 26, color: '#999' }} />
          <div className='ant-upload-text'>{textBtn}</div>
        </div>
      );
    }

    if (children) btn = children;
    if (docs.length >= maxFiles) btn = null;

    return (
      <Upload
        {...props}
        action={this.url}
        listType={listType}
        onChange={this.onChange}
        onPreview={this.onPreview}
        beforeUpload={this.onBeforeUpload}
        onRemove={this.onRemove}
        showUploadList={showUploadList}
        withCredentials
        data={data}
        fileList={docs}
        disabled={disabled}
      >
        {btn}
      </Upload>
    );
  }
}
