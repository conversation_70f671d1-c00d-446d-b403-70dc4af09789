import React from 'react';
import { useTranslation } from 'react-i18next';

export default function TranslationOption({ language }) {
  const { i18n } = useTranslation();

  const changeLanguage = lng => i18n.changeLanguage(lng);

  return (
    <div onClick={() => changeLanguage(language)}>
      <span className={`flag-icon flag-icon-squared flag-icon-${language !== 'en' ? language : 'gb'}`} />

      <span
        style={{
          marginLeft: 5,
          fontFamily: "'Helvetica Neue Light', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",
        }}
      >
        {language?.toUpperCase()}
      </span>
    </div>
  );
}
