import React from 'react';
import { Tooltip } from 'antd';
import Item from './Item';

/**
 * ItemRadio
 */
export default class ItemRadio extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value,
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value });
  };

  getValue = () => this.state.value;

  handleChange = value => this.setState({ value });

  render() {
    const { label, data } = this.props;
    const { value } = this.state;

    return (
      <Item label={label}>
        {data.map(v => (
          <Tooltip title={v.value === null && 'Not Mentioned in Regulations'}>
            <label key={v.value} style={{ marginRight: '10px' }}>
              <input
                type='checkbox'
                checked={value === v.value}
                onChange={this.handleChange.bind(this, v.value)}
              />{' '}
              {v.text}
            </label>
          </Tooltip>
        ))}
      </Item>
    );
  }
}
