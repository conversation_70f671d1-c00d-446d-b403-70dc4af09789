import React from 'react';
import { Button, Input, message, Table, Tooltip } from 'antd';
import Item from './Item';
import styles from './index.less';

/**
 * Item Array
 */
export default class ItemArray extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value || [],
      text: '',
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value, text: '' });
  };

  getValue = () => this.state.value;

  addItem = () => {
    const { autoSave, handleSubmitForm } = this.props;
    const { text, value } = this.state;

    if (!text) return message.warn(`Please enter ${this.props.placeholder || 'text'}`);

    value.push(text);
    this.setState({ value, text: '' });
    if (autoSave && handleSubmitForm) handleSubmitForm();
  };

  removeItem = id => {
    const { handleSubmitForm } = this.props;
    const { value } = this.state;

    value.splice(id, 1);
    this.setState({ value });
    if (handleSubmitForm) handleSubmitForm();
  };

  render() {
    const { label, readOnly, addonAfter, accessRemove, ...props } = this.props;
    const { value = [], text } = this.state;
    const newData = value.map((item, i) => ({ id: i, name: item }));

    return (
      <Item label={label}>
        <div style={{ display: 'flex', marginBottom: 20 }}>
          <Input
            {...props}
            className='b-flex-flex'
            value={text}
            onChange={e => this.setState({ text: e.target.value })}
          />

          <Button
            type='primary'
            onClick={this.addItem}
            style={{ marginLeft: 20 }}
          >
            Add
          </Button>
        </div>

        <Table
          bordered
          rowKey='id'
          size='small'
          columns={[
            {
              title: 'Name',
              dataIndex: 'name',
            },
            {
              title: 'Action',
              dataIndex: 'id',
              render: id => (
                <Tooltip title={!accessRemove && 'No permission'}>
                  <a className={!accessRemove ? styles.disabled : ''} onClick={() => accessRemove && this.removeItem(id)}>Delete</a>
                </Tooltip>
              ),
            },
          ]}
          dataSource={newData}
        />
      </Item>
    );
  }
}
