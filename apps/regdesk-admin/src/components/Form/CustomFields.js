import React from 'react';
import { Button, Tooltip, message } from 'antd';
import ItemInput from './ItemInput';

/**
 * Custom Fields
 */
export default class CustomFields extends React.Component {
  constructor(props) {
    super(props);

    this.customFieldsRefs = {};

    this.state = {
      value: props.value || [],
      input: '',
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value || [] });
  };

  getValue = () => {
    return Object.entries(this.customFieldsRefs)
      .filter(([, ref]) => ref !== null)
      .map(([key, ref]) => ({ name: key, content: ref?.getValue() }));
  };

  handleAddCustomField = () => {
    const { handleSubmitForm } = this.props;
    const { input, value } = this.state;

    if (value.map(customField => customField.name).includes(input)) {
      message.warn('Duplicate field!');

      return;
    }

    value.push({ name: input, content: '' });

    this.setState({ value, input: '' });
    this.customFieldsRefs[input] = undefined;

    if (handleSubmitForm) handleSubmitForm();
  };

  handleRemoveCustomField = key => {
    const { handleSubmitForm } = this.props;
    const { value } = this.state;
    const newValue = value.filter(item => item.name !== key);

    this.setState({ value: newValue });
    delete this.customFieldsRefs[key];

    if (handleSubmitForm) handleSubmitForm();
  };

  onChange = (name, content) => {
    const { onChange } = this.props;
    const { value = [] } = this.state;

    const index = value.findIndex(item => item.name === name);

    if (index >= 0) {
      value.splice(index, 1, { name, content });

      if (onChange) onChange(content);
    }
  }

  render() {
    const { accessRemove } = this.props;
    const { value = [], input } = this.state;

    return (
      <div>
        {value.map(customField =>
          <ItemInput
            addonAfter={
              <Tooltip title={!accessRemove && 'No permission'}>
                <Button
                  type='link'
                  size='small'
                  icon='minus-circle'
                  disabled={!accessRemove}
                  style={{ color: 'rgba(0, 0, 0, 0.65)' }}
                  onClick={() => this.handleRemoveCustomField(customField.name)}
                />
              </Tooltip>
            }
            ref={ref => { this.customFieldsRefs[customField.name] = ref; }}
            key={customField.name}
            label={customField.name}
            value={customField.content || ''}
            onChange={content => this.onChange(customField.name, content)}
          />
        )}

        <input
          type='text'
          className='ant-input'
          value={input}
          onChange={e => this.setState({ input: e.target.value })}
          style={{ width: '400px', marginRight: 10 }}
        />

        <Button onClick={this.handleAddCustomField}>Add</Button>
        <div className='b-padding-10' />
      </div>
    );
  }
}
