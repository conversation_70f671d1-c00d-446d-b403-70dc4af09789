import React from 'react';
import ItemInput from './ItemInput';
import Form<PERSON>ithButtonTop from './FormWithButtonTop';
import FormWithButton from './FormWithButton';
import CustomFields from './CustomFields';
import ItemSample from './ItemSample';
import MarkDownEditor from './MarkDownEditor';
import Links from './Links';
import ItemNumber from './ItemNumber';
import ItemRadio from './ItemRadio';
import ItemDocType from './ItemDocType';
import ItemCategory from './ItemCategory';
import ItemLevel from './ItemLevel';
import ItemCountryCode from './ItemCountryCode';
import ItemArRequired from './ItemArRequired';
import Classifications from './Classifications';
import Procedure from './Procedure';
import Requirements from './Requirements';
import Changes from './Changes';
import ItemRange from './ItemRange';
import ItemCheckbox from './ItemCheckbox';
import ItemId from './ItemId';
import Buffer from './Buffer';
import Versions from './Versions';
import ItemArray from './ItemArray';
import Item from './Item';
import DocumentList from './DocumentList';
import ApprovalRoutes from './ApprovalRoutes';
import Licenses from './Licenses';
import ItemSwitch from './ItemSwitch';
import Timer from './Timer';
import events from '../../utils/events';

export {
  Item,
  FormWithButton,
  FormWithButtonTop,
  ItemInput,
  CustomFields,
  ItemSample,
  MarkDownEditor,
  Links,
  ItemNumber,
  ItemRadio,
  ItemDocType,
  ItemCategory,
  ItemLevel,
  ItemCountryCode,
  ItemArRequired,
  Classifications,
  Procedure,
  Requirements,
  ItemRange,
  ItemId,
  Buffer,
  ItemCheckbox,
  ItemArray,
  DocumentList,
  ApprovalRoutes,
  Licenses,
  Versions,
  ItemSwitch,
  Timer,
  Changes,
};

export default class Form extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      data: props.data || {},
    };
  }

  /**
   * ComponentDidMount
   */
  componentDidMount() {
    const { autoSave } = this.props;

    if (autoSave) {
      window.addEventListener('beforeunload', this.beforeUnloadHandler);
      events.on('checkRegForm', this.checkTimer);
    }
  }

  /**
   * ComponentWillReceiveProps
   * @param nextProps
   */
  componentWillReceiveProps = nextProps => {
    const { onAutoSave, fields, itemTitle, autoSave } = this.props;

    if (autoSave && itemTitle !== nextProps.itemTitle && fields && onAutoSave) {
      const data = this.getValue();

      Timer.check(() => onAutoSave(data));
      Timer.stop();
    }

    this.setState({ data: nextProps.data || {} });
  };

  /**
   * ComponentWillUnmount
   */
  componentWillUnmount() {
    const { autoSave } = this.props;

    if (autoSave) {
      this.checkTimer();
      window.removeEventListener('beforeunload', this.beforeUnloadHandler);
      events.off('checkRegForm', this.checkTimer);
    }
  }

  /**
   * Before Unload Handler
   * @param event
   */
  beforeUnloadHandler = (event) => {
    event.preventDefault();
    event.returnValue = true;
    this.checkTimer();
  };

  /**
   * Check timer
   */
  checkTimer = () => Timer.check(this.handleSubmit);

  /**
   * Update
   */
  handleSubmit = () => {
    const { onUpdate, fields, autoSave } = this.props;

    if (autoSave) Timer.stop();

    if (onUpdate && fields) {
      const data = this.getValue();

      onUpdate(data);
    }
  };

  /**
   * Event Change
   */
  onChange = () => {
    const { autoSave } = this.props;

    if (autoSave) Timer.start(this.handleSubmit);
  }

  /**
   * Get value
   * @returns {{checked: boolean, comment: string}}
   */
  getValue = () => {
    const { fields } = this.props;
    const data = { checked: false, comment: '' };

    fields.forEach(({ key }) => { data[key] = this[`ref${key}`].getValue(); });

    return data;
  };

  /**
   * Agree from
   * @param checked
   * @param comment
   */
  handleAgree = (checked, comment) => {
    const { onUpdate, autoSave } = this.props;

    if (autoSave) Timer.stop();

    if (onUpdate) {
      const data = this.getValue();

      onUpdate({ ...data, checked, comment });
    }
  };

  render() {
    const { data = {} } = this.state;
    const { loading, fields = [], onRemove, country = {}, module, buttonTop = false, autoSave, accessRemoveData, accessEnableBausch } = this.props;
    const { checked, comment } = data;
    const FormContainer = buttonTop ? FormWithButtonTop : FormWithButton;

    return (
      <FormContainer
        onSubmit={this.handleSubmit}
        onAgree={this.handleAgree}
        onClear={onRemove}
        loading={loading}
        check={checked}
        comment={comment}
        showAgree={checked !== undefined}
        getValue={this.getValue}
        accessRemoveData={accessRemoveData}
        {...this.props}
      >
        {fields.map(({ key, type, ...props }) => {
          if (type === 'markDown') {
            return (
              <MarkDownEditor
                key={key}
                ref={ref => { this[`ref${key}`] = ref; }}
                value={data[key] || ''}
                onChange={this.onChange}
                {...props}
              />
            );
          }

          if (type === 'links') {
            return (
              <Links
                key={key}
                ref={ref => { this[`ref${key}`] = ref; }}
                data={data[key] || []}
                accessRemove={accessRemoveData}
                params={{ countryId: country.id, module }}
                handleSubmitForm={this.handleSubmit}
                autoSave={autoSave}
                {...props}
              />
            );
          }

          if (type === 'procedure') {
            return (
              <Procedure
                key={key}
                ref={ref => { this[`ref${key}`] = ref; }}
                value={data[key]}
                accessRemove={accessRemoveData}
                handleSubmitForm={this.handleSubmit}
                {...props}
              />
            );
          }

          if (type === 'input') {
            return (
              <ItemInput
                key={key}
                ref={ref => { this[`ref${key}`] = ref; }}
                value={data[key]}
                onChange={this.onChange}
                {...props}
              />
            );
          }

          if (type === 'sample') {
            return (
              <ItemSample
                page='md'
                countryId={country.id}
                key={key}
                label='Sample'
                ref={ref => { this[`ref${key}`] = ref; }}
                value={data[key]}
                accessRemove={accessRemoveData}
                handleSubmitForm={this.handleSubmit}
                onChange={this.onChange}
                autoSave={autoSave}
                {...props}
              />
            );
          }

          if (type === 'customFields') {
            return (
              <CustomFields
                key={key}
                ref={ref => { this[`ref${key}`] = ref; }}
                value={data[key]}
                accessRemove={accessRemoveData}
                onChange={this.onChange}
                handleSubmitForm={this.handleSubmit}
                {...props}
              />
            );
          }

          if (type === 'classifications') {
            return (
              <Classifications
                key={key}
                ref={ref => { this[`ref${key}`] = ref; }}
                value={data[key]}
                accessRemove={accessRemoveData}
                accessEnableBausch={accessEnableBausch}
                handleSubmitForm={this.handleSubmit}
                autoSave={autoSave}
                buffer={this.props.buffer}
                module={this.props.module}
                country={this.props.country}
                itemTitle={this.props.itemTitle}
                {...props}
              />
            );
          }

          if (type === 'requirements') {
            return (
              <Requirements
                key={key}
                ref={ref => { this[`ref${key}`] = ref; }}
                value={data[key]}
                accessRemove={accessRemoveData}
                handleSubmitForm={this.handleSubmit}
                autoSave={autoSave}
                buffer={this.props.buffer}
                module={this.props.module}
                country={this.props.country}
                itemTitle={this.props.itemTitle}
                {...props}
              />
            );
          }

          if (type === 'approvalRoutes') {
            return (
              <ApprovalRoutes
                key={key}
                ref={ref => { this[`ref${key}`] = ref; }}
                value={data[key]}
                accessRemove={accessRemoveData}
                handleSubmitForm={this.handleSubmit}
                autoSave={autoSave}
                buffer={this.props.buffer}
                module={this.props.module}
                country={this.props.country}
                itemTitle={this.props.itemTitle}
                {...props}
              />
            );
          }

          if (type === 'licenses') {
            return (
              <Licenses
                key={key}
                ref={ref => { this[`ref${key}`] = ref; }}
                value={data[key]}
                countryId={country.id}
                accessRemove={accessRemoveData}
                onChange={this.onChange}
                handleSubmitForm={this.handleSubmit}
                autoSave={autoSave}
                {...props}
              />
            );
          }

          if (type === 'range') {
            return (
              <ItemRange
                key={key}
                ref={ref => { this[`ref${key}`] = ref; }}
                value={data[key]}
                onChange={this.onChange}
                {...props}
              />
            );
          }

          if (type === 'id') {
            return (
              <ItemId
                key={key}
                ref={ref => { this[`ref${key}`] = ref; }}
                value={data[key]}
                {...props}
              />
            );
          }

          return null;
        })}
      </FormContainer>
    );
  }
}
