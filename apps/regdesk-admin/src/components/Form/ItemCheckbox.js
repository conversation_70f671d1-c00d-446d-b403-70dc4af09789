import React from 'react';
import Item from './Item';

/**
 * ItemCheckbox
 */
export default class ItemCheckbox extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value || false,
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value ? nextProps.value : false });
  };

  onChange = ({ value }) => {
    const { onChange } = this.props;

    this.setState({ value });
    if (onChange) onChange(value);
  };

  getValue = () => this.state.value;

  render() {
    const { label } = this.props;
    const { value } = this.state;

    return (
      <Item label={label}>
        <input
          type='checkbox'
          checked={value}
          onChange={() => this.onChange({ value: !value })}
        />
      </Item>
    );
  }
}
