import React from 'react';
import { Input } from 'antd';
import Item from './Item';

const { TextArea } = Input;

/**
 * ItemInput
 */
export default class ItemInput extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value,
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value });
  };

  onChange = ({ text }) => {
    const { onChange } = this.props;

    this.setState({ value: text });
    if (onChange) onChange(text);
  };

  getValue = () => this.state.value;

  setValue = value => this.setState({ value });

  clearValue = () => this.setState({ value: '' });

  render() {
    const { label, unit, readOnly, rows, ...otherProps } = this.props;
    const { value } = this.state;

    return (
      <Item label={label}>
        <div className='b-flex' style={{ display: 'flex', alignItems: 'center' }}>
          {
            rows ? (
              <TextArea
                {...otherProps}
                rows={rows}
                className='b-flex-flex'
                value={value}
                disabled={readOnly}
                onChange={e => this.onChange({ text: e.target.value })}
              />
            ) : (
              <Input
                {...otherProps}
                className='b-flex-flex'
                value={value}
                disabled={readOnly}
                onChange={e => this.onChange({ text: e.target.value })}
              />
            )
          }

          {unit && (
            <span style={{ marginLeft: '10px', display: 'inline-block' }}>
              {unit}
            </span>
          )}
        </div>
      </Item>
    );
  }
}
