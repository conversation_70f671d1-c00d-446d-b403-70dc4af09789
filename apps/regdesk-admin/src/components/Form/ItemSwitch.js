import React from 'react';
import { Switch } from 'antd';
import Item from './Item';

/**
 * Item Switch
 */
export default class ItemSwitch extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value,
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value });
  };

  getValue = () => this.state.value;

  handleChange = value => this.setState({ value });

  render() {
    const { label, unit, disabled } = this.props;
    const { value } = this.state;

    return (
      <Item label={label}>
        <Switch
          disabled={!disabled ? false : disabled}
          checked={value}
          onChange={checked => this.setState({ value: checked })}
        />
        &nbsp;&nbsp;
        {unit}
      </Item>
    );
  }
}
