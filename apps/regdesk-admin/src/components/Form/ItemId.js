import React from 'react';

/**
 * Generate Id
 * @Public
 * @param length
 */
export const generateId = (length = 32) => {
  let hash = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

  for (let i = 0; i < length; i += 1) {
    hash += possible.charAt(Math.floor(Math.random() * possible.length));
  }

  return hash;
};

/**
 * Generate ObjectId
 * @returns {string}
 */
export const generateObjectId = () => {
  const timestamp = Math.floor(new Date().getTime() / 1000).toString(16);

  return timestamp + 'xxxxxxxxxxxxxxxx'.replace(/[x]/g, () => Math.floor(Math.random() * 16).toString(16)).toLowerCase();
};

/**
 * ItemId
 */
export default class ItemId extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value || generateObjectId(),
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value || generateObjectId() });
  };

  getValue = () => this.state.value;

  render() {
    return <div />;
  }
}
