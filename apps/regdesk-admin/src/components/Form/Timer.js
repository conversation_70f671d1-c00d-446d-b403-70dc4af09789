let timer = 0;
const duration = 30 * 1000; // ms

/**
 * Stop timer
 */
const stop = () => {
  clearTimeout(timer);
  timer = 0;
};

/**
 * Start timer
 * @param callback
 */
const start = (callback) => {
  stop();
  timer = setTimeout(callback, duration);
};

/**
 * Check timer
 * @param callback
 */
const check = (callback) => {
  if (timer) callback();
};

export default {
  start,
  stop,
  check,
};
