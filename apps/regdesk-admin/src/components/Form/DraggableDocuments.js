import React, { useState, useEffect } from 'react';
import { Icon } from 'antd';
import { arrayMove, SortableContext, verticalListSortingStrategy, useSortable } from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';

const SortableItem = ({ id, children }) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });

  return (
    <div
      ref={setNodeRef}
      style={{
        transition,
        transform: CSS.Transform.toString(transform),
        marginBottom: 4,
        display: 'flex',
        alignItems: 'center',
        gap: 8,
      }}
    >
      <a {...listeners} {...attributes} style={{ cursor: 'grab' }}>
        <Icon type='file' />
      </a>

      {children}
    </div>
  );
};

export default ({ documents = [], parentKey, onEdit, onChange }) => {
  const [items, setItems] = useState(documents.map((doc, index) => ({ ...doc, key: index })));

  useEffect(() => {
    setItems(documents.map((doc, index) => ({ ...doc, key: index })));
  }, [JSON.stringify(documents)]);

  const handleDragEnd = ({ active, over }) => {
    if (!over || active.id === over.id) return;

    const oldIndex = items.findIndex((item) => item.id === active.id);
    const newIndex = items.findIndex((item) => item.id === over.id);
    const newItems = arrayMove(items, oldIndex, newIndex);

    setItems(newItems);

    if (onChange) onChange(newItems);
  };

  if (!items.length) return <span style={{ color: 'rgba(0, 0, 0, 0.25)' }}>No documents</span>;

  return (
    <DndContext
      collisionDetection={closestCenter}
      modifiers={[restrictToVerticalAxis]}
      onDragEnd={handleDragEnd}
    >
      <SortableContext items={items.map(({ id }) => id)} strategy={verticalListSortingStrategy}>
        {items.map((doc, key) => {
          const { id, name } = doc;

          return (
            <SortableItem key={id} id={id}>
              <a onClick={() => onEdit({ ...doc, parentKey, key })}>
                {name}
              </a>
            </SortableItem>
          );
        })}
      </SortableContext>
    </DndContext>
  );
};
