import React from 'react';
import { Modal, message, But<PERSON>, Tooltip } from 'antd';
import MarkDownEditor from './MarkDownEditor';
import ItemInput from './ItemInput';
import ItemSample from './ItemSample';
import ItemNumber from './ItemNumber';
import ItemRadio from './ItemRadio';
import ItemDocType from './ItemDocType';
import ItemCategory from './ItemCategory';
import ItemId from './ItemId';
import Timer from './Timer';
import Procedure from './Procedure';
import styles from './index.less';

/**
 * Document
 */
export default class Document extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      data: {},
      showModal: false,
    };
  }

  handleSubmit = () => {
    const { module, onUpdate, onAdd, handleSubmitForm } = this.props;
    const { data } = this.state;
    const isEdit = !!data.name;

    const docData = {
      ...data,
      id: this.refId.getValue(),
      name: this.refName.getValue(),
      dicType: this.refDicType.getValue(),
      dicCategory: this.dicCategory.getValue(),
      translation: this.refTranslation.getValue(),
      notary: this.refNotary.getValue(),
      legalize: this.refLegalize.getValue(),
      apostille: this.refApostille.getValue(),
      fee: this.refFee.getValue(),
      original: this.refOriginal.getValue(),
      highlight: this.refHighlight.getValue(),
      copies: this.refCopies.getValue(),
      eCopies: this.refECopies?.getValue(),
      definition: this.refDefinition.getValue(),
      attention: this.refAttention.getValue(),
      sample: this.refSample.getValue(),
    };

    if (!docData.name) return message.warn('Name empty!');
    if (![true, false, module === 'md' && null].includes(docData.translation)) return message.warn('Select Translation!');
    if (![true, false, module === 'md' && null].includes(docData.notary)) return message.warn('Select Notary!');
    if (![true, false, module === 'md' && null].includes(docData.legalize)) return message.warn('Select Legalize!');
    if (![true, false, module === 'md' && null].includes(docData.apostille)) return message.warn('Select Apostille!');
    if (![true, false, module === 'md' && null].includes(docData.fee)) return message.warn('Select Fee!');
    if (![true, false, module === 'md' && null].includes(docData.original)) return message.warn('Select Original!');
    if (![true, false].includes(docData.highlight)) return message.warn('Select Highlight!');
    if (!docData.dicType.dicId) return message.warn('Select Type!');

    if (['md', 'pharma', 'ccp'].includes(module)) {
      if (!(Number.isInteger(Number(docData.copies)) && docData.copies >= 0)) return message.warn('Check Hard Copies!');
      if (!(Number.isInteger(Number(docData.eCopies)) && docData.eCopies >= 0)) return message.warn('Check E-Copies!');
    } else if (!(Number.isInteger(Number(docData.copies)) && docData.copies >= 0)) {
      return message.warn('Check Copies!');
    }

    if (isEdit && onUpdate) onUpdate(docData);
    if (!isEdit && onAdd) onAdd(docData);
    if (handleSubmitForm) handleSubmitForm();

    this.closeModal();

    return null;
  };

  handleDelete = () => {
    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure want to clear it?',
      onOk: () => {
        const { onRemove, handleSubmitForm } = this.props;
        const { data } = this.state;

        if (onRemove) onRemove(data);
        if (handleSubmitForm) handleSubmitForm();

        this.closeModal();
      },
    });
  };

  closeModal = () => this.setState({ showModal: false, data: {} });

  showModal = (data = {}) => {
    const { autoSave, handleSubmitForm } = this.props;

    if (autoSave && handleSubmitForm) Timer.check(handleSubmitForm);
    this.setState({ showModal: true, data });
  };

  render() {
    const { data, showModal } = this.state;
    const { module, accessRemove } = this.props;
    const isEdit = !!data.name;
    const valuesForRadio = [{ value: true, text: 'Yes' }, { value: false, text: 'No' }];
    const valuesForRadioExtended = module === 'md' ? [...valuesForRadio, { value: null, text: 'NMiR' }] : valuesForRadio;

    return (
      <Modal
        title={isEdit ? 'Edit Document' : 'Add Document'}
        visible={showModal}
        footer={null}
        centered
        destroyOnClose
        style={{ top: 40 }}
        maskClosable={false}
        onCancel={this.closeModal}
        onClose={this.closeModal}
        width={1000}
        zIndex={998}
      >
        {showModal && (
          <div className={styles.container}>
            <ItemId
              ref={ref => { this.refId = ref; }}
              value={data.id}
            />

            <ItemInput
              autoFocus
              ref={ref => { this.refName = ref; }}
              label='Name'
              value={data.name}
            />

            <ItemDocType
              label='Type'
              ref={ref => { this.refDicType = ref; }}
              module={module}
              {...(data.dicType || {})}
            />

            <ItemCategory
              ref={ref => { this.dicCategory = ref; }}
              label='Category'
              value={data.dicCategory}
            />

            <ItemRadio
              ref={ref => { this.refTranslation = ref; }}
              label='Translation'
              value={data.translation}
              data={valuesForRadioExtended}
            />

            <ItemRadio
              ref={ref => { this.refNotary = ref; }}
              label='Notary'
              value={data.notary}
              data={valuesForRadioExtended}
            />

            <ItemRadio
              ref={ref => { this.refLegalize = ref; }}
              label='Legalize'
              value={data.legalize}
              data={valuesForRadioExtended}
            />

            <ItemRadio
              ref={ref => { this.refApostille = ref; }}
              label='Apostille'
              value={data.apostille}
              data={valuesForRadioExtended}
            />

            <ItemRadio
              ref={ref => { this.refFee = ref; }}
              label='Fee'
              value={data.fee}
              data={valuesForRadioExtended}
            />

            <ItemRadio
              ref={ref => { this.refOriginal = ref; }}
              label='Original'
              value={data.original}
              data={valuesForRadioExtended}
            />

            <ItemRadio
              ref={ref => { this.refHighlight = ref; }}
              label='Highlight'
              value={data.highlight}
              data={valuesForRadio}
            />

            <ItemNumber
              ref={ref => { this.refCopies = ref; }}
              label='Hard Copies'
              value={data.copies}
            />

            {['md', 'pharma', 'ccp'].includes(module) && (
              <ItemNumber
                ref={ref => { this.refECopies = ref; }}
                label='E-Copies'
                value={data.eCopies}
              />
            )}

            <MarkDownEditor
              ref={ref => { this.refDefinition = ref; }}
              value={data.definition}
              label='Definition'
            />

            <Procedure
              ref={ref => { this.refAttention = ref; }}
              label='Content'
              value={data.attention || []}
              accessRemove={accessRemove}
            />

            <ItemSample
              ref={ref => { this.refSample = ref; }}
              label='Sample'
              value={data.sample || []}
              accessRemove={accessRemove}
              page={module}
            />

            <div>
              <Button onClick={this.closeModal}>Cancel</Button>

              <Button style={{ marginLeft: 8 }} type='primary' onClick={this.handleSubmit}>
                {isEdit ? 'Update' : 'Add'}
              </Button>

              {isEdit && (
                <Tooltip title={!accessRemove && 'No permission'}>
                  <Button style={{ marginLeft: 8 }} type='danger' disabled={!accessRemove} onClick={this.handleDelete}>Delete</Button>
                </Tooltip>
              )}
            </div>
          </div>
        )}
      </Modal>
    );
  }
}
