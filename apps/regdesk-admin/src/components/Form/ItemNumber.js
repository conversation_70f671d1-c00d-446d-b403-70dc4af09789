import React from 'react';
import Item from './Item';

/**
 * ItemNumber
 */
export default class ItemNumber extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value || 0,
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value });
  };

  onChange = ({ text }) => {
    const { onChange } = this.props;

    this.setState({ value: text });
    if (onChange) onChange(text);
  };

  getValue = () => parseFloat(this.state.value);

  render() {
    const { label, unit } = this.props;
    const { value = 0 } = this.state;

    return (
      <Item label={label}>
        <div className='b-flex'>
          <input
            type='number'
            className='ant-input b-flex-flex'
            value={value}
            onChange={e => this.onChange({ text: e.target.value })}
          />

          {unit && (
            <span style={{ marginLeft: '10px', display: 'inline-block' }}>
              {unit}
            </span>
          )}
        </div>
      </Item>
    );
  }
}
