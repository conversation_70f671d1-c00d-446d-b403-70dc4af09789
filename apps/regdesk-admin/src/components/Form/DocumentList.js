import React from 'react';
import { Button, Descriptions, Divider, Modal, Table, Tooltip } from 'antd';
import Document from './Document';
import styles from './index.less';

/**
 * Document List
 */
export default class DocumentList extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value || [],
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value || [] });
  };

  /**
   * Event add new doc
   */
  onAdd = () => {
    const newDoc = {
      id: undefined,
      name: '',
      dicType: '',
      dicCategory: '',
      translation: false,
      fee: false,
      notary: false,
      legalize: false,
      apostille: false,
      original: false,
      highlight: false,
      copies: 0,
      eCopies: 0,
      definition: '',
      attention: [],
      sample: [],
    };

    this.formDocument.showModal(newDoc);
  };

  /**
   * Event edit doc
   * @param item
   */
  onEdit = item => this.formDocument.showModal(item);

  /**
   * Event remove doc
   * @param item
   */
  onRemove = item => {
    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure want to clear it?',
      onOk: () => {
        const { handleSubmitForm } = this.props;

        this.remove(item);
        if (handleSubmitForm) handleSubmitForm();
      },
    });
  };

  /**
   * Add doc
   * @param item
   */
  add = item => {
    const { value } = this.state;

    value.push(item);
    this.setState({ value });
  };

  /**
   * Remove doc
   * @param item
   */
  remove = item => {
    const { value } = this.state;
    const { key } = item;

    value.splice(key, 1);
    this.setState({ value });
  };

  /**
   * Update doc
   * @param item
   */
  update = item => {
    const { value } = this.state;
    const { key } = item;

    value[key] = item;
    this.setState({ value });
  };

  /**
   * Get value
   * @returns {*}
   */
  getValue = () => this.state.value;

  render() {
    const { module, handleSubmitForm, autoSave, accessRemove } = this.props;
    const { value = [] } = this.state;
    const data = value.map((v, i) => ({ ...v, key: i }));

    return (
      <div style={{ marginBottom: 20 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 30 }}>
          <Descriptions title='Documents' />
          <Button size='small' icon='plus' type='primary' style={{ marginBottom: 20 }} onClick={this.onAdd}>Add doc</Button>
        </div>

        <Table
          rowKey='id'
          dataSource={data}
          columns={[
            {
              title: 'Name',
              dataIndex: 'name',
              render: (v, d) => <a onClick={() => this.onEdit(d)}>{v}</a>,
            },
            {
              title: 'Actions',
              dataIndex: 'key',
              key: 'actions',
              width: 200,
              render: (v, d) => (
                <div>
                  <a onClick={() => this.onEdit(d)}>Edit</a>
                  <Divider type='vertical' />

                  <Tooltip title={!accessRemove && 'No permission'}>
                    <a className={!accessRemove ? styles.disabled : ''} onClick={() => accessRemove && this.onRemove(v)}>Delete</a>
                  </Tooltip>
                </div>
              ),
            },
          ]}
        />

        <Document
          module={module}
          onAdd={this.add}
          autoSave={autoSave}
          onRemove={this.remove}
          onUpdate={this.update}
          accessRemove={accessRemove}
          handleSubmitForm={handleSubmitForm}
          ref={ref => { this.formDocument = ref; }}
        />
      </div>
    );
  }
}
