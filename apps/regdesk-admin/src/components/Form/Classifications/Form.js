import { Button, message, Modal, Tooltip } from 'antd';
import React from 'react';
import styles from '../index.less';
import Item from '../Item';
import ItemArRequired from '../ItemArRequired';
import ItemId from '../ItemId';
import ItemInput from '../ItemInput';
import ItemLevel from '../ItemLevel';
import ItemRange from '../ItemRange';
import ItemSwitch from '../ItemSwitch';
import MarkDownEditor from '../MarkDownEditor';
import Procedure from '../Procedure';
import Timer from '../Timer';

/**
 * Classification Form
 */
export default class ClassificationForm extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      data: props.data || {},
      showModal: false,
    };
  }

  handleSubmit = () => {
    const { onUpdate, onAdd, handleSubmitForm } = this.props;
    const { data } = this.state;
    const isEdit = !!data.name;

    const newData = {
      ...data,
      id: this.refId.getValue(),
      criteria: this.refCriteria.getValue(),
      definition: this.refDefinition.getValue(),
      name: this.refName.getValue(),
      alternativeName: this.refAlternativeName.getValue(),
      previewInDevReg: this.refPreviewInDevReg.getValue(),
      previewInProducts: this.refPreviewInProducts.getValue(),
      previewInBauschProducts: this.refPreviewInBauschProducts.getValue(),
      accessLevel: this.refAccessLevel.getValue(),
      fdaClassification: this.refFdaClassification.getValue(),
      arRequired: this.refArRequired.getValue(),
      dossierLanguage: this.refDossierLanguage.getValue(),
      licenseValidity: this.refLicenseValidity.getValue(),
      procedures: this.refProcedures.getValue(),
      fee: this.refFee.getValue(),
      time: this.refTime.getValue(),
      lastUpdated: new Date(),
    };

    if (!newData.name) {
      message.warn('Name empty!');

      return;
    }

    if (isEdit && onUpdate) onUpdate(newData);
    if (!isEdit && onAdd) onAdd(newData);
    if (handleSubmitForm) handleSubmitForm();

    this.closeModal();
  };

  /**
   * Handle Delete
   */
  handleDelete = () => {
    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure want to clear it?',
      onOk: () => {
        const { onRemove, handleSubmitForm } = this.props;
        const { data } = this.state;

        if (onRemove) onRemove(data);
        if (handleSubmitForm) handleSubmitForm();

        this.closeModal();
      },
    });
  };

  /**
   * Close Modal
   */
  closeModal = () => this.setState({ showModal: false, data: {} });

  /**
   * Show Modal
   * @param data
   */
  showModal = (data = {}) => {
    const { autoSave, handleSubmitForm } = this.props;

    if (autoSave && handleSubmitForm) Timer.check(handleSubmitForm);
    this.setState({ showModal: true, data });
  };

  render() {
    const { accessRemove, accessEnableBausch } = this.props;
    const { data, showModal } = this.state;

    const {
      id,
      name,
      alternativeName,
      previewInDevReg,
      previewInProducts,
      previewInBauschProducts,
      accessLevel,
      fdaClassification,
      definition,
      criteria,
      arRequired,
      dossierLanguage,
      licenseValidity,
      fee = {},
      time = {},
      procedures = [],
    } = data;

    const isEdit = !!name;

    return (
      <Modal
        title={isEdit ? 'Edit classification' : 'Add classification'}
        visible={showModal}
        footer={null}
        centered
        style={{ top: 40 }}
        destroyOnClose
        maskClosable={false}
        onCancel={this.closeModal}
        onClose={this.closeModal}
        width={1000}
        zIndex={998}
      >
        {showModal && (
          <div className={styles.container}>
            <ItemId
              ref={ref => { this.refId = ref; }}
              value={id}
            />

            <ItemInput
              autoFocus
              ref={ref => { this.refName = ref; }}
              label='Name'
              value={name}
            />

            <ItemInput
              ref={ref => { this.refAlternativeName = ref; }}
              label='Alternative Name'
              value={alternativeName}
            />

            <ItemInput
              ref={ref => { this.refFdaClassification = ref; }}
              label='FDA Classification'
              value={fdaClassification}
            />

            <ItemSwitch
              ref={ref => { this.refPreviewInDevReg = ref; }}
              label='Preview In'
              value={previewInDevReg}
              unit='Device Regs & DCT'
            />

            <ItemSwitch
              ref={ref => { this.refPreviewInProducts = ref; }}
              label=''
              unit='Products'
              value={previewInProducts}
            />

            <ItemSwitch
              disabled={accessEnableBausch}
              ref={ref => { this.refPreviewInBauschProducts = ref; }}
              label=''
              unit='Products (Bausch)'
              value={previewInBauschProducts}
            />

            <ItemLevel
              ref={ref => { this.refAccessLevel = ref; }}
              label='Access Level'
              value={accessLevel}
            />

            <ItemRange
              ref={ref => { this.refFee = ref; }}
              label='Fee'
              value={fee}
              unit='USD'
              showTextInput
            />

            <ItemRange
              ref={ref => { this.refTime = ref; }}
              label='Time'
              value={time}
              unit='Months'
              showTextInput
            />

            <ItemArRequired
              ref={ref => { this.refArRequired = ref; }}
              label='AR Required'
              value={arRequired}
            />

            <ItemInput
              ref={ref => { this.refDossierLanguage = ref; }}
              label='Dossier Language'
              value={dossierLanguage}
            />

            <ItemInput
              ref={ref => { this.refLicenseValidity = ref; }}
              label='License Validity'
              value={licenseValidity}
              unit='Years'
            />

            <MarkDownEditor
              ref={ref => { this.refDefinition = ref; }}
              value={definition}
              label='Definition'
            />

            <MarkDownEditor
              ref={ref => { this.refCriteria = ref; }}
              value={criteria}
              label='Criteria'
            />

            <Procedure
              showTag
              ref={ref => { this.refProcedures = ref; }}
              label='Procedures'
              value={procedures}
              accessRemove={accessRemove}
            />

            <Item style={{ marginBottom: 0 }}>
              <Button onClick={this.closeModal}>Cancel</Button>

              <Button style={{ marginLeft: 8 }} type='primary' onClick={this.handleSubmit}>
                {isEdit ? 'Update' : 'Add'}
              </Button>

              {isEdit && (
                <Tooltip title={!accessRemove && 'No permission'}>
                  <Button style={{ marginLeft: 8 }} type='danger' disabled={!accessRemove} onClick={this.handleDelete}>Delete</Button>
                </Tooltip>
              )}
            </Item>
          </div>
        )}
      </Modal>
    );
  }
}
