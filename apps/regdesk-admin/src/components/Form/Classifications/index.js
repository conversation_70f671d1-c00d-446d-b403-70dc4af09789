import React from 'react';
import { Button, Descriptions, Divider, Modal, Table, Tooltip } from 'antd';
import Form from './Form';
import Document from '../Document';
import BufferModal from '../Buffer';
import Documents from '../DraggableDocuments';
import { generateObjectId } from '../ItemId';
import actions from '../../../actions';
import styles from '../index.less';

/**
 * Classifications
 */
export default class Classifications extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value || [],
    };
  }

  componentWillReceiveProps = (nextProps) => {
    this.setState({ value: nextProps.value || [] });
  };

  /**
   * Event add new classification
   */
  onAdd = () => {
    const newItem = {
      id: undefined,
      name: '',
      alternativeName: '',
      previewInDevReg: true,
      previewInProducts: true,
      accessLevel: '',
      countryCode: '',
      definition: '',
      criteria: '',
      arRequired: false,
      dossierLanguage: '',
      licenseValidity: '',
      labelingLanguage: '',
      format: '',
      links: [],
      documents: [],
      procedures: [],
    };

    this.formClassification.showModal(newItem);
  };

  /**
   * Event edit classification
   * @param item
   */
  onEdit = (item) => this.formClassification.showModal(item);

  /**
   * Event remove classification
   * @param item
   */
  onRemove = (item) => {
    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure want to clear it?',
      onOk: () => {
        const { handleSubmitForm } = this.props;

        this.remove(item);
        if (handleSubmitForm) handleSubmitForm();
      },
    });
  }

  /**
   * Add classification
   * @param item
   */
  add = (item) => {
    const { value } = this.state;

    value.push(item);
    this.setState({ value });
  };

  /**
   * Remove classification
   * @param index
   */
  remove = (index) => {
    const { value } = this.state;

    value.splice(index, 1);
    this.setState({ value });
  };

  /**
   * Update classification
   * @param item
   */
  update = (item) => {
    const { value } = this.state;
    const { key } = item;

    value[key] = item;
    this.setState({ value });
  }

  /**
   * Event add new document
   * @param parentKey
   */
  onAddDoc = (parentKey) => {
    const newDoc = {
      id: undefined,
      parentKey,
      name: '',
      dicType: '',
      dicCategory: '',
      translation: false,
      fee: false,
      notary: false,
      legalize: false,
      apostille: false,
      original: false,
      highlight: false,
      copies: 0,
      eCopies: 0,
      definition: '',
      attention: [],
      sample: [],
    };

    this.formDocument.showModal(newDoc);
  };

  /**
   * Event edit doc
   * @param item
   */
  onEditDoc = (item) => this.formDocument.showModal(item);

  /**
   * Add mew doc
   * @param item
   */
  addDoc = (item) => {
    const { value } = this.state;
    const { parentKey } = item;

    value[parentKey].documents.push(item);
    value[parentKey].lastUpdated = new Date();
    this.setState({ value });
  };

  /**
   * Remove new doc
   * @param item
   */
  removeDoc = (item) => {
    const { value } = this.state;
    const { parentKey, key } = item;

    value[parentKey].documents.splice(key, 1);
    value[parentKey].lastUpdated = new Date();
    this.setState({ value });
  };

  /**
   * Update doc
   * @param item
   */
  updateDoc = (item) => {
    const { value } = this.state;
    const { parentKey, key } = item;

    value[parentKey].documents[key] = item;
    value[parentKey].lastUpdated = new Date();
    this.setState({ value });
  }

  /**
   * Get value
   * @returns {*}
   */
  getValue = () => this.state.value;

  render() {
    const { handleSubmitForm, autoSave, module, buffer = [], country, itemTitle, accessRemove, accessEnableBausch } = this.props;
    const { value = [] } = this.state;
    const data = value.map((v, i) => ({ ...v, key: i }));
    const bufferModule = module === 'device-reg' ? 'mdr' : 'phr';

    return (
      <div style={{ marginBottom: 20 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 30 }}>
          <Descriptions title='Classifications' />

          <div style={{ display: 'flex' }}>
            <Button
              size='small'
              icon='import'
              style={{ marginBottom: 20, marginRight: 15 }}
              onClick={() => { if (this.modalBuffer) this.modalBuffer.show(); }}
            >
              Insert from buffer
            </Button>

            <Button
              size='small'
              icon='plus'
              type='primary'
              style={{ marginBottom: 20 }}
              onClick={this.onAdd}
            >
              Add classification
            </Button>
          </div>
        </div>

        <Table
          rowKey='id'
          dataSource={data}
          columns={[
            {
              title: 'Name',
              dataIndex: 'name',
              render: (name, record) => <a onClick={() => this.onEdit(record)}>{name}</a>,
            },
            {
              title: 'Documents',
              dataIndex: 'documents',
              render: (documents, record) => (
                <Documents
                  documents={documents}
                  parentKey={record.key}
                  onEdit={this.onEditDoc}
                  onChange={newDocs => {
                    this.update({ ...record, documents: newDocs });
                    handleSubmitForm();
                  }}
                />
              ),
            },
            {
              title: 'Actions',
              dataIndex: 'key',
              key: 'actions',
              width: 300,
              render: (key, record) => (
                <div>
                  <a onClick={() => actions[bufferModule].pushBuffer(`${country.name} / ${itemTitle} / ${record.name}`, 'classification', record)}>Copy to buffer</a>
                  <Divider type='vertical' />
                  <a onClick={() => this.onAddDoc(key)}>Add Doc</a>
                  <Divider type='vertical' />
                  <a onClick={() => this.onEdit(record)}>Edit</a>
                  <Divider type='vertical' />

                  <Tooltip title={!accessRemove && 'No permission'}>
                    <a className={!accessRemove ? styles.disabled : ''} onClick={() => accessRemove && this.onRemove(key)}>Delete</a>
                  </Tooltip>
                </div>
              ),
            }
          ]}
        />

        <Form
          onAdd={this.add}
          autoSave={autoSave}
          onRemove={this.remove}
          onUpdate={this.update}
          accessRemove={accessRemove}
          accessEnableBausch={accessEnableBausch}
          handleSubmitForm={handleSubmitForm}
          ref={ref => { this.formClassification = ref; }}
        />

        <Document
          module='md'
          onAdd={this.addDoc}
          autoSave={autoSave}
          onRemove={this.removeDoc}
          onUpdate={this.updateDoc}
          accessRemove={accessRemove}
          handleSubmitForm={handleSubmitForm}
          ref={ref => { this.formDocument = ref; }}
        />

        <BufferModal
          type='classification'
          ref={ref => { this.modalBuffer = ref; }}
          onRemove={newBuffer => { actions[bufferModule].change({ buffer: newBuffer }); }}
          onSelect={newBuffer => {
            const newItem = { ...newBuffer, id: generateObjectId() };

            if (!newItem.accessLevel) newItem.accessLevel = 'Level 1';

            this.add(newItem);
          }}
          buffer={buffer}
        />
      </div>
    );
  }
}
