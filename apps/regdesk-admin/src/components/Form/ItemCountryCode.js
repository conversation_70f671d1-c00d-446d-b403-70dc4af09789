import React from 'react';
import { Select } from 'antd';
import Item from './Item';
import { allCountriesWithEU } from '../../utils/countries';

const { Option } = Select;

/**
 * ItemCountryCode
 */
export default class ItemCountryCode extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      countryCode: props.value || '',
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ countryCode: nextProps.value });
  };

  getValue = () => this.state.countryCode;

  handleChange = value => this.setState({ countryCode: value });

  render() {
    const { label } = this.props;
    const { countryCode } = this.state;
    const sortedCountriesWithEU = allCountriesWithEU.sort((a, b) => a.alpha2code.toLowerCase() > b.alpha2code.toLowerCase() ? 1 : -1);

    return (
      <Item label={label}>
        <Select
          value={countryCode}
          onChange={this.handleChange}
          style={{ width: '200px' }}
        >
          {sortedCountriesWithEU.map(country => (
            <Option value={country.alpha2code} key={country.alpha2code}>{country.alpha2code}</Option>
          ))}
        </Select>
      </Item>
    );
  }
}
