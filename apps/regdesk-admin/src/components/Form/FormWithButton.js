import React from 'react';
import { Button, message, Modal } from 'antd';

/**
 * Form With Button
 */
export default class FormWithButton extends React.Component {
  handleSubmit = e => {
    e.preventDefault();
    this.props.onSubmit();
  };

  handleClear = () => {
    const { onClear } = this.props;

    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure want to clear it?',
      onOk: () => onClear(),
    });
  };

  handleAgree = check => {
    const { onAgree } = this.props;

    Modal.confirm({
      title: 'Confirm',
      content: (
        <div>
          <div>Are you sure want to {check ? 'Approve' : 'Reject'} it?</div>

          {!check && (
            <div>
              <textarea
                type='text'
                style={{ height: 100 }}
                className='ant-input'
                id='comment'
              />
            </div>
          )}
        </div>
      ),
      okText: check ? 'Approve' : 'Reject',
      onOk: () => {
        const com = document.getElementById('comment');

        if (com) {
          if (!com.value) {
            message.info('Please comment!');

            return Promise.reject();
          }

          onAgree(check, com.value);
        } else {
          onAgree(check, '');
        }
      },
    });
  };

  render() {
    const {
      onClear,
      onAgree,
      comment,
      children,
      loading,
      showAgree,
    } = this.props;

    return (
      <form onSubmit={this.handleSubmit}>
        {children}

        <div className='b-flex'>
          {onAgree && showAgree && (
            <div>
              <div>
                <Button loading={loading} disabled={loading} onClick={() => this.handleAgree(false)}>
                  Reject
                </Button>

                <span style={{ width: '10px', display: 'inline-block' }} />

                <Button loading={loading} disabled={loading} onClick={() => this.handleAgree(true)}>
                  Approve
                </Button>
              </div>

              {comment && (
                <div style={{ color: 'red', marginTop: 20, marginLeft: 10 }}>
                  <hr />
                  <h5>Comment</h5>

                  <pre style={{ background: 'none', border: 'none', padding: 0 }}>
                    {comment}
                  </pre>
                </div>
              )}
            </div>
          )}

          <div className='b-flex-flex' />

          <Button disabled={loading} type='primary' htmlType='submit'>
            Save
          </Button>

          {onClear && <span style={{ width: '20px', display: 'inline-block' }} />}

          {onClear && (
            <Button loading={loading} disabled={loading} type='danger' onClick={this.handleClear}>
              Delete
            </Button>
          )}
        </div>
      </form>
    );
  }
}
