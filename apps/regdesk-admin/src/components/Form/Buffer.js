import React, { PureComponent } from 'react';
import { Divider, Table, Modal } from 'antd';

/**
 * Buffer Modal
 */
export default class BufferModal extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      showModal: false
    };
  }

  show = () => this.setState({ showModal: true });

  onCloseModal = () => this.setState({ showModal: false });

  /**
   * Event select buffer
   * @param data
   */
  onSelect = (data = {}) => {
    const { onSelect } = this.props;

    if (onSelect) onSelect(data);
    this.onCloseModal();
  };

  /**
   * Remove item buffer
   * @param key
   */
  onRemove = key => {
    const { buffer = [], onRemove } = this.props;
    const newBuffer = buffer.filter(item => item.key !== key);

    if (onRemove) onRemove(newBuffer);
  };

  render() {
    const { showModal } = this.state;
    const { buffer = [], type, types = [] } = this.props;
    const data = buffer.filter(item => item.type === type || types.includes(item.type));

    const columns = [
      {
        title: 'Data From',
        dataIndex: 'key',
      },
      {
        title: 'Action',
        dataIndex: 'data',
        width: 150,
        render: (item, record) => (
          <div>
            <a onClick={() => this.onSelect(item)}>Select</a>
            <Divider type='vertical' />
            <a onClick={() => this.onRemove(record.key)}>Remove</a>
          </div>
        ),
      },
    ];

    return (
      <Modal
        title='Buffer'
        visible={showModal}
        footer={null}
        onCancel={this.onCloseModal}
        onClose={this.onCloseModal}
        width={800}
        zIndex={1001}
      >
        {showModal && (
          <Table
            bordered
            rowKey='key'
            size='small'
            columns={columns}
            dataSource={data}
          />
        )}
      </Modal>
    );
  }
}
