import React from 'react';
import { Select } from 'antd';
import Item from './Item';

const { Option } = Select;

/**
 * Item Level
 */
export default class ItemLevel extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value || '',
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value });
  };

  getValue = () => this.state.value;

  handleChange = value => this.setState({ value });

  render() {
    const { label } = this.props;
    const { value } = this.state;

    return (
      <Item label={label}>
        <Select
          value={value}
          onChange={this.handleChange}
          style={{ width: '200px' }}
        >
          <Option value='Level 1'>Level 1</Option>
          <Option value='Level 2'>Level 2</Option>
          <Option value='Level 3'>Level 3</Option>
        </Select>
      </Item>
    );
  }
}
