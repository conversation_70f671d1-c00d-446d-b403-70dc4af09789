import { Button, message, Modal, Tooltip } from 'antd';
import React from 'react';
import styles from '../index.less';
import Item from '../Item';
import ItemArRequired from '../ItemArRequired';
import ItemId from '../ItemId';
import ItemInput from '../ItemInput';
import ItemRange from '../ItemRange';
import MarkDownEditor from '../MarkDownEditor';
import Procedure from '../Procedure';
import Timer from '../Timer';

/**
 * Requirement Form
 */
export default class RequirementForm extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      data: props.data || {},
      showModal: false,
    };
  }

  /**
   * Handle Submit
   */
  handleSubmit = () => {
    const { onUpdate, onAdd, handleSubmitForm } = this.props;
    const { data } = this.state;
    const isEdit = !!data.name;

    const newData = {
      ...data,
      id: this.refId.getValue(),
      criteria: this.refCriteria?.getValue(),
      generalPrinciples: this.refGeneralPrinciples?.getValue(),
      name: this.refName.getValue(),
      fee: this.refFee?.getValue(),
      time: this.refTime?.getValue(),
      arRequired: this.refArRequired?.getValue(),
      dossierLanguage: this.refDossierLanguage?.getValue(),
      procedures: this.refProcedures.getValue(),
      checklist: this.refChecklist?.getValue(),
      lastUpdated: new Date(),
    };

    if (!newData.name) {
      message.warn('Name empty!');

      return;
    }

    if (isEdit && onUpdate) onUpdate(newData);
    if (!isEdit && onAdd) onAdd(newData);
    if (handleSubmitForm) handleSubmitForm();

    this.closeModal();
  };

  handleDelete = () => {
    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure want to clear it?',
      onOk: () => {
        const { onRemove, handleSubmitForm } = this.props;
        const { data } = this.state;

        if (onRemove) onRemove(data);
        if (handleSubmitForm) handleSubmitForm();

        this.closeModal();
      },
    });
  };

  closeModal = () => this.setState({ showModal: false, data: {} });

  showModal = (data = {}) => {
    const { autoSave, handleSubmitForm } = this.props;

    if (autoSave && handleSubmitForm) Timer.check(handleSubmitForm);
    this.setState({ showModal: true, data });
  };

  render() {
    const { extra = [], accessRemove } = this.props;
    const { data, showModal } = this.state;
    const {
      id,
      fee,
      time,
      name,
      criteria,
      generalPrinciples,
      arRequired,
      dossierLanguage,
      procedures = [],
      checklist = [],
    } = data;

    const isEdit = !!name;

    return (
      <Modal
        title={isEdit ? 'Edit requirement' : 'Add requirement'}
        visible={showModal}
        footer={null}
        centered
        style={{ top: 40 }}
        destroyOnClose
        maskClosable={false}
        onCancel={this.closeModal}
        onClose={this.closeModal}
        width={1000}
        zIndex={998}
      >
        {showModal && (
          <div className={styles.container}>
            <ItemId
              ref={ref => { this.refId = ref; }}
              value={id}
            />

            <ItemInput
              autoFocus
              ref={ref => { this.refName = ref; }}
              label='Name'
              value={name}
            />

            {extra.includes('timelineAndFees') && (
              <>
                <ItemRange
                  ref={ref => { this.refFee = ref; }}
                  label='Fee'
                  value={fee}
                  unit='USD'
                  showTextInput
                />

                <ItemRange
                  ref={ref => { this.refTime = ref; }}
                  label='Time'
                  value={time}
                  unit='Months'
                  showTextInput
                />
              </>
            )}

            {extra.includes('arRequired') && (
              <ItemArRequired
                ref={ref => { this.refArRequired = ref; }}
                label='AR Required'
                value={arRequired}
              />
            )}

            {extra.includes('dossierLanguage') && (
              <ItemInput
                ref={ref => { this.refDossierLanguage = ref; }}
                label='Dossier Language'
                value={dossierLanguage}
              />
            )}

            {extra.includes('generalPrinciples') ? (
              <MarkDownEditor
                ref={ref => { this.refGeneralPrinciples = ref; }}
                value={generalPrinciples}
                label='General Principles'
              />
            ) : (
              <MarkDownEditor
                ref={ref => { this.refCriteria = ref; }}
                value={criteria}
                label='Criteria'
              />
            )}

            <Procedure
              showTag
              ref={ref => { this.refProcedures = ref; }}
              label='Procedures'
              value={procedures}
              accessRemove={accessRemove}
            />

            {extra.includes('checklists') && (
              <Procedure
                showTag
                ref={ref => { this.refChecklist = ref; }}
                label='Checklist'
                value={checklist}
                accessRemove={accessRemove}
              />
            )}

            <Item style={{ marginBottom: 0 }}>
              <Button onClick={this.closeModal}>Cancel</Button>

              <Button style={{ marginLeft: 8 }} type='primary' onClick={this.handleSubmit}>
                {isEdit ? 'Update' : 'Add'}
              </Button>

              {isEdit && (
                <Tooltip title={!accessRemove && 'No permission'}>
                  <Button style={{ marginLeft: 8 }} type='danger' disabled={!accessRemove} onClick={this.handleDelete}>Delete</Button>
                </Tooltip>
              )}
            </Item>
          </div>
        )}
      </Modal>
    );
  }
}
