import React from 'react';

export default (props) => {
  const {
    width,
    children,
    label = '',
    childrenStyle = {},
    ...otherProps
  } = props;

  return (
    <div className='b-flex' style={{ marginBottom: '20px' }} {...otherProps}>
      <div style={{ width: width || '140px', fontWeight: 'bold' }}>
        {label}
      </div>

      <div className='b-flex-flex b-flex-column' style={childrenStyle}>{children}</div>
    </div>
  );
};
