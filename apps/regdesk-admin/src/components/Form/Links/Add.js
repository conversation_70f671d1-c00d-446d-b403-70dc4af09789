import React, { PureComponent } from 'react';
import moment from 'moment';
import { Form, Button, Input, DatePicker, Icon } from 'antd';
import ModalUpload from '../../Upload/Modal';
import { FULL_DATE_FORMAT } from '../../../utils/date';

const FormItem = Form.Item;

@Form.create()

export default class AddLink extends PureComponent {
  constructor(props) {
    super(props);

    this.formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 },
      },
    };

    this.submitFormLayout = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 14, offset: 6 },
      },
    };
  }

  handleSubmit = () => {
    this.props.form.validateFieldsAndScroll((err, values) => {
      if (!err) {
        const { item, onChange } = this.props;
        let { id } = item;

        if (!id) id = Date.now();
        if (onChange) onChange({ id, ...values });

        this.closeModal();
      }
    });
  };

  closeModal = () => {
    const { onCloseModal } = this.props;

    if (onCloseModal) onCloseModal();
  }

  render() {
    const { form, item = {}, params={} } = this.props;
    const { getFieldDecorator } = form;
    const { name, description, references, date, url, id } = item;
    const { countryId, moduleName } = params;

    return (
      <Form onSubmit={this.handleSubmit}>
        <FormItem label='Name' {...this.formItemLayout}>
          {getFieldDecorator('name', {
            rules: [{ required: true, message: 'Please enter name' }],
            initialValue: name || '',
          })(
            <Input
              autoFocus
              placeholder='Name...'
              autoComplete='off'
            />
          )}
        </FormItem>

        <FormItem label='Description' {...this.formItemLayout}>
          {getFieldDecorator('description', {
            initialValue: description || '',
          })(
            <Input.TextArea
              rows={8}
              placeholder='Description...'
              autoComplete='off'
            />
          )}
        </FormItem>

        <FormItem label='References' {...this.formItemLayout}>
          {getFieldDecorator('references', {
            rules: [{ required: true, message: 'Please enter references' }],
            initialValue: references || '',
          })(
            <Input.TextArea
              rows={3}
              placeholder='References...'
              autoComplete='off'
            />
          )}
        </FormItem>

        <Form.Item label='Date' {...this.formItemLayout}>
          {getFieldDecorator('date', {
            rules: [{ type: 'object', required: true, message: 'Please select date' }],
            initialValue: date ? moment(date): moment(),
          })(
            <DatePicker
              placeholder='Date...'
              autoComplete='off'
              format={FULL_DATE_FORMAT}
            />
          )}
        </Form.Item>

        <FormItem label='URL' {...this.formItemLayout}>
          {getFieldDecorator('url', {
            rules: [{
              required: true,
              pattern: /[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)?/gi,
              message: 'Please input only URL link.',
            }],
            initialValue: url || '',
          })(
            <Input
              placeholder='Url...'
              autoComplete='off'
              addonAfter={<Icon type='upload' onClick={()=> { this.uploadModal && this.uploadModal.showModal(true); }} />}
            />,
          )}
        </FormItem>

        <ModalUpload
          ref={ref => { this.uploadModal = ref; }}
          onUpload={(link) => this.props.form.setFieldsValue({ url: link })}
          info={{ module: moduleName, countryId }}
        />

        <FormItem style={{ marginBottom: 0 }} {...this.submitFormLayout}>
          <Button onClick={() => { this.props.onCancel ? this.props.onCancel() : this.closeModal(); }}>Cancel</Button>

          <Button
            style={{ marginLeft: 8 }}
            type='primary'
            onClick={this.handleSubmit}
          >
            {id ? 'Update' : 'Add'}
          </Button>
        </FormItem>
      </Form>
    );
  }
}
