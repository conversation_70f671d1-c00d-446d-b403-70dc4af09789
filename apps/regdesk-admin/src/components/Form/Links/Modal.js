import React, { PureComponent } from 'react';
import { Modal } from 'antd';
import LinkLegislation from './LinkLegislation';

export default class AddLegislationModal extends PureComponent {
  render() {
    const { item = {}, showModal, onCancel } = this.props;

    return (
      <Modal
        title={item.id ? 'Edit' : 'Add'}
        visible={showModal}
        footer={null}
        centered
        destroyOnClose
        maskClosable={false}
        onCancel={onCancel}
        onClose={onCancel}
        width={820}
        zIndex={999}
      >
        {showModal && <LinkLegislation {...this.props} />}
      </Modal>
    );
  }
}
