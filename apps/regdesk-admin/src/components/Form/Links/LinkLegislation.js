import React from 'react';
import moment from 'moment';
import classNames from 'classnames';
import { Button, Input, List, Tooltip } from 'antd';
import api from '../../../utils/api';
import styles from './index.less';

export default class LinkLegislation extends React.Component {
  constructor(props) {
    super(props);

    this.defaultPagination = {
      current: 1,
      pageSize: 5,
      total: 0,
    };

    this.steps = {
      0: {
        title: 'Select Legislation',
        description: 'Please select a legislation',
        onSearchChange: e => this.setState({ searchValue: e.target.value }, this.getLegislations),
        onSearch: this.getLegislations,
      },
      1: {
        title: 'Select Reference',
        description: 'Please select a reference',
        onSearchChange: e => this.setState({ searchReferenceValue: e.target.value }),
      },
    };

    this.state = {
      current: 0,
      list: [],
      references: [],
      selectedLegislation: null,
      selectedReference: null,
      loading: false,
      pagination: this.defaultPagination,
      searchValue: '',
      searchReferenceValue: '',
    };
  }

  componentDidMount() {
    this.getLegislations();
  }

  getLegislations = ({ pagination = this.defaultPagination } = {}) => {
    const { params = {}, skipLegislations = [] } = this.props;
    const { countryId, module } = params;
    const { searchValue } = this.state;

    this.setState({ loading: true });

    api.legislations
      .get({ pagination, filters: { searchValue, module, countryId, skipLegislations } })
      .then(data => this.setState({ ...data, loading: false }))
      .catch(() => this.setState({ loading: false }));
  }

  onPrevClick =() => {
    const { current } = this.state;

    this.setState({ current: current - 1, selectedReference: null, searchReferenceValue: '' });
  }

  onLegislationClick = (id, references) => {
    const { legislationOnly } = this.props;

    if (legislationOnly) {
      this.setState({ selectedLegislation: id });
    } else {
      this.setState({ selectedLegislation: id, current: 1, references });
    }
  };

  onReferenceClick = (id) => {
    const { selectedReference } = this.state;

    this.setState({ selectedReference: selectedReference === id ? null : id });
  };

  selectedClassname = (id, compareId) => classNames(id === compareId ? styles.active : '');

  onSubmit = () => {
    const { onSubmit } = this.props;
    const { selectedLegislation, selectedReference } = this.state;

    if (onSubmit) onSubmit({ selectedLegislation, selectedReference });
  }

  calcSubmitDisabled = () => {
    const { selectedLegislation, selectedReference } = this.state;
    const { legislationOnly } = this.props;

    if (legislationOnly) return !selectedLegislation;

    return !selectedLegislation || !selectedReference;
  }

  render() {
    const {
      list,
      references,
      pagination,
      loading,
      searchValue,
      searchReferenceValue = '',
      selectedLegislation,
      selectedReference,
      current,
    } = this.state;
    const submitDisabled = this.calcSubmitDisabled() ;
    const btnPrevDisabled = current === 0;
    const { title, description, onSearchChange, onSearch } = this.steps[current];
    const filteredReferences = references.filter(({ text }) => text.toLowerCase().includes(searchReferenceValue.toLowerCase()));

    return (
      <div className={styles.container}>
        <div className={styles.description}>
          <div>
            <h3>{title}</h3>
            <p>{description}</p>
          </div>

          <div style={{ display: 'flex' }}>
            <Input.Search
              loading={loading}
              value={current === 0 ? searchValue : searchReferenceValue}
              className={styles.search}
              placeholder='Search by name...'
              onSearch={onSearch}
              onChange={onSearchChange}
            />
          </div>
        </div>

        {current === 0 && (
          <List
            size='large'
            pagination={{
              size: 'Pagination',
              onChange: (nextPage) => this.getLegislations({ pagination: { ...pagination, current: nextPage } }),
              ...pagination,
            }}
            loading={loading}
            dataSource={list}
            style={{ maxHeight: 'auto', overflow: 'auto', marginBottom: 15 }}
            renderItem={({ _id: id, ...item }) => (
              <List.Item
                onClick={() => this.onLegislationClick(id, item.references)}
                className={this.selectedClassname(id, selectedLegislation)}
              >
                <List.Item.Meta
                  title={<div className={styles.title}><Tooltip title={item.title}>{item.title}</Tooltip></div>}
                  description={
                    <Tooltip title={item.description}>
                      <span style={{ fontWeight: 700 }}>Description: </span>{item.description}
                    </Tooltip>
                  }
                />

                <div className={styles.date}>
                  <span>Published Date</span>
                  <p>{item.isPublishDateDisabled ? 'Not Announced' : moment(item.publishDate).getUTC()}</p>
                </div>
              </List.Item>
            )}
          />
        )}

        {current === 1 && (
          <List
            size='large'
            dataSource={filteredReferences}
            pagination={{
              pageSize: 5,
              total: filteredReferences.length,
            }}
            style={{ maxHeight: 'auto', overflow: 'auto', marginBottom: 15 }}
            renderItem={({ id, text }) => (
              <List.Item
                onClick={() => this.onReferenceClick(id)}
                className={this.selectedClassname(id, selectedReference)}
              >
                <List.Item.Meta
                  description={
                    <Tooltip title={text}>
                      <span style={{ fontWeight: 700 }}>Reference: </span>{text}
                    </Tooltip>
                  }
                />
              </List.Item>
            )}
          />
        )}

        <div className={styles.buttons}>
          <Button onClick={this.onPrevClick} disabled={btnPrevDisabled}>Prev</Button>

          <Button
            style={{ marginLeft: 8 }}
            type='primary'
            onClick={this.onSubmit}
            disabled={submitDisabled}
          >
            Done
          </Button>
        </div>
      </div>
    );
  }
}
