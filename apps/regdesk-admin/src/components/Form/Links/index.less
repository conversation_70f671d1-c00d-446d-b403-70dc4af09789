.container {
  :global {
    .ant-list-item {
      padding-left: 6px;
      border-left: 4px solid transparent;

      &:first-child {
        border-top: 1px solid #e8e8e8;
      }

      &:hover {
        cursor: pointer;
        background-color: rgba(108,192,229,0.2);
        border-left: 4px solid #6CC0E5;
        border-bottom: 1px solid #ffffff;
      }
    }

    .ant-list-item-action {
      display: flex;
      align-items: center;
    }
  }
  
  ::-webkit-scrollbar {
    display: none !important;
  }

  .active {
    background-color: rgba(108,192,229,0.2);
    border-left: 4px solid #6CC0E5;
    border-bottom: 1px solid #ffffff !important;
  }

  .title {
    color: #41ADDD;
    word-break: break-word;
  }

  .description {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      font-size: 22px;
      margin-top: 0;
    }
  }

  .search {
    min-width: 300px;
  }

  .date {
    width: 120px;
    margin-left: 10px;
  }

  .buttons {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    margin-top: 20px;

    button {
      margin-left: 10px;
    }
  }
}
