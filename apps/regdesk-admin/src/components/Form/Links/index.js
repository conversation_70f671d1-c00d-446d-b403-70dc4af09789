import React from 'react';
import moment from 'moment';
import { Table, Modal, Button, Descriptions, Tooltip, Tag, message } from 'antd';
import { DndProvider, DragSource, DropTarget } from 'react-dnd';
import HTML5Backend from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import AddLegislationModal from './Modal';
import Timer from '../Timer';
import styles from '../index.less';
import api from '../../../utils/api';

let dragingIndex = -1;

class BodyRow extends React.Component {
  render() {
    const { isOver, connectDragSource, connectDropTarget, moveRow, ...restProps } = this.props;
    const style = { ...restProps.style, cursor: 'move' };

    let { className } = restProps;

    if (isOver) {
      if (restProps.index > dragingIndex) className += ' drop-over-downward';
      if (restProps.index < dragingIndex) className += ' drop-over-upward';
    }

    return connectDragSource(
      connectDropTarget(<tr {...restProps} className={className} style={style} />),
    );
  }
}

const rowSource = {
  beginDrag(props) {
    dragingIndex = props.index;

    return { index: props.index };
  },
};

const rowTarget = {
  drop(props, monitor) {
    const dragIndex = monitor.getItem().index;
    const hoverIndex = props.index;

    // Don't replace items with themselves
    if (dragIndex === hoverIndex) return;

    // Time to actually perform the action
    props.moveRow(dragIndex, hoverIndex);

    // Note: we're mutating the monitor item here!
    // Generally it's better to avoid mutations,
    // but it's good here for the sake of performance
    // to avoid expensive index searches.
    monitor.getItem().index = hoverIndex;
  },
};

const DragableBodyRow = DropTarget('row', rowTarget, (connect, monitor) => ({
  connectDropTarget: connect.dropTarget(),
  isOver: monitor.isOver(),
}))(
  DragSource('row', rowSource, connect => ({
    connectDragSource: connect.dragSource(),
  }))(BodyRow),
);

export default class DragSortingTable extends React.Component {
  constructor(props) {
    super(props);

    const { data, params, accessRemove } = this.props;

    this.state = {
      loading: false,
      showModal: false,
      data: data || [],
      params: params || {},
      legislations: [],
    };

    this.components = { body: { row: DragableBodyRow } };

    this.columns = [
      {
        title: 'Name',
        dataIndex: 'title',
        key: 'name',
        ellipsis: true,
        render: text => <Tooltip title={text}>{text}</Tooltip>,
      },
      {
        title: 'Description',
        dataIndex: 'description',
        key: 'description',
        ellipsis: true,
        render: text => <Tooltip title={text}>{text}</Tooltip>,
      },
      {
        title: 'References',
        dataIndex: 'references',
        key: 'references',
        ellipsis: true,
        render: text => <Tooltip title={text}>{text}</Tooltip>,
      },
      {
        title: 'Date',
        dataIndex: 'publishDate',
        key: 'date',
        width: 130,
        render: (date, { isPublishDateDisabled }) => isPublishDateDisabled ? 'Not Announced' : moment(date).getUTC(),
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        sorter: (a, b) => {
          if (a.status > b.status) return 1;
          if (a.status < b.status) return -1;

          return 0;
        },
        render: status => {
          switch (status) {
            case 'current':
              return <Tag color='green'>Current</Tag>;
            case 'draft':
              return <Tag color='gold'>Draft</Tag>;
            case 'superseded':
              return <Tag color='volcano'>Superseded</Tag>;
            default:
              return null;
          }
        }
      },
      {
        title: 'Url',
        dataIndex: 'url',
        key: 'url',
        render: (text) => (
          <Tooltip title={text}>
            <a target='_blank' rel='noreferrer' href={text}>{this.ellipsis(text)}</a>
          </Tooltip>
        ),
      },
      {
        title: 'Actions',
        dataIndex: '_id',
        key: 'actions',
        width: 90,
        render: (id) => (
          <Tooltip title={!accessRemove && 'No permission'}>
            <a className={!accessRemove ? styles.disabled : ''} onClick={() => accessRemove && this.onRemove(id)}>Delete</a>
          </Tooltip>
        )
      }
    ];
  }

  componentDidMount() {
    this.getLegislations();
  }

  componentDidUpdate(prevProps) {
    const { data } = this.props;

    if (prevProps.data !== data) this.getLegislations();
  }

  componentWillReceiveProps = (nextProps) => {
    this.setState({ data: nextProps.data, params: nextProps.params });
  };

  getLegislations = () => {
    const { data = [] } = this.state;

    this.setState({ loading: true });

    api.legislations
      .getByIds({ data: JSON.stringify(data) })
      .then(({ items = [] }) => {
        items.sort((a, b) => a.status.localeCompare(b.status));
        this.setState({ legislations: items, loading: false });
      })
      .catch(() => this.setState({ loading: false }));
  }

  ellipsis = text => (
    <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', maxWidth: 200 }}>
      {text}
    </div>
  );

  moveRow = (dragIndex, hoverIndex) => {
    const { data, legislations } = this.state;
    const dragRow = data[dragIndex];
    const legislationDragRow = legislations[dragIndex];

    this.setState(
      update(this.state, {
        data: { $splice: [[dragIndex, 1], [hoverIndex, 0, dragRow]] },
        legislations: { $splice: [[dragIndex, 1], [hoverIndex, 0, legislationDragRow]] },
      }),
    );
  };

  onAdd = () => {
    const { autoSave, handleSubmitForm } = this.props;

    if (autoSave && handleSubmitForm) Timer.check(handleSubmitForm);
    this.setState({ showModal: true });
  };

  onCloseModal = () => this.setState({ showModal: false });

  onRemove = itemId => {
    const { handleSubmitForm } = this.props;
    const { data, legislations } = this.state;

    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure you would like to delete this? The law will only be unlinked from this location!',
      onOk: () => {
        this.setState({
          data: data.filter(({ legislation }) => itemId !== legislation),
          legislations: legislations.filter(({ _id: id }) => itemId !== id),
        }, () => {
          if (handleSubmitForm) handleSubmitForm();
        });
      },
    });
  };

  onChange = ({ selectedLegislation, selectedReference }) => {
    const { handleSubmitForm } = this.props;
    const { data } = this.state;
    const newData = [...data];
    const isAlreadyAdded = newData.find(({ legislation }) => legislation === selectedLegislation);

    if (isAlreadyAdded) {
      message.warn('This Legislation Is Already Added');

      return;
    }

    newData.push({ legislation: selectedLegislation, referenceId: selectedReference });

    this.setState(
      { data: newData, showModal: false },
      () => {
        this.getLegislations();

        if (handleSubmitForm) handleSubmitForm();
      });
  };

  getValue = () => this.state.data;

  render() {
    const { title = 'Links', addTitle = 'Add link' } = this.props;
    const { params, showModal, loading, legislations, data } = this.state;

    return (
      <div>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 30 }}>
          <Descriptions title={title} />
          <Button size='small' icon='plus' type='primary' style={{ marginBottom: 20 }} onClick={this.onAdd}>{addTitle}</Button>
        </div>

        <DndProvider backend={HTML5Backend}>
          <Table
            rowKey='_id'
            className={styles.tableDragSorting}
            columns={this.columns}
            dataSource={legislations}
            components={this.components}
            onRow={(record, index) => ({ index, moveRow: this.moveRow })}
            loading={loading}
          />
        </DndProvider>

        <AddLegislationModal
          skipLegislations={data?.map(({ legislation }) => legislation) || []}
          params={params}
          showModal={showModal}
          onSubmit={this.onChange}
          onCancel={this.onCloseModal}
          onCloseModal={this.onCloseModal}
        />
      </div>
    );
  }
}
