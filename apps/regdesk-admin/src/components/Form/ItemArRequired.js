import React from 'react';
import { Select } from 'antd';
import Item from './Item';

const { Option } = Select;

/**
 * ItemArRequired
 */
export default class ItemArRequired extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value,
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value });
  };

  getValue = () => this.state.value;

  handleSelect = value => {
    const { onChange } = this.props;

    this.setState({ value });
    if (onChange) onChange(value);
  };

  render() {
    const { label, isNA = false } = this.props;
    const { value } = this.state;

    return (
      <Item label={label}>
        <Select
          value={value}
          onChange={this.handleSelect}
          style={{ width: '200px' }}
        >
          {isNA && <Option value=''>N/A</Option>}
          <Option value='true'>YES</Option>
          <Option value='false'>NO</Option>
        </Select>
      </Item>
    );
  }
}
