import React from 'react';
import { Button, Descriptions, Divider, Modal, Table, Tooltip } from 'antd';
import Form from './Form';
import Document from '../Document';
import Documents from '../DraggableDocuments';
import styles from '../index.less';

/**
 * Licenses
 */
export default class Licenses extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value || [],
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value || [] });
  };

  /**
   * Event add new license
   */
  onAdd = () => {
    const newItem = {
      id: undefined,
      name: '',
      accessLevel: '',
      definition: '',
      criteria: '',
      arRequired: false,
      licenseLanguage: '',
      licenseValidity: '',
      links: [],
      documents: [],
      procedures: [],
      checklist: [],
      sample: [],
    };

    this.formApprovalRoute.showModal(newItem);
  };

  /**
   * Event edit license
   * @param item
   */
  onEdit = item => this.formApprovalRoute.showModal(item);

  /**
   * Event remove license
   * @param item
   */
  onRemove = item => {
    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure want to clear it?',
      onOk: () => {
        const { handleSubmitForm } = this.props;

        this.remove(item);
        if (handleSubmitForm) handleSubmitForm();
      },
    });
  };

  /**
   * Add license
   * @param item
   */
  add = item => {
    const { value } = this.state;

    value.push(item);
    this.setState({ value });
  };

  /**
   * Remove license
   * @param item
   */
  remove = item => {
    const { value } = this.state;
    const { key } = item;

    value.splice(key, 1);
    this.setState({ value });
  };

  /**
   * Update license
   * @param item
   */
  update = item => {
    const { value } = this.state;
    const { key } = item;

    value[key] = item;
    this.setState({ value });
  };

  /**
   * Event add new document
   * @param parentKey
   */
  onAddDoc = parentKey => {
    const newDoc = {
      id: undefined,
      parentKey,
      name: '',
      dicType: '',
      dicCategory: '',
      translation: false,
      fee: false,
      notary: false,
      legalize: false,
      apostille: false,
      original: false,
      highlight: false,
      copies: 0,
      eCopies: 0,
      definition: '',
      attention: [],
      sample: [],
    };

    this.formDocument.showModal(newDoc);
  };

  /**
   * Event edit doc
   * @param item
   */
  onEditDoc = item => this.formDocument.showModal(item);

  /**
   * Add new doc
   * @param item
   */
  addDoc = item => {
    const { value } = this.state;
    const { parentKey } = item;

    value[parentKey].documents.push(item);
    value[parentKey].lastUpdated = new Date();
    this.setState({ value });
  };

  /**
   * Remove new doc
   * @param item
   */
  removeDoc = item => {
    const { value } = this.state;
    const { parentKey, key } = item;

    value[parentKey].documents.splice(key, 1);
    value[parentKey].lastUpdated = new Date();
    this.setState({ value });
  };

  /**
   * Update doc
   * @param item
   */
  updateDoc = item => {
    const { value } = this.state;
    const { parentKey, key } = item;

    value[parentKey].documents[key] = item;
    value[parentKey].lastUpdated = new Date();
    this.setState({ value });
  };

  /**
   * Get value
   * @returns {*}
   */
  getValue = () => this.state.value;

  render() {
    const { handleSubmitForm, autoSave, accessRemove, countryId } = this.props;
    const { value = [] } = this.state;
    const data = value.map((v, i) => ({ ...v, key: i }));

    return (
      <div style={{ marginBottom: 20 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 30 }}>
          <Descriptions title='Licenses and Certificates' />
          <Button size='small' icon='plus' type='primary' style={{ marginBottom: 20 }} onClick={this.onAdd}>Add</Button>
        </div>

        <Table
          rowKey='id'
          dataSource={data}
          columns={[
            {
              title: 'Name',
              dataIndex: 'name',
              render: (name, record) => <a onClick={() => this.onEdit(record)}>{name}</a>,
            },
            {
              title: 'Documents',
              dataIndex: 'documents',
              render: (documents, record) => (
                <Documents
                  documents={documents}
                  parentKey={record.key}
                  onEdit={this.onEditDoc}
                  onChange={newDocs => {
                    this.update({ ...record, documents: newDocs });
                    handleSubmitForm();
                  }}
                />
              ),
            },
            {
              title: 'Actions',
              dataIndex: 'key',
              key: 'actions',
              width: 200,
              render: (key, record) => (
                <div>
                  <a onClick={() => this.onAddDoc(key)}>Add Doc</a>
                  <Divider type='vertical' />
                  <a onClick={() => this.onEdit(record)}>Edit</a>
                  <Divider type='vertical' />

                  <Tooltip title={!accessRemove && 'No permission'}>
                    <a className={!accessRemove ? styles.disabled : ''} onClick={() => accessRemove && this.onRemove(key)}>Delete</a>
                  </Tooltip>
                </div>
              ),
            },
          ]}
        />

        <Form
          onAdd={this.add}
          autoSave={autoSave}
          onRemove={this.remove}
          onUpdate={this.update}
          countryId={countryId}
          accessRemove={accessRemove}
          handleSubmitForm={handleSubmitForm}
          ref={ref => { this.formApprovalRoute = ref; }}
        />

        <Document
          module='pharma'
          onAdd={this.addDoc}
          autoSave={autoSave}
          onRemove={this.removeDoc}
          onUpdate={this.updateDoc}
          accessRemove={accessRemove}
          handleSubmitForm={handleSubmitForm}
          ref={ref => { this.formDocument = ref; }}
        />
      </div>
    );
  }
}
