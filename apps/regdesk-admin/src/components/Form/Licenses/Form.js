import { Button, message, Modal, Tooltip } from 'antd';
import React from 'react';
import styles from '../index.less';
import ItemArRequired from '../ItemArRequired';
import ItemId from '../ItemId';
import ItemInput from '../ItemInput';
import ItemLevel from '../ItemLevel';
import ItemSample from '../ItemSample';
import Links from '../Links';
import MarkDownEditor from '../MarkDownEditor';
import Procedure from '../Procedure';
import Timer from '../Timer';

/**
 * Form
 */
export default class Form extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      data: props.data || {},
      showModal: false,
    };
  }

  /**
   * Handle Submit
   */
  handleSubmit = () => {
    const { onUpdate, onAdd, handleSubmitForm } = this.props;
    const { data } = this.state;
    const isEdit = !!data.name;

    const newData = {
      ...data,
      id: this.refId.getValue(),
      criteria: this.refCriteria.getValue(),
      definition: this.refDefinition.getValue(),
      name: this.refName.getValue(),
      links: this.refLinks.getValue(),
      sample: this.refSample.getValue(),
      accessLevel: this.refAccessLevel.getValue(),
      arRequired: this.refArRequired.getValue(),
      licenseLanguage: this.refLicenseLanguage.getValue(),
      licenseValidity: this.refLicenseValidity.getValue(),
      procedures: this.refProcedures.getValue(),
      checklist: this.refChecklist.getValue(),
      lastUpdated: new Date(),
    };

    if (!newData.name) {
      message.warn('Name empty!');

      return;
    }

    if (isEdit && onUpdate) onUpdate(newData);
    if (!isEdit && onAdd) onAdd(newData);
    if (handleSubmitForm) handleSubmitForm();

    this.closeModal();
  };

  /**
   * Handle Delete
   */
  handleDelete = () => {
    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure want to clear it?',
      onOk: () => {
        const { onRemove, handleSubmitForm } = this.props;
        const { data } = this.state;

        if (onRemove) onRemove(data);
        if (handleSubmitForm) handleSubmitForm();

        this.closeModal();
      },
    });
  };

  /**
   * Close Modal
   */
  closeModal = () => this.setState({ showModal: false, data: {} });

  /**
   * Show Modal
   * @param data
   */
  showModal = (data = {}) => {
    const { autoSave, handleSubmitForm } = this.props;

    if (autoSave && handleSubmitForm) Timer.check(handleSubmitForm);
    this.setState({ showModal: true, data });
  };

  render() {
    const { accessRemove, countryId } = this.props;
    const { data, showModal } = this.state;
    const {
      id,
      name,
      accessLevel,
      definition,
      criteria,
      arRequired,
      licenseLanguage,
      licenseValidity,
      procedures = [],
      checklist = [],
      links = [],
      sample = [],
    } = data;

    const isEdit = !!name;

    return (
      <Modal
        title={isEdit ? 'Edit licenses or certificates' : 'Add licenses or certificates'}
        visible={showModal}
        footer={null}
        centered
        style={{ top: 40 }}
        destroyOnClose
        maskClosable={false}
        onCancel={this.closeModal}
        onClose={this.closeModal}
        width={1000}
        zIndex={998}
      >
        {showModal && (
          <div className={styles.container}>
            <ItemId
              ref={ref => { this.refId = ref; }}
              value={id}
            />

            <ItemInput
              autoFocus
              ref={ref => { this.refName = ref; }}
              label='Name'
              value={name}
            />

            <ItemLevel
              ref={ref => { this.refAccessLevel = ref; }}
              label='Access Level'
              value={accessLevel}
            />

            <ItemArRequired
              ref={ref => { this.refArRequired = ref; }}
              label='AR Required'
              value={arRequired}
            />

            <ItemInput
              ref={ref => { this.refLicenseLanguage = ref; }}
              label='License Language'
              value={licenseLanguage}
            />

            <ItemInput
              ref={ref => { this.refLicenseValidity = ref; }}
              label='License Validity'
              value={licenseValidity}
              unit='Years'
            />

            <MarkDownEditor
              ref={ref => { this.refDefinition = ref; }}
              value={definition}
              label='Definition'
            />

            <MarkDownEditor
              ref={ref => { this.refCriteria = ref; }}
              value={criteria}
              label='Criteria'
            />

            <Procedure
              ref={ref => { this.refChecklist = ref; }}
              label='Checklist'
              value={checklist}
              accessRemove={accessRemove}
            />

            <Procedure
              showTag
              ref={ref => { this.refProcedures = ref; }}
              label='Procedures'
              value={procedures}
              accessRemove={accessRemove}
            />

            <Links
              ref={ref => { this.refLinks = ref; }}
              label='Links'
              data={links}
              accessRemove={accessRemove}
              params={{ module: 'pharma-reg', countryId }}
            />

            <ItemSample
              page='pharma'
              label='Sample'
              ref={ref => { this.refSample = ref; }}
              value={sample}
              accessRemove={accessRemove}
            />

            <div style={{ marginBottom: 0 }}>
              <Button onClick={this.closeModal}>Cancel</Button>

              <Button style={{ marginLeft: 8 }} type='primary' onClick={this.handleSubmit}>
                {isEdit ? 'Update' : 'Add'}
              </Button>

              {isEdit && (
                <Tooltip title={!accessRemove && 'No permission'}>
                  <Button style={{ marginLeft: 8 }} type='danger' disabled={!accessRemove} onClick={this.handleDelete}>Delete</Button>
                </Tooltip>
              )}
            </div>
          </div>
        )}
      </Modal>
    );
  }
}
