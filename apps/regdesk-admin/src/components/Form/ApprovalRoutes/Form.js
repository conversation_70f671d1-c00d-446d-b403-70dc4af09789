import { Button, message, Modal, Tooltip } from 'antd';
import React from 'react';
import styles from '../index.less';
import ItemArRequired from '../ItemArRequired';
import ItemCountryCode from '../ItemCountryCode';
import ItemId from '../ItemId';
import ItemInput from '../ItemInput';
import ItemLevel from '../ItemLevel';
import ItemSample from '../ItemSample';
import Links from '../Links';
import MarkDownEditor from '../MarkDownEditor';
import Procedure from '../Procedure';
import Timer from '../Timer';

/**
 * Form
 */
export default class Form extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      data: props.data || {},
      showModal: false,
    };
  }

  /**
   * Handle Submit
   */
  handleSubmit = () => {
    const { onUpdate, onAdd, handleSubmitForm } = this.props;
    const { data } = this.state;
    const isEdit = !!data.name;

    const newData = {
      ...data,
      id: this.refId.getValue(),
      criteria: this.refCriteria.getValue(),
      definition: this.refDefinition.getValue(),
      name: this.refName.getValue(),
      format: this.refFormat.getValue(),
      links: this.refLinks.getValue(),
      sample: this.refSample.getValue(),
      accessLevel: this.refAccessLevel.getValue(),
      countryCode: this.refCountryCode.getValue(),
      arRequired: this.refArRequired.getValue(),
      dossierLanguage: this.refDossierLanguage.getValue(),
      licenseValidity: this.refLicenseValidity.getValue(),
      procedures: this.refProcedures.getValue(),
      lastUpdated: new Date(),
    };

    if (!newData.name) return message.warn('Name empty!');

    if (isEdit && onUpdate) onUpdate(newData);
    if (!isEdit && onAdd) onAdd(newData);
    if (handleSubmitForm) handleSubmitForm();

    this.closeModal();
  };

  /**
   * handle Delete
   */
  handleDelete = () => {
    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure want to clear it?',
      onOk: () => {
        const { onRemove, handleSubmitForm } = this.props;
        const { data } = this.state;

        if (onRemove) onRemove(data);
        if (handleSubmitForm) handleSubmitForm();

        this.closeModal();
      },
    });
  };

  /**
   * close Modal
   */
  closeModal = () => this.setState({ showModal: false, data: {} });

  /**
   * show Modal
   * @param data
   */
  showModal = (data = {}) => {
    const { autoSave, handleSubmitForm } = this.props;

    if (autoSave && handleSubmitForm) Timer.check(handleSubmitForm);
    this.setState({ showModal: true, data });
  };

  render() {
    const { accessRemove, countryId } = this.props;
    const { data, showModal } = this.state;
    const {
      id,
      name,
      format,
      accessLevel,
      countryCode,
      definition,
      criteria,
      arRequired,
      dossierLanguage,
      licenseValidity,
      procedures = [],
      links = [],
      sample = [],
    } = data;

    const isEdit = !!name;

    return (
      <Modal
        title={isEdit ? 'Edit approval route' : 'Add approval route'}
        visible={showModal}
        footer={null}
        centered
        style={{ top: 40 }}
        destroyOnClose
        maskClosable={false}
        onCancel={this.closeModal}
        onClose={this.closeModal}
        width={1000}
        zIndex={998}
      >
        {showModal && (
          <div className={styles.container}>
            <ItemId
              ref={ref => { this.refId = ref; }}
              value={id}
            />

            <ItemInput
              autoFocus
              ref={ref => { this.refName = ref; }}
              label='Name'
              value={name}
            />

            <ItemLevel
              ref={ref => { this.refAccessLevel = ref; }}
              label='Access Level'
              value={accessLevel}
            />

            <ItemCountryCode
              ref={ref => { this.refCountryCode = ref; }}
              label='Country Code'
              value={countryCode}
            />

            <ItemArRequired
              ref={ref => { this.refArRequired = ref; }}
              label='AR Required'
              value={arRequired}
            />

            <ItemInput
              ref={ref => { this.refDossierLanguage = ref; }}
              label='Dossier Language'
              value={dossierLanguage}
            />

            <ItemInput
              ref={ref => { this.refLicenseValidity = ref; }}
              label='License Validity'
              value={licenseValidity}
              unit='Years'
            />

            <MarkDownEditor
              ref={ref => { this.refDefinition = ref; }}
              value={definition}
              label='Definition'
            />

            <MarkDownEditor
              ref={ref => { this.refCriteria = ref; }}
              value={criteria}
              label='Criteria'
            />

            <ItemInput
              ref={ref => { this.refFormat = ref; }}
              label='Format'
              value={format}
            />

            <Procedure
              showTag
              ref={ref => { this.refProcedures = ref; }}
              label='Procedures'
              value={procedures}
              accessRemove={accessRemove}
            />

            <Links
              ref={ref => { this.refLinks = ref; }}
              label='Links'
              data={links}
              accessRemove={accessRemove}
              params={{ module: 'pharma-reg', countryId }}
            />

            <ItemSample
              page='pharma'
              label='Sample'
              ref={ref => { this.refSample = ref; }}
              value={sample}
              accessRemove={accessRemove}
            />

            <div style={{ marginBottom: 0 }}>
              <Button onClick={this.closeModal}>Cancel</Button>

              <Button style={{ marginLeft: 8 }} type='primary' onClick={this.handleSubmit}>
                {isEdit ? 'Update' : 'Add'}
              </Button>

              {isEdit && (
                <Tooltip title={!accessRemove && 'No permission'}>
                  <Button style={{ marginLeft: 8 }} type='danger' disabled={!accessRemove} onClick={this.handleDelete}>Delete</Button>
                </Tooltip>
              )}
            </div>
          </div>
        )}
      </Modal>
    );
  }
}
