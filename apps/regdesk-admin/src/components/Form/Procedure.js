import React from 'react';
import { Button, Icon, Modal, Empty, Tag, Tooltip } from 'antd';
import Item from './Item';
import ItemInput from './ItemInput';
import styles from './index.less';

/**
 * Procedure
 */
export default class Procedure extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      input: '',
      value: props.value,
      showModal: false,
      item: {},
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value });
  };

  /**
   * Add item
   */
  add = () => {
    const { handleSubmitForm } = this.props;
    const { input, value } = this.state;

    if (input) {
      value.push({ name: input });
      this.setState({ value, input: '' });
      if (handleSubmitForm) handleSubmitForm();
    }
  };

  /**
   * Event for insert new data
   * @param item
   */
  onInsert = item => this.setState({ showModal: true, item: { insertKey: item.key + 1 } });

  /**
   * Event on edit item
   * @param item
   */
  onEdit = item => this.setState({ showModal: true, item });

  /**
   * Event close modal window
   */
  onCloseModal = () => this.setState({ showModal: false, item: {} });

  /**
   * Insert and update item
   */
  handleSubmit = () => {
    const { handleSubmitForm } = this.props;
    const { item, value } = this.state;
    const { key, insertKey } = item;
    const newValue = this.refName.getValue();

    if (Number.isInteger(key)) value[key] = { name: newValue };
    if (insertKey) value.splice(insertKey, 0, { name: newValue });

    this.setState({ value, showModal: false, item: {} });
    if (handleSubmitForm) handleSubmitForm();
  };

  /**
   * Remove item
   * @param item
   */
  remove = item => {
    const { handleSubmitForm } = this.props;
    const { value } = this.state;
    const { key } = item;

    value.splice(key, 1);
    this.setState({ value });
    if (handleSubmitForm) handleSubmitForm();
  };

  /**
   * Get value
   * @returns {*}
   */
  getValue = () => this.state.value;

  render() {
    const { value = [], input, showModal, item } = this.state;
    const { label = 'Procedures', showTag = false, accessRemove } = this.props;
    const data = value.map((v, i) => ({ ...v, key: i }));

    return (
      <Item label={label}>
        <textarea
          rows={4}
          className='ant-input'
          value={input}
          onChange={e => this.setState({ input: e.target.value })}
          style={{ width: '600px', marginRight: 10 }}
        />

        <Button onClick={this.add} type='primary' disabled={!input?.length}>Add</Button>
        <div style={{ marginTop: 20, height: 1, borderTop: '1px solid #e8e8e8', width: '100%' }} />
        {!data.length && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}

        <div>
          {data.map((v, i) => (
            <div key={`${v.name}_${v.key}`} className='b-flex b-padding-10' style={{ alignItems: 'center' }}>
              {showTag && <Tag style={{ marginRight: 10 }}>Step {i + 1}</Tag>}

              <div style={{ wordWrap: 'break-word', maxWidth: 'calc(100% - 80px)', paddingRight: 10 }} className='b-flex-flex'>
                {v.name}
              </div>

              <Icon type='plus-circle' onClick={() => this.onInsert(v)} />
              <Icon type='edit' style={{ marginLeft: 10, marginRight: 10 }} onClick={() => this.onEdit(v)} />

              <Tooltip title={!accessRemove && 'No permission'}>
                <Icon type='minus-circle' className={!accessRemove ? styles.disabled : ''} onClick={() => accessRemove && this.remove(v)} />
              </Tooltip>
            </div>
          ))}
        </div>

        <div style={{ height: 1, borderTop: '1px solid #e8e8e8', width: '100%', marginBottom: 20 }} />

        <Modal
          title={item.key ? 'Edit' : 'Add'}
          visible={showModal}
          footer={null}
          maskClosable
          onCancel={this.onCloseModal}
          onClose={this.onCloseModal}
          width={500}
          zIndex={1001}
        >
          {showModal && (
            <div>
              <ItemInput
                rows={6}
                label='Name'
                placeholder='Name...'
                autoComplete='off'
                ref={ref => { this.refName = ref; }}
                value={item.name || ''}
              />

              <div>
                <Button onClick={this.onCloseModal}>Cancel</Button>

                <Button style={{ marginLeft: 8 }} type='primary' onClick={this.handleSubmit}>
                  {Number.isInteger(item.key) ? 'Update' : 'Add'}
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </Item>
    );
  }
}
