import React from 'react';
import { Button, Descriptions, Divider, Table, Modal, Icon, Tooltip, message } from 'antd';
import ModalUpload from '../Upload/Modal';
import ItemInput from './ItemInput';
import Item from './Item';
import Timer from './Timer';
import styles from './index.less';

/**
 * ItemSample
 */
export default class ItemSample extends React.Component {
  constructor(props) {
    super(props);

    this.uploadModal = null;

    this.state = {
      value: props.value || [],
      item: {},
      showModal: false,
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value || [] });
  };

  onAdd = () => {
    const { autoSave, handleSubmitForm } = this.props;

    if (autoSave && handleSubmitForm) Timer.check(handleSubmitForm);
    this.setState({ item: {}, showModal: true });
  };

  onEdit = item => {
    const { autoSave, handleSubmitForm } = this.props;

    if (autoSave && handleSubmitForm) Timer.check(handleSubmitForm);
    this.setState({ item, showModal: true });
  };

  onCloseModal = () => this.setState({ showModal: false, item: {} });

  getValue = () => this.state.value;

  handleRemove = key => {
    const { handleSubmitForm } = this.props;
    const { value } = this.state;

    value.splice(key, 1);
    this.setState({ value });
    if (handleSubmitForm) handleSubmitForm();
  };

  handleSubmit = () => {
    const { handleSubmitForm } = this.props;

    const text = this.refText.getValue();
    const link = this.refLink.getValue();

    if (!text) return message.warn('Please enter text');
    if (!link) return message.warn('Please input URL link');

    const { value, item = {} } = this.state;
    const { key } = item;
    const newData = { text, link };

    if (key !== undefined) value[key] = newData;
    else value.push(newData);

    if (handleSubmitForm) handleSubmitForm();
    this.setState({ value });
    this.onCloseModal();
  };

  render() {
    const { label, page, countryId, accessRemove } = this.props;
    const { value = [], item, showModal } = this.state;
    const data = value.map((v, i) => ({ ...v, key: i }));

    return (
      <div style={{ marginBottom: 20 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 30 }}>
          <Descriptions title={label} />
          <Button size='small' icon='plus' type='primary' style={{ marginBottom: 20 }} onClick={this.onAdd}>Add Item</Button>
        </div>

        <Table
          rowKey='key'
          dataSource={data}
          columns={[
            {
              title: 'Link',
              dataIndex: 'text',
              render: (v, d) => <a target='_blank' rel='noreferrer' href={d.link}>{v}</a>,
            },
            {
              title: 'Actions',
              dataIndex: 'key',
              key: 'actions',
              width: 200,
              render: (v, d) => (
                <div>
                  <a onClick={() => this.onEdit(d)}>Edit</a>
                  <Divider type='vertical' />

                  <Tooltip title={!accessRemove && 'No permission'}>
                    <a className={!accessRemove ? styles.disabled : ''} onClick={() => accessRemove && this.handleRemove(v)}>Delete</a>
                  </Tooltip>
                </div>
              ),
            },
          ]}
        />

        <Modal
          title={item.key ? 'Edit Sample' : 'Add Sample'}
          visible={showModal}
          footer={null}
          centered
          destroyOnClose
          maskClosable={false}
          onCancel={this.onCloseModal}
          onClose={this.onCloseModal}
          width={500}
          zIndex={1001}
        >
          {showModal && (
            <div className={styles.container}>
              <ItemInput
                label='Text'
                placeholder='Text...'
                autoComplete='off'
                ref={ref => { this.refText = ref; }}
                value={item.text || ''}
              />

              <ItemInput
                label='Link'
                placeholder='Link...'
                autoComplete='off'
                ref={ref => { this.refLink = ref; }}
                value={item.link || ''}
                addonAfter={<Icon type='upload' onClick={() => { this.uploadModal && this.uploadModal.showModal(true); }} />}
              />

              <Item style={{ marginBottom: 0 }}>
                <Button onClick={this.onCloseModal}>Cancel</Button>

                <Button style={{ marginLeft: 8 }} type='primary' onClick={this.handleSubmit}>
                  {item.key ? 'Update' : 'Add'}
                </Button>
              </Item>
            </div>
          )}
        </Modal>

        <ModalUpload
          ref={ref => { this.uploadModal = ref; }}
          onUpload={newLink => this.refLink.setValue(newLink)}
          info={{ module: page, countryId }}
        />
      </div>
    );
  }
}
