import React from 'react';
import { <PERSON><PERSON>, message, Modal, Affix, Icon, <PERSON><PERSON><PERSON>, Card, Tag } from 'antd';
import moment from 'moment';
import BufferModal from './Buffer';
import actions from '../../actions';

/**
 * Form With Button on the top
 */
export default class FormWithButtonTop extends React.Component {
  handleSubmit = e => {
    const { onSubmit } = this.props;

    e.preventDefault();
    onSubmit();
  };

  handleClear = () => {
    const { onClear } = this.props;

    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure want to clear it?',
      onOk: () => onClear(),
    });
  };

  handleAgree = check => {
    const { onAgree } = this.props;

    Modal.confirm({
      title: 'Confirm',
      content: (
        <div>
          <div>Are you sure want to {check ? 'Approve' : 'Reject'} it?</div>

          {!check && (
            <div>
              <textarea
                type='text'
                style={{ height: 100 }}
                className='ant-input'
                id='comment'
              />
            </div>
          )}
        </div>
      ),
      okText: check ? 'Approve' : 'Reject',
      onOk: () => {
        const com = document.getElementById('comment');

        if (com) {
          if (!com.value) {
            message.info('Please comment!');

            return Promise.reject();
          }

          onAgree(check, com.value);
        } else {
          onAgree(check, '');
        }
      },
    });
  };

  /**
   * Get tag
   * @param check
   * @param comment
   * @returns {JSX.Element}
   */
  renderTag = (check, comment) => {
    let color = '';
    let text = 'No data';

    if (check === false) {
      color = 'gold';
      text = 'Under review';
    }

    if (check === false && comment) {
      color = 'volcano';
      text = 'Rejected';
    }

    if (check === true) {
      color = 'green';
      text = 'Approved';
    }

    return <Tag color={color} style={{ marginRight: 20 }}>{text}</Tag>;
  };

  render() {
    const {
      onClear,
      comment,
      children,
      loading,
      country,
      itemTitle,
      buffer,
      updatedAt,
      onSelectBuffer,
      getValue,
      check,
      module,
      accessApprove,
      accessRemoveData
    } = this.props;

    const bufferModule = module === 'device-reg' ? 'mdr' : 'phr';

    return (
      <>
        <form onSubmit={this.handleSubmit}>
          <Affix>
            <Card style={{ marginBottom: -1, position: 'sticky' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div>
                  {module !== 'ccp' && (
                    <>
                      <Tooltip title={!accessApprove && 'No permission'}>
                        <Button
                          icon='check'
                          loading={loading}
                          disabled={loading || !accessApprove}
                          onClick={() => this.handleAgree(true)}
                          style={{ marginRight: 10 }}
                        >
                          Approve
                        </Button>
                      </Tooltip>

                      <Tooltip title={!accessApprove && 'No permission'}>
                        <Button
                          icon='close'
                          loading={loading}
                          disabled={loading || !accessApprove}
                          onClick={() => this.handleAgree(false)}
                          style={{ marginRight: 10 }}
                        >
                          Reject
                        </Button>
                      </Tooltip>

                      {comment && (
                        <Tooltip title={comment} placement='bottom'>
                          <Icon type='warning' theme='filled' style={{ color: '#FAAD14' }} />
                        </Tooltip>
                      )}
                    </>
                  )}
                </div>

                <div style={{ display: 'flex', alignItems: 'center' }}>
                  {[true, false].includes(check) && this.renderTag(check, comment)}

                  {updatedAt && (
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', justifyContent: 'center', height: 32, marginRight: 20 }}>
                      <span style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)' }}>Last update</span>
                      <span style={{ fontSize: 14, lineHeight: '18px' }}>{moment(updatedAt).getFullUTC()}</span>
                    </div>
                  )}

                  <Button
                    type='primary'
                    icon='save'
                    disabled={loading}
                    htmlType='submit'
                  >
                    Save
                  </Button>

                  {onClear && (
                    <Tooltip title={!accessRemoveData && 'No permission'}>
                      <Button
                        loading={loading}
                        disabled={loading || !accessRemoveData}
                        type='danger'
                        onClick={this.handleClear}
                        style={{ marginLeft: 10 }}
                      >
                        Delete
                      </Button>
                    </Tooltip>
                  )}
                </div>
              </div>
            </Card>
          </Affix>

          <Card>
            {module !== 'ccp' && (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  backgroundColor: 'rgb(241, 241, 241)',
                  padding: '5px 10px 5px 17px',
                  margin: '0 0 20px'
                }}
              >
                <div style={{
                  color: 'rgba(0, 0, 0, 0.85)',
                  fontWeight: 500,
                  fontSize: 16,
                  lineHeight: '30px'
                }}
                >
                  Form
                </div>

                <div>
                  <span style={{ marginRight: 5 }}>Buffer:</span>

                  <Button
                    type='link'
                    icon='export'
                    style={{ padding: '0 5px' }}
                    onClick={() => {
                      const data = getValue();

                      actions[bufferModule].pushBuffer(`${country.name} / ${itemTitle}`, 'form', data);
                    }}
                  >
                    Copy form
                  </Button>

                  <Button
                    type='link'
                    icon='import'
                    style={{ padding: '0 5px' }}
                    onClick={() => { if (this.modalBuffer) this.modalBuffer.show(); }}
                  >
                    Insert
                  </Button>
                </div>
              </div>
            )}

            {children}
          </Card>
        </form>

        <BufferModal
          type='form'
          ref={ref => { this.modalBuffer = ref; }}
          onSelect={newItem => onSelectBuffer(newItem)}
          onRemove={newBuffer => { actions[bufferModule].change({ buffer: newBuffer }); }}
          buffer={buffer}
        />
      </>
    );
  }
}
