import React from 'react';
import { Switch } from 'antd';
import Item from './Item';

/**
 * Item Range
 */
export default class ItemRange extends React.Component {
  constructor(props) {
    super(props);

    const { value = {} } = props;

    this.state = {
      min: value.min || 0,
      max: value.max || 0,
      text: value.text || '',
      isText: value.isText || false,
    };
  }

  componentWillReceiveProps = nextProps => {
    const { value = {} } = nextProps;

    this.setState({ min: value.min || 0, max: value.max || 0, text: value.text || '', isText: value.isText || false });
  };

  onChange = ({ min = 0, max = 0, text, isText }) => {
    const { onChange } = this.props;

    if (typeof isText === 'boolean') this.setState({ isText });
    if (typeof text === 'string') this.setState({ text });
    if (typeof min === 'string' && (/^\d*$/.test(min))) this.setState({ min });
    if (typeof max === 'string' && (/^\d*$/.test(max))) this.setState({ max });
    if (onChange) onChange();
  };

  getValue = () => {
    const { min, max, text = '', isText } = this.state;

    return { min: ~~min, max: ~~max, text, isText: !!isText };
  };

  render() {
    const { label, unit, showTextInput = false } = this.props;
    const { min, max, text, isText } = this.state;

    return (
      <Item label={label}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ width: 276 }}>
            <input
              min={0}
              value={min}
              type='number'
              disabled={isText}
              className='ant-input'
              style={{ width: 100 }}
              onBlur={() => !min && this.setState({ min: 0 })}
              onChange={e => this.onChange({ min: e.target.value })}
            />

            &nbsp;~&nbsp;

            <input
              min={0}
              value={max}
              type='number'
              disabled={isText}
              className='ant-input'
              style={{ width: 100 }}
              onBlur={() => !max && this.setState({ max: 0 })}
              onChange={e => this.onChange({ max: e.target.value })}
            />

            &nbsp;&nbsp;

            {unit}
          </div>

          { showTextInput && (
            <Switch
              checkedChildren='←'
              unCheckedChildren='→'
              checked={!isText}
              onChange={checked => this.onChange({ isText: !checked })}
            />
          )}

          { showTextInput && (
            <div>
              Text
              &nbsp;&nbsp;
              <input
                placeholder='For example: N/A'
                disabled={!isText}
                className='ant-input'
                style={{ width: '300px' }}
                value={text}
                onChange={e => this.onChange({ text: e.target.value })}
              />
            </div>
          )}
        </div>
      </Item>
    );
  }
}
