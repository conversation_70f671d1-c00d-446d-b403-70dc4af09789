.container {
  :global {
    .b-flex {
      display: -ms-flexbox;
      display: flex;
      -ms-flex-direction: row;
      flex-direction: row;
      -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
      -ms-flex-pack: start;
      justify-content: flex-start;
      -ms-flex-line-pack: stretch;
      align-content: stretch;
      -ms-flex-align: stretch;
      align-items: stretch;
      box-sizing: border-box;
    }

    .tool-bar {
      top: 1px;
    }

    .AgenciesList ul.ant-pagination {
      float: right
    }

    .b-flex-column {
      -ms-flex-direction: column;
      flex-direction: column;
    }

    .b-flex-align-center {
      -ms-flex-align: center;
      align-items: center;
    }

    .b-flex-flex {
      -ms-flex: 1;
      flex: 1;
    }

    .b-padding-10 {
      padding: 10px;
    }

    .b-border {
      border: 1px solid #e8e8e8;
    }

    .b-padding-5 {
      padding: 5px;
    }

    .regulation-editor-editor {
      min-height: 200px;
    }

    .modal {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.7);
      z-index: 100
    }
  }

  .disabled {
    cursor: not-allowed;
    color: rgba(0, 0, 0, 0.25);
  }
}

.tableDragSorting {
  margin-bottom: 30px;

  :global {
    tr.drop-over-downward td {
      border-bottom: 2px dashed #1890ff;
    }

    tr.drop-over-upward td {
      border-top: 2px dashed #1890ff;
    }
  }
}

:global {
  .ant-calendar-picker-input {
    text-transform: uppercase;
  }
}
