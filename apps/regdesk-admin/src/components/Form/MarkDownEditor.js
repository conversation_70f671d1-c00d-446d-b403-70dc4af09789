import React from 'react';
import MarkdownIt from 'markdown-it';
import MdEditor from 'react-markdown-editor-lite';
import insert from 'markdown-it-ins';
import mila from 'markdown-it-link-attributes';
import 'react-markdown-editor-lite/lib/index.css';

const mdParser = new MarkdownIt().use(insert);

mdParser.use(mila, { attrs: { target: '_blank', rel: 'noopener' } });

/**
 * Editor
 */
export default class Editor extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value || '',
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value || '' });
  };

  getValue = () => this.state.value;

  onChange = ({ text }) => {
    const { onChange } = this.props;

    this.setState({ value: text });
    if (onChange) onChange(text);
  };

  render() {
    const { label } = this.props;
    const { value } = this.state;

    return (
      <div style={{ display: 'flex', margin: '0 0 20px' }}>
        <div
          style={{
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: '#f1f1f1',
            padding: '17px',
          }}
        >
          <div
            style={{
              whiteSpace: 'pre',
              fontWeight: 'bold',
              backgroundColor: '#f1f1f1',
              fontSize: '115%',
              padding: '0 0 8px',
            }}
          >
            {label}
          </div>

          <MdEditor
            style={{ height: '500px' }}
            renderHTML={text => mdParser.render(text)}
            onChange={this.onChange}
            value={value}
          />
        </div>
      </div>
    );
  }
}
