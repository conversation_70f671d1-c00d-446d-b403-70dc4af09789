import React from 'react';
import { Select, Spin } from 'antd';
import _ from 'lodash';
import Item from './Item';
import api from '../../utils/api';

const { Option } = Select;

/**
 * ItemDocType
 */
export default class ItemDocType extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      loading: true,
      data: [],
      dicId: props.dicId || undefined,
      dicName: props.dicName || undefined,
      type: props.type || 'product',
      compare: props.compare || undefined,
    };
  }

  componentDidMount() {
    const { module } = this.props;

    api[module === 'pharma' ? 'phrDocType' : 'mdrDocType']
      .get()
      .then(({ list }) => {
        const { dicId } = this.state;
        const item = list.find(({ _id }) => _id === dicId);

        this.setState({ data: list, type: item?.type || 'product', loading: false });
      });
  }

  componentWillReceiveProps = nextProps => {
    this.setState({
      dicId: nextProps.dicId || '',
      dicName: nextProps.dicName || '',
      type: nextProps.type || 'product',
      compare: !!nextProps.compare,
    });
  };

  getValue = () => {
    const { dicId, dicName, type, compare } = this.state;

    return { dicId, dicName, type, compare };
  };

  handleType = type => this.setState({ type, dicId: undefined, dicName: undefined, compare: undefined });

  handleSelect = dicId => {
    const { data } = this.state;
    const item = data.find(({ _id }) => _id === dicId);

    this.setState({ dicId, dicName: item.dicName, compare: item.compare });
  };

  render() {
    const { label } = this.props;
    const { type, dicId, data, loading } = this.state;

    if (loading) return <Item label={label}><Spin spinning /></Item>;

    return (
      <Item label={label}>
        <Select
          value={type}
          onChange={this.handleType}
          style={{ width: '200px' }}
        >
          <Option value='company'>Company</Option>
          <Option value='product'>Product</Option>
        </Select>
        &nbsp;&nbsp;
        <Select
          showSearch
          value={dicId}
          onChange={this.handleSelect}
          style={{ width: '200px' }}
          placeholder='Please select ID'
          filterOption={(input, option) =>
            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {_.map(_.filter(data, v => v.type === type), v => (
            <Option key={v._id} value={v._id}>
              {v.dicName}
            </Option>
          ))}
        </Select>
      </Item>
    );
  }
}
