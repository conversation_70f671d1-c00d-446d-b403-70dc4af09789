import React from 'react';
import { But<PERSON>, Descriptions, Divider, Modal, Table, Icon, Tooltip } from 'antd';
import Form from './Form';
import Document from '../Document';
import BufferModal from '../Buffer';
import actions from '../../../actions';
import styles from '../index.less';

/**
 * Changes
 */
export default class Changes extends React.Component {
  constructor(props) {
    super(props);

    this.insertIndex = undefined;

    this.state = {
      value: props.value || [],
    };
  }

  componentWillReceiveProps = (nextProps) => {
    this.setState({ value: nextProps.value || [] });
  };

  /**
   * Get name change
   * @param id
   * @returns {*}
   */
  getNameChange = (id) => {
    const { listChanges = [] } = this.props;
    const change = listChanges.find(({ _id }) => _id === id);

    return change ? change.name : id;
  }

  /**
   * Event add new change
   */
  onAdd = () => {
    this.form.showModal();
  };

  /**
   * Event remove change
   * @param item
   */
  onRemove = (item) => {
    Modal.confirm({
      title: 'Confirm',
      content: 'Are you sure want to clear it?',
      onOk: () => {
        const { handleSubmitForm } = this.props;

        this.remove(item);
        if (handleSubmitForm) handleSubmitForm();
      },
    });
  }

  /**
   * Add
   * @param items
   */
  add = (items = []) => {
    const { value } = this.state;

    value.push(...items);
    this.setState({ value });
  };

  /**
   * Set Docs from buffer
   * @param documents
   */
  setDocs = (documents) => {
    const { value } = this.state;

    if (value[this.insertIndex]) {
      value[this.insertIndex].documents = [...value[this.insertIndex].documents, ...documents];
    }

    this.setState({ value });
  }

  /**
   * Remove
   * @param index
   */
  remove = (index) => {
    const { value } = this.state;

    value.splice(index, 1);
    this.setState({ value });
  };

  /**
   * Event add new document
   * @param parentKey
   */
  onAddDoc = (parentKey) => {
    const newDoc = {
      id: undefined,
      parentKey,
      name: '',
      dicType: '',
      dicCategory: '',
      translation: false,
      fee: false,
      notary: false,
      legalize: false,
      apostille: false,
      original: false,
      highlight: false,
      copies: 0,
      eCopies: 0,
      definition: '',
      attention: [],
      sample: [],
    };

    this.formDocument.showModal(newDoc);
  };

  /**
   * Event edit doc
   * @param item
   */
  onEditDoc = (item) => this.formDocument.showModal(item);

  /**
   * Add mew doc
   * @param item
   */
  addDoc = (item) => {
    const { value } = this.state;
    const { parentKey } = item;

    value[parentKey].documents.push(item);
    value[parentKey].lastUpdated = new Date();
    this.setState({ value });
  };

  /**
   * Remove new doc
   * @param item
   */
  removeDoc = (item) => {
    const { value } = this.state;
    const { parentKey, key } = item;

    value[parentKey].documents.splice(key, 1);
    value[parentKey].lastUpdated = new Date();
    this.setState({ value });
  };

  /**
   * Update doc
   * @param item
   */
  updateDoc = (item) => {
    const { value } = this.state;
    const { parentKey, key } = item;

    value[parentKey].documents[key] = item;
    value[parentKey].lastUpdated = new Date();
    this.setState({ value });
  }

  /**
   * Get value
   * @returns {*}
   */
  getValue = () => {
    return this.state.value;
  };

  render() {
    const {
      loading,
      countryId,
      autoSave,
      itemTitle,
      buffer = [],
      usedChanges,
      accessRemove,
      listChanges = [],
      handleSubmitForm,
      decisionTreeMenu,
    } = this.props;

    const { value = [] } = this.state;
    const data = loading ? [] : value.map((v, i) => ({ ...v, key: i }));
    const itemChanges = value.map(({ changeId }) => changeId);
    const list = listChanges
      .filter(({ _id }) => !usedChanges.includes(_id) && !itemChanges.includes(_id))
      .filter(({ decisionTree }) => decisionTreeMenu ? decisionTree === true : decisionTree === false);

    data.forEach((change) => { change.name = this.getNameChange(change.changeId); });

    data.sort((a, b) => {
      if (a.name < b.name) return -1;
      if (a.name > b.name) return 1;

      return 0;
    });

    return (
      <div style={{ marginBottom: 20 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 30 }}>
          <Descriptions title='Changes' />

          <div style={{ display: 'flex' }}>
            <Button
              size='small'
              icon='plus'
              type='primary'
              disabled={loading}
              style={{ marginBottom: 20 }}
              onClick={this.onAdd}
            >
              Add change
            </Button>
          </div>
        </div>

        <Table
          rowKey='id'
          dataSource={data}
          loading={loading}
          columns={[
            {
              title: 'Name',
              dataIndex: 'name',
              width: 300,
            },
            {
              title: 'Documents',
              dataIndex: 'documents',
              render: (v = [], d) => (
                !v.length
                  ? <span style={{ color: 'rgba(0, 0, 0, 0.25)' }}>No documents</span>
                  : (
                    v.map((doc, key) => (
                      <div key={doc.id}>
                        <a onClick={() => this.onEditDoc({ ...doc, parentKey: d.key, key })}>
                          <Icon type='file' style={{ marginRight: 10 }} />{doc.name}
                        </a>
                      </div>
                    ))
                  )
              ),
            },
            {
              title: 'Actions',
              dataIndex: 'key',
              key: 'actions',
              width: 340,
              render: (v, d) => (
                <div>
                  <a onClick={() => actions.ccp.pushBuffer(`${countryId} / Impact${decisionTreeMenu ? ' for DT' : ''} / ${itemTitle} / ${d.name}`, 'ccpDocs', d.documents)}>Copy docs</a>
                  <Divider type='vertical' />
                  <a onClick={() => { this.insertIndex = v; this.modalBuffer.show(); }}>Insert docs</a>
                  <Divider type='vertical' />
                  <a onClick={() => this.onAddDoc(v)}>Add Doc</a>
                  <Divider type='vertical' />

                  <Tooltip title={!accessRemove && 'No permission'}>
                    <a className={!accessRemove ? styles.disabled : ''} onClick={() => accessRemove && this.onRemove(v)}>Delete</a>
                  </Tooltip>
                </div>
              ),
            }
          ]}
        />

        <Form
          list={list}
          onAdd={this.add}
          ref={ref => {
            this.form = ref; }}
        />

        <Document
          module='ccp'
          onAdd={this.addDoc}
          autoSave={autoSave}
          onRemove={this.removeDoc}
          onUpdate={this.updateDoc}
          accessRemove={accessRemove}
          handleSubmitForm={handleSubmitForm}
          ref={ref => { this.formDocument = ref; }}
        />

        <BufferModal
          type='ccpDocs'
          ref={ref => { this.modalBuffer = ref; }}
          onRemove={newBuffer => { actions.ccp.change({ buffer: newBuffer }); }}
          onSelect={newBuffer => { this.setDocs(newBuffer); }}
          buffer={buffer}
        />
      </div>
    );
  }
}
