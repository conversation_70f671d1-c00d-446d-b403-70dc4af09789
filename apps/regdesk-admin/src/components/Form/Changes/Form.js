import config from 'config';
import React, { PureComponent } from 'react';
import { Button, Modal, Table, Input } from 'antd';
import actions from '../../../actions';

const { Search } = Input;

/**
 * ChangeForm
 */
export default class ChangeForm extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      showModal: false,
      loading: false,
      search: '',
      selectedRows: [],
    };
  }

  /**
   * Open modal
   */
  showModal = () => this.setState({ showModal: true });

  /**
   * Close modal
   */
  onClose = () => this.setState({ showModal: false, search: '', selectedRows: [] });

  /**
   * Add changes
   */
  onAdd = () => {
    const { selectedRows } = this.state;
    const data = selectedRows.map(({ _id: changeId }) => ({ changeId, documents: [] }));

    if (this.props.onAdd) this.props.onAdd(data);

    this.onClose();
  }

  /**
   * Get Changes
   */
  getChanges = () => {
    this.setState({ loading: true });

    actions.ccpChanges
      .get()
      .then(() => this.setState({ loading: false }));
  }

  render() {
    const { item = {}, showModal, loading, search, selectedRows = [] } = this.state;
    const { list = [] } = this.props;
    const newList = list.filter(({ name }) => search.length === 0 || name.toLowerCase().includes(search.toLowerCase()) );

    newList.sort((a, b) => {
      if (a.name < b.name) return -1;
      if (a.name > b.name) return 1;

      return 0;
    });

    return (
      <Modal
        title={item.id ? 'Edit variation change' : 'Add variation change'}
        visible={showModal}
        footer={null}
        centered
        destroyOnClose
        maskClosable={false}
        onCancel={this.onClose}
        onClose={this.onClose}
        width={900}
        zIndex={999}
      >
        {showModal && (
          <div>
            <div style={{ marginBottom: 20, display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <a target='_blank' href={`${config.rootRoute}/ccp/changes`} rel='noreferrer'>
                  <Button icon='link' size='small'>Manage changes</Button>
                </a>

                <a target='_blank' href={`${config.rootRoute}/ccp/changes/dt`} rel='noreferrer'>
                  <Button style={{ marginLeft: 10 }} icon='link' size='small'>Manage changes for DT</Button>
                </a>

                <Button
                  loading={loading}
                  onClick={this.getChanges}
                  style={{ marginLeft: 10 }}
                  icon='redo'
                  size='small'
                  disabled={loading}
                >
                  Refresh table
                </Button>
              </div>

              <Search
                size='small'
                placeholder='Search by name'
                onChange={(e) => this.setState({ search: e.target.value })}
                style={{ width: 200 }}
              />
            </div>

            <Table
              rowKey='_id'
              size='small'
              loading={loading}
              rowSelection={{
                onChange: (selectedRowKeys, selected) => this.setState({ selectedRows: selected })
              }}
              columns={[
                {
                  title: 'Name',
                  dataIndex: 'name',
                },
              ]}
              dataSource={newList}
            />

            <div style={{ paddingTop: newList.length === 0 ? 20 : 0 }}>
              <Button onClick={this.onClose}>Cancel</Button>
              <Button style={{ marginLeft: 8 }} disabled={selectedRows.length === 0 || loading} type='primary' onClick={this.onAdd}>Add</Button>
            </div>
          </div>
        )}
      </Modal>
    );
  }
}
