import React from 'react';
import { But<PERSON>, Divider, Select, Spin } from 'antd';
import Item from './Item';

const { Option } = Select;

/**
 * ItemCategory
 */
export default class ItemCategory extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: props.value || 'Common',
      items: [
        'Report',
        'Common',
        'Module 3',
        'Module 5',
        'Module 2',
        'Administrative Document',
        'Module 4',
        'Module 1',
        'Technical Document',
        'Administrative Documents',
        'Certificates',
        'Declarations',
        'Forms',
        'Importation Documents',
        'Photographs',
        'Reports',
        'Samples',
        'Technical Documents',
      ],
      loading: false,
    };
  }

  componentWillReceiveProps = nextProps => {
    this.setState({ value: nextProps.value || 'Common' });
  };

  getValue = () => this.state.value;

  handleChange = value => this.setState({ value });

  addItem = inputValue => {
    const { items } = this.state;

    this.setState({ items: [inputValue, ...items] });
  };

  render() {
    const { label } = this.props;
    const { value, items = [], loading } = this.state;

    if (loading) return <Item label={label}><Spin spinning /></Item>;

    return (
      <Item label={label}>
        <Select
          value={value}
          onChange={this.handleChange}
          style={{ width: 260 }}
          placeholder='custom dropdown render'
          showSearch
          dropdownRender={(menu, { inputValue }) => (
            <>
              {inputValue !== '' && !items.includes(inputValue) ? (
                <>
                  <Button
                    onClick={() => this.addItem(inputValue)}
                    onMouseDown={e => e.preventDefault()}
                    icon='plus'
                    type='link'
                  >
                    Add the entered value to the list
                  </Button>

                  <Divider style={{ margin: '4px 0' }} />
                </>
              ) : (
                inputValue === '' && (
                  <>
                    <div style={{ padding: '5px 20px' }}>
                      Enter value for search or adding to the list
                    </div>

                    <Divider style={{ margin: '4px 0' }} />
                  </>
                )
              )}

              {menu}
            </>
          )}
        >
          {items.map(item => (
            <Option key={item} value={item}>
              {item}
            </Option>
          ))}
        </Select>
      </Item>
    );
  }
}
