import React, { PureComponent } from 'react';
import { Table, Modal, Spin, Divider } from 'antd';
import JSONInput from 'react-json-editor-ajrm';
import { JsonDiffComponent } from 'json-diff-react';
import api from '../../utils/api';

/**
 * Versions
 */
export default class Versions extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      showModal: false,
      list: [],
      loading: false,
      loadingItem: false,
      loadingDiff: false,
      pagination: {},
      showModalItem: false,
      showModalDiff: false,
      item: {},
      prevItem: {},
    };
  }

  show = () => {
    this.setState({ showModal: true });
    this.load();
  };

  onCloseModal = () => this.setState({ showModal: false });

  onCloseModalItem = () => this.setState({ showModalItem: false, item: {} });

  onCloseModalDiff = () => this.setState({ showModalDiff: false, item: {}, prevItem: {} });

  load = (props = {}) => {
    const { countryId, collectionName } = this.props;
    const { pagination } = this.state;

    if (countryId && collectionName) {
      this.setState({ loading: true });

      api.versions
        .get({ pagination, ...props, countryId, collectionName })
        .then(data => this.setState({ ...data, loading: false }))
        .catch(() => this.setState({ loading: false }));
    }
  };

  /**
   * Event select buffer
   * @param id
   */
  onSelect = id => {
    const { onSelect } = this.props;

    if (id) {
      this.setState({ loadingItem: true });

      api.versions
        .getById(id)
        .then(data => {
          if (onSelect) onSelect(data?.item?.object);
          this.onCloseModal();
        })
        .catch(() => this.setState({ loadingItem: false }));
    }
  };

  /**
  * Event show JSON
  * @param id
  */
  onOpenJSON = id => {
    if (id) {
      this.setState({ loadingItem: true, showModalItem: true });

      api.versions
        .getById(id)
        .then(data => this.setState({ item: data.item, loadingItem: false }))
        .catch(() => this.setState({ loadingItem: false }));
    }
  };

  /**
  * Event show diff JSON
  * @param id
  */
  onOpenDiffJSON = id => {
    const { countryId, collectionName } = this.props;

    if (id) {
      this.setState({ loadingDiff: true, showModalDiff: true });

      api.versions
        .getDiff({ id, countryId, collectionName })
        .then(({ items }) => this.setState({
          item: items[0]?.object || {},
          prevItem: items[1]?.object || {},
          loadingDiff: false
        }))
        .catch(() => this.setState({ loadingDiff: false }));
    }
  };

  /**
   * Handle table change
   * @param pagination
   */
  onTableChange = pagination => this.load({ pagination });

  render() {
    const {
      showModal,
      loading,
      loadingItem,
      loadingDiff,
      list = [],
      pagination,
      item = {},
      prevItem = {},
      showModalItem,
      showModalDiff,
    } = this.state;
    const { _id: itemId } = item;

    const columns = [
      {
        title: 'Date',
        dataIndex: 'createdAt',
        render: date => new Date(date).getFullUTC(),
      },
      {
        title: 'User',
        dataIndex: 'userName',
      },
      {
        title: 'Action',
        dataIndex: '_id',
        width: 200,
        render: id => (
          <div>
            <a onClick={() => this.onOpenJSON(id)}>Object</a>
            <Divider type='vertical' />
            <a onClick={() => this.onOpenDiffJSON(id)}>Diff</a>
            <Divider type='vertical' />
            <a onClick={() => this.onSelect(id)}>Restore</a>
          </div>
        )
      },
    ];

    const Loader = () => (
      <div style={{ minHeight: 200, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Spin />
      </div>
    );

    return (
      <div>
        <Modal
          title='Versions history'
          visible={showModal}
          footer={null}
          onCancel={this.onCloseModal}
          onClose={this.onCloseModal}
          width={800}
          zIndex={1001}
        >
          {showModal && (
            <Table
              bordered
              rowKey='_id'
              columns={columns}
              dataSource={list}
              loading={loading}
              pagination={{
                defaultPageSize: 10,
                size: 'Pagination',
                ...pagination,
              }}
              onChange={this.onTableChange}
            />
          )}
        </Modal>

        {showModalItem && (
          <Modal
            title='Version history'
            visible={showModalItem}
            footer={null}
            onCancel={this.onCloseModalItem}
            onClose={this.onCloseModalItem}
            width={900}
            zIndex={1002}
          >
            {loadingItem
              ? <Loader />
              : (
                <JSONInput
                  id={itemId}
                  placeholder={item}
                  height='500px'
                  width='100%'
                  viewOnly
                />
              )}
          </Modal>
        )}

        {showModalDiff && (
          <Modal
            title='Version history'
            visible={showModalDiff}
            footer={null}
            onCancel={this.onCloseModalDiff}
            onClose={this.onCloseModalDiff}
            width={900}
            zIndex={1002}
          >
            {loadingDiff
              ? <Loader />
              : (
                <div style={{ maxHeight: 500, overflow: 'auto' }}>
                  <JsonDiffComponent
                    jsonA={prevItem}
                    jsonB={item}
                    styleCustomization={{
                      additionLineStyle: { color: 'green' },
                      deletionLineStyle: { color: 'red' },
                      unchangedLineStyle: { color: 'gray' },
                      frameStyle: { fontFamily: 'monospace', whiteSpace: 'pre-wrap' },
                    }}
                    jsonDiffOptions={{ maxElisions: 3 }}
                  />
                </div>
              )}
          </Modal>
        )}
      </div>
    );
  }
}
