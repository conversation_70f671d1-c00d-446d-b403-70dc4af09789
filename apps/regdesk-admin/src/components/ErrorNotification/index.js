import React from 'react';
import { notification, Typography } from 'antd';
import birdIcon from './birdIcon';

import styles from './style.less';

const { Text } = Typography;

const ErrorNotification = (message) => {
  notification.error({
    message: (
      <div className={styles.message}>
        <Text>{message}</Text>
      </div>
    ),
    icon: birdIcon,
    className: styles.errorNotification,
    style: {
      padding: '10px 16px 10px 16px',
      borderStyle: 'solid',
      borderWidth: 1,
      borderColor: '#f5222d',
      minHeight: 74,
    },
  });

  return null;
};

export default ErrorNotification;