import React from 'react';
import { Breadcrumb } from 'antd';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

export default function BreadcrumbHeader({ routes, ...props }) {
  const { t } = useTranslation();

  return (
    <Breadcrumb separator='>' style={{ padding: '10px 0 16px' }} {...props}>
      {routes.map(({ href, title }, index) => (
        <Breadcrumb.Item key={title || index}>
          { href ? <Link to={href}>{t(title)}</Link> : `${t(title)}` }
        </Breadcrumb.Item>
      ))}
    </Breadcrumb>
  );
}

