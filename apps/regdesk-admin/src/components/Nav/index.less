@pro-header-hover-bg: rgba(0, 0, 0, 0.025);
@pro-header-hover-bg: rgba(128, 128, 128, 0.15);
@text-color: gray;
@primary-color: gray;

.nav {
  background: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
  z-index: 2;
  padding-left: 30px;
  padding-right: 30px;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);

  .menu {
    border-bottom-width: 0;
    line-height: 63px;
    margin-right: 30px;

    @media (max-width: 1280px) {
      margin-right: 0;
    }

    max-width: 90%;
  }
  
  .nameContainer {
    height: 100%;
    margin-left: 10px;
    white-space: nowrap;
    display: flex;
    flex-direction: column;
  }

  .logo {
    height: 40px;
    width: 132px;
    background-image: url('../../assets/logo.png');
    background-size: contain;
    background-repeat: no-repeat;
  }

  .name {
    margin-left: 10px;
    line-height: 1;
    @media (max-width: 1280px) {
      max-width: 65%;
    }
  }

  .action {
    cursor: pointer;
    padding: 0 12px;
    transition: all 0.3s;
    height: 100%;
    display: flex;
    align-items: center;
    max-width: 400px;

    .ant-badge {
      margin: auto;
    }

    > i {
      vertical-align: middle;
      color: @text-color;
    }

    &:hover {
      background: @pro-header-hover-bg;
    }

    &:global(.opened) {
      background: @pro-header-hover-bg;
    }
  }

  .timer {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: flex-end;
    line-height: initial;
    height: 64px;
    padding: 15px 0;
    margin-right: 10px;

    .title {
      font-size: 11px;
      color: rgba(0, 0, 0, 0.45);
    }
  }
}
