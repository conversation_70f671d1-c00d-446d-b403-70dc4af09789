import React, { Component } from 'react';
import { <PERSON>, withRout<PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import config from 'config';
import { Layout, Dropdown, Menu, Icon, Avatar, Tooltip, Modal, Tag } from 'antd';
import TranslationOption from '../Translation';
import actions from '../../actions';
import events from '../../utils/events';
import styles from './index.less';

@withRouter
@connect(({ account }) => ({
  su: account.su,
  name: account.name,
  avatar: account.avatar,
  adminPermissions: account.adminPermissions,
  userPermissions: account.permission,
  clientName: account.clientName,
}))

class Nav extends Component {
  constructor(props) {
    super(props);

    this.timerForDate = null;

    this.state = {
      date: new Date(),
    };

    const { su, adminPermissions } = props;

    this.menu = [
      { route: `${config.rootRoute}/users`, name: 'Users', icon: 'team', permission: 'users' },
      { route: `${config.rootRoute}/devicereg`, name: '<PERSON><PERSON>', icon: 'global', permission: 'regulations' },
      { route: `${config.rootRoute}/pharmareg`, name: 'Pharma Regs', icon: 'global', permission: 'pharmaRegulations' },
      { route: `${config.rootRoute}/ccp`, name: 'CCP', icon: 'eye', permission: 'control' },
      { route: `${config.rootRoute}/alerts`, name: 'Alerts', icon: 'notification', permission: 'dataAlerts' },
      { route: `${config.rootRoute}/pharmaAlerts`, name: 'PharmaAlerts', icon: 'notification', permission: 'pharmaAlerts' },
      { route: `${config.rootRoute}/notifications`, name: 'NotificationCenter', icon: 'sound', permission: 'updates' },
      { route: `${config.rootRoute}/standards`, name: 'Standards', icon: 'profile', permission: 'standards' },
      { route: `${config.rootRoute}/wizards`, name: 'Wizards', icon: 'star', permission: 'wizards' },
      { route: `${config.rootRoute}/appUsage`, name: 'Application Usage', icon: 'notification', permission: 'appUsage' },
      { route: `${config.rootRoute}/forms`, name: 'Forms', icon: 'star', permission: 'forms' },
      { route: `${config.rootRoute}/regplan`, name: 'RegPlan', icon: 'reconciliation', permission: 'regPlan' },
      { route: `${config.rootRoute}/autofillhistory`, name: 'AutofillHistory', icon: 'branches', permission: 'autofillHistory' },
      { route: `${config.rootRoute}/cron`, name: 'Sync', icon: 'cloud-sync', permission: 'cron' },
      { route: `${config.rootRoute}/grafanaDashboards`, name: 'Grafana Dashboard', icon: 'cloud-sync', permission: 'grafanaDashboard' },
      { route: `${config.rootRoute}/dbMode`, name: 'DB Mode', icon: 'container', permission: 'dbMode' },
      { route: `${config.rootRoute}/logs`, name: 'Logs', icon: 'code', permission: 'logs' },
    ].filter(({ permission }) => (su && permission === 'users') || (adminPermissions && adminPermissions.includes(permission)));

    this.reg = new RegExp(`${config.rootRoute.replace(/[-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, '\\$&')}\/+`, 'i');
  }

  componentDidMount() {
    this.timerForDate = setInterval(() => this.setState({ date: new Date() }), 1000);
  }

  componentDidUpdate(prevProps) {
    if (this.props.location.pathname !== prevProps.location.pathname) this.forceUpdate();
  }

  componentWillUnmount() {
    clearInterval(this.timerForDate);
  }

  onLogout = () => {
    events.emit('checkRegForm');

    Modal.confirm({
      title: 'Warning',
      content: 'Please make sure you save the data',
      autoFocusButton: null,
      icon: <Icon type='info-circle' color='#faad14' />,
      okText: 'Logout',
      okType: 'danger',
      onOk: () => actions.auth.logout(),
    });
  };

  render() {
    const { language } = this.props.i18n;
    const { t, name, avatar, su, adminPermissions, userPermissions, clientName } = this.props;
    const { date } = this.state;

    return (
      <Layout.Header className={styles.nav}>
        <Link to={`${config.rootRoute}/`} className={styles.logo}>
          <div />
        </Link>

        <div style={{ flex: 1 }} />

        <Menu
          mode='horizontal'
          className={styles.menu}
          selectedKeys={window.location.pathname.replace(this.reg, '').split('/')}
        >
          {this.menu.map(({ route, name: moduleName, icon }) => (
            <Menu.Item key={route.replace(this.reg, '')}>
              <Link to={route} replace={route === window.location.pathname}>
                <Icon type={icon} style={{ marginRight: 8 }} />
                <span>{t(moduleName)}</span>
              </Link>
            </Menu.Item>
          ))}
        </Menu>

        <div className={styles.timer}>
          <div className={styles.title}>Server time</div>
          <div>{date.getFullUTC()}</div>
        </div>

        {adminPermissions.includes('dbMode') && clientName && (
          <div style={{ marginLeft: 10 }}>
            <Tag color='orange'>{clientName}</Tag>
          </div>
        )}

        {/* dropdown for changing languages */}

        <Dropdown
          trigger={['click']}
          overlay={
            <Menu>
              <Menu.Item key='cn' disabled>
                <TranslationOption language='cn' />
              </Menu.Item>

              <Menu.Item key='de'>
                <TranslationOption language='de' />
              </Menu.Item>

              <Menu.Item key='es' disabled>
                <TranslationOption language='es' />
              </Menu.Item>

              <Menu.Item key='jp' disabled>
                <TranslationOption language='jp' />
              </Menu.Item>

              <Menu.Item key='pt' disabled>
                <TranslationOption language='pt' />
              </Menu.Item>

              <Menu.Item key='fr' disabled>
                <TranslationOption language='fr' />
              </Menu.Item>

              <Menu.Item key='kr' disabled>
                <TranslationOption language='kr' />
              </Menu.Item>

              <Menu.Item key='ru'>
                <TranslationOption language='ru' />
              </Menu.Item>

              <Menu.Item key='en'>
                <TranslationOption language='en' />
              </Menu.Item>
            </Menu>
          }
        >
          <div className={styles.action}>
            <Icon type='global' />

            <div className={styles.nameContainer}>
              <span>{language?.toUpperCase()}</span>
            </div>
          </div>
        </Dropdown>

        <Dropdown
          overlay={(
            <Menu>
              <Menu.Item onClick={() => actions.auth.openClient()}>
                <Icon type='home' style={{ marginRight: 5 }} />
                <span>Go to Portal</span>
              </Menu.Item>

              <Menu.Item>
                <Link to={`${config.rootRoute}/profile/info`}>
                  <Icon type='user' style={{ marginRight: 5 }} />
                  <span>Profile</span>
                </Link>
              </Menu.Item>

              <Menu.Item>
                <Link to={`${config.rootRoute}/guide`}>
                  <Icon type='question-circle' style={{ marginRight: 5 }} />
                  <span>Guide</span>
                </Link>
              </Menu.Item>

              {
                userPermissions.includes('apiDocs') && (
                  <Menu.Item>
                    <a href={`${config.apiServer}/api/docs`} target='_blank' rel='noreferrer'>
                      <Icon type='code' style={{ marginRight: 5 }} />
                      <span>API Docs</span>
                    </a>
                  </Menu.Item>
                )
              }

              <Menu.Item onClick={() => this.onLogout()}>
                <Icon type='logout' style={{ marginRight: 5 }} />
                <span>Logout</span>
              </Menu.Item>
            </Menu>
          )}
        >
          <div className={styles.action}>
            <Avatar
              icon='user'
              size='small'
              src={avatar && `${config.apiServer}/api/doc/${avatar}`}
              alt='avatar'
            />

            <span className={styles.name}>{name || ''}</span>
          </div>
        </Dropdown>

        {this.props.su && (
          <Tooltip title='You have access SU' placement='bottomRight'>
            <Icon type='info-circle-o' style={{ marginLeft: 10 }} />
          </Tooltip>
        )}
      </Layout.Header>
    );
  }
}

export default withTranslation()(Nav);
