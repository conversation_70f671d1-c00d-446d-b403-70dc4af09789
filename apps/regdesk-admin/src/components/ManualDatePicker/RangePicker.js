import React, { Component } from 'react';
import moment from 'moment-timezone';
import { DatePicker } from 'antd';
import { FULL_DATE_FORMAT } from '../../utils/date';

const { RangePicker: AntRangePicker } = DatePicker;

class RangePicker extends Component {
  rangePickerRef = React.createRef();

  startInput = null;

  endInput = null;

  eventsAttached = false;

  state = { isOpen: false };

  componentDidMount() {
    this.attachInputEvents();
  }

  componentWillUnmount() {
    this.detachInputEvents();
  }

  attachInputEvents = () => {
    setTimeout(() => {
      if (this.eventsAttached) return;
      
      const holder = this.rangePickerRef.current?.picker?.holder;

      if (!holder) return;

      const inputs = holder.querySelectorAll('input');

      if (inputs.length !== 2) return;

      [this.startInput, this.endInput] = inputs;
      this.startInput.addEventListener('blur', this.handleStartBlur);
      this.startInput.addEventListener('keydown', this.handleStartKeyDown);
      this.endInput.addEventListener('blur', this.handleEndBlur);
      this.endInput.addEventListener('keydown', this.handleEndKeyDown);
      
      this.eventsAttached = true;
    }, 0);
  };

  detachInputEvents = () => {
    if (this.startInput) {
      this.startInput.removeEventListener('blur', this.handleStartBlur);
      this.startInput.removeEventListener('keydown', this.handleStartKeyDown);
    }

    if (this.endInput) {
      this.endInput.removeEventListener('blur', this.handleEndBlur);
      this.endInput.removeEventListener('keydown', this.handleEndKeyDown);
    }
    
    this.eventsAttached = false;
  };

  handleStartBlur = (e) => this.validateAndSetDate(e.target.value, 0, 'blur');

  handleEndBlur = (e) => this.validateAndSetDate(e.target.value, 1, 'blur');
  
  handleStartKeyDown = (e) => {
    if (e.key === 'Enter') this.validateAndSetDate(e.target.value, 0, 'enter');
  };
  
  handleEndKeyDown = (e) => {
    if (e.key === 'Enter') this.validateAndSetDate(e.target.value, 1, 'enter');
  };

  handleOpenChange = open => {
    this.setState({ isOpen: open });
  };

  validateAndSetDate = (valueStr, index, eventType) => {
    const { value = [null, null], onChange } = this.props;
    
    if (!valueStr) return;
    
    const parsed = moment.utc(valueStr, FULL_DATE_FORMAT, true);
    const newValue = [...value];
    
    if (parsed.isValid()) {
      newValue[index] = parsed.startOf('day');
    } else if (eventType === 'enter') {
      newValue[index] = moment.utc().startOf('day');
    } else {
      return;
    }

    if (onChange) {
      onChange(newValue);
    }
  };

  render() {
    const { value, placeholder, defaultValue, ...rest } = this.props;
    const { isOpen } = this.state;

    const defaultPlaceholders = [FULL_DATE_FORMAT, FULL_DATE_FORMAT];
    const placeholders = Array.isArray(placeholder)
      ? placeholder
      : [placeholder || 'Start date', placeholder || 'End date'];

    const momentDefaultValue = defaultValue || [null, null];

    return (
      <AntRangePicker
        ref={this.rangePickerRef}
        dropdownClassName='manual-ant-calendar-picker-container'
        format={FULL_DATE_FORMAT}
        defaultValue={momentDefaultValue}
        placeholder={isOpen ? defaultPlaceholders : placeholders}
        onOpenChange={this.handleOpenChange}
        {...rest}
      />
    );
  }
}

export default RangePicker;