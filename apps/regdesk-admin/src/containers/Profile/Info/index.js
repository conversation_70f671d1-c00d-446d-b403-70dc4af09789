import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Form, Input, Button, Col, Row, Avatar, Spin } from 'antd';
import config from 'config';
import Upload from '../../../components/Upload';
import actions from '../../../actions';
import styles from '../index.less';

const FormItem = Form.Item;

@connect(({ account, doc }) => ({
  name: account.name,
  email: account.email,
  company: account.company,
  phone: account.phone,
  avatar: account.avatar,
  loading: account.loading,
  loadingDoc: doc.loading,
}))
@Form.create()

export default class Info extends PureComponent {
  handleSubmit = e => {
    e.preventDefault();
    this.update();
  };

  /**
   * Update profile
   * @param avatar
   */
  update = ({ avatar } = {}) => {
    this.props.form.validateFieldsAndScroll((err, values) => {
      if (!err) {
        let data = values;

        if (avatar) data = { ...values, avatar };

        actions.profile.update(data);
      }
    });
  };

  render() {
    const {
      name,
      email,
      avatar,
      phone,
      company,
      form: { getFieldDecorator }
    } = this.props;

    const loading = this.props.loading || this.props.loadingDoc;

    return (
      <div className={styles.container}>
        <div className={styles.title}>Info</div>

        <Row gutter={24} type='flex'>
          <Col xl={12} lg={12} md={12} sm={24} xs={24}>
            <Form onSubmit={this.handleSubmit}>
              <FormItem label='Name'>
                {getFieldDecorator('name', {
                  rules: [{
                    required: true, message: 'Please! Enter a name'
                  }],
                  initialValue: name || '',
                })(
                  <Input
                    placeholder='Name...'
                    disabled={loading}
                    autoComplete='off'
                  />
                )}
              </FormItem>

              <FormItem label='Email'>
                {getFieldDecorator('email', {
                  rules: [
                    { required: true, message: 'Please! Enter a email' },
                  ],
                  initialValue: email || '',
                })(
                  <Input
                    disabled
                    placeholder='Email...'
                    autoComplete='off'
                  />
                )}
              </FormItem>

              <FormItem label='Phone'>
                {getFieldDecorator('phone', {
                  rules: [
                    { pattern: /^\+\d{12}$/, message: '+zzzyyxxxxxxx international phone numbers are 12 digits long' },
                  ],
                  initialValue: phone || '',
                })(
                  <Input
                    placeholder='+zzzyyxxxxxxx'
                    disabled={loading}
                    autoComplete='off'
                  />
                )}
              </FormItem>

              <FormItem label='Company'>
                {getFieldDecorator('company', {
                  initialValue: company || '',
                })(
                  <Input
                    placeholder='Company name...'
                    disabled={loading}
                    autoComplete='off'
                  />
                )}
              </FormItem>

              <FormItem style={{ marginBottom: 0 }}>
                <Button
                  icon='save'
                  type='primary'
                  htmlType='submit'
                  disabled={loading}
                  loading={loading}
                >
                  Update
                </Button>
              </FormItem>
            </Form>
          </Col>

          <div className={styles.avatar}>
            <Spin spinning={loading}>
              <Avatar
                size={140}
                icon='user'
                src={avatar && `${config.apiServer}/api/doc/${avatar}`}
                alt='avatar'
                style={{ marginBottom: 20 }}
              />
            </Spin>

            <Upload
              fileType='image'
              disabled={loading}
              listType='text'
              showUploadList={false}
              shouldResetList
              data={{ avatar: true }}
              onChange={([{ _id: avatarId }]) => this.update({ avatar: avatarId })}
            >
              <Button
                icon='upload'
                disabled={loading}
                loading={loading}
              >
                Change avatar
              </Button>
            </Upload>
          </div>
        </Row>
      </div>
    );
  }
}
