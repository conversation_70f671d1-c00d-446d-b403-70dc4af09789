import React, { PureComponent } from 'react';
import { <PERSON>, Col, Row, Menu, Icon } from 'antd';
import { <PERSON>, withR<PERSON><PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import config from 'config';
import Breadcrumb from '../../components/Breadcrumb';
import Info from './Info';
import ChangePassword from './ChangePassword';
import Destroy from './Destroy';
import styles from './index.less';

@withRouter
@connect(({ account }) => ({
  role: account.role,
}))

export default class Profile extends PureComponent {
  render() {
    const menuItem = this.props.match.params.item;
    const { role } = this.props;

    return (
      <div>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Profile' }
          ]}
        />

        <Card bodyStyle={{ paddingLeft: 0 }}>
          <Row gutter={24} type='flex'>
            <Col xl={6} lg={6} md={6} sm={24} xs={24}>
              <Menu
                onClick={() => {}}
                selectedKeys={[menuItem]}
                mode='inline'
                style={{ height: '100%' }}
              >
                <Menu.Item key='info'>
                  <Link to={`${config.rootRoute}/profile/info`}><Icon type='user' className={styles.menuIcon} />Info</Link>
                </Menu.Item>

                <Menu.Item key='password'>
                  <Link to={`${config.rootRoute}/profile/password`}><Icon type='lock' className={styles.menuIcon} />Change password</Link>
                </Menu.Item>

                {role !== 'sub-client' && (
                  <Menu.Item key='destroy'>
                    <Link to={`${config.rootRoute}/profile/destroy`}>
                      <Icon type='delete' className={styles.menuIcon} />Delete account
                    </Link>
                  </Menu.Item>
                )}
              </Menu>
            </Col>

            <Col xl={18} lg={18} md={18} sm={24} xs={24}>
              {menuItem === 'info' && <Info />}
              {menuItem === 'password' && <ChangePassword />}
              {menuItem === 'destroy' && role !== 'sub-client' && <Destroy />}
            </Col>
          </Row>
        </Card>
      </div>
    );
  }
}
