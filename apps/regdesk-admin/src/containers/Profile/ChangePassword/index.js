import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Form, Input, Button, Icon, Tooltip, Col, Row } from 'antd';

import actions from '../../../actions';
import { totalPasswordRules, generatePassword } from '../../../utils/helpers';
import styles from '../index.less';

const FormItem = Form.Item;

@connect(({ account: { loading } }) => ({ loading }))
@Form.create()

export default class ChangePassword extends PureComponent {
  handleSubmit = e => {
    e.preventDefault();

    const { form, loading } = this.props;

    form.validateFieldsAndScroll((err, values) => {
      if (!err && ! loading) {
        actions.profile
          .changePassword(values)
          .then(() => form.setFieldsValue({ password: '', oldPassword: '' }));
      }
    });
  };

  generatePassword = () => {
    const { form } = this.props;

    form.setFieldsValue({ password: generatePassword() });
  }

  render() {
    const { loading, form } = this.props;
    const { getFieldDecorator } = form;

    return (
      <div className={styles.container}>
        <div className={styles.title}>Change password</div>

        <Row gutter={24} type='flex'>
          <Col xl={12} lg={12} md={12} sm={24} xs={24}>
            <Form onSubmit={this.handleSubmit}>
              <FormItem label='Current password'>
                {getFieldDecorator('oldPassword', totalPasswordRules())(
                  <Input
                    disabled={loading}
                    placeholder='Password...'
                    autoComplete='off'
                    autoFocus
                    type='password'
                  />
                )}
              </FormItem>

              <FormItem label='New Password'>
                {getFieldDecorator('password', totalPasswordRules('newPassword'))(
                  <Input
                    disabled={loading}
                    placeholder='Password...'
                    autoComplete='off'
                    addonAfter={
                      <Tooltip title='Generate password'>
                        <Icon style={{ fontSize: '18px' }} onClick={this.generatePassword} type='key' />
                      </Tooltip>
                    }
                  />
                )}
              </FormItem>

              <Button
                icon='save'
                type='primary'
                htmlType='submit'
                disabled={loading}
                loading={loading}
              >
                Update
              </Button>
            </Form>
          </Col>
        </Row>
      </div>
    );
  }
}
