import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Form, Input, Button, Row, Col } from 'antd';
import actions from '../../../actions';
import styles from '../index.less';

const FormItem = Form.Item;

@connect(({ account }) => ({
  loading: account.loading,
}))
@Form.create()

export default class Destroy extends PureComponent {
  handleSubmit = e => {
    e.preventDefault();

    const { form, loading } = this.props;

    form.validateFieldsAndScroll((err, values) => {
      if (!err && !loading) actions.profile.destroy(values);
    });
  };

  render() {
    const { loading, form } = this.props;
    const { getFieldDecorator } = form;

    return (
      <div className={styles.container}>
        <div className={styles.title}>Delete account</div>

        <Row gutter={24} type='flex'>
          <Col xl={12} lg={12} md={12} sm={24} xs={24}>
            <Form onSubmit={this.handleSubmit}>
              <FormItem
                label='Current password'
                extra='All your content will be delete and its not recoverable.'
              >
                {getFieldDecorator('password', {
                  rules: [
                    {
                      required: true,
                      message: 'Please! Enter current password.',
                    },
                  ],
                })(<Input autoFocus placeholder='Password...' disabled={loading} type='password' autoComplete='off' />)}
              </FormItem>

              <Button
                icon='delete'
                type='primary'
                htmlType='submit'
                disabled={loading}
                loading={loading}
              >
                Delete
              </Button>
            </Form>
          </Col>
        </Row>
      </div>
    );
  }
}
