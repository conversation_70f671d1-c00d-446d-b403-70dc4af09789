import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Button, Table, Select, Row, Col, Card, message, Tag, Switch, Input, Icon, Popconfirm, Menu, Dropdown, Divider, Avatar } from 'antd';
import { Link } from 'react-router-dom';
import config from 'config';
import moment from 'moment';
import * as constants from '../../const';
import Breadcrumb from '../../components/Breadcrumb';
import Assign from './Assign';
import actions from '../../actions';

@connect(({ account, users }) => ({
  _id: account._id,
  su: account.su,
  list: users.list,
  loading: users.loading,
  pagination: users.pagination,
  filters: users.filters,
  sorter: users.sorter,
  view: users.view,
  select: users.select,
}))

export default class Users extends PureComponent {
  constructor(props) {
    super(props);

    this.timerName = null;
    this.timerEmail = null;

    this.menuColumns = [
      { key: 'avatar', name: '<PERSON><PERSON>' },
      { key: 'name', name: 'Name', disabled: true },
      { key: 'email', name: '<PERSON><PERSON>' },
      { key: 'is2FAEnable', name: '2FA' },
      { key: 'phone', name: 'Phone' },
      { key: 'company', name: 'Company' },
      { key: 'expiryDate', name: 'Expiry Date' },
      { key: 'role', name: 'Role' },
      { key: 'lastLogin', name: 'Last Login' },
      { key: 'actions', name: 'Actions', disabled: true },
    ];
  }

  componentDidMount() {
    const userStore = localStorage.getItem('users');
    const {
      view = {},
      select = { avatar: true, email: true, role: true },
      pageSize = 20,
    } = userStore ? JSON.parse(userStore) : {};

    this.load({ view, select, pagination: { pageSize } });
  }

  /**
   * Get columns for table
   * @returns {*[]}
   */
  getColumns = () => {
    const { select, view, sorter } = this.props;
    const {
      email,
      role,
      phone,
      avatar,
      company,
      expiryDate,
      lastLogin,
      is2FAEnable,
    } = select || {};

    const { groupSubUsers, groupActions } = view || {};

    const columns = [];

    if (avatar) {
      columns.push({
        title: '',
        dataIndex: 'avatar',
        width: groupSubUsers ? 'auto' : 32,
        render: avatarId => (
          <Avatar
            icon='user'
            src={avatarId && `${config.apiServer}/api/doc/${avatarId}`}
            alt='avatar'
            style={{ margin: '-5px 0' }}
          />
        )
      });
    }

    columns.push({
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      sortOrder: sorter && sorter.columnKey === 'name' && sorter.order,
      render: (name, val) => {
        const { _id: userId } = val;
        const noSubUser = val.role !== 'sub-client';

        return <Link to={noSubUser ?`${config.rootRoute}/users/edit/${userId}` : '#'}>{name}</Link>;
      }
    });

    if (email) {
      columns.push({
        title: 'Email',
        dataIndex: 'email',
        sorter: true,
        sortOrder: sorter && sorter.columnKey === 'email' && sorter.order,
      });
    }

    if (phone) {
      columns.push({
        title: 'Phone',
        dataIndex: 'contact.phone',
        sorter: true,
        sortOrder: sorter && sorter.columnKey === 'contact.phone' && sorter.order,
        render: val => val || ''
      });
    }

    if (company) {
      columns.push({
        title: 'Company',
        dataIndex: 'contact.company',
        sorter: true,
        sortOrder: sorter && sorter.columnKey === 'contact.company' && sorter.order,
        render: val => val || ''
      });
    }

    if (expiryDate) {
      columns.push({
        title: 'Expiry days',
        dataIndex: 'expiryDate',
        sorter: true,
        sortOrder: sorter && sorter.columnKey === 'expiryDate' && sorter.order,
        render: date => {
          const oneDay = 24*60*60*1000;
          const today = new Date();

          if (!date) return <Tag color='#f50'>No data</Tag>;

          const dateEnd = new Date(date);

          if (today > dateEnd) return <Tag color='#f50'>0</Tag>;

          return <Tag>{Math.round((dateEnd.getTime() - today.getTime()) / oneDay)}</Tag>;
        }
      });
    }

    if (role) {
      columns.push({
        title: 'Role',
        dataIndex: 'role',
        sorter: true,
        sortOrder: sorter && sorter.columnKey === 'role' && sorter.order,
        render: (roleUser, val) => {
          switch (roleUser) {
            case 'admin':
              return <Tag color='#87d068'>Admin</Tag>;

            case 'client':
              return <Tag color='#108ee9'>User</Tag>;

            case 'sub-client':
              return (
                <div>
                  <Tag color='#d9b762' style={{ marginRight: 5 }}>Sub User</Tag>
                  {val.readOnly && <Tag color='green'>Read</Tag>}
                </div>
              );

            default:
              return <Tag color='#f50'>No role</Tag>;
          }
        }
      });
    }

    if (is2FAEnable) {
      columns.push({
        title: '2FA',
        dataIndex: 'is2FAEnable',
        sorter: true,
        sortOrder: sorter && sorter.columnKey === 'is2FAEnable' && sorter.order,
        render: (val) => val === true ? <Tag color='#87d068'>Yes</Tag> :<Tag>No</Tag>
      });
    }

    if (lastLogin) {
      columns.push({
        title: 'Last login',
        dataIndex: 'lastLogin',
        sorter: true,
        sortOrder: sorter && sorter.columnKey === 'lastLogin' && sorter.order,
        render: val => {
          const { timestamp } = val || {};

          return timestamp ? moment(timestamp).getFullUTC() : <Tag color='#f50'>No data</Tag>;
        }
      });
    }

    columns.push({
      title: 'Actions',
      dataIndex: '_id',
      render: (userId, val) => {
        const { su, _id } = this.props;
        const noSubUser = val.role !== 'sub-client';
        const currentUser = _id === userId;
        const actionsItem = {
          login: <a onClick={() => this.loginAs(val.email)}>Login As</a>,
          password: <Link to={`${config.rootRoute}/users/change/password/${userId}`}>Change password</Link>,
          edit: <Link to={`${config.rootRoute}/users/edit/${userId}`}>Edit</Link>,
          remove: (
            !noSubUser
              ? (
                <a
                  href='#!'
                  className='ant-dropdown-link'
                  onClick={(e) => {
                    e.preventDefault();
                    actions.team.change({ showModal: 'deleteSubUser', idu: userId });
                  }}
                >
                  Delete
                </a>
              )
              : (
                <Popconfirm
                  title='Remove user ?'
                  onConfirm={() => this.remove(userId)}
                  onCancel={() => {}}
                  okText='Yes'
                  cancelText='No'
                >
                  <a className='ant-dropdown-link'>Delete</a>
                </Popconfirm>
              )
          ),
          forceChangeSubUsersPassword: (
            noSubUser && currentUser && (
              <Popconfirm
                title='Force all sub-users to change password?'
                onConfirm={() => this.forceChangePassword()}
                onCancel={() => {}}
                okText='Yes'
                cancelText='No'
              >
                <a className='ant-dropdown-link' style={{ whiteSpace: 'nowrap' }}>Force password change</a>
              </Popconfirm>
            )
          ),
        };

        if (groupActions) {
          const menu = (
            <Menu>
              {su && <Menu.Item>{actionsItem.login}</Menu.Item>}
              {su && <Menu.Item>{actionsItem.password}</Menu.Item>}
              {noSubUser && <Menu.Item>{actionsItem.edit}</Menu.Item>}
              <Menu.Item>{actionsItem.remove}</Menu.Item>
            </Menu>
          );

          return (
            <Dropdown overlay={menu} placement='bottomRight'>
              <a className='ant-dropdown-link'>
                Actions <Icon type='down' />
              </a>
            </Dropdown>
          );
        }

        return (
          <span>
            {su && actionsItem.login}
            {su && <Divider type='vertical' />}
            {su && actionsItem.password}
            {su && <Divider type='vertical' />}
            {noSubUser && actionsItem.edit}
            {noSubUser && <Divider type='vertical' />}
            {actionsItem.remove}
            {noSubUser && currentUser && <Divider type='vertical' />}
            {noSubUser && currentUser && actionsItem.forceChangeSubUsersPassword}
          </span>
        );
      }
    });

    return columns;
  };

  /**
   * Set filter
   * @param filters
   */
  setFilters = (filters = {}) => this.load({ filters, pagination: { ...this.props.pagination, current: 1 } });

  /**
   * Set view
   * @param view
   */
  setView = (view = {}) => {
    const newView = { ...this.props.view, ...view };

    this.load({ view: newView });
    this.updateLocalStorage({ view: newView });
  };

  /**
   * Set select
   * @param select
   */
  setSelect = (select = {}) => {
    const selected = { ...this.props.select, ...select };

    this.load({ select: selected });
    this.updateLocalStorage({ select: selected });
  };

  /**
   * Update local Storage
   * @param view
   * @param select
   * @param pagination
   */
  updateLocalStorage = ({ view = {}, select = {}, pageSize }) => {
    localStorage.setItem('users', JSON.stringify({
      view: { ...this.props.view, ...view },
      select: { ...this.props.select, ...select },
      pageSize: pageSize || this.props.pagination.pageSize || 20,
    }));
  };

  /**
   * Load list users
   * @param props
   */
  load = (props = {}) => {
    const { sorter, filters, pagination, view, select } = this.props;

    actions.user.load({ sorter, filters, pagination, view, select, ...props });
  };

  /**
   * Handle table change
   * @param pagination
   * @param filters
   * @param sorter
   */
  handleTableChange = (pagination, filters, sorter) => {
    this.load({ pagination, sorter });
    this.updateLocalStorage({ pageSize: pagination.pageSize });
  };

  /**
   * Remove application by ID
   * @param userId
   */
  remove = (userId, assignee = null) => {
    return actions.user
      .remove(userId, assignee)
      .then(() => {
        const { _id: id } = this.props;

        message.success('Deleted Success');

        if (id === userId) actions.auth.logout();
        else this.load();
      });
  };

  /**
   * Login As
   * @param email
   */
  loginAs = email => actions.auth.loginAs(email);

  /**
   * Force sub-users to change their passwords
   */
  forceChangePassword = () => actions.user.forceChangePassword().then(() => message.success('Success'));

  render() {
    const {
      su,
      list = [],
      loading,
      pagination = {},
      filters = {},
      view = {},
      select = {},
    } = this.props;

    return (
      <div>
        <Breadcrumb routes={[{ title: 'Dashboard', href: `${config.rootRoute}/` }, { title: 'Users' }]} />

        <Row gutter={24}>
          <Col xl={6} lg={6} md={24} sm={24} xs={24}>
            <Card title='New user' style={{ marginBottom: 24 }} bordered={false}>
              <Link to={`${config.rootRoute}/users/add`}>
                <Button size='large' icon='user-add' type='primary' style={{ width: '100%' }}>
                  Add user
                </Button>
              </Link>
            </Card>

            <Card
              title='Filters'
              style={{ marginBottom: 24 }}
              bodyStyle={{ display: 'flex', flexDirection: 'column' }}
              bordered={false}
              extra={Object.keys(filters).length !== 0 && <a onClick={() => this.setFilters()}>Clean all</a>}
            >
              <Input
                key='name'
                size='large'
                addonBefore={<Icon type='search' />}
                placeholder='By Name'
                value={filters.name || ''}
                onChange={e => {
                  const val = e.target.value;

                  if (!val) {
                    const { name, ...newFilters } = filters;

                    actions.user.change({ filters: { ...newFilters } });
                  } else {
                    actions.user.change({ filters: { ...filters, name: val } });
                  }

                  clearTimeout(this.timerName);
                  this.timerName = setTimeout(() => this.load(), 600);
                }}
              />

              <Input
                key='email'
                size='large'
                style={{ marginTop: 24 }}
                addonBefore={<Icon type='search' />}
                placeholder='By Email'
                value={filters.email || ''}
                onChange={e => {
                  const val = e.target.value;

                  if (!val) {
                    const { email, ...newFilters } = filters;

                    actions.user.change({ filters: { ...newFilters } });
                  } else {
                    actions.user.change({ filters: { ...filters, email: val } });
                  }

                  clearTimeout(this.timerEmail);
                  this.timerEmail = setTimeout(() => this.load(), 600);
                }}
              />

              <Select
                key='role'
                value={filters.role}
                size='large'
                style={{ marginTop: 24 }}
                placeholder='By role'
                onChange={role => {
                  const { ...newFilters } = filters;
                  const { groupSubUsers } = view;

                  if (role === 'sub-client' && groupSubUsers) {
                    const newView = { ...view, groupSubUsers: false };

                    this.load({
                      filters: { ...newFilters, role },
                      pagination: { ...this.props.pagination, current: 1 },
                      view: newView,
                    });
                    this.updateLocalStorage({ view: newView });
                  } else {
                    this.setFilters({ ...newFilters, role });
                  }
                }}
              >
                <Select.Option value='admin'>Admin</Select.Option>
                <Select.Option value='client'>User</Select.Option>
                <Select.Option value='sub-client'>Sub User</Select.Option>
              </Select>

              <Select
                key='permission'
                value={filters.permission}
                size='large'
                style={{ marginTop: 24 }}
                placeholder='By permission'
                onChange={permission => this.setFilters({ ...filters, permission })}
              >
                {constants.PERMISSIONS.map(({ key, name }) => (
                  <Select.Option key={key} value={key}>{name}</Select.Option>
                ))}
              </Select>

              {su && (
                <Select
                  key='adminPermission'
                  value={filters.adminPermission}
                  size='large'
                  style={{ marginTop: 24 }}
                  placeholder='By admin permission'
                  onChange={adminPermission => this.setFilters({ ...filters, adminPermission })}
                >
                  {constants.ADMIN_PERMISSIONS.map(({ key, name }) => (
                    <Select.Option key={key} value={key}>{name}</Select.Option>
                  ))}
                </Select>
              )}
            </Card>

            <Card title='View' style={{ marginBottom: 24 }} bordered={false}>
              <div>
                <Switch checked={view.groupSubUsers} onChange={() => this.setView({ groupSubUsers: !view.groupSubUsers })} />
                <span style={{ marginLeft: 10 }}>to group sub users</span>
              </div>

              <div style={{ marginTop: 15 }}>
                <Switch checked={view.groupActions} onChange={() => this.setView({ groupActions: !view.groupActions })} />
                <span style={{ marginLeft: 10 }}>to group actions</span>
              </div>
            </Card>

            <Card title='Columns' style={{ marginBottom: 24 }} bodyStyle={{ paddingTop: 9 }} bordered={false}>
              {this.menuColumns.map(({ key, name, disabled }) => (
                <div key={key} style={{ marginTop: 15 }}>
                  <Switch
                    checked={disabled || select[key]}
                    disabled={disabled}
                    onChange={() => this.setSelect({ [key]: !select[key] })}
                  />

                  <span style={{ marginLeft: 10 }}>{name}</span>
                </div>
              ))}
            </Card>
          </Col>

          <Col xl={18} lg={18} md={24} sm={24} xs={24}>
            <Card title='Users' style={{ marginBottom: 24 }} bordered={false}>
              <Table
                loading={loading}
                rowKey='_id'
                dataSource={list}
                columns={this.getColumns()}
                pagination={{
                  showSizeChanger: true,
                  ...pagination,
                }}
                onChange={this.handleTableChange}
              />
            </Card>
          </Col>
        </Row>

        <Assign deleteSubUser={(userId, assignee) => this.remove(userId, assignee)} />
      </div>
    );
  }
}
