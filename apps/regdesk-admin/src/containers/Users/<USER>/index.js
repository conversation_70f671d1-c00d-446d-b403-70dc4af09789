import React, { PureComponent } from 'react';
import { Select, Modal, Form, Button, Col } from 'antd';
import { connect } from 'react-redux';
import actions from '../../../actions';

const FormItem = Form.Item;

@connect(({ team }) => ({
  idu: team.idu,
  list: team.list,
  showModal: team.showModal,
}))
@Form.create()

export default class AssignTasks extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      loading: false
    };
  }

  handleSubmit = e => {
    e.preventDefault();

    const { form, idu, deleteSubUser } = this.props;

    form.validateFieldsAndScroll((err, { assignee }) => {
      if (!err) {
        this.setState({ loading: true });

        deleteSubUser(idu, assignee)
          .then(() => {
            this.setState({ loading: false });
            actions.team.closeModal();
          })
          .catch(() => this.setState({ loading: false }));
      }
    });
  };

  render() {
    const { form, list, showModal, idu } = this.props;
    const { loading } = this.state;
    const { getFieldDecorator } = form;
    const mainUser = list.find(i => i.role === 'client');

    return (
      <Modal
        title='Delete member'
        visible={showModal === 'deleteSubUser'}
        footer={null}
        centered
        destroyOnClose
        maskClosable={false}
        onCancel={() => actions.team.closeModal()}
        onClose={() => actions.team.closeModal()}
        width={520}
      >
        <Form style={{ marginTop: 8 }}>
          <Col span={14} offset={6}><b>Assign data to</b></Col>

          <FormItem
            label='Responder'
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 14 }}
            extra={<b>(Ensure the user has appropriate permission for the data that is assigned first)</b>}
          >
            {getFieldDecorator('assignee', {
              rules: [{ required: true, message: 'Please select user' }],
              initialValue: mainUser ? mainUser._id : undefined,
            })(
              <Select
                disabled={loading}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              >
                {list.filter(({ _id }) => _id !== idu).map(({ _id: id, name }) => (
                  <Select.Option key={id} value={id}>
                    {name}
                  </Select.Option>
                ))}
              </Select>
            )}
          </FormItem>

          <FormItem style={{ marginBottom: 0 }} wrapperCol={{ span: 14, offset: 6 }}>
            <Button
              loading={loading}
              disabled={loading}
              onClick={() => actions.team.closeModal()}
              style={{ marginRight: 8 }}
            >
              Cancel
            </Button>

            <Button
              loading={loading}
              disabled={loading}
              type='primary'
              onClick={this.handleSubmit}
              style={{ marginRight: 8 }}
            >
              Assign and Delete
            </Button>
          </FormItem>
        </Form>
      </Modal>
    );
  }
}
