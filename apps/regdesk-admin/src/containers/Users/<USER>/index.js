import React, { PureComponent } from 'react';
import { Icon, Card } from 'antd';
import config from 'config';
import Breadcrumb from '../../../components/Breadcrumb';

export default class ItemUser extends PureComponent {
  render() {
    return (
      <div>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Users', href: `${config.rootRoute}/users` },
            { title: 'Item' },
          ]}
        />

        <Card bordered={false}>
          <div style={{ textAlign: 'center' }}>
            <Icon type='tool' style={{ fontSize: 32 }} />
            <div>In work</div>
          </div>
        </Card>
      </div>
    );
  }
}
