import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Form, Input, Button, Icon, Tooltip, message, Card, Row, Col, Spin, Switch, Slider, Select, DatePicker, Checkbox } from 'antd';
import { Link } from 'react-router-dom';
import config from 'config';
import moment from 'moment';

import Breadcrumb from '../../../components/Breadcrumb';
import { allCountriesWithEU } from '../../../utils/countries';
import { FULL_DATE_FORMAT } from '../../../utils/date';
import history from '../../../utils/history';
import { totalPasswordRules, generatePassword } from '../../../utils/helpers';
import actions from '../../../actions';
import api from '../../../utils/api';
import styles from './index.less';

const { Option } = Select;
const FormItem = Form.Item;

@connect(({ account, users }) => ({
  _id: account._id,
  su: account.su,
  list: users.list,
  item: users.item,
  loading: users.loading,
}))
@Form.create()

export default class AddUser extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      checked: true,
    };

    this.formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 12 },
        md: { span: 10 },
      },
    };

    this.submitFormLayout = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 10, offset: 7 },
      },
    };

    this.userId = this.props.match.params.userId;
  }

  componentDidMount() {
    actions.user.load({
      view: { userEdit: true },
      select: { avatar: true, email: true, role: true },
    });

    if (this.userId) {
      actions.user.getById(this.userId);
    }
  }

  componentWillUnmount() {
    actions.user.change({ item: {} });
  }

  /**
   * Send form
   * @param e
   */
  handleSubmit = (e) => {
    e.preventDefault();

    this.props.form.validateFieldsAndScroll((err, values) => {
      if (!err && !this.props.loading) {
        const { permission = [], adminPermissions = [] } = this.props.item || {};

        const data = {
          ...values,
          permission,
          adminPermissions,
        };

        if (!this.userId) {
          const { checked } = this.state;

          data.sendDetails = checked;
        }

        actions.user.change({ loading: true });

        Promise.resolve()
          .then(() => {
            if (this.userId) return api.user.update({ userId: this.userId, body: data });

            return api.user.add(data);
          })
          .then(() => {
            actions.user.change({ loading: false });
            message.success(this.userId ? 'User updated' : 'User added');
            history.push(`${config.rootRoute}/users`);

            const { _id: userId } = this.props;

            if (this.userId === userId) actions.profile.get();
          })
          .catch(() => actions.user.change({ loading: false }));
      }
    });
  };

  generatePassword = () => {
    const { form } = this.props;

    form.setFieldsValue({ password: generatePassword() });
  }

  /**
   * Add suffix to mark along with the division
   * @param {number} mark '55000'
   * @returns {string} '55k'
   */
  addKiloSuffix = mark => `${(mark / 1000).toString() }k`;

  /**
   * Get marks for slide
   * @param min
   * @param max
   * @param divide
   * @returns {{}}
   */
  getMarks = (min, max, divide = 10) => {
    const start = Math.ceil(min / divide);
    const finish = Math.ceil(max / divide);
    const marks = { [min]: min.toString() };

    for (let i = start; i <= finish; i += 1) {
      const mark = i * divide;

      if (mark > 1000 && !(mark % 1000)) marks[mark] = this.addKiloSuffix(mark);
      else marks[mark] = mark.toString();
    }

    return marks;
  };

  /**
   * Update form
   * @param data
   */
  update = (data = {}) => {
    const { item = {} } = this.props;

    actions.user.change({ item: { ...item, ...data } });
  };

  onChangeSendDetails = (event) => this.setState({ checked: event.target.checked });

  render() {
    const { checked } = this.state;
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { list, item, loading, su } = this.props;

    const USER_OPTIONS = list.filter(({ _id }) => _id !== this.userId);

    const breadcrumb = (
      <Breadcrumb
        routes={[
          { title: 'Dashboard', href: `${config.rootRoute}/` },
          { title: 'Users', href: `${config.rootRoute}/users` },
          { title: this.userId ? 'Edit' : 'Add' },
        ]}
      />
    );

    if (this.userId && (loading || Object.keys(item).length === 0)) {
      return (
        <div>
          {breadcrumb}
          <Spin size='large' style={{ width: '100%', marginTop: '40px' }} />
        </div>
      );
    }

    const permissions = item.permission || [];
    const adminPermissions = item.adminPermissions || [];

    const switchPermission = ({
      permission,
      adminPermission = false,
      title,
      disabled = false,
      onChange,
    }) => (
      <div key={permission}>
        <Switch
          disabled={disabled}
          checked={
            adminPermission
              ? adminPermissions.includes(permission) || permission === 'dashboard'
              : permissions.includes(permission)
          }
          checkedChildren='On'
          unCheckedChildren='Off'
          size='small'
          style={title ? { marginRight: 5 } : {}}
          onChange={(val) => {
            let newPermissions = adminPermission
              ? [...adminPermissions]
              : [...permissions];

            if (val) newPermissions.push(permission);
            else newPermissions = newPermissions.filter((el) => el !== permission);

            this.update(
              adminPermission
                ? { adminPermissions: newPermissions }
                : { permission: newPermissions },
            );

            if (onChange) onChange(val);
          }}
        />

        {title}
      </div>
    );

    const cardPermission = ({
      permission,
      title,
      adminPermission = false,
      body = null,
      disabled = false,
      disabledExtra = false,
    }) => (
      <Card
        size='small'
        title={title}
        key={permission}
        style={{ marginBottom: 15 }}
        extra={!disabledExtra && switchPermission({ permission, adminPermission, disabled })}
        bodyStyle={
          (adminPermission
            ? adminPermissions.includes(permission) || permission === 'dashboard'
            : permissions.includes(permission)) && body ? {} : { display: 'none' }
        }
      >
        {body}
      </Card>
    );

    const countriesLimit = (key, keyLimit, title = '') => (
      <div key={key} style={{ display: 'flex', alignItems: 'center', marginTop: 10 }}>
        {getFieldDecorator(keyLimit, {
          valuePropName: 'checked',
          initialValue: this.userId ? item[keyLimit] : false,
        })(
          <Switch
            size='small'
            checkedChildren='On'
            unCheckedChildren='Off'
            style={{ marginRight: 5 }}
          />,
        )}

        <span>{title || 'Limit countries'}: </span>

        <div style={{ flex: 1, padding: '0 10px' }}>
          {getFieldDecorator(key, {
            initialValue: this.userId && item[key] ? item[key] : [],
          })(
            <Select
              size='small'
              mode='multiple'
              disabled={loading}
              placeholder='Please select countries'
              filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
            >
              {allCountriesWithEU.map(({ name, alpha3code }) => (
                <Option value={alpha3code} key={alpha3code}>
                  {name}
                </Option>
              ))}
            </Select>
          )}
        </div>
      </div>
    );

    const productLimit = (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Limit: </span>

        <div style={{ flex: 1, padding: '0 10px' }}>
          {getFieldDecorator('limitProducts', {
            initialValue:
              this.userId && item.limitProducts ? item.limitProducts : 0,
          })(
            <Slider
              max={500000}
              disabled={loading}
              min={this.userId && item.used && item.used.products ? item.used.products : 0}
              marks={this.getMarks(this.userId && item.used && item.used.products ? item.used.products : 0, 500000, 50000 )}
            />,
          )}
        </div>
      </div>
    );

    const teamLimit = (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>
          Limit
          <em style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
            <Tooltip title='Max number of members in a team'>
              <Icon
                type='info-circle-o'
                style={{ marginLeft: 8, marginRight: 4 }}
              />
            </Tooltip>
          </em>
          :
        </span>

        <div style={{ flex: 1, padding: '0 10px' }}>
          {getFieldDecorator('limitTeam', {
            initialValue: this.userId && item.limitTeam ? item.limitTeam : 0,
          })(
            <Slider
              min={this.userId && item.used && item.used.team ? item.used.team : 0}
              max={1000}
              disabled={loading}
              marks={this.getMarks(this.userId && item.used && item.used.team ? item.used.team : 0, 1000, 100,)}
            />,
          )}
        </div>
      </div>
    );

    const roleLimit = (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>
          Limit
          <em style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
            <Tooltip title='Max number of roles'>
              <Icon
                type='info-circle-o'
                style={{ marginLeft: 8, marginRight: 4 }}
              />
            </Tooltip>
          </em>
          :
        </span>

        <div style={{ flex: 1, padding: '0 10px' }}>
          {getFieldDecorator('limitRole', {
            initialValue: this.userId && item.limitRole ? item.limitRole : 0,
          })(
            <Slider
              max={1000}
              disabled={loading}
              min={this.userId && item.used && item.used.role ? item.used.role : 0}
              marks={this.getMarks(this.userId && item.used && item.used.role ? item.used.role : 0, 1000, 100,)}
            />,
          )}
        </div>
      </div>
    );

    const onChangeReg = (parentPermission, isNew) => {
      let subPermissions = ['-qms', '-ct', '-imp', '-udi', '-lab', '-aap', '-pac', '-pms', '-aer', '-ren', '-mdfao'];

      if (['reg-md', 'reg-ivd'].includes(parentPermission)) subPermissions.push('-l1', '-l2', '-l3');

      subPermissions = subPermissions.map((val) => (parentPermission + val));
      subPermissions.push(parentPermission);

      const newPermissions = permissions.filter((el) => !subPermissions.includes(el));

      if (isNew) newPermissions.push(...subPermissions);

      this.update({ permission: newPermissions });
    };

    const deviceRegulations = [
      <div key='reg-clr-title' className={styles.regTitle}>Reg Classification Rules:</div>,
      switchPermission({ permission: 'reg-classrules', title: 'Reg Classification Rules' }),

      <div key='reg-md-title' className={styles.regTitle}>Reg Medical Device:</div>,
      switchPermission({ permission: 'reg-md', title: 'Reg MD', onChange: (val) => onChangeReg('reg-md', val) }),
      switchPermission({ permission: 'reg-md-l1', title: 'Reg MD L1', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-l2', title: 'Reg MD L2', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-l3', title: 'Reg MD L3', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-qms', title: 'Reg MD QMS', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-ct', title: 'Reg MD Clinical Trials', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-imp', title: 'Reg MD Importation', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-udi', title: 'Reg MD UDI', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-lab', title: 'Reg MD Labeling', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-aap', title: 'Reg MD Advertisement and Promotion', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-pac', title: 'Reg MD Packaging', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-pms', title: 'Reg MD PMS', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-aer', title: 'Reg MD Adverse Event Report', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-ren', title: 'Reg MD Renewal', disabled: !permissions.includes('reg-md') }),
      switchPermission({ permission: 'reg-md-mdfao', title: 'Reg MD Materials Derived from Animal Origin', disabled: !permissions.includes('reg-md') }),

      <div key='reg-ivd-title' className={styles.regTitle}>Reg In-Vitro Diagnostic Device:</div>,
      switchPermission({ permission: 'reg-ivd', title: 'Reg IVD', onChange: (val) => onChangeReg('reg-ivd', val) }),
      switchPermission({ permission: 'reg-ivd-l1', title: 'Reg IVD L1', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-l2', title: 'Reg IVD L2', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-l3', title: 'Reg IVD L3', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-qms', title: 'Reg IVD QMS', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-ct', title: 'Reg IVD Clinical Trials', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-imp', title: 'Reg IVD Importation', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-udi', title: 'Reg IVD UDI', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-lab', title: 'Reg IVD Labeling', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-aap', title: 'Reg IVD Advertisement and Promotion', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-pac', title: 'Reg IVD Packaging', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-pms', title: 'Reg IVD PMS', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-aer', title: 'Reg IVD Adverse Event Report', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-ren', title: 'Reg IVD Renewal', disabled: !permissions.includes('reg-ivd') }),
      switchPermission({ permission: 'reg-ivd-mdfao', title: 'Reg IVD Materials Derived from Animal Origin', disabled: !permissions.includes('reg-ivd') }),

      <div key='reg-cd-title' className={styles.regTitle}>Reg Combination Device:</div>,
      switchPermission({ permission: 'reg-cd', title: 'Reg CD', onChange: (val) => onChangeReg('reg-cd', val) }),
      switchPermission({ permission: 'reg-cd-qms', title: 'Reg CD QMS', disabled: !permissions.includes('reg-cd') }),
      switchPermission({ permission: 'reg-cd-ct', title: 'Reg CD Clinical Trials', disabled: !permissions.includes('reg-cd') }),
      switchPermission({ permission: 'reg-cd-imp', title: 'Reg CD Importation', disabled: !permissions.includes('reg-cd') }),
      switchPermission({ permission: 'reg-cd-udi', title: 'Reg CD UDI', disabled: !permissions.includes('reg-cd') }),
      switchPermission({ permission: 'reg-cd-lab', title: 'Reg CD Labeling', disabled: !permissions.includes('reg-cd') }),
      switchPermission({ permission: 'reg-cd-aap', title: 'Reg CD Advertisement and Promotion', disabled: !permissions.includes('reg-cd') }),
      switchPermission({ permission: 'reg-cd-pac', title: 'Reg CD Packaging', disabled: !permissions.includes('reg-cd') }),
      switchPermission({ permission: 'reg-cd-pms', title: 'Reg CD PMS', disabled: !permissions.includes('reg-cd') }),
      switchPermission({ permission: 'reg-cd-aer', title: 'Reg CD Adverse Event Report', disabled: !permissions.includes('reg-cd') }),
      switchPermission({ permission: 'reg-cd-mdfao', title: 'Reg CD Materials Derived from Animal Origin', disabled: !permissions.includes('reg-cd') }),

      <div key='reg-red-title' className={styles.regTitle}>Reg Radiation Emitting Device:</div>,
      switchPermission({ permission: 'reg-red', title: 'Reg RED', onChange: (val) => onChangeReg('reg-red', val) }),
      switchPermission({ permission: 'reg-red-qms', title: 'Reg RED QMS', disabled: !permissions.includes('reg-red') }),
      switchPermission({ permission: 'reg-red-ct', title: 'Reg RED Clinical Trials', disabled: !permissions.includes('reg-red') }),
      switchPermission({ permission: 'reg-red-imp', title: 'Reg RED Importation', disabled: !permissions.includes('reg-red') }),
      switchPermission({ permission: 'reg-red-udi', title: 'Reg RED UDI', disabled: !permissions.includes('reg-red') }),
      switchPermission({ permission: 'reg-red-lab', title: 'Reg RED Labeling', disabled: !permissions.includes('reg-red') }),
      switchPermission({ permission: 'reg-red-aap', title: 'Reg RED Advertisement and Promotion', disabled: !permissions.includes('reg-red') }),
      switchPermission({ permission: 'reg-red-pac', title: 'Reg RED Packaging', disabled: !permissions.includes('reg-red') }),
      switchPermission({ permission: 'reg-red-pms', title: 'Reg RED PMS', disabled: !permissions.includes('reg-red') }),
      switchPermission({ permission: 'reg-red-aer', title: 'Reg RED Adverse Event Report', disabled: !permissions.includes('reg-red') }),

      <div key='reg-wld-title' className={styles.regTitle}>Reg Wireless Device:</div>,
      switchPermission({ permission: 'reg-wld', title: 'Reg WLD', onChange: (val) => onChangeReg('reg-wld', val) }),
      switchPermission({ permission: 'reg-wld-qms', title: 'Reg WLD QMS', disabled: !permissions.includes('reg-wld') }),
      switchPermission({ permission: 'reg-wld-ct', title: 'Reg WLD Clinical Trials', disabled: !permissions.includes('reg-wld') }),
      switchPermission({ permission: 'reg-wld-imp', title: 'Reg WLD Importation', disabled: !permissions.includes('reg-wld') }),
      switchPermission({ permission: 'reg-wld-udi', title: 'Reg WLD UDI', disabled: !permissions.includes('reg-wld') }),
      switchPermission({ permission: 'reg-wld-lab', title: 'Reg WLD Labeling', disabled: !permissions.includes('reg-wld') }),
      switchPermission({ permission: 'reg-wld-aap', title: 'Reg WLD Advertisement and Promotion', disabled: !permissions.includes('reg-wld') }),
      switchPermission({ permission: 'reg-wld-pac', title: 'Reg WLD Packaging', disabled: !permissions.includes('reg-wld') }),
      switchPermission({ permission: 'reg-wld-pms', title: 'Reg WLD PMS', disabled: !permissions.includes('reg-wld') }),
      switchPermission({ permission: 'reg-wld-aer', title: 'Reg WLD Adverse Event Report', disabled: !permissions.includes('reg-wld') }),

      <div key='reg-emc-title' className={styles.regTitle}>Reg Electromagnetic Compatibility Device:</div>,
      switchPermission({ permission: 'reg-emc', title: 'Reg EMC', onChange: (val) => onChangeReg('reg-emc', val) }),
      switchPermission({ permission: 'reg-emc-qms', title: 'Reg EMC QMS', disabled: !permissions.includes('reg-emc') }),
      switchPermission({ permission: 'reg-emc-ct', title: 'Reg EMC Clinical Trials', disabled: !permissions.includes('reg-emc') }),
      switchPermission({ permission: 'reg-emc-imp', title: 'Reg EMC Importation', disabled: !permissions.includes('reg-emc') }),
      switchPermission({ permission: 'reg-emc-udi', title: 'Reg EMC UDI', disabled: !permissions.includes('reg-emc') }),
      switchPermission({ permission: 'reg-emc-lab', title: 'Reg EMC Labeling', disabled: !permissions.includes('reg-emc') }),
      switchPermission({ permission: 'reg-emc-aap', title: 'Reg EMC Advertisement and Promotion', disabled: !permissions.includes('reg-emc') }),
      switchPermission({ permission: 'reg-emc-pac', title: 'Reg EMC Packaging', disabled: !permissions.includes('reg-emc') }),
      switchPermission({ permission: 'reg-emc-pms', title: 'Reg EMC PMS', disabled: !permissions.includes('reg-emc') }),
      switchPermission({ permission: 'reg-emc-aer', title: 'Reg EMC Adverse Event Report', disabled: !permissions.includes('reg-emc') }),

      <div key='reg-sw-title' className={styles.regTitle}>Reg Software Device:</div>,
      switchPermission({ permission: 'reg-sw', title: 'Reg SW', onChange: (val) => onChangeReg('reg-sw', val) }),
      switchPermission({ permission: 'reg-sw-qms', title: 'Reg SW QMS', disabled: !permissions.includes('reg-sw') }),
      switchPermission({ permission: 'reg-sw-ct', title: 'Reg SW Clinical Trials', disabled: !permissions.includes('reg-sw') }),
      switchPermission({ permission: 'reg-sw-imp', title: 'Reg SW Importation', disabled: !permissions.includes('reg-sw') }),
      switchPermission({ permission: 'reg-sw-udi', title: 'Reg SW UDI', disabled: !permissions.includes('reg-sw') }),
      switchPermission({ permission: 'reg-sw-lab', title: 'Reg SW Labeling', disabled: !permissions.includes('reg-sw') }),
      switchPermission({ permission: 'reg-sw-aap', title: 'Reg SW Advertisement and Promotion', disabled: !permissions.includes('reg-sw') }),
      switchPermission({ permission: 'reg-sw-pac', title: 'Reg SW Packaging', disabled: !permissions.includes('reg-sw') }),
      switchPermission({ permission: 'reg-sw-pms', title: 'Reg SW PMS', disabled: !permissions.includes('reg-sw') }),
      switchPermission({ permission: 'reg-sw-aer', title: 'Reg SW Adverse Event Report', disabled: !permissions.includes('reg-sw') }),

      <div key='reg-id-title' className={styles.regTitle}>Reg Implant Device:</div>,
      switchPermission({ permission: 'reg-id', title: 'Reg ID', onChange: (val) => onChangeReg('reg-id', val) }),
      switchPermission({ permission: 'reg-id-qms', title: 'Reg ID QMS', disabled: !permissions.includes('reg-id') }),
      switchPermission({ permission: 'reg-id-ct', title: 'Reg ID Clinical Trials', disabled: !permissions.includes('reg-id') }),
      switchPermission({ permission: 'reg-id-imp', title: 'Reg ID Importation', disabled: !permissions.includes('reg-id') }),
      switchPermission({ permission: 'reg-id-udi', title: 'Reg ID UDI', disabled: !permissions.includes('reg-id') }),
      switchPermission({ permission: 'reg-id-lab', title: 'Reg ID Labeling', disabled: !permissions.includes('reg-id') }),
      switchPermission({ permission: 'reg-id-aap', title: 'Reg ID Advertisement and Promotion', disabled: !permissions.includes('reg-id') }),
      switchPermission({ permission: 'reg-id-pac', title: 'Reg ID Packaging', disabled: !permissions.includes('reg-id') }),
      switchPermission({ permission: 'reg-id-pms', title: 'Reg ID PMS', disabled: !permissions.includes('reg-id') }),
      switchPermission({ permission: 'reg-id-aer', title: 'Reg ID Adverse Event Report', disabled: !permissions.includes('reg-id') }),
      switchPermission({ permission: 'reg-id-mdfao', title: 'Reg ID Materials Derived from Animal Origin', disabled: !permissions.includes('reg-id') }),

      <div key='reg-ai-title' className={styles.regTitle}>Reg AI Medical Device:</div>,
      switchPermission({ permission: 'reg-ai', title: 'Reg AI', onChange: (val) => onChangeReg('reg-ai', val) }),
      switchPermission({ permission: 'reg-ai-qms', title: 'Reg AI QMS', disabled: !permissions.includes('reg-ai') }),
      switchPermission({ permission: 'reg-ai-lab', title: 'Reg AI Labeling', disabled: !permissions.includes('reg-ai') }),
      switchPermission({ permission: 'reg-ai-pms', title: 'Reg AI PMS', disabled: !permissions.includes('reg-ai') }),

      <div key='reg-cr-title' className={styles.regTitle}>Reg Common Regulations:</div>,
      switchPermission({ permission: 'reg-qms', title: 'Reg QMS' }),
      switchPermission({ permission: 'reg-clinicaltrials', title: 'Reg Clinical Trials' }),
      switchPermission({ permission: 'reg-imp', title: 'Reg Importation' }),
      switchPermission({ permission: 'reg-udi', title: 'Reg UDI' }),
      switchPermission({ permission: 'reg-labeling', title: 'Reg Labeling' }),
      switchPermission({ permission: 'reg-ce-marking', title: 'Reg CE Marking' }),
      switchPermission({ permission: 'reg-aap', title: 'Reg Advertisement and Promotion' }),
      switchPermission({ permission: 'reg-packaging', title: 'Reg Packaging' }),
      switchPermission({ permission: 'reg-cybersecurity', title: 'Reg Cybersecurity' }),
      switchPermission({ permission: 'reg-vig', title: 'Reg PMS' }),
      switchPermission({ permission: 'reg-aer', title: 'Reg Adverse Event Report' }),
      switchPermission({ permission: 'reg-recalls', title: 'Reg Recalls' }),
      switchPermission({ permission: 'reg-renewal', title: 'Reg Renewals' }),
      switchPermission({ permission: 'reg-esl', title: 'Reg Establishment Licence' }),
      switchPermission({ permission: 'reg-timeline', title: 'Reg Timeline and Fees' }),
      switchPermission({ permission: 'reg-e-submission', title: 'Reg E-Submission' }),
      switchPermission({ permission: 'reg-battery', title: 'Reg Battery Requirements' }),
      switchPermission({ permission: 'reg-shelflife', title: 'Reg Shelf-Life Requirements' }),
      switchPermission({ permission: 'reg-reach', title: 'Reg REACH' }),
      switchPermission({ permission: 'reg-rohs', title: 'Reg ROHS' }),
      switchPermission({ permission: 'reg-changecontrol', title: 'Reg Change Control Requirements' }),
      switchPermission({ permission: 'reg-otc', title: 'Reg Over-The Counter (OTC) Requirements' }),
      switchPermission({ permission: 'reg-exportcerts', title: 'Reg Export Certificates' }),
      switchPermission({ permission: 'reg-transport', title: 'Reg Transportation and Storage' }),
      switchPermission({ permission: 'reg-waste', title: 'Reg Waste Management' }),

      <div key='reg-others-title' className={styles.regTitle}>Others:</div>,
      switchPermission({ permission: 'reg-report', title: 'Can Access Monthly Report' }),
      countriesLimit('countriesForRegulation', 'limitCountriesForRegulation'),
    ];

    const pharmaRegulations = [
      switchPermission({ permission: 'pharma-reg-drugs', title: 'Reg Drugs' }),
      switchPermission({ permission: 'pharma-reg-drug approval routes', title: 'Reg Drugs Approval Route' }),
      switchPermission({ permission: 'pharma-reg-clinical trial', title: 'Reg Drugs Clinical Trial' }),
      switchPermission({ permission: 'pharma-reg-pms', title: 'Reg Drugs PMS' }),
      switchPermission({ permission: 'pharma-reg-summary', title: 'Reg Drugs Summary' }),
      switchPermission({ permission: 'pharma-reg-biologics', title: 'Reg Biologics' }),
      switchPermission({ permission: 'pharma-reg-biologics approval routes', title: 'Reg Biologics Approval Route' }),
      switchPermission({ permission: 'pharma-reg-biologics clinical trial', title: 'Reg Biologics Clinical Trial' }),
      switchPermission({ permission: 'pharma-reg-biologics pms', title: 'Reg Biologics PMS' }),
      switchPermission({ permission: 'pharma-reg-biologics summary', title: 'Reg Biologics Summary' }),
      switchPermission({ permission: 'pharma-reg-api', title: 'Reg API' }),
      switchPermission({ permission: 'pharma-reg-excipients', title: 'Reg Excipients' }),
      switchPermission({ permission: 'pharma-reg-labeling', title: 'Reg Labeling' }),
      switchPermission({ permission: 'pharma-reg-licenses and certificates', title: 'Reg Licenses and Certificates' }),
      switchPermission({ permission: 'pharma-reg-gxps', title: 'Reg GXPs' }),
      switchPermission({ permission: 'pharma-reg-post-approval variations', title: 'Reg Post Approval Variations' }),
      switchPermission({ permission: 'pharma-reg-advertisement and promotion', title: 'Reg Advertisement and Promotion' }),
      switchPermission({ permission: 'pharma-reg-renewal', title: 'Reg Renewal' }),
      switchPermission({ permission: 'pharma-reg-recalls', title: 'Reg Recalls' }),
      switchPermission({ permission: 'pharma-reg-importation', title: 'Reg Importation' }),
      switchPermission({ permission: 'pharma-reg-timeline and fees', title: 'Reg Timeline and Fees' }),
      switchPermission({ permission: 'pharma-reg-e-submission', title: 'Reg E-Submission' }),
      switchPermission({ permission: 'pharma-reg-plant master file', title: 'Reg Plant Master File' }),
      switchPermission({ permission: 'pharma-reg-manufacturing license', title: 'Reg Manufacturing License' }),
      switchPermission({ permission: 'pharma-reg-pic/s certification', title: 'Reg PIC/S Certification' }),
      <div key='pharma-reg-others-title' className={styles.regTitle}>Others:</div>,
      switchPermission({ permission: 'pharma-report', title: 'Can Access Monthly Report' }),
      countriesLimit('countriesForPharmaRegulation', 'limitCountriesForPharmaRegulation'),
    ];

    const widgets = [
      switchPermission({ permission: 'widget-timer', title: 'Widget Timer', adminPermission: true }),
      switchPermission({ permission: 'widget-migrations', title: 'Widget Migrations', adminPermission: true }),
      switchPermission({ permission: 'widget-services', title: 'Widget Services', adminPermission: true }),
    ];

    const adminDeviceRegulations = [
      switchPermission({ permission: 'accessRegApproveRejectSection', title: 'Can Approve/Reject a Section', adminPermission: true }),
      switchPermission({ permission: 'accessRegReleaseCountry', title: 'Can Release a Country', adminPermission: true }),
      switchPermission({ permission: 'accessRegChangeStatus', title: 'Can Change Country Status', adminPermission: true }),
      switchPermission({ permission: 'accessRegRemoveData', title: 'Can Delete Data', adminPermission: true }),
      switchPermission({ permission: 'accessBauschEnableClass', title: 'Can Enable Bausch Class', adminPermission: true }),
      switchPermission({ permission: 'accessRegUpdateDocTypes', title: 'Can Update Doc Types', adminPermission: true }),
      switchPermission({ permission: 'accessRegUpdateReports', title: 'Can Update Reports', adminPermission: true }),
      countriesLimit('adminCountriesForRegulation', 'adminLimitCountriesForRegulation'),
    ];

    const adminPharmaRegulations = [
      switchPermission({ permission: 'accessPharmaRegApproveRejectSection', title: 'Can Approve/Reject a Section', adminPermission: true }),
      switchPermission({ permission: 'accessPharmaRegReleaseCountry', title: 'Can Release a Country', adminPermission: true }),
      switchPermission({ permission: 'accessPharmaRegChangeStatus', title: 'Can Change Country Status', adminPermission: true }),
      switchPermission({ permission: 'accessPharmaRegRemoveData', title: 'Can Delete Data', adminPermission: true }),
      switchPermission({ permission: 'accessPharmaRegUpdateDocTypes', title: 'Can Update Doc Types', adminPermission: true }),
      switchPermission({ permission: 'accessPharmaRegUpdateReports', title: 'Can Update Reports', adminPermission: true }),
      countriesLimit('adminCountriesForPharmaRegulation', 'adminLimitCountriesForPharmaRegulation'),
    ];

    const adminChangeControl = [
      switchPermission({ permission: 'accessCCPUpdateChanges', title: 'Can Update Changes', adminPermission: true }),
      switchPermission({ permission: 'accessCCPUpdateDTChanges', title: 'Can Update Decision Tree Changes', adminPermission: true }),
      switchPermission({ permission: 'accessCCPReleaseCountry', title: 'Can Release a Country', adminPermission: true }),
      switchPermission({ permission: 'accessCCPRemoveData', title: 'Can Delete Data', adminPermission: true }),
      countriesLimit('adminCountriesForCCP', 'adminLimitCountriesForCCP'),
    ];

    const adminDataAlerts = [
      switchPermission({ permission: 'accessAlertRemove', title: 'Can Delete Alerts', adminPermission: true }),
      countriesLimit('adminCountriesForAlert', 'adminLimitCountriesForAlert'),
    ];

    const adminPharmaAlerts = [
      switchPermission({ permission: 'accessPharmaAlertRemove', title: 'Can Delete Alerts', adminPermission: true }),
      countriesLimit('adminCountriesForPharmaAlert', 'adminLimitCountriesForPharmaAlert'),
    ];

    const adminStandards = [
      switchPermission({ permission: 'accessStandardUpdateData', title: 'Can Update Data', adminPermission: true }),
      switchPermission({ permission: 'accessStandardRemoveData', title: 'Can Delete Data', adminPermission: true }),
      switchPermission({ permission: 'accessStandardCompareData', title: 'Can Compare Data', adminPermission: true }),
      switchPermission({ permission: 'accessStandards', title: 'Can Access Standards', adminPermission: true }),
      switchPermission({ permission: 'accessGuidances', title: 'Can Access Guidance', adminPermission: true }),
      switchPermission({ permission: 'accessLegislations', title: 'Can Access Legislations', adminPermission: true }),
      countriesLimit('adminCountriesForLegislation', 'adminLimitCountriesForLegislation', 'Limit countries (Legislations)'),
    ];

    const adminForms = [
      switchPermission({ permission: 'accessFormRemoveTemplate', title: 'Can Delete a Form Template', adminPermission: true }),
      countriesLimit('adminCountriesForForm', 'adminLimitCountriesForForm'),
    ];

    const adminWizards = [
      switchPermission({ permission: 'accessWizardRemove', title: 'Can Delete a Wizard', adminPermission: true }),
      switchPermission({ permission: 'accessWizardAddParams', title: 'Can Add New Template Parameters', adminPermission: true }),
      switchPermission({ permission: 'accessWizardUpdateID', title: 'Can Update ID', adminPermission: true }),
      switchPermission({ permission: 'accessWizardUpdateTag', title: 'Can Update System Tags', adminPermission: true }),
      countriesLimit('adminCountriesForWizards', 'adminLimitCountriesForWizards'),
    ];

    return (
      <div>
        {breadcrumb}

        <Card bordered={false}>
          <Form onSubmit={this.handleSubmit} style={{ marginTop: 8 }}>
            <Row>
              <Col {...this.formItemLayout.labelCol} />

              <Col {...this.formItemLayout.wrapperCol}>
                <h4 className={styles.h4}>User Info</h4>
              </Col>
            </Row>

            <FormItem label='First & Last Name' {...this.formItemLayout}>
              {getFieldDecorator('name', {
                rules: [{
                  required: true,
                  message: 'Please! Enter a name',
                }],
                initialValue: this.userId && item.name ? item.name : '',
              })(
                <Input
                  placeholder='Name...'
                  disabled={loading}
                  autoComplete='off'
                />,
              )}
            </FormItem>

            <FormItem label='Email' {...this.formItemLayout}>
              {getFieldDecorator('email', {
                rules: [
                  { required: true, message: 'Please! Enter a email' },
                  {
                    type: 'email',
                    message: 'Invalid e-mail format',
                  },
                ],
                initialValue: this.userId && item.email ? item.email : '',
              })(
                <Input
                  disabled={!!this.userId || loading}
                  placeholder='Email...'
                  autoComplete='off'
                />,
              )}
            </FormItem>

            {!this.userId && (
              <FormItem
                label={
                  <span>
                    Password
                    <em style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
                      <Tooltip title='Password must have at least 8 characters and contain symbols: [A-Z], [a-z], [0-9], [_ ! @ # $ % ^ & * ( ) ~ -]'>
                        <Icon
                          type='info-circle-o'
                          style={{ marginLeft: 8, marginRight: 4 }}
                        />
                      </Tooltip>
                    </em>
                  </span>
                }
                {...this.formItemLayout}
              >
                {getFieldDecorator('password', totalPasswordRules('newPassword'))(
                  <Input
                    disabled={loading}
                    placeholder='Password...'
                    autoComplete='off'
                    addonAfter={
                      <Tooltip title='Generate password'>
                        <Icon
                          style={{ fontSize: '18px' }}
                          onClick={this.generatePassword}
                          type='key'
                        />
                      </Tooltip>
                    }
                  />,
                )}
              </FormItem>
            )}

            <FormItem {...this.formItemLayout} label='Phone'>
              {getFieldDecorator('phone', {
                rules: [{
                  pattern: /^\+\d{12}$/,
                  message: '+zzzyyxxxxxxx international phone numbers are 12 digits long',
                }],
                initialValue:
                  this.userId && item.contact && item.contact.phone
                    ? item.contact.phone
                    : '',
              })(
                <Input
                  placeholder='+zzzyyxxxxxxx'
                  disabled={loading}
                  autoComplete='off'
                />,
              )}
            </FormItem>

            <FormItem label='Company' {...this.formItemLayout}>
              {getFieldDecorator('company', {
                initialValue:
                  this.userId && item.contact && item.contact.company
                    ? item.contact.company
                    : '',
              })(
                <Input
                  placeholder='Company name...'
                  disabled={loading}
                  autoComplete='off'
                />,
              )}
            </FormItem>

            {!this.userId && (
              <FormItem label='Send Login Details' {...this.formItemLayout}>
                {getFieldDecorator('sendLoginDetails', {
                  initialValue: checked,
                })(
                  <Checkbox
                    checked={checked}
                    onChange={(event) => this.onChangeSendDetails(event)}
                  />,
                )}
              </FormItem>
            )}

            <Row style={{ paddingTop: 15 }}>
              <Col {...this.formItemLayout.labelCol} />

              <Col {...this.formItemLayout.wrapperCol}>
                <h4 className={styles.h4}>User Permission</h4>
              </Col>
            </Row>

            <FormItem
              label='Expiry Date'
              {...this.formItemLayout}
              extra={su ? 'Expiry date not work for admin' : ''}
            >
              {getFieldDecorator('expiryDate', {
                initialValue: this.userId && item.expiryDate && moment(item.expiryDate)
              })(<DatePicker format={FULL_DATE_FORMAT} disabled={loading} />)}
            </FormItem>

            <FormItem label='Permission' {...this.formItemLayout}>
              {cardPermission({ permission: 'products', title: 'Products', body: productLimit })}
              {cardPermission({ permission: 'dms', title: 'Documents' })}
              {cardPermission({ permission: 'projects', title: 'Product Lifecycle Management' })}
              {cardPermission({ permission: 'team', title: 'My team', body: teamLimit })}
              {cardPermission({ permission: 'roles', title: 'Role Management', body: roleLimit })}
              {cardPermission({ permission: 'share', title: 'Share Management' })}
              {cardPermission({ permission: 'applications', title: 'Applications' })}
              {cardPermission({ permission: 'regPlan', title: 'Regulatory Classification Plan' })}
              {cardPermission({ permission: 'tracking', title: 'Product Tracking' })}
              {cardPermission({ permission: 'checklists', title: 'Distributor Collaboration Tool' })}
              {cardPermission({ permission: 'regulations', title: 'Device Regulations', body: deviceRegulations })}
              {cardPermission({ permission: 'pharmaRegulations', title: 'Pharma Regulations', body: pharmaRegulations })}
              {cardPermission({ permission: 'control', title: 'Change Control Projects', body: countriesLimit('countriesForCCP', 'limitCountriesForCCP') })}
              {cardPermission({ permission: 'reg-cmp', title: 'Planning Tool' })}
              {cardPermission({ permission: 'data-alerts', title: 'Alerts', body: countriesLimit('countriesForDataAlerts', 'limitCountriesForDataAlerts') })}
              {cardPermission({ permission: 'pharma-alerts', title: 'Pharma Alerts', body: countriesLimit('countriesForPharmaAlerts', 'limitCountriesForPharmaAlerts') })}
              {cardPermission({ permission: 'standards', title: 'Standards' })}
              {cardPermission({ permission: 'integrations', title: 'Integrations' })}
              {cardPermission({ permission: 'forms', title: 'Forms' })}
              {cardPermission({ permission: 'associatedData', title: 'Associated Data' })}
              {cardPermission({ permission: 'apiDocs', title: 'API Docs' })}
            </FormItem>

            {su && (
              <div>
                <Row style={{ paddingTop: 15 }}>
                  <Col {...this.formItemLayout.labelCol} />

                  <Col {...this.formItemLayout.wrapperCol}>
                    <h4 className={styles.h4}>Admin Permission</h4>
                  </Col>
                </Row>

                <FormItem {...this.formItemLayout} label='Access to admin panel'>
                  {getFieldDecorator('isAdmin', {
                    valuePropName: 'checked',
                    initialValue: this.userId && item.role ? item.role === 'admin' : false,
                  })(<Switch disabled={loading} />)}
                </FormItem>

                <FormItem {...this.formItemLayout} label='Permission'>
                  {cardPermission({ permission: 'dashboard', title: 'Dashboard', adminPermission: true, disabledExtra: true, body: widgets })}
                  {cardPermission({ permission: 'users', title: 'Users', adminPermission: true })}
                  {cardPermission({ permission: 'regulations', title: 'Device Regs', adminPermission: true, body: adminDeviceRegulations })}
                  {cardPermission({ permission: 'pharmaRegulations', title: 'Pharma Regs', adminPermission: true, body: adminPharmaRegulations })}
                  {cardPermission({ permission: 'control', title: 'Change Control Projects', adminPermission: true, body: adminChangeControl })}
                  {cardPermission({ permission: 'dataAlerts', title: 'Data Alerts', adminPermission: true, body: adminDataAlerts })}
                  {cardPermission({ permission: 'pharmaAlerts', title: 'Pharma Alerts', adminPermission: true, body: adminPharmaAlerts })}
                  {cardPermission({ permission: 'updates', title: 'Notification Center', adminPermission: true })}
                  {cardPermission({ permission: 'standards', title: 'Standards', adminPermission: true, body: adminStandards })}
                  {cardPermission({ permission: 'regPlan', title: 'Regulatory Classification Plan', adminPermission: true })}
                  {cardPermission({ permission: 'wizards', title: 'Wizards', adminPermission: true, body: adminWizards })}
                  {cardPermission({ permission: 'appUsage', title: 'Application Usage', adminPermission: true })}
                  {cardPermission({ permission: 'forms', title: 'Forms', adminPermission: true, body: adminForms })}
                  {cardPermission({ permission: 'autofillHistory', title: 'Autofill History', adminPermission: true })}
                  {cardPermission({ permission: 'logs', title: 'Logs', adminPermission: true })}
                </FormItem>

                <Form.Item {...this.formItemLayout} label='Link to users'>
                  <div style={{ flex: 1, padding: '0 10px' }}>
                    {getFieldDecorator('linkedUsers', {
                      initialValue: this.userId && item.linkedUsers ? item.linkedUsers : [],
                    })(
                      <Select
                        size='small'
                        mode='multiple'
                        disabled={loading || getFieldValue('isAdmin')}
                        placeholder='Please select users to add a link'
                      >
                        {USER_OPTIONS.map(({ _id, email, name }) => (
                          <Option value={_id} key={_id}>
                            {name} ({email})
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </div>
                </Form.Item>
              </div>
            )}

            <FormItem {...this.submitFormLayout} style={{ marginTop: 32 }}>
              <Link to={`${config.rootRoute}/users`}>
                <Button>Cancel</Button>
              </Link>

              <Button
                style={{ marginLeft: 8 }}
                type='primary'
                htmlType='submit'
                disabled={loading}
                loading={loading}
              >
                {this.userId ? 'Edit User' : 'Add User'}
              </Button>
            </FormItem>
          </Form>
        </Card>
      </div>
    );
  }
}
