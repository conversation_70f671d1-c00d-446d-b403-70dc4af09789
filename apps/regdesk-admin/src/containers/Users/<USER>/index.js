import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Form, Input, Button, Icon, Tooltip, Card, message } from 'antd';
import { Link } from 'react-router-dom';
import config from 'config';

import Breadcrumb from '../../../components/Breadcrumb';
import history from '../../../utils/history';
import { totalPasswordRules, generatePassword } from '../../../utils/helpers';
import actions from '../../../actions';

const FormItem = Form.Item;

@connect(({ account: { loading } }) => ({ loading }))
@Form.create()

export default class ChangePassword extends PureComponent {
  constructor(props) {
    super(props);

    this.formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 12 },
        md: { span: 10 },
      },
    };

    this.submitFormLayout = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 10, offset: 7 },
      },
    };
  }

  handleSubmit = e => {
    e.preventDefault();

    const { form, loading, match } = this.props;

    form.validateFieldsAndScroll((err, values) => {
      if (!err && !loading) {
        actions.user.change({ loading: true });

        actions.user
          .changePassword({ ...values, userId: match.params.userId })
          .then(() => {
            actions.user.change({ loading: false });
            message.success('Password changed successfully');
            history.push(`${config.rootRoute}/users`);
          })
          .catch(() => actions.user.change({ loading: false }));
      }
    });
  };

  generatePassword = () => {
    const { form } = this.props;

    form.setFieldsValue({ password: generatePassword() });
  }

  render() {
    const { loading, form } = this.props;
    const { getFieldDecorator } = form;

    return (
      <div>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Users', href: `${config.rootRoute}/users` },
            { title: 'Change Password' }
          ]}
        />

        <Card bordered={false}>
          <Form onSubmit={this.handleSubmit} hideRequiredMark style={{ marginTop: 8 }}>
            <FormItem label='New Password' {...this.formItemLayout}>
              {getFieldDecorator('password', totalPasswordRules('newPassword'))(
                <Input
                  disabled={loading}
                  placeholder='Password...'
                  autoComplete='off'
                  addonAfter={
                    <Tooltip title='Generate password'>
                      <Icon style={{ fontSize: '18px' }} onClick={this.generatePassword} type='key' />
                    </Tooltip>
                  }
                />
              )}
            </FormItem>

            <FormItem style={{ marginBottom: 0 }} {...this.submitFormLayout}>
              <Link to={`${config.rootRoute}/users`}>
                <Button>Cancel</Button>
              </Link>

              <Button
                style={{ marginLeft: 8 }}
                icon='save'
                type='primary'
                htmlType='submit'
                disabled={loading}
                loading={loading}
              >
                Confirm
              </Button>
            </FormItem>
          </Form>
        </Card>
      </div>
    );
  }
}
