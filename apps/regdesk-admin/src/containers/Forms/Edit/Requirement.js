import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Form, Button, Input, Card } from 'antd';
import MDEditor from '../../../components/MarkDownEditor';
import actions from '../../../actions';
import styles from '../index.less';
import ModalUpload from '../../../components/Upload/Modal';

const FormItem = Form.Item;

@connect(({ forms }) => ({
  currentTemplate: forms.currentTemplate,
  currentRequirement: forms.currentRequirement,
}))
@Form.create()

export default class EditRequirement extends Component {
  constructor(props) {
    super(props);

    this.state = {
      required: false,
      loading: false,
      description: '',
      prefix: '',
    };
  }

  componentDidMount() {
    const { currentRequirement } = this.props;

    if (currentRequirement) {
      const { description = '', prefix = '' } = currentRequirement;

      this.setState({ description, prefix });
    }
  }

  componentWillReceiveProps(nextProps) {
    const { currentRequirement, form } = nextProps;
    const { currentRequirement: oldRequirement } = this.props;

    if (currentRequirement && ((oldRequirement && oldRequirement._id !== currentRequirement._id) || !oldRequirement)) {
      const { prefix = '', description = '' } = currentRequirement;

      this.setState({ description });

      const newData = { prefix };

      form.setFieldsValue(newData);
    } else if (oldRequirement && !currentRequirement) {
      this.setState({ description: '' });

      form.setFieldsValue({ prefix: '' });
    }
  }

  addRequirement = () => {
    const { form, currentRequirement } = this.props;

    form.validateFields((err, { prefix }) => {
      const { description } = this.state;

      if (!err) {
        const data = { description, prefix };

        this.setState({ loading: true });

        if (!currentRequirement) {
          actions.forms.addRequirement(data).then(() => this.setState({ prefix: '', description: '' }));
        } else {
          actions.forms.updateRequirement(data);
        }

        this.setState({ loading: false });
      }
    });

    !currentRequirement && form.setFieldsValue({ prefix: '' });
  };

  render() {
    const { form, currentRequirement } = this.props;
    const { getFieldDecorator } = form;

    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    };

    const { loading, description, prefix } = this.state;

    return (
      <Card
        title={
          <div className={styles.cardTitle}>
            <div>
              {currentRequirement ? 'Edit requirement' : 'Add requirement'}
            </div>

            <Button
              type='primary'
              onClick={() => this.addRequirement()}
              loading={loading}
              disabled={loading}
            >
              {currentRequirement ? 'Save' : 'Add'}
            </Button>
          </div>
        }
        bordered={false}
      >
        <Form {...formItemLayout}>
          <FormItem label='Prefix'>
            {getFieldDecorator('prefix', {
              initialValue: prefix,
            })(
              <Input
                disabled={!!loading}
                placeholder='Prefix...'
                autoComplete='off'
              />
            )}
          </FormItem>

          <MDEditor
            label='Description'
            setValue={(value) => this.setState({ description: value })}
            value={description}
          />

          <FormItem>
            <Button
              type='primary'
              onClick={() => this.addRequirement()}
              loading={loading}
              disabled={loading}
            >
              {currentRequirement ? 'Save' : 'Add'}
            </Button>
          </FormItem>
        </Form>

        <ModalUpload
          ref={ref => { this.uploadModal = ref; }}
          onUpload={link => form.setFieldsValue({ [`[${activeLink}].link`]: link })}
          info={{ module: 'forms', countryId: '' }}
        />
      </Card>
    );
  }
}
