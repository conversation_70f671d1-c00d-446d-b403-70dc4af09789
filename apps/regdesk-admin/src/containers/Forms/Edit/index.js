import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { Row, Col, Spin } from 'antd';
import Breadcrumb from '../../../components/Breadcrumb';
import EditChapter from './Chapter';
import EditSection from './Section';
import EditRequirement from './Requirement';
import EditSubRequirement from './SubRequirement';
import Fields from '../Fields';
import actions from '../../../actions';

@connect(({ forms }) => ({
  currentChapter: forms.currentChapter,
  currentSection: forms.currentSection,
  currentRequirement: forms.currentRequirement,
  currentSubRequirement: forms.currentSubRequirement,
  currentTemplate: forms.currentTemplate,
  loading: forms.loading,
}))

export default class Edit extends PureComponent {
  componentDidMount() {
    const { match } = this.props;
    const { params } = match;

    actions.forms.change({
      currentChapter: null,
      currentSection: null,
      currentRequirement: null,
      currentSubRequirement: null,
    });

    if (params.id) actions.forms.getById(params.id);
  }

  render() {
    const {
      currentChapter,
      currentSection,
      currentRequirement,
      currentSubRequirement,
      currentTemplate,
      loading,
    } = this.props;

    return (
      <Spin spinning={loading}>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Forms', href: `${config.rootRoute}/forms` },
            { title: currentTemplate ? 'Edit' : 'Create' },
          ]}
        />

        <Row gutter={24} loading='true'>
          <Col xl={6} lg={6} md={24} sm={24} xs={24}>
            <Fields />
          </Col>

          {currentTemplate && (
            <Col xl={18} lg={18} md={24} sm={24} xs={24}>
              {!currentSection && !currentRequirement && !currentSubRequirement && <EditChapter />}
              {(currentSection || currentChapter) && <EditSection />}
              {(currentSection || currentRequirement) && <EditRequirement />}
              {(currentRequirement || currentSubRequirement) && <EditSubRequirement />}
            </Col>
          )}
        </Row>
      </Spin>
    );
  }
}
