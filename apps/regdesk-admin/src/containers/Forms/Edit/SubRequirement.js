import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Form, Button, Input, Card } from 'antd';

import MDEditor from '../../../components/MarkDownEditor';
import actions from '../../../actions';
import styles from '../index.less';
import ModalUpload from '../../../components/Upload/Modal';

const FormItem = Form.Item;

@connect(({ forms }) => ({
  currentTemplate: forms.currentTemplate,
  currentSubRequirement: forms.currentSubRequirement,
}))
@Form.create()

export default class EditRequirement extends Component {
  constructor(props) {
    super(props);

    this.state = {
      required: false,
      loading: false,
      description: '',
      prefix: '',
    };
  }

  componentDidMount() {
    const { currentSubRequirement } = this.props;

    if (currentSubRequirement) {
      const { description = '', prefix = '' } = currentSubRequirement;

      this.setState({ description, prefix });
    }
  }

  componentWillReceiveProps(nextProps) {
    const { currentSubRequirement, form } = nextProps;
    const { currentSubRequirement: oldSubRequirement } = this.props;

    if (
      (oldSubRequirement
        && currentSubRequirement
        && oldSubRequirement._id !== currentSubRequirement._id)
      || (!oldSubRequirement && currentSubRequirement)
    ) {
      const { prefix = '', description = '' } = currentSubRequirement;

      this.setState({ description });

      const newData = { prefix };

      form.setFieldsValue(newData);
    }

    if (oldSubRequirement && !currentSubRequirement) {
      this.setState({ description: '' });

      form.setFieldsValue({ prefix: '' });
    }
  }

  addSubRequirement = () => {
    const { form, currentSubRequirement } = this.props;

    form.validateFields((err, { prefix }) => {
      const { description } = this.state;

      if (!err) {
        const data = { description, prefix };

        this.setState({ loading: true });

        if (!currentSubRequirement) {
          actions.forms.addSubRequirement(data).then(() => this.setState({ prefix: '', description: '' }));
        } else {
          actions.forms.updateSubRequirement(data);
        }

        this.setState({ loading: false });
      }
    });

    !currentSubRequirement && form.setFieldsValue({ prefix: '' });
  };

  render() {
    const { form, currentSubRequirement } = this.props;
    const { getFieldDecorator } = form;

    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    };
    const { loading, description, prefix } = this.state;

    return (
      <Card
        title={
          <div className={styles.cardTitle}>
            <div>
              {currentSubRequirement
                ? 'Edit Sub Requirement'
                : 'Add Sub Requirement'}
            </div>

            <Button
              type='primary'
              onClick={() => this.addSubRequirement()}
              loading={loading}
              disabled={loading}
            >
              {currentSubRequirement ? 'Save' : 'Add'}
            </Button>
          </div>
        }
        bordered={false}
      >
        <Form {...formItemLayout}>
          <FormItem label='Prefix'>
            {getFieldDecorator('prefix', {
              initialValue: prefix,
            })(
              <Input
                disabled={!!loading}
                placeholder='Prefix...'
                autoComplete='off'
              />
            )}
          </FormItem>

          <MDEditor
            label='Description'
            setValue={(value) => this.setState({ description: value })}
            value={description}
          />

          <FormItem>
            <Button
              type='primary'
              onClick={() => this.addSubRequirement()}
              loading={loading}
              disabled={loading}
            >
              {currentSubRequirement ? 'Save' : 'Add'}
            </Button>
          </FormItem>
        </Form>

        <ModalUpload
          ref={ref => { this.uploadModal = ref; }}
          onUpload={link => form.setFieldsValue({ [`[${activeLink}].link`]: link })}
          info={{ module: 'forms', countryId: '' }}
        />
      </Card>
    );
  }
}
