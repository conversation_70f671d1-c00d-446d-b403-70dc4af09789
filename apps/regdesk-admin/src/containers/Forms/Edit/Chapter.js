import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Form, Button, Input, Card, Popconfirm } from 'antd';

import actions from '../../../actions';

const FormItem = Form.Item;

@connect(({ forms }) => ({
  currentChapter: forms.currentChapter,
}))
@Form.create()

export default class EditChapter extends Component {
  constructor(props) {
    super(props);

    this.state = {
      loading: null,
    };
  }

  componentWillReceiveProps(nextProps) {
    const { currentChapter, form } = nextProps;
    const { currentChapter: oldChapter } = this.props;

    if (oldChapter && currentChapter && oldChapter._id !== currentChapter._id) {
      const { name } = currentChapter;

      form.setFieldsValue({ name });
    }
  }

  addChapter = () => {
    const { form, currentChapter } = this.props;

    form.validateFields((err, { name }) => {
      if (!err) {
        this.setState({ loading: 'update' });

        if (!currentChapter) {
          actions.forms
            .addChapter({ name })
            .then((chapter) => this.setState({ selectedKeys: [chapter._id], loading: null }))
            .catch(() => this.setState({ loading: null }));
        } else {
          actions.forms
            .updateChapter({ name })
            .then(() => this.setState({ loading: null }))
            .catch(() => this.setState({ loading: null }));
        }
      }
    });
  };

  deleteChapter = () => {
    this.setState({ loading: 'delete' });

    actions.forms
      .deleteChapter()
      .then(() => this.setState({ loading: null }))
      .catch(() => this.setState({ loading: null }));
  };

  render() {
    const { form, currentChapter } = this.props;
    const { getFieldDecorator } = form;

    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    };
    const { name } = currentChapter || { name: '' };
    const { loading } = this.state;

    return (
      <Card
        title={currentChapter ? 'Edit chapter' : 'Add chapter'}
        bordered={false}
        style={{ marginBottom: 20 }}
      >
        <Form {...formItemLayout}>
          <FormItem label='Name'>
            {getFieldDecorator('name', {
              rules: [
                {
                  required: true,
                  message: 'Please, enter section name',
                },
              ],
              initialValue: name,
            })(
              <Input
                autoFocus
                disabled={!!loading}
                placeholder='Chapter name...'
                autoComplete='off'
              />
            )}
          </FormItem>

          <FormItem wrapperCol={{ offset: 4 }}>
            {currentChapter && (
              <Popconfirm
                title='Are you sure you want to delete the chapter and all the included data?'
                okText='Confirm'
                cancelText='Cancel'
                onConfirm={() => this.deleteChapter()}
              >
                <Button
                  style={{ marginRight: 10 }}
                  loading={loading === 'delete'}
                  disabled={!!loading}
                >
                  Delete
                </Button>
              </Popconfirm>
            )}

            <Button
              type='primary'
              onClick={() => this.addChapter()}
              loading={loading === 'update'}
              disabled={!!loading}
            >
              {currentChapter ? 'Save' : 'Add'}
            </Button>
          </FormItem>
        </Form>
      </Card>
    );
  }
}
