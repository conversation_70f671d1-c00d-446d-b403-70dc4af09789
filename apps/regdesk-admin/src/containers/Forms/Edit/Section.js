import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Form, Button, Input, Card, Popconfirm } from 'antd';

import actions from '../../../actions';

const FormItem = Form.Item;

@connect(({ forms }) => ({
  currentSection: forms.currentSection,
}))
@Form.create()

export default class EditSection extends Component {
  constructor(props) {
    super(props);

    this.state = {
      loading: null,
    };
  }

  componentWillReceiveProps(nextProps) {
    const { currentSection, form } = nextProps;
    const { currentSection: oldSection } = this.props;

    if (currentSection && ((oldSection && oldSection._id !== currentSection._id) || !oldSection)) {
      const { name } = currentSection;

      form.setFieldsValue({ name });
    } else if (oldSection && !currentSection) {
      form.setFieldsValue({ name: '' });
    }
  }

  addSection = () => {
    const { form, currentSection } = this.props;

    form.validateFields((err, { name }) => {
      if (!err) {
        this.setState({ loading: 'update' });

        if (!currentSection) {
          actions.forms
            .addSection({ name })
            .then((section) => this.setState({ selectedKeys: [section._id], loading: null }))
            .catch(() => this.setState({ loading: null }));
        } else {
          actions.forms
            .updateSection({ name })
            .then(() => this.setState({ loading: null }))
            .catch(() => this.setState({ loading: null }));
        }
      }
    });

    !currentSection && form.setFieldsValue({ name: '' });
  };

  deleteSection = () => {
    this.setState({ loading: 'delete' });

    actions.forms
      .deleteSection()
      .then(() => this.setState({ loading: null }))
      .catch(() => this.setState({ loading: null }));
  };

  render() {
    const { form, currentSection } = this.props;
    const { getFieldDecorator } = form;

    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    };
    const { name } = currentSection || { name: '' };
    const { loading } = this.state;

    return (
      <Card
        title={currentSection ? 'Edit section' : 'Add section'}
        bordered={false}
        style={{ marginBottom: 20 }}
      >
        <Form {...formItemLayout}>
          <FormItem label='Name'>
            {getFieldDecorator('name', {
              initialValue: name,
            })(
              <Input
                autoFocus
                disabled={!!loading}
                placeholder='Section name...'
                autoComplete='off'
              />
            )}
          </FormItem>

          <FormItem wrapperCol={{ offset: 4 }}>
            {currentSection && (
              <Popconfirm
                title='Are you sure you want to delete the section and all the included requirements?'
                okText='Confirm'
                cancelText='Cancel'
                onConfirm={() => this.deleteSection()}
              >
                <Button
                  style={{ marginRight: 10 }}
                  loading={loading === 'delete'}
                  disabled={!!loading}
                >
                  Delete
                </Button>
              </Popconfirm>
            )}

            <Button
              type='primary'
              onClick={() => this.addSection()}
              loading={loading === 'update'}
              disabled={!!loading}
            >
              {currentSection ? 'Save' : 'Add'}
            </Button>
          </FormItem>
        </Form>
      </Card>
    );
  }
}
