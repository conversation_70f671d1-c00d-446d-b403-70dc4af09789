import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { withRouter } from 'react-router-dom';
import { Form, Icon, Button, Card, Input, Select, Row, Col, message } from 'antd';
import actions from '../../../actions';
import { productTypes, paramsIsUnique } from '../utils';
import { allCountriesWithEU } from '../../../utils/countries';
import styles from '../index.less';

let id = 0;
const { Option } = Select;

@connect(({ forms, account }) => ({
  loading: forms.loading,
  currentWizard: forms.currentTemplate,
  availableCountries: account.adminCountriesForForm,
  limitCountries: account.adminLimitCountriesForForm,
}))
@Form.create()
@withRouter

export default class CloneForm extends PureComponent {
  remove = (k) => {
    const { form } = this.props;
    const keys = form.getFieldValue('keys');

    if (keys.length === 1) return;

    form.setFieldsValue({ keys: keys.filter((key) => key !== k) });
  };

  add = () => {
    const { form } = this.props;
    const keys = form.getFieldValue('keys');
    const nextKeys = keys.concat(++id);

    form.setFieldsValue({ keys: nextKeys });
  };

  handleSubmit = (e) => {
    e.preventDefault();

    const { form, history } = this.props;

    form.validateFields((err, { keys, ...rest }) => {
      if (!err) {
        const params = Object.values(rest);

        if (paramsIsUnique(params)) {
          actions.forms.clone(params).then((error) => {
            if (!error) history.push(`${config.rootRoute}/forms`);
          });
        } else {
          message.warn('All parameter sets must be unique');
        }
      }
    });
  };

  getItem = (k, index, all) => {
    const { form, limitCountries, availableCountries } = this.props;
    const { getFieldDecorator } = form;
    const { keys } = all;
    let countries = allCountriesWithEU;

    if (limitCountries) countries = countries.filter(({ alpha3code }) => availableCountries.includes(alpha3code));

    return (
      <div key={k} className={styles.cloneItem}>
        <Col>
          <Form.Item label={index === 0 ? 'Country' : ''}>
            {getFieldDecorator(`[${k}].country`)(
              <Select
                showSearch
                placeholder='Select country'
                style={{ width: '100%' }}
              >
                {countries.map(({ alpha3code, name }) => (
                  <Option key={alpha3code} value={alpha3code}>
                    {name}
                  </Option>
                ))}
              </Select>
            )}
          </Form.Item>
        </Col>

        <Col>
          <Form.Item label={index === 0 ? 'Product type' : ''}>
            {getFieldDecorator(`[${k}].productType`)(
              <Select
                showSearch
                placeholder='Select product type'
                style={{ width: '100%' }}
              >
                {productTypes.map(({ id: typeId, name }) => (
                  <Option key={typeId} value={typeId}>
                    {name}
                  </Option>
                ))}
              </Select>
            )}
          </Form.Item>
        </Col>

        <Col>
          <Form.Item label={index === 0 ? 'Version Number' : ''}>
            {getFieldDecorator(`[${k}].versionNumber`)(
              <Input
                type='text'
                placeholder='Version Number'
                style={{ width: ' 100%' }}
              />
            )}
          </Form.Item>
        </Col>

        <Col>
          <Form.Item label={index === 0 ? 'Version Name' : ''}>
            {getFieldDecorator(`[${k}].versionName`)(
              <Input
                type='text'
                placeholder='Version Name'
                style={{ width: ' 100%' }}
              />
            )}
          </Form.Item>
        </Col>

        <Col>
          <Form.Item label={index === 0 ? 'Version Description' : ''}>
            {getFieldDecorator(`[${k}].versionDescription`)(
              <Input.TextArea
                type='text'
                placeholder='Version Description'
                style={{ width: ' 100%' }}
              />
            )}
          </Form.Item>
        </Col>

        <Col>
          <Form.Item label={index === 0 ? 'Title' : ''}>
            {getFieldDecorator(`[${k}].title`)(
              <Input.TextArea
                type='text'
                placeholder='Title'
                style={{ width: ' 100%' }}
              />
            )}
          </Form.Item>
        </Col>

        <Col>
          <Form.Item label={index === 0 ? 'SubTitle' : ''}>
            {getFieldDecorator(`[${k}].subTitle`)(
              <Input.TextArea
                type='text'
                placeholder='SubTitle'
                style={{ width: ' 100%' }}
              />
            )}
          </Form.Item>
        </Col>

        <Col className={index === 0 ? styles.firstRemoveIcon : undefined}>
          {keys.length > 1 && (
            <Icon
              className={styles.dynamicDeleteButton}
              type='minus-circle-o'
              onClick={() => this.remove(k)}
            />
          )}
        </Col>
      </div>
    );
  };

  _renderItems = () => {
    const { getFieldDecorator, getFieldsValue } = this.props.form;

    getFieldDecorator('keys', { initialValue: [0] });

    const all = getFieldsValue();
    const { keys } = all;
    const formItems = keys.map((k, index) => this.getItem(k, index, all));

    return formItems;
  };

  render() {
    const { form, loading } = this.props;
    const { getFieldsValue } = form;
    const all = getFieldsValue();
    const { keys } = all;

    if (!!all[keys] && all[keys].hasOwnProperty('versionDescription')) {
      delete all[keys].versionDescription;
    }

    const lastNotFull
      = !keys
      || !keys.length
      || !all[keys[keys.length - 1]]
      || Object.values(all[keys[keys.length - 1]]).some((i) => i === undefined);

    return (
      <Card
        title={
          <div className={styles.cardTitle}>
            <div>New template parameters</div>

            <Button
              type='primary'
              onClick={(e) => this.handleSubmit(e)}
              loading={loading}
              disabled={loading || lastNotFull}
            >
              Clone
            </Button>
          </div>
        }
        bordered={false}
      >
        <Form onSubmit={this.handleSubmit}>
          <Row gutter={24}>{this._renderItems()}</Row>

          <Button
            type='primary'
            htmlType='submit'
            style={{ width: '20%' }}
            loading={loading}
            disabled={loading || lastNotFull}
          >
            Clone
          </Button>

          <Button
            type='dashed'
            onClick={this.add}
            style={{ marginLeft: '5%', width: '75%' }}
            disabled={loading || lastNotFull}
          >
            <Icon type='plus' /> Add fields set
          </Button>
        </Form>
      </Card>
    );
  }
}
