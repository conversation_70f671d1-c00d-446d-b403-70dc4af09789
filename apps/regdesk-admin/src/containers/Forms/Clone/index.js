import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { Row, Col, Spin } from 'antd';
import Breadcrumb from '../../../components/Breadcrumb';
import actions from '../../../actions';
import Fields from '../Fields';
import CloneForm from './Form';

@connect(({ forms }) => ({
  currentTemplate: forms.currentTemplate,
  loading: forms.loading,
}))

export default class Clone extends PureComponent {
  componentDidMount() {
    const { match } = this.props;
    const { params } = match;

    if (params.id) actions.forms.getById(params.id);
  }

  render() {
    const { currentTemplate, loading } = this.props;

    return (
      <Spin spinning={loading}>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Forms', href: `${config.rootRoute}/forms` },
            { title: 'Clone' },
          ]}
        />

        <Row gutter={24}>
          <Col xl={6} lg={6} md={24} sm={24} xs={24}>
            <Fields />
          </Col>

          {currentTemplate && (
            <Col xl={18} lg={18} md={24} sm={24} xs={24}>
              <CloneForm />
            </Col>
          )}
        </Row>
      </Spin>
    );
  }
}
