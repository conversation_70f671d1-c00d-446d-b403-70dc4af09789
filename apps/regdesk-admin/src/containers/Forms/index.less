.addButtonWrapper {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.cardTitle {
  display: flex;
  justify-content: space-between;
}

.dynamicDeleteButton {
  font-size: 24px;
  color: #999;
  transition: all 0.3s;
}

.cloneItem {
  display: grid;
  grid-template-columns: repeat(4, minmax(100px, 1fr));
  margin-bottom: 15px;

  .firstRemoveIcon {
    align-self: flex-end;
    bottom: 6px;
  }

  :global {
    .ant-form-item {
      margin-bottom: 0;
    }
  }
}

.disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.25) !important;
}

@media (max-width: 800px) {
  .cloneItem {
    display: block;
  }
}
