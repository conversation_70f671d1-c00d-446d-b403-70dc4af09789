import { LEGACY_DATE_FORMAT } from '../../utils/date';

export const applicationTypes = [
  { id: '0', name: 'New Registration' },
  { id: '1', name: 'Renewal' },
  { id: '2', name: 'Change Notification' },
  { id: '3', name: 'Letter to file' },
  { id: '4', name: 'Re-registration' },
];

export const productTypes = [
  { id: '0', name: 'Medical Device' },
  { id: '1', name: 'IVD' },
];

export const classifications = [
  { id: '0', name: 'General' },
  { id: '1', name: 'Class I' },
  { id: '2', name: 'Class Ir' },
  { id: '3', name: 'Class Im' },
  { id: '4', name: 'Class Is' },
  { id: '5', name: 'Class II' },
  { id: '6', name: 'Class IIa' },
  { id: '7', name: 'Class IIb' },
  { id: '8', name: 'Class III' },
  { id: '9', name: 'Class IV' },
  { id: '10', name: 'Class A' },
  { id: '11', name: 'Class B' },
  { id: '12', name: 'Class C' },
  { id: '13', name: 'Class D' },
];

export const additionalUSAClassifications = [
  { id: '14', name: 'Traditional 510k' },
  { id: '15', name: 'Abbreviated 510k' },
  { id: '16', name: 'Special 510k' },
  { id: '17', name: 'PMA' },
];

export const getObjectFromArray = (arr, keyField = 'id', valueField = 'name') => {
  const obj = arr.reduce((total, current) => {
    total[current[keyField]] = valueField ? current[valueField] : current;

    return total;
  }, {});

  return obj;
};

export const paramsIsUnique = params => {
  const l = params.length;

  if (l > 1) {
    for (let i = 0; i < l - 1; i++) {
      for (let j = i + 1; j < l; j++) {
        const keys = Object.keys(params[i]);
        let count = 0;

        for (const key of keys) {
          if (params[i][key] !== params[j][key]) {
            break;
          } else {
            count++;
          }
        }

        if (count === keys.length) return false;
      }
    }
  }

  return true;
};

export const optionsIsUnique = options => {
  if (options.length > 1) {
    const uniqueTexts = new Set(options.filter((o) => o.text).map((o) => o.text));
    const uniqueValues = new Set(options.filter((o) => o.value).map((o) => o.value));

    if (uniqueTexts.size !== options.length|| uniqueValues.size !== options.length) {
      return false;
    }
  }

  return true;
};

export const linksIsNotEmpty = links => {
  const l = links.length;

  if (l >= 1) {
    const empty = links.find((i) => i.text === '' || i.link === '');

    if (empty) return false;
  }

  return true;
};

export const questionsScopes = [
  { label: 'Global', value: 'company' },
  { label: 'Country', value: 'country' },
  { label: 'Product', value: 'product' },
];

export const questionsTypes = [
  { label: 'Checkbox', value: 'checkbox', extra: ['column', 'options'] },
  {
    label: 'Date-picker',
    value: 'date-picker',
    defaultFormat: LEGACY_DATE_FORMAT,
    extra: ['showTime', 'format'],
  },
  { label: 'Input', value: 'input', extra: ['placeholder'] },
  {
    label: 'Input-number',
    value: 'input-number',
    extra: ['placeholder'],
  },
  { label: 'Markdown-editor', value: 'markdown-editor' },
  {
    label: 'Month-picker',
    value: 'month-picker',
    defaultFormat: 'YYYY-MM',
    extra: ['showTime', 'format'],
  },
  { label: 'Radio', value: 'radio', extra: ['column', 'options'] },
  {
    label: 'Range-picker',
    value: 'range-picker',
    defaultFormat: 'YYYY-MM-DD HH:mm:ss',
    extra: ['showTime', 'format'],
  },
  {
    label: 'Select',
    value: 'select',
    extra: ['placeholder', 'multiple', 'options'],
  },
  { label: 'Switch', value: 'switch' },
  { label: 'Textarea', value: 'textarea', extra: ['placeholder'] },
  { label: 'Time-picker', value: 'time-picker' },
  {
    label: 'Upload',
    value: 'upload',
    extra: ['multiple', 'maxFileSize', 'maxFiles', 'fileType', 'example'],
  },
];

export const fileTypes = [
  { label: 'Any', value: 'any' },
  { label: 'Image', value: 'image' },
  { label: 'Pdf', value: 'pdf' },
];

export const notifiedBodies = [
  { id: 0, name: 'BSI' },
  { id: 1, name: 'DEKRA' },
  { id: 2, name: 'BSI MDD Volcano' },
  { id: 3, name: 'BSI Technical File Legacy SPNC' },
  { id: 4, name: '3EC International' },
  { id: 5, name: 'TUV SUD' },
  { id: 6, name: 'TUV Rheinland' }
];
