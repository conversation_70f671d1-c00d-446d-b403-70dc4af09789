import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { Form, Button, Card, Tree, Input, Select, Popconfirm, Icon } from 'antd';
import config from 'config';
import { productTypes } from './utils';
import { allCountriesWithEU } from '../../utils/countries';
import actions from '../../actions';
import styles from './index.less';

const { Option } = Select;
const { TreeNode } = Tree;

@connect(({ forms, account }) => ({
  loading: forms.loading,
  currentTemplate: forms.currentTemplate,
  currentRequirement: forms.currentRequirement,
  chapters: forms.chapters,
  availableCountries: account.adminCountriesForForm,
  limitCountries: account.adminLimitCountriesForForm,
}))
@Form.create()
@withRouter

export default class WizardFields extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      country: undefined,
      productType: undefined,
      versionNumber: '',
      versionName: '',
      versionDescription: '',
      title: '',
      subTitle: '',
      selectedKeys: [],
      deleting: null,
    };
  }

  componentDidMount() {
    const { currentTemplate } = this.props;

    this.init(currentTemplate);
  }

  componentWillReceiveProps(nextProps) {
    const { currentTemplate } = nextProps;
    const { currentTemplate: oldWizard } = this.props;

    if ((!oldWizard && currentTemplate) || (currentTemplate && oldWizard._id !== currentTemplate._id)) {
      this.init(currentTemplate);
    }
  }

  init = (currentTemplate) => {
    if (currentTemplate) {
      const {
        country,
        productType,
        versionNumber,
        versionName,
        versionDescription,
        title,
        subTitle
      } = currentTemplate;

      this.setState({
        country,
        productType,
        versionNumber,
        versionName,
        versionDescription,
        title,
        subTitle
      });
    }
  };

  handleSelect = (key, info) => {
    const { chapter, section, requirement, subRequirement, pos } = info.node.props;

    this.setState({ selectedKeys: key });

    let currentChapter = null;
    let currentSection = null;
    let currentRequirement = null;
    let currentSubRequirement = null;

    if (chapter) currentChapter = chapter;
    if (section) currentSection = section;
    if (requirement) currentRequirement = requirement;
    if (subRequirement) currentSubRequirement = subRequirement;

    actions.forms.change({
      currentSubRequirement,
      currentRequirement,
      currentSection,
      currentChapter,
      pos,
    });
  };

  createTemplate = () => {
    const {
      country,
      productType,
      versionNumber,
      versionName,
      versionDescription,
      title,
      subTitle
    } = this.state;

    actions.forms
      .create({
        country,
        productType,
        versionNumber,
        versionName,
        versionDescription,
        title,
        subTitle
      })
      .then((err) => {
        if (!err) {
          const { history, currentTemplate } = this.props;

          history.push(`${config.rootRoute}/forms/${currentTemplate._id}`);
        }
      });
  };

  updateTemplate = () => {
    const { currentTemplate } = this.props;
    const id = currentTemplate._id;
    const template = { ...currentTemplate, ...this.state };

    actions.forms.updateVersion(id, template);
  };

  deleteChapter = (deleting) => {
    this.setState({ deleting });

    actions.forms
      .deleteChapter()
      .then(() => this.setState({ deleting: null }))
      .catch(() => this.setState({ deleting: null }));
  };

  deleteSection = (deleting) => {
    this.setState({ deleting });

    actions.forms
      .deleteSection()
      .then(() => this.setState({ deleting: null }))
      .catch(() => this.setState({ deleting: null }));
  };

  deleteRequirement = (deleting) => {
    this.setState({ deleting });

    actions.forms
      .deleteRequirement()
      .then(() => this.setState({ deleting: null }))
      .catch(() => this.setState({ deleting: null }));
  };

  deleteSubRequirement = (deleting) => {
    this.setState({ deleting });

    actions.forms
      .deleteSubRequirement()
      .then(() => this.setState({ deleting: null }))
      .catch(() => this.setState({ deleting: null }));
  };

  viewAddChapter = () => {
    actions.forms.change({
      currentChapter: null,
      currentSection: null,
      currentRequirement: null,
      currentSubRequirement: null,
    });
    this.setState({ selectedKeys: [] });
  };

  render() {
    const {
      loading,
      currentTemplate,
      chapters,
      match: { path },
      history,
      limitCountries,
      availableCountries
    } = this.props;

    const {
      country,
      productType,
      versionNumber,
      versionName,
      versionDescription,
      title,
      subTitle,
      selectedKeys,
      deleting,
    } = this.state;

    const isClonePage = path === '/forms/clone/:id';
    let countries = allCountriesWithEU;

    if (limitCountries) countries = countries.filter(({ alpha3code }) => availableCountries.includes(alpha3code));

    return (
      <Card
        title={
          <div className={styles.cardTitle}>
            <div>Form parameters</div>

            {currentTemplate && (
              <Button
                type='primary'
                onClick={() =>
                  isClonePage
                    ? history.push(`${config.rootRoute}/forms/${currentTemplate._id}`)
                    : history.push(`${config.rootRoute}/forms/clone/${currentTemplate._id}`)
                }
              >
                {isClonePage ? 'Edit' : 'Clone'}
              </Button>
            )}
          </div>
        }
        style={{ marginBottom: 24 }}
        bordered={false}
      >
        <Select
          showSearch
          placeholder='Select country'
          value={country}
          onChange={(countryName) => this.setState({ country: countryName, notifiedBody: undefined })}
          style={{ width: ' 100%', marginBottom: 20 }}
          disabled={!!currentTemplate}
        >
          {countries.map(({ alpha3code, name }) => (
            <Option key={alpha3code} value={alpha3code}>
              {name}
            </Option>
          ))}
        </Select>

        <Select
          showSearch
          placeholder='Select product type'
          value={productType}
          onChange={(type) => this.setState({ productType: type })}
          style={{ width: ' 100%', marginBottom: 20 }}
          disabled={!!currentTemplate}
        >
          {productTypes.map(({ id, name }) => (
            <Option key={id} value={id}>
              {name}
            </Option>
          ))}
        </Select>

        <Input
          type='text'
          placeholder='Version Number'
          value={versionNumber}
          onChange={(e) => this.setState({ versionNumber: e.target.value })}
          style={{ width: ' 100%', marginBottom: 20 }}
          disabled={!!currentTemplate?.versionNumber}
        />

        <Input
          type='text'
          placeholder='Version Name'
          value={versionName}
          onChange={(e) => this.setState({ versionName: e.target.value })}
          style={{ width: ' 100%', marginBottom: 20 }}
          disabled={!!currentTemplate?.versionName}
        />

        <Input.TextArea
          autosize={{ minRows: 2 }}
          placeholder='Version Description'
          value={versionDescription}
          onChange={(e) => this.setState({ versionDescription: e.target.value })}
          style={{ width: ' 100%', marginBottom: 20 }}
          disabled={!!currentTemplate?.versionDescription}
        />

        <Input.TextArea
          autosize={{ minRows: 2 }}
          placeholder='Form Title'
          value={title}
          onChange={(e) => this.setState({ title: e.target.value })}
          style={{ width: ' 100%', marginBottom: 20 }}
          disabled={!!currentTemplate?.title}
        />

        <Input.TextArea
          autosize={{ minRows: 2 }}
          placeholder='Form SubTitle'
          value={subTitle}
          onChange={(e) => this.setState({ subTitle: e.target.value })}
          style={{ width: ' 100%', marginBottom: 20 }}
          disabled={!!currentTemplate?.subTitle}
        />

        {!currentTemplate && (
          <Button
            type='primary'
            icon='check'
            onClick={() => this.createTemplate()}
            loading={loading}
            disabled={!country || !productType}
            style={{ marginBottom: 15 }}
          >
            Save
          </Button>
        )}

        {!!currentTemplate && (
          <Button
            type='primary'
            icon='check'
            onClick={() => this.updateTemplate()}
            loading={loading}
            disabled={
              !!currentTemplate?.versionName
              && !!currentTemplate?.versionNumber
              && !!currentTemplate?.versionDescription
              && !!currentTemplate?.title
              && !!currentTemplate?.subTitle
            }
            style={{ marginBottom: 15 }}
          >
            Update
          </Button>
        )}

        <div style={{ borderTop: '1px solid #e8e8e8', margin: '5px -24px' }} />

        {currentTemplate && (
          <div style={{ margin: '10px 0', fontSize: 16 }}>
            Chapters count - {chapters?.length}
          </div>
        )}

        {!isClonePage && (
          <Button
            type='primary'
            icon='plus'
            onClick={() => this.viewAddChapter()}
            style={{ margin: '10px 0' }}
          >
            Add chapter
          </Button>
        )}

        {chapters?.filter(c => !!c).length > 0 && (
          <Tree
            showLine
            onSelect={(key, info) => this.handleSelect(key, info)}
            defaultExpandAll
            selectedKeys={selectedKeys}
            disabled={isClonePage || loading}
            draggable
          >
            {chapters?.filter(c => !!c).map((c) => (
              <TreeNode
                key={c?._id}
                title={
                  <span className='b-hover'>
                    {c?.name}&nbsp;

                    {!isClonePage && (
                      <Popconfirm
                        title='Are you sure you want to delete the chapter and all the included data?'
                        okText='Confirm'
                        cancelText='Cancel'
                        onConfirm={() => this.deleteChapter(c?._id)}
                        placement='topLeft'
                      >
                        <a href='#!'>
                          <Icon type={deleting === c?._id ? 'loading' : 'minus-circle'} style={{ fontSize: 14 }} />
                        </a>
                      </Popconfirm>
                    )}
                  </span>
                }
                chapter={c}
              >
                {c?.sections?.map((s) => (
                  <TreeNode
                    key={s._id}
                    title={
                      <span className='b-hover'>
                        {s.id || s.prefix || s.name}&nbsp;

                        {!isClonePage && (
                          <Popconfirm
                            title='Are you sure you want to delete the section and all the included requirements?'
                            okText='Confirm'
                            cancelText='Cancel'
                            onConfirm={() => this.deleteSection(s._id)}
                            placement='topLeft'
                          >
                            <a href='#!'>
                              <Icon type={deleting === s._id ? 'loading' : 'minus-circle'} style={{ fontSize: 14 }} />
                            </a>
                          </Popconfirm>
                        )}
                      </span>
                    }
                    section={s}
                  >
                    {s.requirements?.map((r) => (
                      <TreeNode
                        key={r._id}
                        title={
                          <span className='b-hover'>
                            {r.id || r.prefix}&nbsp;

                            {!isClonePage && (
                              <Popconfirm
                                title='Are you sure you want to delete the requirement?'
                                okText='Confirm'
                                cancelText='Cancel'
                                onConfirm={() => this.deleteRequirement(r._id)}
                                placement='topLeft'
                              >
                                <a href='#!'>
                                  <Icon type={deleting === r._id ? 'loading' : 'minus-circle'} style={{ fontSize: 14 }} />
                                </a>
                              </Popconfirm>
                            )}
                          </span>
                        }
                        requirement={r}
                      >
                        {r.subRequirements?.map((sr) => (
                          <TreeNode
                            key={sr._id}
                            title={
                              <span className='b-hover'>
                                {sr.id || sr.prefix}&nbsp;

                                {!isClonePage && (
                                  <Popconfirm
                                    title='Are you sure you want to delete the sub requirement?'
                                    okText='Confirm'
                                    cancelText='Cancel'
                                    onConfirm={() =>
                                      this.deleteSubRequirement(sr._id)
                                    }
                                    placement='topLeft'
                                  >
                                    <a href='#!'>
                                      <Icon type={deleting === sr._id ? 'loading' : 'minus-circle'} style={{ fontSize: 14 }} />
                                    </a>
                                  </Popconfirm>
                                )}
                              </span>
                            }
                            subRequirement={sr}
                          />
                        ))}
                      </TreeNode>
                    ))}
                  </TreeNode>
                ))}
              </TreeNode>
            ))}
          </Tree>
        )}
      </Card>
    );
  }
}
