import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import config from 'config';
import { Divider, Button, Table, Card, Popconfirm, Icon, Select, Tooltip } from 'antd';
import Breadcrumb from '../../components/Breadcrumb';
import { getObjectFromArray, productTypes } from './utils';
import { allCountriesWithEU } from '../../utils/countries';
import actions from '../../actions';
import styles from './index.less';

const productTypesObject = getObjectFromArray(productTypes);

const { Option } = Select;

@connect(({ forms, account }) => ({
  list: forms.list,
  loading: forms.loading,
  filtersData: forms.filtersData,
  permissions: account.adminPermissions,
  availableCountries: account.adminCountriesForForm,
  limitCountries: account.adminLimitCountriesForForm,
}))

export default class Forms extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      country: undefined,
    };
  }

  componentDidMount() {
    actions.forms.getPage({ page: 1, filters: {} });
    actions.forms.getFiltersData();
  }

  onTableChange = (pagination, filters) => actions.forms.getPage({ page: pagination.current, filters });

  getColumns = () => {
    const { filtersData, permissions, limitCountries, availableCountries } = this.props;
    const { country } = this.state;

    const { existsCountries = [], existsProductsTypes = [] } = filtersData || {};
    const accessRemove = permissions.includes('accessFormRemoveTemplate');
    let countries = allCountriesWithEU;

    if (limitCountries) countries = countries.filter(({ alpha3code }) => availableCountries.includes(alpha3code));

    return [
      {
        title: 'Country',
        dataIndex: 'country',
        filterDropdown: ({ setSelectedKeys, confirm, clearFilters }) => (
          <div style={{ padding: 8 }}>
            <Select
              showSearch
              style={{ width: 188, marginBottom: 8, display: 'block' }}
              placeholder='Search country'
              onChange={(countryName) => {
                this.setState({ country: countryName });
                setSelectedKeys(countryName ? [countryName] : []);
                confirm();
              }}
              value={country}
            >
              {countries
                .filter((i) => existsCountries.includes(i.alpha3code))
                .map(({ alpha3code, name }) => (
                  <Option key={alpha3code} value={alpha3code}>
                    {name}
                  </Option>
                ))}
            </Select>

            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                onClick={() => {
                  clearFilters();
                  this.setState({ country: undefined });
                }}
                size='small'
                style={{ width: 90 }}
              >
                Reset
              </Button>
            </div>
          </div>
        ),
        filterIcon: (filtered) => <Icon type='search' style={{ color: filtered ? '#1890ff' : undefined }} />,
      },
      {
        title: 'Product type',
        dataIndex: 'productType',
        filters: productTypes
          .filter((i) => existsProductsTypes.includes(i.id))
          .map((i) => ({ text: i.name, value: i.id })),
        render: (key) => <span>{productTypesObject[key]}</span>,
      },
      {
        title: 'Version Number',
        dataIndex: 'versionNumber',
      },
      {
        title: 'Version Name',
        dataIndex: 'versionName',
      },
      {
        title: 'Version Description',
        dataIndex: 'versionDescription',
      },
      {
        title: 'Actions',
        render: (text, item) => {
          return (
            <div>
              <Link to={{ pathname: `${config.rootRoute}/forms/clone/${item._id}`, state: { item } }}>
                Clone
              </Link>

              <Divider type='vertical' />

              <Link to={{ pathname: `${config.rootRoute}/forms/${item._id}`, state: { item } }}>
                Edit
              </Link>

              <Divider type='vertical' />

              <a href='#!' onClick={() => this.handleRelease(item)}>
                {item?.is_released === 1 ? 'Unrelease' : 'Release'}
              </a>

              <Divider type='vertical' />

              <Tooltip title={!accessRemove && 'No permission'}>
                <Popconfirm
                  title='Delete the form template?'
                  onConfirm={() => this.deleteFormTemplate(item._id)}
                  okText='Confirm'
                  cancelText='Cancel'
                  disabled={!accessRemove}
                >
                  <a className={!accessRemove ? styles.disabled : ''}>Delete</a>
                </Popconfirm>
              </Tooltip>
            </div>
          );
        },
      },
      {
        title: 'Date Released',
        dataIndex: 'date_released',
        render: (item) => <span>{!!item && new Date(item)?.toLocaleDateString()}</span>
      },
    ];
  };

  handleRelease = (item) => actions.forms.updateRelease(item);

  deleteFormTemplate = (templateId) => actions.forms.delete(templateId);

  goToCreate = () => {
    const { history } = this.props;

    actions.forms.change({
      currentTemplate: null,
      currentChapter: null,
      chapters: [],
    });

    history.push(`${config.rootRoute}/forms/create`);
  };

  render() {
    const { list, loading } = this.props;
    const { docs, totalDocs, page, limit } = list;

    return (
      <div>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Forms' },
          ]}
        />

        <Card bordered={false}>
          <div className={styles.addButtonWrapper}>
            <Button
              type='primary'
              icon='plus'
              onClick={() => this.goToCreate()}
            >
              Create Form Template
            </Button>
          </div>

          <Table
            rowKey='_id'
            bordered
            loading={loading}
            columns={this.getColumns()}
            dataSource={docs || []}
            pagination={{
              showTotal: () => `Total ${totalDocs} items`,
              current: page,
              pageSize: limit,
              total: totalDocs,
            }}
            onChange={this.onTableChange}
          />
        </Card>
      </div>
    );
  }
}
