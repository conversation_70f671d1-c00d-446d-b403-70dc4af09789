import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Button, Input, Form, Select } from 'antd';
import actions from '../../../actions';

const { Option } = Select;
const FormItem = Form.Item;

@connect(({ regPlan }) => ({
  questions: regPlan.questions,
  loading: regPlan.modalLoading,
}))
@Form.create()

export default class addCondition extends PureComponent {
  componentDidMount() {
    actions.regPlan.getQuestions();
  }

  add = () => {
    const { form, questions, loading, onChange } = this.props;

    form.validateFields((err, data) => {
      if (!err && !loading) {
        const { questionId } = data;
        const question = questions.find(({ _id: id }) => id === questionId) || {};
        const { type, name } = question;

        if (onChange) onChange({ ...data, type, name });

        actions.regPlan.closeModal();
      }
    }
    );
  }

  render() {
    const { form, questions = [], loading } = this.props;
    const { getFieldDecorator, getFieldValue, setFieldsValue } = form;
    const formItemLayout = {
      labelCol: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
    };
    const questionId = getFieldValue('questionId');
    const condition = getFieldValue('condition');
    const question = questions.find(({ _id: id }) => id === questionId) || {};
    const { type, options } = question;
    const conditions = [];

    if (['input', 'checkbox', 'select'].includes(type)) {
      conditions.push('EMPTY');
      conditions.push('NOT_EMPTY');
      conditions.push('EQUALS');
      conditions.push('NOT_EQUALS');
    }

    if (['switch'].includes(type)) {
      conditions.push('ENABLED');
      conditions.push('DISABLED');
    }

    return (
      <Form {...formItemLayout}>
        <FormItem label='Question'>
          {getFieldDecorator('questionId', {
            rules: [
              {
                required: true,
                message: 'Please, select question',
              },
            ],
          })(
            <Select
              showSearch
              placeholder='Select question'
              style={{ width: '100%' }}
              disabled={loading}
              onChange={() => setFieldsValue({ condition: undefined })}
            >
              {questions.map(({ _id: id, name }) => (
                <Option key={id} value={id}>
                  {name}
                </Option>
              ))}
            </Select>
          )}
        </FormItem>

        {
          questionId && (
            <FormItem label='Condition'>
              {getFieldDecorator('condition', {
                rules: [{
                  required: true,
                  message: 'Please, select condition',
                }],
              })(
                <Select
                  showSearch
                  placeholder='Select condition'
                  style={{ width: '100%' }}
                  disabled={loading}
                >
                  {conditions.map((item) => (
                    <Option key={item} value={item}>
                      {item}
                    </Option>
                  ))}
                </Select>
              )}
            </FormItem>
          )
        }

        {
          ['input'].includes(type) && ['EQUALS', 'NOT_EQUALS'].includes(condition) && (
            <FormItem label='Value'>
              {getFieldDecorator('value')(
                <Input disabled={loading} placeholder='Value' autoComplete='off' />
              )}
            </FormItem>
          )
        }

        {
          ['checkbox', 'select'].includes(type) && ['EQUALS', 'NOT_EQUALS'].includes(condition) && options && (
            <FormItem label='Value'>
              {getFieldDecorator('value')(
                <Select
                  showSearch
                  placeholder='Select value'
                  style={{ width: '100%' }}
                  disabled={loading}
                >
                  {options.map(({ text, value }) => (
                    <Option key={value} value={value}>
                      {text}
                    </Option>
                  ))}
                </Select>
              )}
            </FormItem>
          )
        }

        <div style={{ paddingLeft: 92 }}>
          <Button
            type='primary'
            onClick={() => this.add()}
            loading={loading}
            disabled={loading || (!questionId && !condition)}
          >
            Add
          </Button>
        </div>
      </Form>
    );
  }
}
