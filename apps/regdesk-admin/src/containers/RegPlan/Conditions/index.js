import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Modal } from 'antd';
import actions from '../../../actions';
import Form from './Form';

@connect(({ regPlan }) => ({
  showModal: regPlan.showModal,
}))

export default class AddCondition extends PureComponent {
  render() {
    const { showModal, ...props } = this.props;

    return (
      <Modal
        title='Add condition'
        visible={showModal === 'addCondition'}
        footer={null}
        centered
        destroyOnClose
        maskClosable={false}
        onCancel={actions.regPlan.closeModal}
        onClose={actions.regPlan.closeModal}
        width={600}
        zIndex={999}
      >
        {showModal === 'addCondition' && <Form {...props} />}
      </Modal>
    );
  }
}
