import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { <PERSON>, <PERSON>, I<PERSON>, Popconfirm, Button, Empty, Divider, Tooltip } from 'antd';
import actions from '../../actions';

const { TreeNode } = Tree;

@connect(({ regPlan }) => ({
  loading: regPlan.loading,
  sections: regPlan.sections,
  wizardId: regPlan.wizardId,
}))

export default class RegPlanFields extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      selectedKeys: [],
    };
  }

  /**
   * Handle Select
   * @param key
   * @param info
   */
  handleSelect = (key, info) => {
    const { section = {}, question = {} } = info.node.props;
    const { _id: sectionId } = section;
    const { _id: questionId, subSectionId } = question;
    const props = {
      currentSection: null,
      currentQuestion: null,
      currentSubSection: null,
      sectionId: null,
      subSectionId: null,
      questionId: null,
      showSection: false,
      showSubSection: false,
      showQuestion: false,
    };

    if (sectionId) {
      props.currentSection = section;
      props.sectionId = sectionId;
      props.showSection = true;
    }

    if (questionId && subSectionId) {
      props.currentSubSection = question;
      props.subSectionId = subSectionId;
      props.showSection = false;
      props.showSubSection = true;
    }

    if (questionId && !subSectionId) {
      props.currentQuestion = question;
      props.questionId = questionId;
      props.showSection = false;
      props.showQuestion = true;
    }

    this.setState({ selectedKeys: key });
    actions.regPlan.change(props);
  };

  /**
   * Show view Add section
   */
  addSection = () => {
    actions.regPlan.clean();
    actions.regPlan.change({ showSection: true });
    this.setState({ selectedKeys: [] });
  };

  /**
   * Add subSection
   * @param e
   * @param section
   */
  addSubSection = (e, section) => {
    this.stopEvent(e);
    actions.regPlan.clean();
    this.setSection(section);
    actions.regPlan.change({ showSubSection: true });
    this.setState({ selectedKeys: [] });
  }

  /**
   * Add question
   * @param e
   * @param section
   */
  addQuestion = (e, section) => {
    this.stopEvent(e);
    actions.regPlan.clean();
    this.setSection(section);
    actions.regPlan.change({ showQuestion: true });
    this.setState({ selectedKeys: [] });
  }

  /**
   * Stop Event
   * @param e
   */
  stopEvent = (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
  }

  /**
   * Set section
   * @param section
   */
  setSection = (section) => {
    const { _id: sectionId } = section;

    if (sectionId) actions.regPlan.change({ sectionId, currentSection: section });
  }

  /**
   * Format name
   * @param name
   * @returns {string|*}
   */
  formatName = name => name && name.length > 30 ? `${name.slice(0, 30)}...` : name;

  /**
   * Get Tree for Questions
   * @param section
   * @returns {unknown[]}
   */
  getTreeQuestions = (section) =>
    section.questions.map((question) => {
      const {
        name,
        questions,
        subSectionId,
        _id: questionId,
      } = question;

      return (
        <TreeNode
          key={questionId}
          title={
            <span className='b-hover'>
              {this.formatName(name)}

              {subSectionId && (
                <span>
                  <Divider type='vertical' />

                  <a onClick={(e) => this.addQuestion(e, { _id: subSectionId, ...questions })} href='#!'>
                    <Tooltip title='Add new question'>
                      <Icon type='question-circle' style={{ fontSize: 14 }} />
                    </Tooltip>
                  </a>

                  <Divider type='vertical' />

                  <a onClick={(e) => this.addSubSection(e, { _id: subSectionId, ...questions })} href='#!'>
                    <Tooltip title='Add new section'>
                      <Icon type='branches' style={{ fontSize: 14 }} />
                    </Tooltip>
                  </a>
                </span>
              )}

              <Divider type='vertical' />

              <Popconfirm
                okText='Confirm'
                cancelText='Cancel'
                placement='topLeft'
                title={`Are you sure you want to delete the ${subSectionId ? 'subsection' : 'question'}?`}
                onConfirm={
                  subSectionId
                    ? () => actions.regPlan.deleteSubSection(subSectionId)
                    : () => actions.regPlan.deleteQuestion(questionId)
                }
              >
                <a onClick={this.stopEvent} href='#!'>
                  <Tooltip title='Delete question'>
                    <Icon type='minus-circle' style={{ fontSize: 14 }} />
                  </Tooltip>
                </a>
              </Popconfirm>
            </span>
          }
          question={question}
          section={section}
        >
          {questions && this.getTreeQuestions(question)}
        </TreeNode>
      );
    })

  getNewOrderedArray = (data, dropPosition, eventKey) => {
    if (!data.length) return [];

    let newData = [...data];
    const dragObjIndex = typeof newData[0] !== 'object'
      ? newData.indexOf(eventKey)
      : data.findIndex((i) => i._id === eventKey);
    const dragObj = newData.splice(dragObjIndex, 1);

    if (dropPosition === -1) {
      newData = [dragObj[0], ...newData];
    } else {
      const start = newData.slice(0, dropPosition);
      const end = newData.slice(dropPosition);

      newData = [...start, dragObj[0], ...end];
    }

    return newData;
  };

  onDrop = (info) => {
    const {
      dropPosition,
      node,
      dragNode: {
        props: { pos, eventKey, section },
      },
    } = info;
    const { wizardId } = this.props;
    const dragNodePos = pos.split('-');
    const nodePos = node.props.pos.split('-');
    const dragNodePosSliced = dragNodePos.slice(0, dragNodePos.length - 1);
    const nodePosSliced = nodePos.slice(0, nodePos.length - 1);
    const dragNodeLevel = dragNodePos.length - 1;
    const nodeLevel = nodePos.length - 1;

    if (dragNodeLevel === 1 && nodeLevel === 1) {
      const { sections } = this.props;
      const sectionsIds = this.getNewOrderedArray(sections, dropPosition, eventKey);

      actions.regPlan.updateWizard({ sectionsIds, wizardId });
    } else if (
      dragNodeLevel === 2
      && nodeLevel === 2
      && dragNodePos[1] === nodePos[1]
    ) {
      const questions = this.getNewOrderedArray(section.questions, dropPosition, eventKey);

      actions.regPlan.updateSection({ sectionId: section._id, questions });
    } else if (
      dragNodeLevel > 2
      && nodeLevel > 2
      && dragNodeLevel === nodeLevel
      && dragNodePosSliced.length === nodePosSliced.length
      && dragNodePosSliced.every((val, index) => val === nodePosSliced[index])
    ) {
      const questions = this.getNewOrderedArray( section.questions, dropPosition, eventKey);

      actions.regPlan.updateSection({ sectionId: section._id, questions });
    }
  };

  render() {
    const { loading, sections = [] } = this.props;

    return (
      <Card
        title='Sections and questions'
        style={{ marginBottom: 24 }}
        bordered={false}
      >
        <Button
          type='primary'
          icon='plus'
          onClick={this.addSection}
          style={{ margin: '10px 0', width: '100%' }}
        >
          Add section
        </Button>

        {sections.length === 0 && <Empty />}

        <Tree
          showLine
          draggable
          defaultExpandAll
          disabled={loading}
          onDrop={this.onDrop}
          onSelect={this.handleSelect}
          selectedKeys={this.state.selectedKeys}
        >
          {sections.map((section) => (
            <TreeNode
              key={section._id}
              section={section}
              title={
                <span className='b-hover'>
                  {this.formatName(section.name)}
                  <Divider type='vertical' />

                  <a onClick={(e) => this.addQuestion(e, section)} href='#!'>
                    <Tooltip title='Add new question'>
                      <Icon type='question-circle' style={{ fontSize: 14 }} />
                    </Tooltip>
                  </a>

                  <Divider type='vertical' />

                  <a onClick={(e) => this.addSubSection(e, section)} href='#!'>
                    <Tooltip title='Add new section'>
                      <Icon type='branches' style={{ fontSize: 14 }} />
                    </Tooltip>
                  </a>

                  <Divider type='vertical' />

                  <Popconfirm
                    title='Are you sure you want to delete the section and all the included questions?'
                    okText='Confirm'
                    cancelText='Cancel'
                    onConfirm={() => actions.regPlan.deleteSection(section._id)}
                    placement='topLeft'
                  >
                    <a onClick={this.stopEvent} href='#!'>
                      <Tooltip title='Delete'>
                        <Icon type='minus-circle' style={{ fontSize: 14 }} />
                      </Tooltip>
                    </a>
                  </Popconfirm>
                </span>
              }
            >
              {this.getTreeQuestions(section)}
            </TreeNode>
          ))}
        </Tree>
      </Card>
    );
  }
}
