import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { Form, Button, Input, Card, Select, Checkbox, Icon, Row, Col, Table, message } from 'antd';
import MDEditor from '../../components/MarkDownEditor';
import actions from '../../actions';
import ConditionsModal from './Conditions';
import { questionsScopes, questionsTypes, fileTypes, optionsIsUnique, linksIsNotEmpty, tableTypes, } from './utils';
import { LEGACY_DATE_FORMAT } from '../../utils/date';
import styles from './index.less';

const { Option } = Select;
const FormItem = Form.Item;

let id = 0;

@connect(({ regPlan }) => ({
  currentQuestion: regPlan.currentQuestion,
  questionIds: regPlan.questionIds,
  loading: regPlan.loading,
  sectionId: regPlan.sectionId,
  questionId: regPlan.questionId,
}))
@Form.create()

export default class RegPlanEditQuestion extends Component {
  constructor(props) {
    super(props);

    this.state = {
      required: false,
      description: '',
      selectFromExists: false,
      column: false,
      multiple: false,
      showTime: false,
      showIf: [],
    };
  }

  componentDidMount() {
    const { currentQuestion } = this.props;

    if (currentQuestion) {
      const {
        required = false,
        description = '',
        column = false,
        multiple = false,
        showTime = false,
        options,
        example,
        type,
        showIf = [],
      } = currentQuestion;

      this.setState({ required, description, column, multiple, showTime, showIf });

      if (type === 'upload' && example?.links?.length > 0) id = example?.links?.length - 1 || 0;
      else id = options.length - 1 || 0;
    }

    actions.regPlan.getQuestionIds();
  }

  componentWillReceiveProps(nextProps) {
    const { currentQuestion, questionId, form } = nextProps;
    const { questionId: oldQuestionId } = this.props;

    if (oldQuestionId !== questionId) {
      const {
        required = false,
        description = '',
        id,
        type,
        name,
        prefix = undefined,
        scope,
        hint,
        column = false,
        multiple = false,
        showTime,
        showIf = [],
      } = currentQuestion || {};
      const newData = {
        id,
        type,
        name,
        prefix,
        scope,
        hint,
      };

      this.setState({ required, description, column, multiple, showTime, showIf });
      form.setFieldsValue(newData);
    }

    if (oldQuestionId && !currentQuestion) {
      this.setState({
        required: false,
        description: '',
        column: false,
        multiple: false,
        showTime: false,
        showIf: [],
      });

      form.setFieldsValue({
        id: '',
        type: undefined,
        prefix: undefined,
        name: '',
        scope: undefined,
        hint: '',
      });
    }
  }

  addQuestion = () => {
    const { form, sectionId, questionId, loading } = this.props;

    form.validateFields(
      (
        err,
        {
          id,
          name,
          prefix,
          scope,
          type,
          hint,
          placeholder,
          format,
          fileType,
          maxFiles,
          maxFileSize,
          options,
          example,
          links,
          tableType,
          ...rest
        }
      ) => {
        if (!err && !loading) {
          const {
            description,
            required,
            column,
            multiple,
            showTime,
            showIf,
          } = this.state;
          const data = {
            id,
            name,
            prefix,
            description,
            scope,
            type,
            hint,
            required,
            placeholder,
            column,
            multiple,
            showTime,
            format,
            fileType,
            maxFiles,
            maxFileSize,
            example,
            sectionId,
            questionId,
            tableType,
            showIf,
          };

          if (options && options.length) {
            const optionsParams = Object.values(rest);

            if (optionsParams && optionsIsUnique(optionsParams)) data.options = optionsParams;
            else return message.warn('All options must be unique');
          }

          if (links && links.length) {
            const linksParams = Object.values(rest);

            if (linksIsNotEmpty(linksParams)) data.example = { ...data.example, links: linksParams };
            else return message.warn('Remove empty links');
          }

          if (questionId) actions.regPlan.updateQuestion(data);
          else actions.regPlan.addQuestion(data);
        }
      }
    );
  };

  removeOption = (k) => {
    const { form } = this.props;
    const options = form.getFieldValue('options');

    if (options.length === 1) return;

    form.setFieldsValue({ options: options.filter((key) => key !== k) });
  };

  addOption = () => {
    const { form } = this.props;
    const options = form.getFieldValue('options');
    const nextKeys = options.concat(++id);

    form.setFieldsValue({ options: nextKeys });
  };

  removeLink = (k) => {
    const { form } = this.props;
    const links = form.getFieldValue('links');

    if (links.length === 1) return;

    form.setFieldsValue({ links: links.filter((key) => key !== k) });
  };

  addLink = () => {
    const { form } = this.props;
    const links = form.getFieldValue('links');
    const nextKeys = links.concat(++id);

    form.setFieldsValue({ links: nextKeys });
  };

  getOption = (k, index) => {
    const { form, currentQuestion } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    const options = getFieldValue('options');

    return (
      <div key={k} className={styles.cloneItem} style={{ marginTop: 20 }}>
        <Col span={11}>
          <Form.Item label={index === 0 ? 'Label' : ''}>
            {getFieldDecorator(`[${k}].text`, {
              initialValue: currentQuestion?.options[k]
                ? currentQuestion.options[k].text
                : '',
            })(<Input placeholder='Label' autoComplete='off' />)}
          </Form.Item>
        </Col>

        <Col span={11}>
          <Form.Item label={index === 0 ? 'Value' : ''}>
            {getFieldDecorator(`[${k}].value`, {
              initialValue: currentQuestion?.options[k]
                ? currentQuestion.options[k].value
                : '',
            })(<Input placeholder='Value' autoComplete='off' />)}
          </Form.Item>
        </Col>

        <Col span={2} className={index === 0 ? styles.firstRemoveIcon : undefined}>
          {options.length > 1 && (
            <Icon
              className={styles.dynamicDeleteButton}
              type='minus-circle-o'
              onClick={() => this.removeOption(k)}
            />
          )}
        </Col>
      </div>
    );
  };

  _renderOptions = () => {
    const { currentQuestion } = this.props;
    const { getFieldDecorator, getFieldValue } = this.props.form;

    getFieldDecorator('options', {
      initialValue: currentQuestion?.options
        ? Array.from(Array(currentQuestion.options.length).keys())
        : [0],
    });

    const options = getFieldValue('options');
    const formItems = options.map((k, index) => this.getOption(k, index));

    return formItems;
  };

  getLink = (k, index) => {
    const { form, currentQuestion } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    const links = getFieldValue('links');

    return (
      <div key={k} className={styles.cloneItem} style={{ marginTop: 20 }}>
        <Col span={11}>
          <Form.Item label={index === 0 ? 'Text' : ''}>
            {getFieldDecorator(`[${k}].text`, {
              initialValue: currentQuestion?.example?.links[k]
                ? currentQuestion?.example?.links[k].text
                : '',
            })(<Input placeholder='Text' autoComplete='off' />)}
          </Form.Item>
        </Col>

        <Col span={11}>
          <Form.Item label={index === 0 ? 'Link' : ''}>
            {getFieldDecorator(`[${k}].link`, {
              initialValue: currentQuestion?.example?.links[k]
                ? currentQuestion?.example?.links[k].link
                : '',
            })(<Input placeholder='Link' autoComplete='off' />)}
          </Form.Item>
        </Col>

        <Col span={2} className={index === 0 ? styles.firstRemoveIcon : undefined}>
          {links.length > 1 && (
            <Icon
              className={styles.dynamicDeleteButton}
              type='minus-circle-o'
              onClick={() => this.removeLink(k)}
            />
          )}
        </Col>
      </div>
    );
  };

  _renderLinks = () => {
    const { currentQuestion } = this.props;
    const { getFieldDecorator, getFieldValue } = this.props.form;

    getFieldDecorator('links', {
      initialValue: currentQuestion?.example?.links
        ? Array.from(Array(currentQuestion?.example?.links.length).keys())
        : [],
    });

    const links = getFieldValue('links');
    const formItems = links.map((k, index) => this.getLink(k, index));

    return formItems;
  };

  onPrefixEnable = () => this.props.form.setFieldsValue({ prefix: '' });

  onPrefixDisable = () => this.props.form.setFieldsValue({ prefix: undefined });

  addCondition = (item) => {
    const { showIf = [] } = this.state;

    showIf.push(item);
    this.setState({ showIf });
  }

  removeCondition = (key) => {
    const { showIf = [] } = this.state;

    showIf.splice(key, 1);
    this.setState({ showIf });
  }

  /**
   * Format name
   * @param name
   * @returns {string|*}
   */
  formatName = name => name && name.length > 15 ? `${name.slice(0, 15)}...` : name;

  render() {
    const { form, currentQuestion, questionIds = [], loading } = this.props;
    const { getFieldDecorator, setFieldsValue, getFieldsValue, getFieldValue } = form;
    const currentPrefix = getFieldValue('prefix');
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    };
    const all = getFieldsValue();
    const { type: currentTypeValue } = all;
    const currentType = questionsTypes.find(t => currentTypeValue === t.value);
    const viewExtra = currentType && currentType.extra;
    const defFormat = viewExtra && currentType.extra.includes('format')
      ? currentType.defaultFormat
      : LEGACY_DATE_FORMAT;
    const {
      id,
      name,
      prefix,
      type,
      scope,
      hint,
      placeholder,
      maxFileSize,
      maxFiles,
      fileType,
      format,
      example,
      tableType,
    } = currentQuestion || {
      id: '',
      name: '',
      prefix: undefined,
      type: undefined,
      scope: undefined,
      hint: '',
      placeholder: '',
      maxFileSize: 500,
      maxFiles: 50,
      fileType: 'any',
      format: defFormat,
      example: {},
      tableType: undefined,
    };

    const {
      description,
      required,
      selectFromExists,
      column,
      multiple,
      showTime,
      showIf,
    } = this.state;

    const showIfData = showIf.map((data, key) => ({ ...data, id: key }));

    return (
      <Card
        title={
          <div className={styles.cardTitle}>
            <div>{currentQuestion ? 'Edit question' : 'Add question'}</div>
          </div>
        }
        bordered={false}
      >
        <Checkbox
          checked={selectFromExists}
          onChange={() => {
            this.setState((ps) => ({ selectFromExists: !ps.selectFromExists }));
            setFieldsValue({ id: undefined });
          }}
        >
          Select ID from exists
        </Checkbox>

        <Form {...formItemLayout}>
          <FormItem label='ID'>
            {getFieldDecorator('id', {
              rules: [{
                required: true,
                message: 'Please, enter ID',
              }],
              initialValue: id,
            })(
              selectFromExists ? (
                <Select
                  showSearch
                  placeholder='Select ID'
                  style={{ width: ' 100%' }}
                  disabled={loading}
                >
                  {questionIds.map((id) => (
                    <Option key={id} value={id}>
                      {id}
                    </Option>
                  ))}
                </Select>
              ) : (
                <Input
                  disabled={loading}
                  placeholder='Enter ID'
                  autoComplete='off'
                />
              )
            )}
          </FormItem>

          <FormItem label='Name'>
            {getFieldDecorator('name', {
              rules: [{
                required: true,
                message: 'Please, enter name',
              }],
              initialValue: name,
            })(
              <Input disabled={loading} placeholder='Name' autoComplete='off' />
            )}
          </FormItem>

          <FormItem label='Prefix'>
            {getFieldDecorator('prefix', {
              initialValue: prefix,
            })(
              <Input
                disabled={!!loading || currentPrefix === undefined}
                placeholder='Prefix...'
                autoComplete='off'
              />
            )}

            <Button
              style={{ marginRight: 10 }}
              disabled={!!loading}
              onClick={currentPrefix === undefined ? this.onPrefixEnable : this.onPrefixDisable}
            >
              {currentPrefix === undefined ? 'Enable' : 'Disable'}
            </Button>
          </FormItem>

          <FormItem label='Type'>
            {getFieldDecorator('type', {
              rules: [{
                required: true,
                message: 'Please, select type',
              }],
              initialValue: type,
            })(
              <Select
                showSearch
                placeholder='Select type'
                style={{ width: ' 100%' }}
                disabled={loading}
              >
                {questionsTypes.map(({ label, value }) => (
                  <Option key={value} value={value}>
                    {label}
                  </Option>
                ))}
              </Select>
            )}
          </FormItem>

          {viewExtra && currentType.extra.includes('tableType') && (
            <FormItem label='Table ID'>
              {getFieldDecorator('tableType', {
                initialValue: tableType,
              })(
                <Select
                  showSearch
                  placeholder='Select table ID'
                  style={{ width: ' 100%' }}
                  disabled={loading}
                >
                  {tableTypes.map(({ label, value }) => (
                    <Option key={value} value={value}>
                      {label}
                    </Option>
                  ))}
                </Select>
              )}
            </FormItem>
          )}

          {viewExtra && currentType.extra.includes('placeholder') && (
            <FormItem label='Placeholder'>
              {getFieldDecorator('placeholder', {
                initialValue: placeholder,
              })(
                <Input
                  disabled={loading}
                  placeholder='Placeholder'
                  autoComplete='off'
                />
              )}
            </FormItem>
          )}

          {viewExtra && currentType.extra.includes('format') && (
            <FormItem label='Format'>
              {getFieldDecorator('format', {
                initialValue: format,
              })(
                <Input
                  disabled={loading}
                  placeholder={LEGACY_DATE_FORMAT}
                  autoComplete='off'
                />
              )}
            </FormItem>
          )}

          {viewExtra && currentType.extra.includes('maxFileSize') && (
            <FormItem label='Max file size (Kb)'>
              {getFieldDecorator('maxFileSize', {
                initialValue: maxFileSize || 500,
              })(
                <Input
                  disabled={loading}
                  type='number'
                  placeholder='500'
                  autoComplete='off'
                />
              )}
            </FormItem>
          )}

          {viewExtra && currentType.extra.includes('column') && (
            <FormItem
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 8, offset: 4 }}
            >
              <Checkbox
                checked={column}
                onChange={() => this.setState((ps) => ({ column: !ps.column }))}
              >
                Column layout
              </Checkbox>
            </FormItem>
          )}

          {viewExtra && currentType.extra.includes('multiple') && (
            <FormItem
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 8, offset: 4 }}
            >
              <Checkbox
                checked={multiple}
                onChange={() => this.setState((ps) => ({ multiple: !ps.multiple }))}
              >
                Allow multiple
              </Checkbox>
            </FormItem>
          )}

          {viewExtra && currentType.extra.includes('maxFiles') && multiple && (
            <FormItem label='Max files'>
              {getFieldDecorator('maxFiles', {
                initialValue: maxFiles || 50,
              })(
                <Input
                  disabled={loading}
                  type='number'
                  placeholder='50'
                  autoComplete='off'
                />
              )}
            </FormItem>
          )}

          {viewExtra && currentType.extra.includes('showTime') && (
            <FormItem labelCol={{ span: 4 }} wrapperCol={{ span: 8, offset: 4 }}>
              <Checkbox
                checked={showTime}
                onChange={() => this.setState((ps) => ({ showTime: !ps.showTime }))}
              >
                Show time
              </Checkbox>
            </FormItem>
          )}

          {viewExtra && currentType.extra.includes('options') && (
            <FormItem label='Options'>
              <Row gutter={24}>{this._renderOptions()}</Row>

              <Button
                type='dashed'
                onClick={this.addOption}
                style={{ width: '100%' }}
                disabled={loading}
              >
                <Icon type='plus' /> Add option
              </Button>
            </FormItem>
          )}

          {viewExtra && currentType.extra.includes('fileType') && (
            <FormItem label='File type'>
              {getFieldDecorator('fileType', {
                initialValue: fileType,
              })(
                <Select
                  showSearch
                  placeholder='Select file type'
                  style={{ width: ' 100%' }}
                  disabled={loading}
                >
                  {fileTypes.map(({ label, value }) => (
                    <Option key={value} value={value}>
                      {label}
                    </Option>
                  ))}
                </Select>
              )}
            </FormItem>
          )}

          {viewExtra && currentType.extra.includes('example') && (
            <Fragment>
              <FormItem label='Example text'>
                {getFieldDecorator('example.text', {
                  initialValue: example.text,
                })(
                  <Input
                    disabled={loading}
                    placeholder='Example text'
                    autoComplete='off'
                  />
                )}
              </FormItem>

              <FormItem label='Example links'>
                <Row gutter={24}>{this._renderLinks()}</Row>

                <Button
                  type='dashed'
                  onClick={this.addLink}
                  style={{ width: '100%' }}
                  disabled={loading}
                >
                  <Icon type='plus' /> Add link
                </Button>
              </FormItem>
            </Fragment>
          )}

          <FormItem label='Scope'>
            {getFieldDecorator('scope', {
              initialValue: scope,
            })(
              <Select
                showSearch
                placeholder='Select scope'
                style={{ width: ' 100%' }}
                disabled={loading}
              >
                {questionsScopes.map(({ label, value }) => (
                  <Option key={value} value={value}>
                    {label}
                  </Option>
                ))}
              </Select>
            )}
          </FormItem>

          <FormItem label='Hint'>
            {getFieldDecorator('hint', {
              initialValue: hint,
            })(
              <Input disabled={loading} placeholder='Hint' autoComplete='off' />
            )}
          </FormItem>

          <FormItem labelCol={{ span: 4 }} wrapperCol={{ span: 8, offset: 4 }}>
            <Checkbox
              checked={required}
              onChange={() => this.setState((ps) => ({ required: !ps.required }))}
            >
              The question is required
            </Checkbox>
          </FormItem>

          <MDEditor
            label='Description'
            setValue={(value) => this.setState({ description: value })}
            value={description}
          />

          <div style={{ display: 'flex', margin: '40px 0 10px', justifyContent: 'space-between' }}>
            <span>Show a question by condition</span>

            <Button
              size='small'
              type='primary'
              onClick={() => actions.regPlan.change({ showModal: 'addCondition' })}
            >
              Add
            </Button>
          </div>

          <Table
            style={{ marginBottom: 30 }}
            size='small'
            rowKey='id'
            pagination={false}
            columns={[
              {
                title: 'Question',
                dataIndex: 'name',
                render: (value) => this.formatName(value)
              },
              {
                title: 'Key',
                dataIndex: 'key',
              },
              {
                title: 'Condition',
                dataIndex: 'condition',
              },
              {
                title: 'Value',
                dataIndex: 'value',
              },
              {
                title: 'Actions',
                dataIndex: 'id',
                render: (key) => <a onClick={() => this.removeCondition(key)}>Remove</a>
              },
            ]}
            dataSource={showIfData}
          />

          <FormItem>
            <Button
              type='primary'
              onClick={() => this.addQuestion()}
              loading={loading}
              disabled={loading}
            >
              {currentQuestion ? 'Save' : 'Add'}
            </Button>
          </FormItem>
        </Form>

        <ConditionsModal onChange={this.addCondition} />
      </Card>
    );
  }
}
