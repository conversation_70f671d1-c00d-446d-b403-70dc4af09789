import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Form, Button, Input, Card, Popconfirm } from 'antd';
import actions from '../../actions';

const FormItem = Form.Item;

@connect(({ regPlan }) => ({
  currentSection: regPlan.currentSection,
  loading: regPlan.loading,
  wizardId: regPlan.wizardId,
  sectionId: regPlan.sectionId,
}))
@Form.create()

export default class RegPlanEditSection extends Component {
  /**
   * Update cmp
   * @param nextProps
   */
  componentWillReceiveProps(nextProps) {
    const { currentSection, sectionId, form } = nextProps;
    const { sectionId: oldSectionId } = this.props;

    if (oldSectionId !== sectionId) {
      const { description = '', name, prefix = undefined } = currentSection || {};

      form.setFieldsValue({ name, description, prefix });
    }
  }

  /**
   * Send form
   */
  addSection = () => {
    const { form, currentSection, wizardId, sectionId, loading } = this.props;

    form.validateFields((err, { name, description, prefix }) => {
      if (!err && !loading) {
        if (!currentSection) actions.regPlan.addSection({ name, description, prefix, wizardId });
        else actions.regPlan.updateSection({ name, description, prefix, sectionId });
      }
    });
  };

  render() {
    const { form, currentSection, sectionId, loading } = this.props;
    const { getFieldDecorator, getFieldValue, setFieldsValue } = form;
    const currentPrefix = getFieldValue('prefix');
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    };
    const {
      name = '',
      description = '',
      prefix = undefined
    } = currentSection || {};

    return (
      <Card
        bordered={false}
        style={{ marginBottom: 20 }}
        title={currentSection ? 'Edit section' : 'Add section'}
      >
        <Form {...formItemLayout}>
          <FormItem label='Name'>
            {getFieldDecorator('name', {
              rules: [{
                required: true,
                message: 'Please, enter section name',
              }],
              initialValue: name,
            })(
              <Input
                autoFocus
                disabled={loading}
                placeholder='Section name...'
                autoComplete='off'
              />
            )}
          </FormItem>

          <FormItem label='Prefix'>
            {getFieldDecorator('prefix', {
              initialValue: prefix,
            })(
              <Input
                disabled={loading || currentPrefix === undefined}
                placeholder='Prefix...'
                autoComplete='off'
              />
            )}

            <Button
              style={{ marginRight: 10 }}
              disabled={loading}
              onClick={() => setFieldsValue({ prefix: currentPrefix === undefined ? '' : undefined })}
            >
              {currentPrefix === undefined ? 'Enable' : 'Disable'}
            </Button>
          </FormItem>

          <FormItem label='Description'>
            {getFieldDecorator('description', {
              initialValue: description,
            })(
              <Input.TextArea
                disabled={loading}
                placeholder='Description...'
                autoComplete='off'
                rows={4}
              />
            )}
          </FormItem>

          <FormItem wrapperCol={{ offset: 4 }}>
            {currentSection && (
              <Popconfirm
                title='Are you sure you want to delete the section and all the included questions?'
                okText='Confirm'
                cancelText='Cancel'
                onConfirm={() => actions.regPlan.deleteSection(sectionId)}
              >
                <Button
                  style={{ marginRight: 10 }}
                  loading={loading}
                  disabled={loading}
                >
                  Delete
                </Button>
              </Popconfirm>
            )}

            <Button
              type='primary'
              onClick={this.addSection}
              loading={loading}
              disabled={loading}
            >
              {currentSection ? 'Save' : 'Add'}
            </Button>
          </FormItem>
        </Form>
      </Card>
    );
  }
}
