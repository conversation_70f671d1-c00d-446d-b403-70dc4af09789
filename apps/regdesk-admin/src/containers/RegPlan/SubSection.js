import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Form, Button, Input, Card, Popconfirm } from 'antd';
import actions from '../../actions';

const FormItem = Form.Item;

@connect(({ regPlan }) => ({
  loading: regPlan.loading,
  currentSubSection: regPlan.currentSubSection,
  subSectionId: regPlan.subSectionId,
  wizardId: regPlan.wizardId,
  sectionId: regPlan.sectionId,
}))
@Form.create()

export default class RegPlanAddSubSection extends Component {
  /**
   * Update cmp
   * @param nextProps
   */
  componentWillReceiveProps(nextProps) {
    const { currentSubSection, form, subSectionId } = nextProps;
    const { subSectionId: oldSubSectionId } = this.props;

    if (subSectionId !== oldSubSectionId) {
      const { prefix = undefined, description = '', name } = currentSubSection || {};

      form.setFieldsValue({ prefix, name, description });
    }
  }

  /**
   * Update subSection
   */
  updateSubSection = () => {
    const { form, wizardId, sectionId, loading, subSectionId } = this.props;

    form.validateFields((err, { prefix, name, description }) => {
      if (!err && !loading) {
        if (subSectionId) {
          actions.regPlan.updateSection({
            name,
            prefix,
            description,
            sectionId: subSectionId,
          });
        }
        else {
          actions.regPlan.addSubSection({
            wizardId,
            name,
            prefix,
            parentId: sectionId,
            description,
          });
        }
      }
    });
  };

  render() {
    const { form, currentSubSection, loading, subSectionId } = this.props;
    const { getFieldDecorator, getFieldValue, setFieldsValue } = form;
    const currentPrefix = getFieldValue('prefix');
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    };
    const {
      name = '',
      description = '',
      prefix = undefined
    } = currentSubSection || {};

    return (
      <Card
        title={currentSubSection ? 'Edit sub section' : 'Add sub section'}
        bordered={false}
        style={{ marginBottom: 20 }}
      >
        <Form {...formItemLayout} name='editSubsection'>
          <FormItem label='Name'>
            {getFieldDecorator('name', {
              rules: [{
                required: true,
                message: 'Please, enter sub section name',
              }],
              initialValue: name,
            })(
              <Input
                autoFocus
                disabled={!!loading}
                placeholder='Sub section name...'
                autoComplete='off'
              />
            )}
          </FormItem>

          <FormItem label='Prefix'>
            {getFieldDecorator('prefix', {
              initialValue: prefix,
            })(
              <Input
                disabled={loading || currentPrefix === undefined}
                placeholder='Prefix...'
                autoComplete='off'
              />
            )}

            <Button
              style={{ marginRight: 10 }}
              disabled={loading}
              onClick={() => setFieldsValue({ prefix: currentPrefix === undefined ? '' : undefined })}
            >
              {currentPrefix === undefined ? 'Enable' : 'Disable'}
            </Button>
          </FormItem>

          <FormItem label='Description'>
            {getFieldDecorator('description', {
              initialValue: description,
            })(
              <Input.TextArea
                disabled={loading}
                placeholder='Description...'
                autoComplete='off'
                rows={4}
              />
            )}
          </FormItem>

          <FormItem wrapperCol={{ offset: 4 }}>
            {
              currentSubSection && (
                <Popconfirm
                  title='Are you sure you want to delete the sub section and all the included questions?'
                  okText='Confirm'
                  cancelText='Cancel'
                  onConfirm={() => actions.regPlan.deleteSubSection(subSectionId)}
                >
                  <Button
                    style={{ marginRight: 10 }}
                    loading={loading}
                    disabled={loading}
                  >
                    Delete
                  </Button>
                </Popconfirm>
              )
            }

            <Button
              type='primary'
              onClick={this.updateSubSection}
              loading={loading}
              disabled={loading}
            >
              {currentSubSection ? 'Save' : 'Add'}
            </Button>
          </FormItem>
        </Form>
      </Card>
    );
  }
}
