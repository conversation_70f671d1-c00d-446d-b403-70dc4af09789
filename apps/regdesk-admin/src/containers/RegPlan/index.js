import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { Row, Col, Spin } from 'antd';
import Breadcrumb from '../../components/Breadcrumb';
import EditSection from './Section';
import EditSubSection from './SubSection';
import EditQuestion from './Question';
import Tree from './Tree';
import actions from '../../actions';

@connect(({ regPlan }) => ({
  showSection: regPlan.showSection,
  showQuestion: regPlan.showQuestion,
  showSubSection: regPlan.showSubSection,
  wizardId: regPlan.wizardId,
  loading: regPlan.loading,
}))

export default class RegPlan extends PureComponent {
  componentDidMount() {
    actions.regPlan.get();
  }

  render() {
    const {
      loading,
      wizardId,
      showSection,
      showQuestion,
      showSubSection,
    } = this.props;

    return (
      <Spin spinning={loading}>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Regulatory Classification Plan' },
          ]}
        />

        <Row gutter={24} loading='true'>
          <Col xl={8} lg={8} md={24} sm={24} xs={24}>
            <Tree />
          </Col>

          {wizardId && (
            <Col xl={16} lg={16} md={24} sm={24} xs={24}>
              {showSection && <EditSection />}
              {showQuestion && <EditQuestion />}
              {showSubSection && <EditSubSection />}
            </Col>
          )}
        </Row>
      </Spin>
    );
  }
}
