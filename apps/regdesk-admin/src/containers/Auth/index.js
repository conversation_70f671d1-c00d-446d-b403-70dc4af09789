import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Form, Input, Button, Icon, Alert, message } from 'antd';
import { withTranslation } from 'react-i18next';

import { totalPasswordRules, checkSession2FA } from '../../utils/helpers';
import actions from '../../actions';
import styles from './index.less';

const FormItem = Form.Item;

@connect(({ account }) => ({
  su: account.su,
  role: account.role,
  msg: account.message,
  email: account.email,
  loading: account.loading,
  phone2FA: account.phone2FA,
  config2FA: account.config2FA,
  loading2FA: account.loading2FA,
  session2FA: account.session2FA,
  is2FAEnable: account.is2FAEnable,
}))
@Form.create()

class Login extends Component {
  /**
   * Login
   */
  onLogin = () => {
    const { form } = this.props;

    form.validateFields((err, values) => {
      if (!err) actions.auth.login(values);
    });
  };

  /**
   * Verify code for 2FA
   */
  onVerifyCode = () => {
    const { form, session2FA } = this.props;
    const { _id: sessionId } = session2FA || {};

    form.validateFields((err, values) => {
      if (!err) {
        actions.twoStep
          .verify({ code: values.code, action: 'auth', id: sessionId })
          .then(() => actions.session.check())
          .catch((error) => {
            if (error?.code === '2SV_WRONG_CODE') {
              actions.auth.change({ message: error.message });
            }

            actions.twoStep.getById(sessionId);
          });
      }
    });
  }

  /**
   * Resend code for 2FA
   */
  onResendCode = () => {
    const { session2FA } = this.props;
    const { _id: sessionId } = session2FA || {};

    actions.twoStep
      .resend({ id: sessionId })
      .then(({ block, code }) => {
        if (code === '2SV_NOT_FOUND') {
          this.onGoBack();

          return;
        }

        if (block === false) message.success('The code is sent');
      });
  }

  /**
   * Go back and reset everything(locally)
   */
  onGoBack = () => {
    actions.auth.change({
      message: '',
      is2FAEnable: false,
      phone2FA: undefined,
      session2FA: undefined
    });
  }

  render() {
    const {
      t,
      msg,
      role,
      email,
      loading,
      phone2FA,
      config2FA,
      loading2FA,
      session2FA,
      is2FAEnable,
      form: { getFieldDecorator }
    } = this.props;

    const { block } = session2FA || {};
    const { EXPIRED_CODE } = config2FA || {};

    let info;
    let form;
    let buttons;
    let alert = msg;

    if (is2FAEnable && session2FA) {
      alert = checkSession2FA({ config2FA, session2FA }) || alert;

      if (!session2FA.block) {
        info = (
          <Alert
            showIcon
            type='info'
            style={{ marginBottom: 24 }}
            message={`A text message with a verification code was sent to ${phone2FA}. The verification code is valid for the next ${EXPIRED_CODE ? EXPIRED_CODE / 60 / 1000 : '1'} minutes.`}
          />
        );
      }

      form = (
        <FormItem>
          {getFieldDecorator('code', {
            rules: [
              {
                required: true,
                message: 'Please fill field',
              },
              {
                pattern: /^\d{6}$/,
                message: 'Code is 6 digits long',
              },
            ],
          })(
            <Input
              autoFocus
              type='text'
              size='large'
              placeholder='Code'
              autoComplete='off'
              disabled={loading2FA || block}
              prefix={<Icon type='mobile' className={styles.prefixIcon} />}
              onKeyUp={(e) => {
                if (e.keyCode === 13) this.onVerifyCode();
              }}
            />
          )}
        </FormItem>
      );

      buttons = (
        <FormItem>
          <Button
            size='large'
            type='primary'
            loading={loading2FA}
            className={styles.submit}
            disabled={loading2FA || block}
            onClick={this.onVerifyCode}
          >
            Submit
          </Button>

          <div style={{ textAlign: 'center', marginTop: 16 }}>
            Didn't get the code?

            <Button
              type='link'
              style={{ paddingLeft: 5 }}
              disabled={loading2FA || block}
              onClick={this.onResendCode}
            >
              Resend
            </Button>
          </div>

          <div style={{ textAlign: 'center', marginTop: -10 }}>
            <Button
              type='link'
              onClick={this.onGoBack}
            >
              Back
            </Button>
          </div>
        </FormItem>
      );
    } else {
      form = [
        <FormItem key='key_field-email'>
          {getFieldDecorator('email', {
            rules: [
              {
                required: true,
                message: t('fillEmailField'),
              },
              {
                type: 'email',
                message: t('emailNotValid'),
              },
            ],
          })(
            <Input
              autoFocus
              size='large'
              disabled={loading}
              placeholder={t('Email')}
              prefix={<Icon type='mail' className={styles.prefixIcon} />}
            />
          )}
        </FormItem>,

        <FormItem key='key_field-password'>
          {getFieldDecorator('password', totalPasswordRules())(
            <Input.Password
              size='large'
              type='password'
              disabled={loading}
              placeholder={t('Password')}
              prefix={<Icon type='lock' className={styles.prefixIcon} />}
            />
          )}
        </FormItem>,
      ];

      buttons = (
        <FormItem>
          <Button
            size='large'
            type='primary'
            loading={loading}
            disabled={loading}
            className={styles.submit}
            onClick={this.onLogin}
          >
            {t('Login')}
          </Button>
        </FormItem>
      );
    }

    return (
      <div className={styles.container}>
        <Form className={styles.form}>
          <img
            alt=''
            src='https://www.regdesk.co/wp-content/uploads/2018/11/logo.png'
            title={t('Login')}
            className={styles.logo}
            style={{ marginTop: -20 }}
          />

          {email && role !== 'admin' && (
            <Alert
              style={{ marginBottom: 24 }}
              message={t('authAlertMessage')}
              type='warning'
              showIcon
            />
          )}

          {alert && !loading2FA && (
            <Alert
              style={{ marginBottom: 24 }}
              message={alert}
              type='error'
              showIcon
              closable
              onClose={() => actions.auth.change({ message: '' })}
            />
          )}

          {info}
          {form}
          {buttons}
        </Form>
      </div>
    );
  }
}

export default withTranslation()(Login);
