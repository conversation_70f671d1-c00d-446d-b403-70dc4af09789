import React from 'react';
import { connect } from 'react-redux';
import { Modal } from 'antd';

@connect(({ account }) => ({
  sessionExpires: account.sessionExpires,
}))

export default class ModalTimer extends React.Component {
  render() {
    const { sessionExpires = 0 } = this.props;
    const showTimerSec = 5 * 60; // sec
    let minutes = Math.trunc(sessionExpires / 60);
    let seconds = sessionExpires % 60;

    if (minutes < 0) {
      minutes = 0;
      seconds = 0;
    }

    return (
      <Modal
        width={300}
        style={{ paddingBottom: 0 }}
        bodyStyle={{ padding: 0 }}
        wrapClassName='modal-wrapper'
        visible={sessionExpires <= showTimerSec && sessionExpires > 0}
        title='Your session is about to expire'
        mask={false}
        footer={null}
        closable={false}
      >
        <div style={{ textAlign: 'center', fontSize: '3.5rem' }}>
          <span>
            {minutes}:{seconds < 10 ? `0${seconds}` : seconds}
          </span>
        </div>
      </Modal>
    );
  }
}
