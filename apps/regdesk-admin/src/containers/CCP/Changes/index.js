import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { Card, Table, Button, Input, Divider, Select, message, Tag, Popconfirm, Icon, Modal, Tooltip, List } from 'antd';
import Breadcrumb from '../../../components/Breadcrumb';
import api from '../../../utils/api';
import actions from '../../../actions';
import styles from '../../../components/Form/index.less';

@connect(({ ccpChanges, account }) => ({
  list: ccpChanges.list,
  permissions: account.adminPermissions,
}))

export default class CCPChanges extends PureComponent {
  constructor(props) {
    super(props);

    const { match, permissions } = this.props;

    this.isDT = !!match?.params?.dt;
    this.accessUpdate = permissions.includes(this.isDT ? 'accessCCPUpdateDTChanges' : 'accessCCPUpdateChanges');

    this.columns = [{
      title: 'Name',
      dataIndex: 'name',
      render: (name, record) => {
        if (record.editable) {
          return (
            <Input
              autoFocus
              value={name}
              placeholder='Name ...'
              onChange={e => {
                const newData = [...this.state.data];
                const target = this.getRowByKey(record.key);

                if (target) {
                  target.name = e.target.value;
                  this.setState({ data: newData });
                }
              }}
              onKeyPress={e => {
                if (e.key === 'Enter') this.saveRow(e, record.key);
              }}
            />
          );
        }

        return name;
      },
    }];

    if (this.isDT) {
      this.columns.push({
        title: 'Default Impact',
        dataIndex: 'decisionTreeDefaultImpact',
        width: 130,
        render: (impact, record) => {
          if (record.editable) {
            return (
              <Select
                value={impact}
                onChange={value => {
                  const newData = [...this.state.data];
                  const target = this.getRowByKey(record.key);

                  if (target) {
                    target.decisionTreeDefaultImpact = value;
                    this.setState({ data: newData });
                  }
                }}
                style={{ minWidth: 100 }}
              >
                <Select.Option value='perCountry'>Per Country</Select.Option>
                <Select.Option value='noImpact'>No Impact</Select.Option>
              </Select>
            );
          }

          if (impact === 'perCountry') return <Tag color='green'>Per Country</Tag>;
          if (impact === 'noImpact') return <Tag color='volcano'>No Impact</Tag>;

          return <Tag>No Data</Tag>;
        },
      });
    }

    this.columns.push(...[
      {
        title: (
          <div>
            Checklist subType
            <Tooltip title={this.accessUpdate ? 'Manage SubTypes' : 'No permission'}>
              <a>
                <Icon
                  type='edit'
                  style={{ marginLeft: 5, fontSize: 15 }}
                  className={!this.accessUpdate ? styles.disabled : ''}
                  onClick={() => this.accessUpdate && this.setState({ subTypeVisible: true })}
                />
              </a>
            </Tooltip>
          </div>
        ),
        dataIndex: 'subType',
        width: 250,
        render: (subType, record) => {
          if (record.editable) {
            return (
              <Select
                allowClear
                showSearch
                value={subType}
                placeholder='SubType...'
                style={{ minWidth: 150 }}
                loading={this.state.subTypeLoading}
                disabled={this.state.subTypeLoading}
                filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                onChange={(value) => {
                  const newData = [...this.state.data];
                  const target = this.getRowByKey(record.key);

                  if (target) {
                    target.subType = value;
                    this.setState({ data: newData });
                  }
                }}
              >
                {
                  this.state.subTypeList.map(({ _id: id, name }) => name && (
                    <Select.Option key={id} value={name}>{name}</Select.Option>
                  ))
                }
              </Select>
            );
          }

          return subType;
        },
      },
      {
        title: (
          <div>
            Tracking submission
            <Tooltip title={this.accessUpdate ? 'Manage Submissions' : 'No permission'}>
              <a>
                <Icon
                  type='edit'
                  style={{ marginLeft: 5, fontSize: 15 }}
                  className={!this.accessUpdate ? styles.disabled : ''}
                  onClick={() => this.accessUpdate && this.setState({ submissionVisible: true })}
                />
              </a>
            </Tooltip>
          </div>
        ),
        dataIndex: 'submission',
        width: 250,
        render: (submission, record) => {
          if (record.editable) {
            return (
              <Select
                allowClear
                showSearch
                value={submission}
                placeholder='Submission...'
                style={{ minWidth: 150 }}
                loading={this.state.submissionLoading}
                disabled={this.state.submissionLoading}
                filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                onChange={(value) => {
                  const newData = [...this.state.data];
                  const target = this.getRowByKey(record.key);

                  if (target) {
                    target.submission = value;
                    this.setState({ data: newData });
                  }
                }}
              >
                {
                  this.state.submissionList.map(({ _id: id, name }) => name && (
                    <Select.Option key={id} value={name}>{name}</Select.Option>
                  ))
                }
              </Select>
            );
          }

          return submission;
        },
      },
      {
        title: 'Actions',
        dataIndex: 'operation',
        width: 150,
        render: (text, record) => {
          if (record.editable) {
            if (record.isNew) {
              return (
                <span>
                  <a onClick={e => this.saveRow(e, record.key)}>Add</a>
                  <Divider type='vertical' />
                  <a onClick={() => this.remove(record.key)}>Cancel</a>
                </span>
              );
            }

            return (
              <span>
                <a onClick={e => this.saveRow(e, record.key)}>Save</a>
                <Divider type='vertical' />
                <a onClick={e => this.cancel(e, record.key)}>Cancel</a>
              </span>
            );
          }

          return (
            <span>
              <Tooltip title={!this.accessUpdate && 'No permission'}>
                <a className={!this.accessUpdate ? styles.disabled : ''} onClick={e => this.accessUpdate && this.toggleEditable(e, record.key)}>Update</a>
              </Tooltip>

              <Divider type='vertical' />

              <Tooltip title={!this.accessUpdate && 'No permission'}>
                <Popconfirm
                  title='Remove the project change ?'
                  onConfirm={() => this.delete(record.key)}
                  onCancel={() => {}}
                  okText='Yes'
                  cancelText='No'
                  disabled={!this.accessUpdate}
                >
                  <a className={!this.accessUpdate ? styles.disabled : ''} style={{ color: this.accessUpdate && 'red' }}>Delete</a>
                </Popconfirm>
              </Tooltip>
            </span>
          );
        },
      }
    ]);

    this.cacheOriginData = {};

    this.state = {
      loading: true,
      data: this.formatData(props.list),
      search: '',
      submissionList: [],
      submissionLoading: true,
      submissionValue: '',
      submissionVisible: false,
      subTypeList: [],
      subTypeLoading: true,
      subTypeValue: '',
      subTypeVisible: false,
    };
  }

  componentDidMount() {
    this.load();
    this.loadCache('submission');
    this.loadCache('subType');
  }

  componentWillReceiveProps(nextProps) {
    const newList = this.formatData(nextProps.list);

    this.state.data.forEach(item => {
      if (item.editable) {
        if (item.isNew) newList.push(item);
        else newList[this.findOne(newList, 'key', item.key)] = item;
      }
    });

    this.setState({
      data: newList,
      loading: false,
    });
  }

  /**
   * Load list
   */
  load = () => actions.ccpChanges.get();

  /**
   * Load cache
   * @param type
   * @returns {Promise<void>}
   */
  loadCache = (type) => {
    this.setState({ [`${type}Loading`]: true });

    return api.ccpCache
      .get({ type })
      .then(({ list = [] }) => this.setState({ [`${type}Loading`]: false, [`${type}List`]: list, [`${type}Value`]: '' }))
      .catch(() => this.setState({ [`${type}Loading`]: false }));
  };

  /**
   * Add cache
   * @param type
   */
  addCache = (type) => {
    this.setState({ [`${type}Loading`]: true });

    return api.ccpCache
      .add({ name: this.state[`${type}Value`], type })
      .then(() => this.loadCache(type))
      .then(() => message.success('Success'))
      .catch(() => this.setState({ [`${type}Loading`]: false }));
  };

  /**
   * Remove cache
   * @param id
   * @param type
   */
  removeCache = (id, type) => {
    this.setState({ [`${type}Loading`]: true });

    api.ccpCache
      .remove(id)
      .then(() => this.loadCache(type))
      .catch(() => this.setState({ [`${type}Loading`]: false }));
  };

  /**
   * Dropdown Input Field
   * @param type
   * @returns {JSX.Element}
   */
  getModal = (type) => (
    <Modal
      footer={null}
      width={400}
      title={`Manage ${type}`}
      visible={this.state[`${type}Visible`]}
      onCancel={() => this.setState({ [`${type}Visible`]: false })}
    >
      <div style={{ display: 'flex', marginBottom: 15 }}>
        <Input
          disabled={this.state[`${type}Loading`]}
          placeholder='Name...'
          value={this.state[`${type}Value`]}
          onChange={event => this.setState({ [`${type}Value`]: event.target.value })}
        />

        <Button
          type='primary'
          style={{ marginLeft: 15 }}
          loading={this.state[`${type}Loading`]}
          disabled={this.state[`${type}Loading`]}
          onClick={() => {
            if (!this.state[`${type}Value`]) message.error('Please enter a name');
            else this.addCache(type);
          }}
        >
          Add
        </Button>
      </div>

      <div style={{ maxHeight: 300, overflowY: 'scroll' }}>
        <List
          bordered
          size='small'
          loading={this.state[`${type}Loading`]}
          dataSource={this.state[`${type}List`]}
          renderItem={({ _id: id, name }) => (
            <List.Item>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                <span>{name}</span>

                <Tooltip title='Delete'>
                  <Icon
                    type='delete'
                    theme='twoTone'
                    disabled={this.state[`${type}Loading`]}
                    onClick={() => this.removeCache(id, type)}
                  />
                </Tooltip>
              </div>
            </List.Item>
          )}
        />
      </div>
    </Modal>
  );

  /**
   * Get Row by Key
   * @param key
   * @returns {*}
   */
  getRowByKey = key => this.state.data.find(item => item.key === key);

  /**
   * Toggle editable
   * @param e
   * @param key
   */
  toggleEditable = (e, key) => {
    e.preventDefault();

    const target = this.getRowByKey(key);

    if (target) {
      if (!target.editable) this.cacheOriginData[key] = { ...target };

      target.editable = !target.editable;
      this.setState({ data: [...this.state.data] });
    }
  };

  formatData = (list = []) => {
    return list.map((item) => {
      const { _id: key } = item;

      return { key, ...item };
    });
  };

  findOne = (list = [], keyField, key) => {
    for (let i = 0; i < list.length; i += 1) {
      if (list[i][keyField] === key) return i;
    }

    return -1;
  };

  remove = key => {
    const newData = this.state.data.filter(item => item.key !== key);

    this.setState({ data: newData });
  };

  /**
   * Add item
   */
  newItem = () => {
    const newData = [...this.state.data];

    newData.push({
      key: `${Date.now()}`,
      name: '',
      level: 'minor',
      minor: [],
      moderate: [],
      major: [],
      subType: '',
      submission: '',
      decisionTree: this.isDT,
      editable: true,
      isNew: true,
    });
    this.setState({ data: newData });
  };

  /**
   * Update table
   */
  updateTable = () => this.load().then(() => message.success('Data updated'));

  /**
   * Delete item
   * @param key
   */
  delete = (key) => {
    const { _id: id } = this.getRowByKey(key);

    this.setState({ loading: true });

    actions.ccpChanges
      .remove(id)
      .then(this.updateTable);
  }

  /**
   * Save new type
   * @param e
   * @param key
   */
  saveRow(e, key) {
    e.persist();

    if (e) {
      e.preventDefault();
    }

    if (this.clickedCancel) {
      this.clickedCancel = false;

      return;
    }

    const target = this.getRowByKey(key) || {};

    if (!target.name) {
      message.error('Please indicate the name');

      return;
    }

    delete target.editable;

    if (target.isNew) {
      delete target.isNew;

      actions.ccpChanges
        .add({ ...target })
        .then(this.updateTable);
    } else {
      const { _id: id, ...props } = target;

      actions.ccpChanges
        .update({ id, ...props })
        .then(this.updateTable);
    }

    this.setState({
      loading: true,
      data: [...this.state.data],
    });
  }

  cancel(e, key) {
    this.clickedCancel = true;
    e.preventDefault();

    const target = this.getRowByKey(key);

    if (this.cacheOriginData[key]) {
      Object.assign(target, this.cacheOriginData[key]);
      target.editable = false;
      delete this.cacheOriginData[key];
    }
    this.setState({ data: [...this.state.data] });
  }

  render() {
    const { data, loading } = this.state;
    const newData = data.filter(({ decisionTree }) => !!decisionTree === this.isDT);

    return (
      <div className={styles.container}>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Change Control Projects', href: `${config.rootRoute}/ccp` },
            { title: 'Project changes' },
          ]}
        />

        <Card bordered={false}>
          <Table
            loading={loading}
            rowKey='key'
            dataSource={newData}
            style={{ marginBottom: 16 }}
            columns={this.columns}
            pagination={false}
          />

          <Tooltip title={!this.accessUpdate && 'No permission'}>
            <Button
              block
              icon='plus'
              size='large'
              type='dashed'
              disabled={!this.accessUpdate}
              style={{ marginBottom: 8 }}
              onClick={this.newItem}
            >
              Add new
            </Button>
          </Tooltip>
        </Card>

        {this.getModal('subType')}
        {this.getModal('submission')}
      </div>
    );
  }
}
