import React from 'react';
import { <PERSON>, Icon, Switch, Popconfirm, Tooltip } from 'antd';
import { connect } from 'react-redux';
import events from '../../../utils/events';

const { TreeNode } = Tree;

@connect(({ ccp }) => ({
  loading: ccp.loading,
  item: ccp.item
}))

export default class Menu extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      expandedKeys: [],
    };
  }

  /**
   * Handle Select
   * @param key
   * @param info
   */
  handleSelect = (key, info) => {
    const { type, item, itemTitle, itemKey, onUpdate, onRemove, onAutoSave } = info.node.props;
    const { onSelect } = this.props;

    if (type || itemTitle) {
      onSelect({
        type,
        item,
        itemTitle,
        itemKey,
        onUpdate,
        onRemove,
        onAutoSave,
      });
    }
  };

  /**
   * Update
   * @param data
   * @returns {Promise<any>}
   */
  update = data => {
    const { onUpdate } = this.props;

    if (onUpdate) onUpdate(data);
  }

  getChangeControl = (key, title, data = {}) => {
    const { loading, decisionTree } = this.props;
    const { readyForReport = false } = data;

    const cmpTitle = (
      <span>
        {!decisionTree && (
          <Switch
            size='small'
            checked={readyForReport}
            style={{ marginRight: 10 }}
            disabled={loading}
            onClick={(checked, e) => {
              e.preventDefault();
              e.stopPropagation();
              events.emit('updateCCP', { newData: { readyForReport: checked } });
            }}
          />
        )}

        {title}

        {decisionTree && (
          <Popconfirm
            title='Are you sure that you want to clone and replace data for this section?'
            onConfirm={() => {
              const newData = this.props.item[key.slice(0, -2)];

              this.props.onSelect({ item: newData });
              this.update({ [key]: newData });
            }}
            okText='Yes'
            cancelText='No'
          >
            <Tooltip title='Clone and replace section'>
              <Icon type='copy' style={{ fontSize: 14, marginLeft: 5 }} />
            </Tooltip>
          </Popconfirm>
        )}
      </span>
    );

    return (
      <TreeNode
        title={cmpTitle}
        itemTitle={{ first: title }}
        item={data}
        type='form'
        onUpdate={(newData, props = {}) => {
          this.props.onSelect({ item: newData });

          this.update({ ...props, [key]: newData } );
        }}
        onAutoSave={newData => {
          this.update({ [key]: newData });
        }}
      />
    );
  }

  render() {
    const { item = {}, decisionTree } = this.props;

    const getName = name =>`${name}${decisionTree ? 'DT' : ''}`;
    const getItem = name => item[getName(name)] || {};

    return (
      <div>
        <Tree
          showLine
          onSelect={this.handleSelect}
          expandedKeys={this.state.expandedKeys}
          onExpand={expandedKeys => this.setState({ expandedKeys })}
          draggable
        >
          {this.getChangeControl(getName('updateTechnicalFile'), 'Update Technical File', getItem('updateTechnicalFile'))}
          {this.getChangeControl(getName('notificationRequired'), 'Notification Required', getItem('notificationRequired'))}
          {this.getChangeControl(getName('reRegistration'), 'Re-Registration', getItem('reRegistration'))}
        </Tree>
      </div>
    );
  }
}
