import React from 'react';
import { connect } from 'react-redux';
import {
  FormWithButtonTop,
  ItemRange,
  ItemNumber,
  ItemArRequired,
  ItemCheckbox,
  ItemInput,
  ItemArray,
  Links,
  Procedure,
  Timer,
  Changes,
} from '../../../../components/Form';
import events from '../../../../utils/events';
import actions from '../../../../actions';

@connect(({ ccp, ccpChanges }) => ({
  item: ccp.item,
  buffer: ccp.buffer,
  loading: ccp.loading,
  listChanges: ccpChanges.list,
  loadingChanges: ccpChanges.loading,
}))

class EditGeneral extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      data: props.data || {},
    };
  }

  /**
   * ComponentDidMount
   */
  componentDidMount() {
    const { autoSave } = this.props;

    if (autoSave) {
      window.addEventListener('beforeunload', this.beforeUnloadHandler);
      events.on('checkCCPForm', this.checkTimer);
    }

    events.on('updateCCP', this.eventUpdate);

    actions.ccpChanges.get();
  }

  /**
   * ComponentWillReceiveProps
   * @param nextProps
   */
  componentWillReceiveProps = nextProps => {
    const { onAutoSave, itemTitle, autoSave } = this.props;

    if (autoSave && itemTitle !== nextProps.itemTitle && onAutoSave) {
      const data = this.getValue();

      Timer.check(() => onAutoSave(data));
      Timer.stop();
    }

    this.setState({ data: nextProps.data || {} });
  };

  /**
   * ComponentWillUnmount
   */
  componentWillUnmount() {
    const { autoSave } = this.props;

    if (autoSave) {
      this.checkTimer();
      window.removeEventListener('beforeunload', this.beforeUnloadHandler);
      events.off('checkCCPForm', this.checkTimer);
    }

    events.off('updateCCP', this.eventUpdate);
  }

  eventUpdate = ({ newData = {}, props = {} }) => {
    const { onUpdate, autoSave } = this.props;

    if (autoSave) Timer.stop();

    const data = this.getValue();

    if (onUpdate) onUpdate({ ...data, ...newData }, props);
  }

  /**
   * Before Unload Handler
   * @param event
   */
  beforeUnloadHandler = (event) => {
    event.preventDefault();
    event.returnValue = true;
    this.checkTimer();
  };

  /**
   * Check timer
   */
  checkTimer = () => Timer.check(this.handleSubmit);

  handleSubmit = () => {
    const { onUpdate, autoSave } = this.props;

    if (autoSave) Timer.stop();

    const data = this.getValue();

    if (onUpdate) onUpdate(data);
  };

  getValue = () => {
    const generalInfo = {
      fee: this.refFee.getValue(),
      countryNomenclature: this.refCountryNomenclature.getValue(),
      govermentApprovalTime: this.refGovermentApprovalTime.getValue(),
      notificationDeadlineTime: this.refNotificationDeadlineTime.getValue(),
      notifyBefore: this.refNotificationDeadlineBefore.getValue(),
      canBeSold: this.refCanBeSold.getValue(),
      additionalInformation: this.refAdditionalInformation.getValue(),
      laws: this.refLaws.getValue(),
      classifications: this.refClassifications ? this.refClassifications.getValue() : [],
    };

    const procedure = this.refProcedures.getValue();
    const changes = this.refChanges.getValue();

    return { generalInfo, procedure, changes };
  };

  /**
   * Event Change
   */
  onChange = () => {
    const { autoSave } = this.props;

    if (autoSave) Timer.start(this.handleSubmit);
  }

  render() {
    const {
      generalInfo = {},
      procedure = [],
      changes = [],
    } = this.state.data;

    const {
      fee,
      countryNomenclature,
      govermentApprovalTime,
      notificationDeadlineTime,
      notifyBefore,
      canBeSold,
      additionalInformation,
      laws = [],
      classifications = [],
    } = generalInfo;

    const {
      item,
      buffer,
      loading,
      autoSave,
      countryId,
      itemTitle,
      listChanges,
      loadingChanges,
      accessRemoveData,
      decisionTreeMenu,
      showClassifications,
    } = this.props;

    const itemChanges = changes.map(({ changeId }) => changeId);
    const usedChanges = [
      ...item.updateTechnicalFile.changes.map(({ changeId }) => changeId),
      ...item.notificationRequired.changes.map(({ changeId }) => changeId),
      ...item.reRegistration.changes.map(({ changeId }) => changeId),
      ...item.updateTechnicalFileDT.changes.map(({ changeId }) => changeId),
      ...item.notificationRequiredDT.changes.map(({ changeId }) => changeId),
      ...item.reRegistrationDT.changes.map(({ changeId }) => changeId),
    ].filter((id) => !itemChanges.includes(id));

    return (
      <div>
        <FormWithButtonTop
          loading={loading}
          onSubmit={this.handleSubmit}
          accessRemoveData={accessRemoveData}
          {...this.props}
        >
          <ItemInput
            ref={ref => { this.refCountryNomenclature = ref; }}
            label='Country Nomenclature'
            value={countryNomenclature}
            onChange={this.onChange}
          />

          {showClassifications && (
            <ItemArray
              ref={ref => { this.refClassifications = ref; }}
              label='Classifications'
              placeholder='Classification'
              value={classifications}
              accessRemove={accessRemoveData}
              handleSubmitForm={this.handleSubmit}
              autoSave={autoSave}
            />
          )}

          <ItemNumber
            ref={ref => { this.refGovermentApprovalTime = ref; }}
            label='Timeline for Government Approval'
            value={govermentApprovalTime}
            onChange={this.onChange}
            unit='Days'
          />

          <ItemCheckbox
            ref={ref => { this.refNotificationDeadlineBefore = ref; }}
            label='Notify before the new product is placed on the market'
            value={notifyBefore}
            onChange={this.onChange}
          />

          <ItemNumber
            ref={ref => { this.refNotificationDeadlineTime = ref; }}
            label='Notification Deadline'
            value={notificationDeadlineTime}
            onChange={this.onChange}
            unit='Days'
          />

          <ItemRange
            ref={ref => { this.refFee = ref; }}
            label='Government Fees'
            value={fee}
            onChange={this.onChange}
            unit='USD'
          />

          <ItemArRequired
            isNA
            ref={ref => { this.refCanBeSold = ref; }}
            label='Can the already approved product continue to be sold during the change notification process?'
            value={canBeSold}
            onChange={this.onChange}
          />

          <ItemInput
            rows={5}
            ref={ref => { this.refAdditionalInformation = ref; }}
            label='Additional Information'
            value={additionalInformation}
            onChange={this.onChange}
          />

          <Links
            title='Laws'
            addTitle='Add law'
            data={laws}
            params={{ module: 'ccp', countryId }}
            accessRemove={accessRemoveData}
            ref={ref => { this.refLaws = ref; }}
            handleSubmitForm={this.handleSubmit}
            autoSave={autoSave}
          />

          <Changes
            ref={ref => { this.refChanges = ref; }}
            value={changes}
            listChanges={listChanges}
            loading={loadingChanges}
            accessRemove={accessRemoveData}
            handleSubmitForm={this.handleSubmit}
            autoSave={autoSave}
            decisionTreeMenu={decisionTreeMenu}
            usedChanges={usedChanges}
            countryId={countryId}
            itemTitle={itemTitle}
            buffer={buffer}
          />

          <Procedure
            showTag
            ref={ref => { this.refProcedures = ref; }}
            label='Procedures'
            value={procedure}
            accessRemove={accessRemoveData}
            handleSubmitForm={this.handleSubmit}
            autoSave={autoSave}
          />
        </FormWithButtonTop>
      </div>
    );
  }
}

export default EditGeneral;
