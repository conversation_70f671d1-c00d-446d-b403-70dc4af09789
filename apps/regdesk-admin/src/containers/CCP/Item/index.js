import React from 'react';
import { connect } from 'react-redux';
import { Row, Col, Card, Button, Modal, message, Radio, Empty, Tooltip } from 'antd';
import config from 'config';
import Menu from './Menu';
import DecisionTreeMenu from './DecisionTreeMenu';
import Form from './Form';
import Workflow from './Workflow';
import EmptyImage from '../../../assets/empty-image.png';
import Breadcrumb from '../../../components/Breadcrumb';
import actions from '../../../actions';
import styles from '../../../components/Form/index.less';

@connect(({ ccp, account }) => ({
  loading: ccp.loading,
  item: ccp.item,
  permissions: account.adminPermissions,
}))

class CCPItem extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      selected: {},
      menu: 'impact',
    };

    this.menu = [
      { key: 'impact', title: 'Impact' },
      { key: 'impactDT', title: 'Impact for DT' },
      { key: 'decisionTree', title: 'Decision Tree' },
    ];
  }

  componentDidMount() {
    const { id } = this.props.match.params;

    actions.ccp.getById(id);
  }

  /**
   * Update
   * @param data
   * @returns {Promise<any>}
   */
  update = (data = {}) => {
    const { item } = this.props;
    const { _id: idp } = item;
    const newData = { ...data };
    let canUpdate = true;

    Object.keys(newData).forEach(key => {
      const { generalInfo } = newData[key];

      if (generalInfo?.fee?.min > generalInfo?.fee?.max) {
        canUpdate = false;

        return message.warn('Not saved. Check Fee Range!');
      }

      if (!('readyForReport' in newData[key])) {
        newData[key].readyForReport = item[key]?.readyForReport;
      }
    });

    if (canUpdate) {
      return actions.ccp
        .update({ ...newData, idp })
        .then(() => message.success('Save success!'));
    }
  }

  /**
   * Update
   * @param data
   * @returns {Promise<any>}
   */
  onSelect = data => {
    const { selected } = this.state;

    this.setState({ selected: { ...selected, ...data } });
  }

  render() {
    const {
      loading,
      permissions,
      item: propsItem,
    } = this.props;

    const {
      _id: idp,
      country = {},
      updatedAt = '',
    } = propsItem || {};

    const {
      menu,
      selected = {},
    } = this.state;

    const {
      type,
      item,
      onUpdate,
      onRemove,
      itemTitle,
      onAutoSave
    } = selected;

    const { first = '', second = '' } = itemTitle || {};
    let title = '';

    if (first) title += first;
    if (second) title += ` / ${second}`;

    const accessRelease = permissions.includes('accessCCPReleaseCountry');
    const accessRemoveData = permissions.includes('accessCCPRemoveData');

    return (
      <div className={styles.container}>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Change Control Projects', href: `${config.rootRoute}/ccp` },
            { title: country.name },
            { title: this.menu.find(({ key }) => key === menu)?.title || '' },
            { title: first },
            { title: second },
          ]}
        />

        <Row gutter={24}>
          <Col xl={7} lg={7} md={24} sm={24} xs={24}>
            <Card title='' style={{ marginBottom: 24 }}>
              <Tooltip title={!accessRelease && 'No permission'}>
                <Button
                  block
                  loading={loading}
                  disabled={loading || !accessRelease}
                  size='large'
                  icon='check'
                  type='primary'
                  onClick={() => {
                    Modal.confirm({
                      title: 'Confirm',
                      content: 'Are you sure want to release?',
                      onOk: () => {
                        actions.ccp
                          .release(idp)
                          .then(() => message.success('Success!'));
                      },
                    });
                  }}
                >
                  Release
                </Button>
              </Tooltip>
            </Card>

            <Card title='' style={{ marginBottom: 24 }}>
              <Radio.Group
                value={menu}
                buttonStyle='solid'
                style={{ marginBottom: 10, width: '100%', display: 'flex', justifyContent: 'center' }}
                onChange={(e) => this.setState({ menu: e.target.value, selected: {} })}
              >
                {this.menu.map(({ key, title: menuTitle }) => <Radio.Button key={menuTitle} value={key}>{menuTitle}</Radio.Button>)}
              </Radio.Group>

              {menu === 'impact' && (
                <Menu
                  onUpdate={this.update}
                  onSelect={this.onSelect}
                  onCloseEditor={() => this.setState({ selected: {} })}
                />
              )}

              {menu === 'impactDT' && (
                <Menu
                  decisionTree
                  onUpdate={this.update}
                  onSelect={this.onSelect}
                  onCloseEditor={() => this.setState({ selected: {} })}
                />
              )}

              {menu === 'decisionTree' && (
                <DecisionTreeMenu
                  accessRemove={accessRemoveData}
                  onSelect={this.onSelect}
                  onCloseEditor={() => this.setState({ selected: {} })}
                />
              )}
            </Card>
          </Col>

          <Col xl={17} lg={17} md={24} sm={24} xs={24}>
            {first && type === 'form' || first && second && type === 'workflow'
              ? (
                <>
                  {type === 'form' && (
                    <Form
                      autoSave
                      data={item}
                      module='ccp'
                      countryId={country.id}
                      itemTitle={title}
                      onUpdate={onUpdate}
                      onRemove={onRemove}
                      updatedAt={updatedAt}
                      onAutoSave={onAutoSave}
                      decisionTreeMenu={menu === 'impactDT'}
                      accessRemoveData={accessRemoveData}
                      showClassifications={menu === 'impactDT'}
                    />
                  )}

                  {type === 'workflow' && (
                    <Card title='' style={{ marginBottom: 24 }} bordered={false}>
                      <Workflow data={item} onUpdate={onUpdate} itemTitle={title} accessRemove={accessRemoveData} />
                    </Card>
                  )}
                </>
              )
              : (
                <Card style={{ height: 400, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  <Empty
                    image={EmptyImage}
                    imageStyle={{ height: 60 }}
                    description={
                      <span style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 15 }}>
                        Please select menu item
                        {first && !second && menu === 'decisionTree' && ' or add new'}
                      </span>
                    }
                  />
                </Card>
              )}
          </Col>
        </Row>
      </div>
    );
  }
}

export default CCPItem;
