import React, { PureComponent } from 'react';
import { Modal, Table, Input, InputNumber, message } from 'antd';
import styles from '../index.less';

const { Search } = Input;

/**
 * Question
 */
export default class Got<PERSON> extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      item: {},
      showModal: false,
      onChange: null,
      search: '',
    };
  }

  /**
   * Open modal
   * @param item
   * @param onChange
   */
  open = (item, onChange) => this.setState({ showModal: true, item, onChange });

  /**
   * Close modal
   */
  onClose = () => this.setState({ showModal: false, onChange: null, search: '' });

  /**
   * Select
   * @param id
   */
  onSelect = (id) => {
    const { list = [] } = this.props;
    const { onChange, item = {} } = this.state;
    const newDate = list.find((question = {}) => question.id === id);

    if (!item.number || !Number.isInteger(item.number) || item.number < 1 || item.number > 100) {
      return message.error('Please enter the number of attempts from 1 to 100');
    }

    if (onChange) onChange({ ...item, ...newDate, badLink: false });

    this.onClose();
  }

  render() {
    const { list = [] } = this.props;
    const { item = {}, showModal, search } = this.state;
    const newList = list.filter(({ text }) => search.length === 0 || text.toLowerCase().includes(search.toLowerCase()) );

    return (
      <Modal
        title={item.id ? 'Edit block' : 'Add new block'}
        visible={showModal}
        footer={null}
        centered
        destroyOnClose
        maskClosable={false}
        onCancel={this.onClose}
        onClose={this.onClose}
        width={900}
        zIndex={999}
      >
        {showModal && (
          <div>
            <div style={{ marginBottom: 20, display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <span className={styles.name} style={{ marginRight: 10 }}>The number of loop attempts:</span>

                <InputNumber
                  size='small'
                  min={1}
                  max={100}
                  value={item.number}
                  onChange={(val) => this.setState({ item: { ...item, number: val } })}
                />
              </div>

              <Search
                size='small'
                placeholder='Search by question'
                onChange={(e) => this.setState({ search: e.target.value })}
                style={{ width: 200 }}
              />
            </div>

            <Table
              bordered
              rowKey='_id'
              size='small'
              columns={[
                {
                  title: 'Link',
                  dataIndex: 'link',
                },
                {
                  title: 'Question | Next',
                  dataIndex: 'text',
                  render: (text) => <div style={{ maxWidth: 500 }} className={styles.ellipsis}>{text}</div>
                },
                {
                  title: 'Action',
                  dataIndex: 'id',
                  render: (id) => <a onClick={() => this.onSelect(id)}>Select</a>
                },
              ]}
              dataSource={newList}
            />
          </div>
        )}
      </Modal>
    );
  }
}
