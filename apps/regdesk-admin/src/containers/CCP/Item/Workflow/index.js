import React from 'react';
import classNames from 'classnames';
import { connect } from 'react-redux';
import { message, Card, Button, Tooltip, Icon, Tag, Popconfirm, Input } from 'antd';
import { Item } from '../../../../components/Form';
import QuestionModal from './Question';
import ChangeModal from './Change';
import GotoModal from './Goto';
import actions from '../../../../actions';
import styles from './index.less';

@connect(({ ccp, ccpChanges }) => ({
  item: ccp.item,
  loading: ccp.loading,
  changes: ccpChanges.list,
}))

class Workflow extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      loading: true,
      data: props.data,
    };
  }

  componentDidMount() {
    actions.ccpChanges
      .get()
      .then(() => this.setState({ loading: false }));
  }

  componentWillReceiveProps = (nextProps) => {
    this.setState({ data: nextProps.data });
  }

  /**
   * Send form
   */
  handleSubmit = () => {
    const { onUpdate } = this.props;
    const { data } = this.state;

    if (!data?.name) return message.warn('Please enter name');
    if (onUpdate) onUpdate(data);
  }

  /**
   * Update or delete data
   * @param link
   * @param newItem
   */
  updateData = (link, newItem = {}) => {
    const newData = this.state.data;

    const getObj = (keys, data) => {
      const newKey = [...keys];
      const key = newKey.shift();

      if (data[key]) {
        if (newKey.length === 0) data[key] = newItem;
        else getObj(newKey, data[key]);
      }
    };

    getObj(link.split('.'), newData);

    this.setState({ data: newData });
  }

  /**
   * Cmp Add
   * @param item
   * @param row
   * @param col
   * @param link
   * @returns {JSX.Element}
   */
  add = (item, row, col, link) => (
    <Card
      size='small'
      title='Add'
      style={{
        marginBottom: 15,
        width: 180,
        height: 120,
        position: 'absolute',
        left: 260 * col,
        top: 200 * row,
      }}
      extra={(
        <Tooltip title='You cannot finish creating a workflow while there are unresolved nodes !'>
          <Icon type='warning' style={{ color: '#faad14' }} />
        </Tooltip>
      )}
    >
      <Button.Group style={{ marginBottom: 10, display: 'flex' }}>
        <Button
          size='small'
          style={{ flex: 1 }}
          onClick={() => this.questionModal.open(item, (newItem) => this.updateData(link, { ...newItem, id: Date.now(), yes: {}, no: {}, type: 'question' }))}
        >
          Question
        </Button>

        <Button
          size='small'
          style={{ flex: 1 }}
          onClick={() => this.changeModal.open(item, (newItem) => this.updateData(link, { ...newItem, type: 'change' }))}
        >
          Change
        </Button>

      </Button.Group>

      <Button.Group style={{ display: 'flex' }}>
        <Button
          size='small'
          style={{ flex: 1 }}
          onClick={() => this.questionModal.open(item, (newItem) => this.updateData(link, { ...newItem, id: Date.now(), next: {}, type: 'next' }))}
        >
          Next
        </Button>

        <Button
          size='small'
          style={{ flex: 1 }}
          onClick={() => this.gotoModal.open({ ...item, number: 3 }, (newItem) => this.updateData(link, { ...newItem, next: {}, type: 'goto' }))}
        >
          GoTo
        </Button>

        <Button
          size='small'
          style={{ flex: 1 }}
          onClick={() => this.questionModal.open(item, (newItem) => this.updateData(link, { ...newItem, type: 'stop' }))}
        >
          Stop
        </Button>
      </Button.Group>
    </Card>
  )

  /**
   * Cmp question
   * @param item
   * @param row
   * @param col
   * @param link
   * @param title
   * @returns {JSX.Element}
   */
  question = (item, row, col, link, title = 'Question') => (
    <Card
      size='small'
      title={title}
      style={{
        marginBottom: 15,
        width: 180,
        height: 120,
        position: 'absolute',
        left: 260 * col,
        top: 200 * row,
      }}
      extra={(
        <div>
          <Tooltip title='Edit'>
            <Icon
              type='edit'
              style={{ color: '#40a9ff', marginRight: 10, cursor: 'pointer' }}
              onClick={() => {
                this.questionModal.open(item, (newItem) => this.updateData(link, { ...newItem }));
              }}
            />
          </Tooltip>

          <Tooltip title='Delete'>
            <Popconfirm
              title='Are you sure delete this block ?'
              onConfirm={() => this.updateData(link, {})}
              okText='Yes'
              cancelText='No'
            >
              <Icon type='delete' style={{ color: '#40a9ff', cursor: 'pointer' }} />
            </Popconfirm>
          </Tooltip>
        </div>
      )}
    >
      <div className={classNames(styles.question, styles.name)}>
        <Tooltip title={item.text}>{item.text}</Tooltip>
      </div>
    </Card>
  )

  /**
   * Cmp question
   * @param item
   * @param row
   * @param col
   * @param link

   * @returns {JSX.Element}
   */
  goto = (item, row, col, link) => (
    <Card
      size='small'
      title='GoTo block'
      style={{
        marginBottom: 15,
        width: 180,
        height: 120,
        position: 'absolute',
        left: 260 * col,
        top: 200 * row,
      }}
      extra={(
        <div>
          { item.badLink && (
            <Tooltip title='The current link is incorrect, please edit it !'>
              <Icon type='warning' style={{ color: '#faad14', marginRight: 10 }} />
            </Tooltip>
          )}

          <Tooltip title='Edit'>
            <Icon
              type='edit'
              style={{ color: '#40a9ff', marginRight: 10, cursor: 'pointer' }}
              onClick={() => {
                this.gotoModal.open(item, (newItem) => this.updateData(link, { ...newItem }));
              }}
            />
          </Tooltip>

          <Tooltip title='Delete'>
            <Popconfirm
              title='Are you sure delete this block ?'
              onConfirm={() => this.updateData(link, {})}
              okText='Yes'
              cancelText='No'
            >
              <Icon type='delete' style={{ color: '#40a9ff', cursor: 'pointer' }} />
            </Popconfirm>
          </Tooltip>
        </div>
      )}
    >
      <div className={styles.question}>
        <div style={{ display: 'flex' }}>Link:&nbsp;<Tooltip title={item.link}><span className={styles.ellipsis}>{item.link}</span></Tooltip></div>
        <div style={{ display: 'flex' }}>Question:&nbsp;<Tooltip title={item.text}><span className={styles.ellipsis}>{item.text}</span></Tooltip></div>
        <div style={{ display: 'flex' }}>Attempts:&nbsp;<span className={styles.ellipsis}>{item.number}</span></div>
      </div>
    </Card>
  )

  /**
   * Get Impact
   * @param action
   * @returns {JSX.Element}
   */
  getImpact = (action) => {
    if (['updateTechnicalFile', 'updateTechnicalFileDT'].includes(action)) return <Tag color='#87d068'>Non Signifi...</Tag>;
    if (['notificationRequired', 'notificationRequiredDT', 'reRegistration', 'reRegistrationDT'].includes(action)) return <Tag color='#f50'>Significant</Tag>;
    if (action === 'notSpecified') return <Tag>Not Listed</Tag>;
    if (action === 'notSpecifiedDT') return <Tag>No Impact</Tag>;

    return <Tag>N/A</Tag>;
  }

  /**
   * Get action
   * @param changeId
   * @returns {string}
   */
  getAction = (changeId) => {
    const { item } = this.props;
    const { updateTechnicalFileDT, notificationRequiredDT, reRegistrationDT } = item;

    if (updateTechnicalFileDT.changes.find((change) => change.changeId === changeId)) return 'updateTechnicalFileDT';
    if (notificationRequiredDT.changes.find((change) => change.changeId === changeId)) return 'notificationRequiredDT';
    if (reRegistrationDT.changes.find((change) => change.changeId === changeId)) return 'reRegistrationDT';

    return 'notSpecified';
  }

  /**
   * Get action Name
   * @param action
   * @returns {string}
   */
  getActionName = (action) => {
    if (action === 'updateTechnicalFileDT') return 'Update Technical File';
    if (action === 'notificationRequiredDT') return 'Notification Required';
    if (action === 'reRegistrationDT') return 'Re-Registration';
    if (['notSpecified', 'notSpecifiedDT'].includes(action)) return 'Not Specified';

    return 'N/A';
  }

  /**
   * Check link
   * @param item
   */
  checkLink = (item) => {
    const newData = this.state.data;
    const getObj = (keys, data) => {
      const newKey = [...keys];
      const key = newKey.shift();

      if (data[key]) {
        if (newKey.length === 0) return data[key];

        return getObj(newKey, data[key]);
      }
    };

    if (!item.id || !item.link || item.badLink) return false;

    const linkItem = getObj(item.link.split('.'), newData);

    if (linkItem && linkItem.id === item.id) {
      item.text = linkItem.text;
    } else {
      item.badLink = true;
    }
  }

  /**
   * Cmp Change
   * @param item
   * @param row
   * @param col
   * @param link
   * @returns {JSX.Element}
   */
  change = (item, row, col, link) => {
    const { changes = [] } = this.props;
    const { loading } = this.state;
    const change = changes.find(({ _id }) => _id === item.changeId) || {};
    const action = change.decisionTreeDefaultImpact === 'noImpact' ? 'notSpecifiedDT' : this.getAction(change._id);
    const impact = this.getImpact(action);

    return (
      <Card
        size='small'
        title='Variation change'
        loading={loading}
        style={{
          marginBottom: 15,
          width: 180,
          height: 120,
          position: 'absolute',
          left: 260 * col,
          top: 200 * row,
        }}
        extra={(
          <div>
            <Tooltip title='Edit'>
              <Icon
                type='edit'
                style={{ color: '#40a9ff', marginRight: 10, cursor: 'pointer' }}
                onClick={() => {
                  this.changeModal.open(item, (newItem) => this.updateData(link, { ...newItem }));
                }}
              />
            </Tooltip>

            <Tooltip title='Delete'>
              <Popconfirm
                title='Are you sure delete ?'
                onConfirm={() => this.updateData(link, {})}
                okText='Yes'
                cancelText='No'
              >
                <Icon type='delete' style={{ color: '#40a9ff', cursor: 'pointer' }} />
              </Popconfirm>
            </Tooltip>
          </div>
        )}
      >
        <div className={styles.question}>
          <div>Impact: {impact}</div>
          <div>Name: <span className={styles.name}><Tooltip title={change.name}>{change.name}</Tooltip></span></div>
        </div>
      </Card>
    );
  }

  render() {
    const { data } = this.state;
    const { workflow = {}, name } = data || {};
    const loading = this.state.loading || this.props.loading;
    const tableQuestions = [];
    let maxRow = 0;
    let maxCol = 0;
    let canSave = true;

    const getTree = (item = {}, row, col, link, isYes) => {
      const { type, yes = {}, no = {}, next = {} } = item;
      let newCol = col;

      if (!type) {
        canSave = false;

        return { row, col: newCol, cmp: this.add(item, row, newCol, link) };
      }

      if (['question', 'next', 'goto'].includes(type)) {
        if (isYes) {
          maxCol += 2;
          newCol = maxCol;
        }

        if (maxRow < row + 1) {
          maxRow = row + 1;
        }
      }

      if (['question', 'next'].includes(type)) {
        if (!item.id) {
          item.id = Date.now();
        }

        tableQuestions.push({ id: item.id, link, text: item.text });
      }

      if (type === 'question') {
        const treeNo = getTree(no, row + 1, newCol, `${link}.no`);
        const treeYes = getTree(yes, row, newCol + 1, `${link}.yes`, true);
        const widthYes = (treeYes.col - newCol - 1) * 260 + 79;
        const leftYes = (newCol + 1) * 260 + widthYes - 89;

        return {
          row,
          col: newCol,
          cmp: (
            <div>
              {this.question(item, row, newCol, link)}
              {treeNo.cmp}
              <div className={styles.line} style={{ width: 1, height: 79, top: 200 * row + 120, left: 260 * newCol + 90 }} />
              <div className={classNames(styles.arrow, styles.down)} style={{ top: 200 * (row + 1), left: 260 * newCol + 90, marginLeft: -3, marginTop: -8 }} />
              <Tag style={{ top: 200 * row + 150, left: 260 * newCol + 74, position: 'absolute' }}>NO</Tag>
              {treeYes.cmp}
              <div className={styles.line} style={{ height: 1, width: widthYes, top: 200 * row + 60, left: 260 * newCol + 180 }} />
              <div className={classNames(styles.arrow, styles.right)} style={{ top: 200 * row + 60, left: leftYes, marginTop: -3 }} />
              <Tag style={{ top: 200 * row + 50, left: 260 * newCol + 200, position: 'absolute' }}>YES</Tag>
            </div>
          )
        };
      }

      if (type === 'next') {
        const treeNext = getTree(next, row + 1, newCol, `${link}.next`);

        return {
          row,
          col: newCol,
          cmp: (
            <div>
              {this.question(item, row, newCol, link, 'Next block')}
              {treeNext.cmp}
              <div className={styles.line} style={{ width: 1, height: 79, top: 200 * row + 120, left: 260 * newCol + 90 }} />
              <div className={classNames(styles.arrow, styles.down)} style={{ top: 200 * (row + 1), left: 260 * newCol + 90, marginLeft: -3, marginTop: -8 }} />
              <Tag style={{ top: 200 * row + 150, left: 260 * newCol + 70, position: 'absolute' }}>Next</Tag>
            </div>
          )
        };
      }

      if (type === 'goto') {
        this.checkLink(item);

        if (item.badLink) {
          canSave = false;
        }

        const treeNext = getTree(next, row + 1, newCol, `${link}.next`);

        return {
          row,
          col: newCol,
          cmp: (
            <div>
              {this.goto(item, row, newCol, link)}
              {treeNext.cmp}
              <div className={styles.line} style={{ width: 1, height: 79, top: 200 * row + 120, left: 260 * newCol + 90 }} />
              <div className={classNames(styles.arrow, styles.down)} style={{ top: 200 * (row + 1), left: 260 * newCol + 90, marginLeft: -3, marginTop: -8 }} />
              <Tag style={{ top: 200 * row + 150, left: 260 * newCol + 54, position: 'absolute' }}>Loophole</Tag>
            </div>
          )
        };
      }

      if (type === 'stop') {
        return { row, col: newCol, cmp: this.question(item, row, newCol, link, 'Stop block') };
      }

      if (type === 'change') {
        return { row, col: newCol, cmp: this.change(item, row, newCol, link) };
      }
    };

    const tree = getTree(workflow, 0, 0, 'workflow').cmp;

    return (
      <>
        <Item label='Name'>
          <Input
            value={name}
            autoFocus
            allowClear
            placeholder='Name...'
            onChange={e => {
              const newData = this.state.data;

              newData.name = e.target.value;
              this.setState({ data: newData });
            }}
          />
        </Item>

        <Item
          label='Workflow'
          childrenStyle={{ position: 'relative', height: maxRow * 200 + 160, overflowX: 'scroll' }}
        >
          {tree}
        </Item>

        <Item>
          <Button
            type='primary'
            loading={loading}
            disabled={loading || !canSave}
            onClick={this.handleSubmit}
            style={{ marginRight: 20 }}
          >
            Save
          </Button>
        </Item>

        <QuestionModal ref={ref => { this.questionModal = ref; }} />
        <GotoModal list={tableQuestions} ref={ref => { this.gotoModal = ref; }} />

        <ChangeModal
          item={this.props.item}
          list={this.props.changes}
          getAction={this.getAction}
          getImpact={this.getImpact}
          getActionName={this.getActionName}
          ref={ref => { this.changeModal = ref; }}
        />
      </>
    );
  }
}

export default Workflow;
