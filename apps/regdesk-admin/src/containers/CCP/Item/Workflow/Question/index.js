import React, { PureComponent } from 'react';
import { Button, Form, Input, Modal } from 'antd';

const { TextArea } = Input;
const { Item } = Form;

/**
 * Question
 */
export default class Question extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      item: {},
      text: '',
      showModal: false,
      onChange: null,
    };
  }

  /**
   * Open modal
   * @param item
   * @param onChange
   */
  open = (item, onChange) => this.setState({ item, text: item.text || '', onChange, showModal: true, });

  /**
   * Close modal
   */
  onClose = () => this.setState({ showModal: false, onChange: null });

  /**
   * Send from
   */
  onUpdate = () => {
    const { onChange, text, item } = this.state;

    if (onChange) onChange({ ...item, text });

    this.onClose();
  }

  render() {
    const { text, item = {}, showModal } = this.state;

    return (
      <Modal
        title={item.text ? 'Edit the block' : 'Add new block'}
        visible={showModal}
        footer={null}
        centered
        destroyOnClose
        maskClosable={false}
        onCancel={this.onClose}
        onClose={this.onClose}
        width={720}
        zIndex={999}
      >
        {showModal && (
          <Form>
            <Item style={{ marginBottom: 0 }}>
              <TextArea
                rows={8}
                placeholder='Text...'
                autoComplete='off'
                autoFocus
                value={text}
                onChange={(e) => this.setState({ text: e.target.value })}
              />
            </Item>

            <Item style={{ marginBottom: 0 }}>
              <Button onClick={this.onClose}>Cancel</Button>

              <Button disabled={!text} style={{ marginLeft: 8 }} type='primary' onClick={this.onUpdate}>
                {item.text ? 'Update' : 'Add'}
              </Button>
            </Item>
          </Form>
        )}
      </Modal>
    );
  }
}
