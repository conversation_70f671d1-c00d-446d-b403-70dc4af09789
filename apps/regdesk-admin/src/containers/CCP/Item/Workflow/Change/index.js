import config from 'config';
import React, { PureComponent } from 'react';
import { Button, Modal, Table, Tag, message, Input } from 'antd';
import actions from '../../../../../actions';

const { Search } = Input;

/**
 * Question
 */
export default class Change extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      item: {},
      showModal: false,
      onChange: null,
      loading: false,
      search: '',
    };
  }

  /**
   * Open modal
   * @param item
   * @param onChange
   */
  open = (item, onChange) => this.setState({ showModal: true, item, onChange });

  /**
   * Close modal
   */
  onClose = () => this.setState({ showModal: false, onChange: null, search: '' });

  /**
   * Select
   * @param id
   * @returns {MessageType}
   */
  onSelect = (id) => {
    const { onChange, item } = this.state;
    const { country = {} } = this.props.item;
    const countryId = country.id;
    const { list = [] } = this.props;
    const change = list.find(({ _id }) => _id === id);

    if (!change) return message.error('Change not found');

    const { level, major, moderate, minor, name } = change;

    if (level === 'NR' && (!major.includes(countryId) && !moderate.includes(countryId) && !minor.includes(countryId))) {
      return message.error(`This change "${name}" don't have impact for country ${country.name}`);
    }

    if (onChange) onChange({ ...item, changeId: id });

    this.onClose();
  }

  /**
   * Get Changes
   */
  getChanges = () => {
    this.setState({ loading: true });

    actions.ccpChanges
      .get()
      .then(() => this.setState({ loading: false }));
  }

  render() {
    const { item = {}, showModal, loading, search } = this.state;
    const { list = [], getAction, getImpact, getActionName } = this.props;
    const newList = list.filter(({ name, decisionTree }) => !!decisionTree && (search.length === 0 || name.toLowerCase().includes(search.toLowerCase())) );

    return (
      <Modal
        title={item.id ? 'Edit variation change' : 'Add variation change'}
        visible={showModal}
        footer={null}
        centered
        destroyOnClose
        maskClosable={false}
        onCancel={this.onClose}
        onClose={this.onClose}
        width={900}
        zIndex={999}
      >
        {showModal && (
          <div>
            <div style={{ marginBottom: 20, display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <a target='_blank' href={`${config.rootRoute}/ccp/changes/dt`} rel='noreferrer'>
                  <Button icon='link' size='small'>Manage changes</Button>
                </a>

                <Button
                  loading={loading}
                  onClick={this.getChanges}
                  style={{ marginLeft: 10 }}
                  icon='redo'
                  size='small'
                  disabled={loading}
                >
                  Refresh table
                </Button>
              </div>

              <Search
                size='small'
                placeholder='Search by name'
                onChange={(e) => this.setState({ search: e.target.value })}
                style={{ width: 200 }}
              />
            </div>

            <Table
              bordered
              rowKey='_id'
              size='small'
              loading={loading}
              columns={[
                {
                  title: 'Name',
                  dataIndex: 'name',
                },
                {
                  title: 'Default Impact',
                  dataIndex: 'decisionTreeDefaultImpact',
                  width: 120,
                  render: (impact) => {
                    if (impact === 'perCountry') return <Tag color='green'>Per Country</Tag>;
                    if (impact === 'noImpact') return <Tag color='volcano'>No Impact</Tag>;

                    return <Tag>No level</Tag>;
                  },
                },
                {
                  title: 'Action Required',
                  dataIndex: '_id',
                  width: 200,
                  render: (id) => {
                    const action = getAction(id);

                    return getActionName(action);
                  },
                },
                {
                  title: 'Impact',
                  dataIndex: '_id',
                  render: (id, change) => {
                    const action = change.decisionTreeDefaultImpact === 'noImpact' ? 'notSpecifiedDT' : getAction(id);

                    return getImpact(action);
                  },
                },
                {
                  title: 'Action',
                  dataIndex: '_id',
                  render: (id) => <a onClick={() => this.onSelect(id)}>Select</a>
                },
              ]}
              dataSource={newList}
            />
          </div>
        )}
      </Modal>
    );
  }
}
