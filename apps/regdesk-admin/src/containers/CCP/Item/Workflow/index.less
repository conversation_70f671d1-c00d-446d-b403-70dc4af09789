.line {
  background-color: #999;
  position: absolute;
}

.arrow {
  border: solid #999;
  border-width: 0 1px 1px 0;
  display: inline-block;
  padding: 3px;
  position: absolute;

  &.right {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
  }

  &.down {
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
  }
}

.question {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-top: -2px;
}

.name {
  color: rgba(0, 0, 0, 0.45);
}

.disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.25);
}

.ellipsis {
  color: rgba(0, 0, 0, 0.45);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
