import React from 'react';
import { Tree, Icon, message, Popconfirm, Tooltip } from 'antd';
import { connect } from 'react-redux';
import actions from '../../../actions';

const { TreeNode } = Tree;

@connect(({ ccp }) => ({
  item: ccp.item
}))

class DecisionTreeMenu extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      expandedKeys: ['0-0'],
    };
  }

  /**
   * Handle Select
   * @param key
   * @param info
   */
  handleSelect = (key, info) => {
    const { type, item = {}, itemTitle, onUpdate, onRemove } = info.node.props;
    const { onSelect } = this.props;

    if (type || itemTitle) {
      onSelect({
        type,
        item: JSON.parse(JSON.stringify(item)),
        itemTitle,
        onUpdate,
        onRemove,
      });
    }
  };

  /**
   * Update
   * @param decisionTree
   * @returns {Promise<any>}
   */
  update = decisionTree => {
    const { item, onCloseEditor } = this.props;
    const { _id: idp } = item || {};

    return actions.ccp
      .update({ idp, decisionTree })
      .then(() => { onCloseEditor(); message.success('Save success!'); });
  }

  render() {
    const { item, accessRemove } = this.props;
    const { _id: idp, decisionTree = [] } = item || {};

    return (
      <div>
        <Tree
          showLine
          onSelect={this.handleSelect}
          expandedKeys={this.state.expandedKeys}
          onExpand={expandedKeys => this.setState({ expandedKeys })}
          draggable
        >
          <TreeNode
            title={
              <span className='b-hover'>Decision Tree Workflow&nbsp;
                <Icon
                  type='plus-circle'
                  style={{ fontSize: 14 }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    this.props.onSelect({
                      type: 'workflow',
                      item: { name: '', workflow: {} },
                      itemTitle: { first: 'Decision Tree Workflow', second: 'Add' },
                      onUpdate: newData => {
                        decisionTree.push(newData);

                        actions.ccp
                          .update({ idp, decisionTree })
                          .then(() => { this.props.onCloseEditor(); message.success('Save success!'); });
                      },
                    });
                  }}
                />
              </span>
            }
            itemTitle={{ first: 'Decision Tree Workflow' }}
          >
            {decisionTree.map((change, i) => (
              <TreeNode
                key={i}
                title={
                  <span className='b-hover'>
                    {change.name && change.name.length > 36 ? `${change.name.slice(0, 36)}...` : change.name}&nbsp;

                    <Tooltip title={!accessRemove && 'No permission'}>
                      <Popconfirm
                        title='Are you sure delete ?'
                        onConfirm={() => {
                          decisionTree.splice(i, 1);

                          this.update(decisionTree);
                        }}
                        okText='Yes'
                        cancelText='No'
                        disabled={!accessRemove}
                      >
                        <Icon type='delete' style={{ fontSize: 14, cursor: !accessRemove && 'not-allowed' }} />
                      </Popconfirm>
                    </Tooltip>
                  </span>
                }
                type='workflow'
                item={change}
                itemTitle={{ first: 'Decision Tree Workflow', second: change.name }}
                onUpdate={newData => {
                  decisionTree[i] = newData;

                  this.update(decisionTree);
                }}
              />
            ))}
          </TreeNode>
        </Tree>
      </div>
    );
  }
}

export default DecisionTreeMenu;
