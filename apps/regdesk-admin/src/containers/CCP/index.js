import React from 'react';
import _ from 'lodash';
import { <PERSON><PERSON>, Card, Table, Row, Col, Icon, Input, Switch, Popconfirm, Tooltip } from 'antd';
import { Link } from 'react-router-dom';
import config from 'config';
import moment from 'moment';
import { connect } from 'react-redux';
import Breadcrumb from '../../components/Breadcrumb';
import { allCountriesWithEU } from '../../utils/countries';
import actions from '../../actions';

@connect(({ ccp, account }) => ({
  loading: ccp.loading,
  data: ccp.data,
  released: ccp.released,
  permissions: account.adminPermissions,
  availableCountries: account.adminCountriesForCCP,
  limitCountries: account.adminLimitCountriesForCCP,
}))

class CCPList extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      search: '',
      countries: [],
      columns: [
        {
          title: 'Country',
          dataIndex: 'alpha3code',
          key: 'alpha3code',
          render: (code, { name }) => <Link to={`${config.rootRoute}/ccp/${code}`}>{name}</Link>,
        },
        {
          title: 'Release Time',
          dataIndex: 'releasedAt',
          key: 'releasedAt',
          render: (text, { releasedAt }) => (releasedAt ? moment(releasedAt).getFullUTC() : ''),
        },
        {
          title: 'Actions',
          dataIndex: 'key',
          key: 'key',
          render: (code, { releasedAt }) => {
            const { permissions } = this.props;
            const accessRelease = permissions.includes('accessCCPReleaseCountry');

            return releasedAt && (
              <Tooltip title={!accessRelease && 'No permission'}>
                <Popconfirm
                  title='Do you make sure ?'
                  onConfirm={() => actions.ccp.unRelease(code)}
                  onCancel={() => {}}
                  okText='Yes'
                  cancelText='No'
                  disabled={!accessRelease}
                >
                  <Button type='link' disabled={!accessRelease}>Recall release</Button>
                </Popconfirm>
              </Tooltip>
            );
          },
        },
      ],
    };
  }

  componentDidMount() {
    const { availableCountries, limitCountries } = this.props;
    let countries = allCountriesWithEU;

    if (limitCountries) countries = countries.filter(({ alpha3code }) => availableCountries.includes(alpha3code));

    actions.ccp
      .get()
      .then(() => this.setState({ countries }));
  }

  handleSearch = e => this.setState({ search: e.target.value });

  render() {
    const { columns, countries, search } = this.state;
    const { loading, data = [], released, permissions } = this.props;
    const releasedObj = {};

    data.forEach(({ country, updatedAt }) => {
      releasedObj[country.id] = updatedAt;
    });

    const dataSource = _.map(countries.slice(), v =>
      Object.assign({}, v, {
        key: v.alpha3code,
        releasedAt: releasedObj[v.alpha3code] || ''
      })
    ).filter(v => (
      (search.length === 0 || v.name.toLowerCase().includes(search.toLowerCase()))
      && (!released || v.releasedAt)
    ));

    const accessUpdateChanges = permissions.includes('accessCCPUpdateChanges');
    const accessUpdateDTChanges = permissions.includes('accessCCPUpdateDTChanges');

    return (
      <div>
        <Breadcrumb routes={[{ title: 'Dashboard', href: `${config.rootRoute}/` }, { title: 'CCP' }]} />

        <Row gutter={24}>
          <Col xl={6} lg={6} md={24} sm={24} xs={24}>
            <Card title='Project changes' style={{ marginBottom: 24 }}>
              <Link to={accessUpdateChanges && `${config.rootRoute}/ccp/changes`}>
                <Tooltip title={!accessUpdateChanges && 'No permission'}>
                  <Button block size='large' icon='setting' type='primary' disabled={!accessUpdateChanges} style={{ marginBottom: 20 }}>
                    Non Decision Tree Changes
                  </Button>
                </Tooltip>
              </Link>

              <Link to={accessUpdateDTChanges && `${config.rootRoute}/ccp/changes/dt`}>
                <Tooltip title={!accessUpdateDTChanges && 'No permission'}>
                  <Button block size='large' icon='setting' type='primary' disabled={!accessUpdateDTChanges}>
                    Decision Tree Changes
                  </Button>
                </Tooltip>
              </Link>
            </Card>

            <Card
              title='Filters'
              style={{ marginBottom: 24 }}
              bodyStyle={{ display: 'flex', flexDirection: 'column' }}
            >
              <Input
                size='large'
                addonBefore={<Icon type='search' />}
                placeholder='Search by Country Name'
                onChange={e => this.handleSearch(e)}
                style={{ marginBottom: 24 }}
              />

              <div>
                <Switch checked={released} onChange={() => actions.ccp.change({ released: !released })} />
                <span style={{ marginLeft: 10 }}>Released</span>
              </div>
            </Card>
          </Col>

          <Col xl={18} lg={18} md={24} sm={24} xs={24}>
            <Card>
              <Table
                bordered
                loading={loading}
                columns={columns}
                dataSource={dataSource}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  }
}

export default CCPList;
