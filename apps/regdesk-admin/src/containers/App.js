import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';
import { Router, Route, Switch, Redirect } from 'react-router-dom';
import { Spin, Layout } from 'antd';
import config from 'config';
import DateUtil from '../utils/date';
import history from '../utils/history';
import Nav from '../components/Nav';
import actions from '../actions';
import Auth from './Auth';
import ModalTimer from './Auth/Countdown';
import Logs from './Logs';
import Users from './Users';
import Profile from './Profile';
import RegPlan from './RegPlan';
import NotFoundPage from './404';
import Alerts from './Alerts';
import PharmaAlerts from './PharmaAlerts';
import Notifications from './Notifications';
import Standards from './Standards';
import AddUser from './Users/<USER>';
import ItemUser from './Users/<USER>';
import ChangePasswordUser from './Users/<USER>';
import Dashboard from './Dashboard';
import PHRList from './PHR';
import PHRDocType from './PHR/DocType';
import PHRItem from './PHR/Item';
import PHRReports from './PHR/Reports/index';
import MDRList from './MDR';
import MDRDocType from './MDR/DocType';
import MDRItem from './MDR/Item';
import MDRReports from './MDR/Reports';
import CCPList from './CCP';
import CCPItem from './CCP/Item';
import CCPChanges from './CCP/Changes';
import Wizards from './Wizards';
import WizardClass from './Wizards/Classifications';
import WizardIDs from './Wizards/Identifiers';
import WizardSystemTags from './Wizards/SystemTags';
import WizardQuestions from './Wizards/Questions';
import WizardEdit from './Wizards/Edit';
import Forms from './Forms';
import EditForm from './Forms/Edit';
import CloneForm from './Forms/Clone';
import AutofillHistory from './AutofillHistory';
import AutofillHistoryDetails from './AutofillHistory/Details';
import Guide from './Guide';
import Cron from './Cron';
import DBMode from './DBMode';
import ApplicationUsageControl from './ApplicationUsage';
import GrafanaDashboards from './GrafanaDashboards';
import ApplicationUsageLogs from './ApplicationUsage/Logs';

import 'flag-icon-css/css/flag-icon.min.css';

@connect(({ account }) => ({
  su: account.su,
  role: account.role,
  email: account.email,
  loaded: account.loaded,
  permissions: account.adminPermissions,
}))

export default class App extends Component {
  constructor() {
    super();

    DateUtil.init();
  }

  componentDidMount() {
    actions.session.check();
  }

  render() {
    const {
      su,
      role,
      email,
      loaded,
      permissions = [],
    } = this.props;

    if (!loaded) {
      return (
        <div
          style={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Spin size='large' />
        </div>
      );
    }

    return (
      <Router history={history}>
        <Switch>
          <Route
            exact
            path={`${config.rootRoute}/`}
            render={props => !email || role !== 'admin' ? <Auth {...props} /> : <Redirect to={`${config.rootRoute}/dashboard`} />}
          />

          <Route
            path={`${config.rootRoute}/`}
            render={() =>
              email && role === 'admin'
                ? (
                  <Layout>
                    <Nav />

                    <Layout.Content style={{ margin: '24px 28px 0', height: '100%' }}>
                      <div style={{ minHeight: 'calc(100vh - 157px)' }}>
                        <Switch>
                          <Route exact path={`${config.rootRoute}/dashboard`} component={Dashboard} />

                          {
                            (su || permissions.includes('users')) && [
                              <Route exact path={`${config.rootRoute}/users`} key='users' component={Users} />,
                              <Route exact path={`${config.rootRoute}/users/add`} key='add-user' component={AddUser} />,
                              <Route exact path={`${config.rootRoute}/users/edit/:userId`} key='edit-user' component={AddUser} />,
                              <Route exact path={`${config.rootRoute}/users/item/:userId`} key='item-user' component={ItemUser} />,
                              su && (
                                <Route
                                  exact
                                  key='change-password-user'
                                  component={ChangePasswordUser}
                                  path={`${config.rootRoute}/users/change/password/:userId`}
                                />
                              ),
                            ]
                          }

                          {
                            permissions.includes('regulations') && [
                              <Route exact path={`${config.rootRoute}/devicereg`} key='mdrList' component={MDRList} />,
                              <Route exact path={`${config.rootRoute}/devicereg/docTypes/`} key='mdrDocType' component={MDRDocType} />,
                              <Route exact path={`${config.rootRoute}/devicereg/reports`} key='mdrReports' component={MDRReports} />,
                              <Route exact path={`${config.rootRoute}/devicereg/:id`} key='mdrItem' component={MDRItem} />,
                            ]
                          }

                          {
                            permissions.includes('pharmaRegulations') && [
                              <Route exact path={`${config.rootRoute}/pharmareg`} key='regs' component={PHRList} />,
                              <Route exact path={`${config.rootRoute}/pharmareg/docTypes/`} key='dic-type' component={PHRDocType} />,
                              <Route exact path={`${config.rootRoute}/pharmareg/reports`} key='pharmaReports' component={PHRReports} />,
                              <Route exact path={`${config.rootRoute}/pharmareg/:id`} key='edit-reg' component={PHRItem} />
                            ]
                          }

                          {
                            permissions.includes('control') && [
                              <Route exact path={`${config.rootRoute}/ccp`} key='ccp' component={CCPList} />,
                              <Route exact path={`${config.rootRoute}/ccp/changes`} key='ccp-changes' component={CCPChanges} />,
                              <Route exact path={`${config.rootRoute}/ccp/changes/:dt`} key='ccp-changes-dt' component={CCPChanges} />,
                              <Route exact path={`${config.rootRoute}/ccp/:id`} key='ccp-detail' component={CCPItem} />,
                            ]
                          }

                          {
                            permissions.includes('dataAlerts')
                              && <Route exact path={`${config.rootRoute}/alerts`} component={Alerts} />
                          }

                          {
                            permissions.includes('pharmaAlerts')
                              && <Route exact path={`${config.rootRoute}/pharmaAlerts`} component={PharmaAlerts} />
                          }

                          {
                            permissions.includes('updates') && [
                              <Route exact path={`${config.rootRoute}/notifications`} key='updates' component={Notifications} />,
                            ]
                          }

                          {
                            permissions.includes('standards') && [
                              <Route exact path={`${config.rootRoute}/standards`} key='standards' component={Standards} />,
                            ]
                          }

                          {
                            permissions.includes('wizards') && [
                              <Route exact path={`${config.rootRoute}/wizards`} key='wizards' component={Wizards} />,
                              <Route exact path={`${config.rootRoute}/wizards/classifications`} key='wizards' component={WizardClass} />,
                              <Route exact path={`${config.rootRoute}/wizards/ids`} key='wizards' component={WizardIDs} />,
                              <Route exact path={`${config.rootRoute}/wizards/tags`} key='wizards' component={WizardSystemTags} />,
                              <Route exact path={`${config.rootRoute}/wizards/questions`} key='wizards' component={WizardQuestions} />,
                              <Route exact path={`${config.rootRoute}/wizards/:id`} key='wizards' component={WizardEdit} />,
                            ]
                          }

                          {
                            permissions.includes('forms') && [
                              <Route exact path={`${config.rootRoute}/forms`} key='forms' component={Forms} />,
                              <Route exact path={`${config.rootRoute}/forms/create`} key='forms' component={EditForm} />,
                              <Route exact path={`${config.rootRoute}/forms/clone/:id`} key='forms' component={CloneForm} />,
                              <Route exact path={`${config.rootRoute}/forms/:id`} key='forms' component={EditForm} />,
                            ]
                          }

                          {
                            permissions.includes('appUsage') && [
                              <Route exact path={`${config.rootRoute}/appUsage`} key='appUsage' component={ApplicationUsageControl} />,
                              <Route exact path={`${config.rootRoute}/appUsage/logs/:id`} key='appUsageLogs' component={ApplicationUsageLogs} />,
                            ]
                          }

                          {
                            permissions.includes('regPlan')
                              && <Route exact path={`${config.rootRoute}/regplan`} component={RegPlan} />
                          }

                          {
                            permissions.includes('autofillHistory') && [
                              <Route exact path={`${config.rootRoute}/autofillhistory`} key='autofill-history' component={AutofillHistory} />,
                              <Route exact path={`${config.rootRoute}/autofillhistory/:id`} key='autofill-history-details' component={AutofillHistoryDetails} />,
                            ]
                          }

                          {
                            permissions.includes('logs')
                              && <Route exact path={`${config.rootRoute}/logs`} component={Logs} />
                          }

                          {
                            permissions.includes('cron')
                              && <Route exact path={`${config.rootRoute}/cron`} component={Cron} />
                          }
                          <Route exact path={`${config.rootRoute}/grafanaDashboards`} component={GrafanaDashboards} />
                          
                          {
                            permissions.includes('dbMode')
                              && <Route exact path={`${config.rootRoute}/dbMode`} component={DBMode} />
                          }
                          <Route exact path={`${config.rootRoute}/profile/:item`} component={Profile} />
                          <Route exact path={`${config.rootRoute}/guide`} component={Guide} />
                          <Route component={NotFoundPage} />
                        </Switch>
                      </div>

                      <Layout.Footer style={{ textAlign: 'center', backgroundColor: 'transparent' }}>
                        © {moment().format('YYYY')} RegDesk, Inc. All rights reserved
                      </Layout.Footer>
                    </Layout.Content>

                    <ModalTimer />
                  </Layout>
                ) : <Redirect to={`${config.rootRoute}/`} />
            }
          />
        </Switch>
      </Router>
    );
  }
}
