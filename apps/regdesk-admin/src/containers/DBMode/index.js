import React, { useState } from 'react';
import { Card, Button, message } from 'antd';
import config from 'config';
import api from '../../utils/api';
import Breadcrumb from '../../components/Breadcrumb';

export default () => {
  const [loading, setLoading] = useState(false);

  /**
   * Request
   * @param mode
   */
  const request = (mode) => {
    setLoading(true);

    api.dbMode
      .change({ mode })
      .then(() => {
        message.success('Request has been sent');
        setLoading(false);
      })
      .catch(() => setLoading(false));
  };

  return (
    <div>
      <Breadcrumb routes={[ { title: 'Dashboard', href: `${config.rootRoute}/` }, { title: 'DB mode' } ]} />

      <div style={{ display: 'flex', justifyContent: 'center', flexDirection: 'column', alignItems: 'center' }}>
        <div style={{ width: 600, marginBottom: 20, textAlign: 'center' }}>
          Please, after starting the script, wait for 5-15 minutes.
          The server must save new configures and restart.
        </div>

        <Card bodyStyle={{ width: 350 }}>
          <Button type='primary' loading={loading} disabled={loading} onClick={() => request('default')} style={{ width: '100%', marginBottom: 15 }}>Default</Button>
          <Button type='primary' loading={loading} disabled={loading} onClick={() => request('bausch')} style={{ width: '100%', marginBottom: 15 }}>Bausch</Button>
        </Card>
      </div>
    </div>
  );
};
