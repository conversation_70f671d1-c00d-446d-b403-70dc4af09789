import React, { useState } from 'react';
import { Card, Button, message } from 'antd';
import config from 'config';
import api from '../../utils/api';
import Breadcrumb from '../../components/Breadcrumb';

export default () => {
  const [loading, setLoading] = useState(false);

  /**
   * Request
   * @param module
   */
  const request = (module) => {
    setLoading(true);

    api.cron
      .sync({ module })
      .then(() => {
        message.success('Request has been sent');
        setLoading(false);
      })
      .catch(() => setLoading(false));
  };

  return (
    <div>
      <Breadcrumb routes={[ { title: 'Dashboard', href: `${config.rootRoute}/` }, { title: 'Sync' } ]} />

      <div style={{ display: 'flex', justifyContent: 'center', flexDirection: 'column', alignItems: 'center' }}>
        <div style={{ width: 600, marginBottom: 20, textAlign: 'center' }}>
          Please, after starting the script, wait for 15-30 minutes before start the next script.
          If sync lasts longer, than the server can break the connections and show error 500.
          At the same time, synchronization itself on the server will continue.
        </div>

        <Card bodyStyle={{ width: 350 }}>
          <Button type='primary' loading={loading} disabled={loading} onClick={() => request('ccp')} style={{ width: '100%', marginBottom: 15 }}>Import CCP</Button>
          <Button type='primary' loading={loading} disabled={loading} onClick={() => request('alerts')} style={{ width: '100%', marginBottom: 15 }}>Import Alerts</Button>
          <Button type='primary' loading={loading} disabled={loading} onClick={() => request('form')} style={{ width: '100%', marginBottom: 15 }}>Import Forms</Button>
          <Button type='primary' loading={loading} disabled={loading} onClick={() => request('guides')} style={{ width: '100%', marginBottom: 15 }}>Import Guides</Button>
          <Button type='primary' loading={loading} disabled={loading} onClick={() => request('regs')} style={{ width: '100%', marginBottom: 15 }}>Import Regs</Button>
          <Button type='primary' loading={loading} disabled={loading} onClick={() => request('applications')} style={{ width: '100%', marginBottom: 15 }}>Import Applications</Button>
          <Button type='primary' loading={loading} disabled={loading} onClick={() => request('standards')} style={{ width: '100%' }}>Import Standards</Button>
        </Card>
      </div>
    </div>
  );
};
