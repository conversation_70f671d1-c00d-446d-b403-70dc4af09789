import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import moment from 'moment';
import { Button, Table, Card, Popconfirm, Icon, Select, Dropdown, Menu, Input } from 'antd';
import { allCountriesWithEU } from '../../utils/countries';
import Breadcrumb from '../../components/Breadcrumb';
import CountryFlag from '../../components/CountryFlag';
import ManualDatePicker from '../../components/ManualDatePicker';
import Add from './Add';
import actions from '../../actions';
import styles from './index.less';

const { Item } = Menu;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = ManualDatePicker;

/**
 * Alerts List
 * @param {Object[]} list
 * @param {boolean} loading
 * @param {Object} pagination
 * @param {string[]} permissions
 * @param {string[]} existsCountries
 * @returns {JSX.Element}
 */
const Alerts = ({
  list,
  loading,
  pagination,
  permissions,
  existsCountries,
}) => {
  const timerRef = useRef(null);
  const [showModal, setShowModal] = useState(false);
  const [alert, setAlert] = useState(null);
  const { total = 0 } = pagination;

  useEffect(() => {
    actions.alerts.get();
    actions.alerts.getCountries();
  }, []);

  const getColumns = () => [
    {
      title: 'Create date',
      dataIndex: 'createdDate',
      width: 120,
      render: date => moment(date).getUTC(),
    },
    {
      title: 'Country',
      dataIndex: 'countryId',
      width: 120,
      render: country => <CountryFlag countryId={country} />,
    },
    {
      title: 'Title',
      dataIndex: 'title',
    },
    {
      title: 'Content',
      dataIndex: 'content',
    },
    {
      title: 'Released',
      dataIndex: 'released',
      width: 90,
      render: released => (
        <div className={styles.column}>
          {released ? <Icon className={styles.iconSuccess} type='check-circle' /> : <Icon className={styles.iconError} type='close-circle' />}
        </div>
      )
    },
    {
      title: 'Actions',
      width: 110,
      render: item => {
        const { _id, released } = item || {};

        const menu = (
          <Menu>
            <Item><a onClick={() => { setShowModal(true); setAlert(item); }}>Edit</a></Item>
            <Item><a onClick={() => actions.alerts.updateRelease(_id, released)}>{released ? 'Unrelease' : 'Release'}</a></Item>

            {permissions.includes('accessAlertRemove') && (
              <Item>
                <Popconfirm
                  okText='Yes'
                  cancelText='No'
                  title='Delete the alert?'
                  onConfirm={() => actions.alerts.delete(_id)}
                >
                  <a>Delete</a>
                </Popconfirm>
              </Item>
            )}
          </Menu>
        );

        return (
          <Dropdown overlay={menu} className={styles.column}>
            <a className='ant-dropdown-link'>
              Actions <Icon type='down' />
            </a>
          </Dropdown>
        );
      }
    },
  ];

  return (
    <>
      <Breadcrumb routes={[{ title: 'Dashboard', href: `${config.rootRoute}/` }, { title: 'Alerts' }]} />

      <Card className={styles.alerts}>
        <div className={styles.btnWrapper}>
          <div className={styles.filters}>
            <Select
              showSearch
              allowClear
              className={styles.select}
              placeholder='Search by country...'
              filterOption={(input, option) => option.props.children.toLowerCase().includes(input.toLowerCase())}
              onChange={countryId => actions.alerts.filter({ countryId })}
            >
              {allCountriesWithEU
                .filter(({ alpha3code }) => existsCountries.includes(alpha3code))
                .sort((a, b) => a.name.localeCompare(b.name))
                .map(({ alpha3code, name }) => <Option key={alpha3code} value={alpha3code}>{name}</Option>)}
            </Select>

            <Search
              allowClear
              className={styles.search}
              placeholder='Search by text...'
              onChange={e => {
                const search = e.target.value;

                clearTimeout(timerRef.current);
                timerRef.current = setTimeout(() => actions.alerts.filter({ search }), 600);
              }}
            />

            <RangePicker className={styles.datePicker} onChange={dateRange => actions.alerts.filter({ dateRange })} />
          </div>

          <Button icon='plus' type='primary' onClick={() => setShowModal(true)}>
            Create New Alert
          </Button>
        </div>

        <Table
          bordered
          rowKey='_id'
          size='middle'
          loading={loading}
          dataSource={list}
          columns={getColumns()}
          className={styles.table}
          pagination={{
            showTotal: () => `Total ${total} items`,
            defaultPageSize: 10,
            size: 'Pagination',
            ...pagination,
          }}
          onChange={newPagination => actions.alerts.get({ pagination: newPagination })}
        />
      </Card>

      {showModal && <Add alert={alert} show={showModal} onClose={() => setShowModal(false)} />}
    </>
  );
};

export default connect(({ alerts, account }) => ({
  list: alerts.list,
  loading: alerts.loading,
  pagination: alerts.pagination,
  permissions: account.adminPermissions,
  existsCountries: alerts.existsCountries,
}))(Alerts);
