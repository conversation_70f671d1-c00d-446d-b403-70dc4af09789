import React, { useReducer, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Form, Button, Input, Badge } from 'antd';
import MDEditor from '../../../components/MarkDownEditor';
import Conditions from './Conditions';

const { Item } = Form;

const formItemLayout = {
  labelCol: { xs: { span: 4 } },
  wrapperCol: { xs: { span: 16 } },
};

const initialState = {
  _id: '',
  name: '',
  prefix: '',
  hint: '',
  description: '',
  oneOfEnabled: false,
  conditions: [],
};

const init = state => ({ ...initialState, ...state });

const reducer = (state, action) => {
  const { type, payload } = action || {};

  switch (type) {
    case 'change':
      return { ...state, ...payload };
    case 'reset':
      return { ...initialState };
    default:
      return { ...state };
  }
};

/**
 * Wizard Section
 * @param {string} type
 * @param {?string} parentId
 * @param {?Object} currentSection
 * @param {Object} ref
 * @returns {JSX.Element}
 */
export default forwardRef(({ type, parentId, currentSection }, ref) => {
  const [activePrefix, dispatchPrefix] = useReducer(active => !active, !!currentSection?.prefix);
  const [state, dispatch] = useReducer(reducer, currentSection, init);
  const { name, prefix, hint, description, oneOfEnabled, conditions } = state;

  useImperativeHandle(
    ref,
    () => ({ getValue: () => ({ ...state, type }) }),
    [name, prefix, hint, description, oneOfEnabled, conditions]
  );

  const onChange = (item, value) => dispatch({ type: 'change', payload: { [item]: value } });

  useEffect(() => {
    dispatch({ type: 'change', payload: currentSection });

    return () => dispatch({ type: 'reset' });
  }, [parentId, currentSection]);

  return (
    <>
      <Item required label='Name' {...formItemLayout}>
        <Input
          autoFocus
          placeholder='Name...'
          value={name}
          onChange={e => onChange('name', e.target.value)}
        />
      </Item>

      <Item label='Prefix' {...formItemLayout}>
        <Input
          placeholder='Prefix...'
          disabled={!activePrefix}
          value={prefix}
          onChange={e => onChange('prefix', e.target.value)}
        />

        <Badge dot={false}>
          <Button
            style={{ fontSize: 14, position: 'absolute', top: -22, left: 8 }}
            onClick={() => { dispatchPrefix(); onChange('prefix', ''); }}
          >
            {!activePrefix ? 'Enable' : 'Disable'}
          </Button>
        </Badge>
      </Item>

      <MDEditor
        value={hint}
        label='Hint'
        height={300}
        setValue={value => onChange('hint', value)}
      />

      <MDEditor
        value={description}
        label='Description (UI)'
        setValue={value => onChange('description', value)}
      />

      <Conditions oneOfEnabled={oneOfEnabled} conditions={conditions} onChange={onChange} />
    </>
  );
});
