import React from 'react';
import { Modal } from 'antd';
import Form from './Form';

/**
 * Update Wizard Version Modal
 * @param {boolean} show
 * @param {string} title
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ show, title, onClose, ...props }) => (
  <Modal
    title={`Add ${title}`}
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={() => onClose()}
    onClose={() => onClose()}
    width={700}
    zIndex={999}
  >
    <Form title={title} onClose={onClose} {...props} />
  </Modal>
);
