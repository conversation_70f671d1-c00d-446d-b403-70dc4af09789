import React, { useState } from 'react';
import { Button, Form, Input } from 'antd';
import actions from '../../../../actions';

const { Item } = Form;

const formItemLayout = {
  labelCol: { xs: { span: 6 }, },
  wrapperCol: { xs: { span: 14 }, },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Update Wizard Version
 * @param {string} item
 * @param {string} title
 * @param {?Object} wizard
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ item, title, wizard, onClose }) => {
  const { _id: id, releaseDate, versionNumber, versionDescription } = wizard || {};
  const [newName, setNewName] = useState('');

  const reset = () => {
    setNewName('');
    onClose();
  };

  const onUpdate = () => {
    const newVersion = { releaseDate, versionNumber, versionDescription };

    newVersion[item] = newName;
    actions.wizards.updateVersion(id, newVersion);
    reset();
  };

  return (
    <>
      <Item required label={title} {...formItemLayout}>
        <Input
          autoFocus
          placeholder={`Enter ${title.toLowerCase()}`}
          value={newName}
          onChange={e => setNewName(e.target.value)}
        />
      </Item>

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={() => reset()}>Cancel</Button>

        <Button
          style={{ marginLeft: 8 }}
          type='primary'
          disabled={!newName}
          onClick={() => onUpdate()}
        >
          Update
        </Button>
      </Item>
    </>
  );
};
