import React, { useEffect, useLayoutEffect, useReducer } from 'react';
import { Form, Input, Select, Checkbox, Tooltip, Icon } from 'antd';
import api from '../../../../utils/api';

const { Item } = Form;
const { Option } = Select;

const formItemLayout = {
  labelCol: { xs: { span: 6 } },
  wrapperCol: { xs: { span: 18 } },
};

const formTailLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16, offset: 4 },
};

const initialState = {
  loading: false,
  fields: [],
  specialties: [],
  selectedSpecialties: [],
};

const reducer = (state, action) => {
  const { type, payload } = action || {};

  switch (type) {
    case 'startLoading':
      return { ...state, loading: true };
    case 'endLoading':
      return { ...state, loading: false };
    case 'change':
      return { ...state, ...payload };
    case 'reset':
      return { ...initialState };
    default:
      return { ...state };
  }
};

/**
 * Classifications Question
 * @param {?string} questionId
 * @param {Object[]} classifications
 * @param {Object[]} classificationData
 * @param {Object} formLayout
 * @param {boolean} selectAll
 * @param {function} onChangeData
 * @param {function} onChangeSelectAll
 * @returns {JSX.Element}
 */
export default ({ questionId, isEStar, classifications, classificationData, formLayout, selectAll, onChange, onChangeData, onChangeSelectAll }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const { loading, fields, specialties, selectedSpecialties } = state;

  const onSelect = (values = []) => {
    dispatch({ type: 'change', payload: { selectedSpecialties: values } });
    dispatch({ type: 'startLoading' });

    api.wizards
      .getClassificationQuestionsData({ fieldName: '_id', filters: { medicalSpecialty: values } })
      .then(({ data }) => onChangeData(data))
      .finally(() => dispatch({ type: 'endLoading' }));
  };

  const onChecked = () => {
    onChangeSelectAll();

    if (!selectAll) {
      dispatch({ type: 'change', payload: { selectedSpecialties: [] } });

      api.wizards
        .getClassificationQuestionsData({ fieldName: '_id' })
        .then(({ data }) => onChangeData(data));
    }
  };

  const onUpdate = (key, item, value) => {
    const data = [...fields];
    const newField = { ...data[key], [item]: value };

    data.splice(key, 1, newField);
    dispatch({ type: 'change', payload: { fields: data } });
    onChange(data);
  };

  useLayoutEffect(() => {
    if (JSON.stringify(classifications) !== JSON.stringify(fields) && classifications.length) {
      dispatch({ type: 'change', payload: { fields: classifications } });

      if (questionId && !selectAll && classificationData?.length) {
        dispatch({ type: 'startLoading' });

        api.wizards
          .getClassificationQuestionsData({ fieldName: 'medicalSpecialty', filters: { ids: classificationData } })
          .then(({ data }) => dispatch({ type: 'change', payload: { selectedSpecialties: data } }))
          .finally(() => dispatch({ type: 'endLoading' }));
      } else if (selectAll) {
        dispatch({ type: 'change', payload: { selectedSpecialties: [] } });
      }
    }
  }, [classifications]);

  useEffect(() => {
    if (!selectAll) {
      dispatch({ type: 'startLoading' });

      api.wizards
        .getClassificationQuestionsData({ fieldName: 'medicalSpecialty' })
        .then(({ data }) => dispatch({ type: 'change', payload: { specialties: data } }))
        .finally(() => dispatch({ type: 'endLoading' }));
    }
  }, [selectAll]);

  return (
    <>
      <Item {...formTailLayout}>
        <Checkbox checked={selectAll} onChange={() => onChecked()}>
          Select All
        </Checkbox>
      </Item>

      <Item label='Options' style={{ margin: 0 }} {...formLayout}>
        <div style={{ marginTop: 30 }}>
          {fields.map(({ key, title, type, placeholder = '', hint, xmlTagName }, index) => (
            <div key={key} style={{ display: 'flex', marginBottom: 16 }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 10, width: '100%', marginBottom: 16 }}>
                <Item label='Title' style={{ margin: 0 }} {...formItemLayout}>
                  <Input disabled value={title} />
                </Item>

                <Item label='Placeholder' style={{ margin: 0 }} {...formItemLayout}>
                  <Input disabled value={placeholder} />
                </Item>

                <Item label='Type' style={{ margin: 0 }} {...formItemLayout}>
                  <Select
                    disabled
                    value={type}
                    style={{ width: '100%' }}
                    optionLabelProp='label'
                  >
                    <Option value='select' label='Select'>Select</Option>
                    <Option value='input' label='Input'>Input</Option>
                  </Select>
                </Item>

                <Item label='Hint' style={{ margin: 0 }} {...formItemLayout}>
                  <Input disabled value={hint} />
                </Item>

                {isEStar && (
                  <Item label='XML Association' style={{ margin: 0 }} {...formItemLayout}>
                    <Input value={xmlTagName} onChange={e => onUpdate(index, 'xmlTagName', e.target.value)} />
                  </Item>
                )}

                {key !== 'associatedProductCode' && (
                  <Item label='Values' style={{ margin: 0 }} {...formItemLayout}>
                    {key === 'medicalSpecialty' && (
                      <Select
                        showSearch
                        mode='multiple'
                        loading={loading}
                        style={{ width: '100%' }}
                        value={selectedSpecialties}
                        placeholder='Select medical specialties'
                        disabled={loading && !selectedSpecialties.length || selectAll}
                        onChange={values => onSelect(values)}
                        filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      >
                        {specialties.map(item => <Option key={item} value={item}>{item}</Option>)}
                      </Select>
                    )}

                    {['regulation', 'productCode'].includes(key) && (
                      <Tooltip title='Values associated with this field will be based on the selected Medical Specialty'>
                        <Icon type='exclamation-circle' style={{ fontSize: '18px' }} />
                      </Tooltip>
                    )}
                  </Item>
                )}
              </div>
            </div>
          ))}
        </div>
      </Item>
    </>
  );
};
