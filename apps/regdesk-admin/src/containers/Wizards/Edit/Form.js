import React from 'react';
import { <PERSON><PERSON>, Affix, <PERSON>, Popcon<PERSON>rm, Tooltip } from 'antd';

/**
 * Wizard Form
 * @param {string} type
 * @param {Object} current
 * @param {string} deleteMessage
 * @param {?boolean} canDelete
 * @param {JSX.Element} children
 * @param {function} onSubmit
 * @param {function} onDelete
 * @param {function} onOpenBuffer
 * @param {function} onPushBuffer
 * @param {function} onPushBufferTemplate
 * @returns {JSX.Element}
 */
export default ({ type, current, deleteMessage, canDelete = true, children, onSubmit, onDelete, onOpenBuffer, onPushBuffer, onPushBufferTemplate }) => {
  const handleSubmit = e => {
    e.preventDefault();
    onSubmit();
  };

  return (
    <>
      <form onSubmit={handleSubmit}>
        <Affix>
          <Card
            style={{ marginBottom: -1, position: 'sticky' }}
            bodyStyle={{ padding: '12px 24px' }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center', fontWeight: 500, fontSize: 16, color: 'rgba(0, 0, 0, 0.85)' }}>
                {current ? 'Edit' : 'Add'} {type}
              </div>

              <div style={{ display: 'flex', alignItems: 'center' }}>
                {current && onDelete && (
                  <Tooltip title={!canDelete && `${type === 'question' ? 'Question' : 'Section'} is used as a condition. Please remove the condition first!`}>
                    <Popconfirm
                      title={deleteMessage}
                      okText='Confirm'
                      cancelText='Cancel'
                      disabled={!canDelete}
                      onConfirm={() => onDelete()}
                    >
                      <Button
                        style={{ marginRight: 10 }}
                        type='danger'
                        disabled={!canDelete}
                      >
                        Delete
                      </Button>
                    </Popconfirm>
                  </Tooltip>
                )}

                <Button type='primary' htmlType='submit'>
                  {current ? 'Save' : 'Add'}
                </Button>
              </div>
            </div>
          </Card>
        </Affix>

        <Card>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              backgroundColor: 'rgb(241, 241, 241)',
              padding: '5px 10px 5px 17px',
              margin: '0 0 20px'
            }}
          >
            <div style={{
              color: 'rgba(0, 0, 0, 0.85)',
              fontWeight: 500,
              fontSize: 16,
              lineHeight: '30px'
            }}
            >
              Form
            </div>

            <div>
              <span style={{ marginRight: 5 }}>Buffer:</span>

              {current && (
                <>
                  <Button
                    type='link'
                    icon='export'
                    style={{ padding: '0 5px' }}
                    onClick={onPushBufferTemplate}
                  >
                    Copy Template
                  </Button>

                  <Button
                    type='link'
                    icon='export'
                    style={{ padding: '0 5px' }}
                    onClick={onPushBuffer}
                  >
                    Copy from
                  </Button>
                </>
              )}

              <Button
                type='link'
                icon='import'
                style={{ padding: '0 5px' }}
                onClick={onOpenBuffer}
              >
                Insert
              </Button>
            </div>
          </div>

          {children}
        </Card>
      </form>
    </>
  );
};
