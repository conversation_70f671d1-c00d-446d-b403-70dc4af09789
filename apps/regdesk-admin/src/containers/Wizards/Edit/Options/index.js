import React, { useState, useEffect } from 'react';
import { Form, Button, Input, Badge, Icon } from 'antd';

const { Item } = Form;

/**
 * Options
 * @param {boolean} hideLabel
 * @param {Object[]} options
 * @param {boolean} disabled
 * @param {Object} layout
 * @param {string} questionId
 * @param {function} onChange
 * @returns {JSX.Element}
 */
export default ({ hideLabel, options, disabled, layout, questionId, onChange }) => {
  const emptyField = { label: '', value: '' };
  const [newOptions, setNewOptions] = useState(options.length ? options : [emptyField]);

  const onAdd = () => {
    const data = [...newOptions, emptyField];

    setNewOptions(data);
    onChange(data);
  };

  const onUpdate = (key, item, value) => {
    const data = [...newOptions];

    data.splice(key, 1, { ...data[key], [item]: value });
    setNewOptions(data);
    onChange(data);
  };

  const onRemove = key => {
    const data = [...newOptions];

    data.splice(key, 1);
    setNewOptions(data);
    onChange(data);
  };

  useEffect(() => {
    if (questionId) setNewOptions(options.length ? options : [emptyField]);
  }, [options]);

  return (
    <Item label={!hideLabel && 'Options'} style={{ margin: 0 }} {...layout}>
      <div style={{ marginTop: 4 }}>
        {newOptions.map(({ _id, label, value }, key) => (
          <div key={_id || key} style={{ display: 'flex' }}>
            <div style={{ display: 'flex', gap: 10, width: '100%', marginBottom: 16 }}>
              <Input
                value={value}
                disabled={disabled}
                placeholder='Enter value'
                onChange={e => onUpdate(key, 'value', e.target.value)}
              />

              <Input
                value={label}
                disabled={disabled}
                placeholder='Enter label'
                onChange={e => onUpdate(key, 'label', e.target.value)}
              />
            </div>

            {newOptions.length > 1 && !disabled && (
              <div style={{ position: 'relative', width: 0, height: 0 }}>
                <Badge dot={false}>
                  <Icon
                    type='delete'
                    style={{ position: 'absolute', top: -17, left: 8 }}
                    onClick={() => onRemove(key)}
                  />
                </Badge>
              </div>
            )}
          </div>
        ))}
      </div>

      <Button type='dashed' disabled={disabled} style={{ width: '100%' }} onClick={onAdd}>
        <Icon type='plus' /> Add label
      </Button>
    </Item>
  );
};
