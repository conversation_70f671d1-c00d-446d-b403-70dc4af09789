import React, { useState, useEffect } from 'react';
import { Form, Button, Input, Badge, Icon } from 'antd';

const { Item } = Form;

/**
 * Values
 * @param {boolean} hideLabel
 * @param {Object[]} values
 * @param {boolean} disabled
 * @param {?string} text
 * @param {Object} layout
 * @param {string} questionId
 * @param {function} onChange
 * @returns {JSX.Element}
 */
export default ({ hideLabel, values, disabled, text, layout, questionId, onChange }) => {
  const emptyField = '';
  const [newValues, setNewValues] = useState(values.length ? values : [emptyField]);

  const onAdd = () => {
    const data = [...newValues, emptyField];

    setNewValues(data);
    onChange(data);
  };

  const onUpdate = (key, value) => {
    const data = [...newValues];

    data.splice(key, 1, value);
    setNewValues(data);
    onChange(data);
  };

  const onRemove = key => {
    const data = [...newValues];

    data.splice(key, 1);
    setNewValues(data);
    onChange(data);
  };

  useEffect(() => {
    if (questionId) setNewValues(values.length ? values : [emptyField]);
  }, [values]);

  return (
    <Item label={!hideLabel && 'Options'} style={{ margin: 0 }} {...layout}>
      <div style={{ marginTop: 4 }}>
        {newValues.map((value, key) => (
          <div key={key} style={{ display: 'flex' }}>
            <Input
              value={value}
              disabled={disabled}
              placeholder={`Enter ${text || 'label'}`}
              style={{ width: '100%', marginBottom: 16 }}
              onChange={e => onUpdate(key, e.target.value)}
            />

            {newValues.length > 1 && !disabled && (
              <div style={{ position: 'relative', width: 0, height: 0 }}>
                <Badge dot={false}>
                  <Icon
                    type='delete'
                    style={{ position: 'absolute', top: -17, left: 8 }}
                    onClick={() => onRemove(key)}
                  />
                </Badge>
              </div>
            )}
          </div>
        ))}
      </div>

      <Button type='dashed' disabled={disabled} style={{ width: '100%' }} onClick={onAdd}>
        <Icon type='plus' /> Add {text || 'label'}
      </Button>
    </Item>
  );
};
