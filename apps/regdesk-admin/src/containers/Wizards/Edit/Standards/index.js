import React, { useEffect, useLayoutEffect, useReducer } from 'react';
import { Form, Input, Select, Checkbox, Tooltip, Icon } from 'antd';
import { usages } from '../../utils';
import Values from '../Values';
import api from '../../../../utils/api';

const { Item } = Form;
const { Option } = Select;

const formItemLayout = {
  labelCol: { xs: { span: 6 } },
  wrapperCol: { xs: { span: 18 } },
};

const formTailLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16, offset: 4 },
};

const initialState = {
  loading: false,
  fields: [],
  organizations: [],
  selectedOrgs: [],
};

const reducer = (state, action) => {
  const { type, payload } = action || {};

  switch (type) {
    case 'startLoading':
      return { ...state, loading: true };
    case 'endLoading':
      return { ...state, loading: false };
    case 'change':
      return { ...state, ...payload };
    case 'reset':
      return { ...initialState };
    default:
      return { ...state };
  }
};

/**
 * Standards Question
 * @param {?string} questionId
 * @param {Object[]} standards
 * @param {Object[]} standardData
 * @param {Object} formLayout
 * @param {boolean} selectAll
 * @param {function} onChange
 * @param {function} onChangeData
 * @param {function} onChangeSelectAll
 * @returns {JSX.Element}
 */
export default ({ questionId, isEStar, standards, standardData, formLayout, selectAll, onChange, onChangeData, onChangeSelectAll }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const { loading, fields, organizations, selectedOrgs } = state;

  const onUpdateOptions = (index, values = []) => {
    const data = [...fields];
    const newField = { ...data[index] };

    newField.values = values;
    data.splice(index, 1, newField);
    dispatch({ type: 'change', payload: { fields: data } });
    onChange(data);
  };

  const onSelect = (values = []) => {
    dispatch({ type: 'change', payload: { selectedOrgs: values } });
    dispatch({ type: 'startLoading' });

    api.wizards
      .getStandardQuestionsData({ fieldName: '_id', filters: { organization: values } })
      .then(({ data }) => onChangeData(data))
      .finally(() => dispatch({ type: 'endLoading' }));
  };

  const onChecked = () => {
    onChangeSelectAll();

    if (!selectAll) {
      dispatch({ type: 'change', payload: { selectedOrgs: [] } });

      api.wizards
        .getStandardQuestionsData({ fieldName: '_id' })
        .then(({ data }) => onChangeData(data));
    }
  };

  const onUpdate = (key, item, value) => {
    const data = [...fields];
    const newField = { ...data[key], [item]: value };

    data.splice(key, 1, newField);
    dispatch({ type: 'change', payload: { fields: data } });
    onChange(data);
  };

  useLayoutEffect(() => {
    if (JSON.stringify(standards) !== JSON.stringify(fields) && standards.length) {
      dispatch({ type: 'change', payload: { fields: standards } });

      if (questionId && !selectAll && standardData?.length) {
        dispatch({ type: 'startLoading' });

        api.wizards
          .getStandardQuestionsData({ fieldName: 'organization', filters: { ids: standardData } })
          .then(({ data }) => dispatch({ type: 'change', payload: { selectedOrgs: data } }))
          .finally(() => dispatch({ type: 'endLoading' }));
      } else if (selectAll) {
        dispatch({ type: 'change', payload: { selectedOrgs: [] } });
      }
    }
  }, [standards]);

  useEffect(() => {
    if (!selectAll) {
      dispatch({ type: 'startLoading' });

      api.wizards
        .getStandardQuestionsData({ fieldName: 'organization' })
        .then(({ data }) => dispatch({ type: 'change', payload: { organizations: data } }))
        .finally(() => dispatch({ type: 'endLoading' }));
    }
  }, [selectAll]);

  return (
    <>
      <Item {...formTailLayout}>
        <Checkbox checked={selectAll} onChange={() => onChecked()}>
          Select All
        </Checkbox>
      </Item>

      <Item label='Options' style={{ margin: 0 }} {...formLayout}>
        <div style={{ marginTop: 30 }}>
          {fields.map(({ key, title, type, placeholder, hint, values: selectedUsages = [], xmlTagName }, index) => (
            <div key={key} style={{ display: 'flex', marginBottom: 16 }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 10, width: '100%', marginBottom: 16 }}>
                <Item label='Title' style={{ margin: 0 }} {...formItemLayout}>
                  <Input disabled value={title} />
                </Item>

                <Item label='Placeholder' style={{ margin: 0 }} {...formItemLayout}>
                  <Input disabled value={placeholder} />
                </Item>

                <Item label='Type' style={{ margin: 0 }} {...formItemLayout}>
                  <Select
                    disabled
                    value={type}
                    style={{ width: '100%' }}
                    optionLabelProp='label'
                  >
                    <Option value='select' label='Select'>Select</Option>
                  </Select>
                </Item>

                <Item label='Hint' style={{ margin: 0 }} {...formItemLayout}>
                  <Input disabled value={hint} />
                </Item>

                {isEStar && (
                  <Item label='XML Association' style={{ margin: 0 }} {...formItemLayout}>
                    <Input value={xmlTagName} onChange={e => onUpdate(index, 'xmlTagName', e.target.value)} />
                  </Item>
                )}

                <Item label='Values' style={{ margin: 0 }} {...formItemLayout}>
                  {key === 'organization' && (
                    <Select
                      showSearch
                      mode='multiple'
                      loading={loading}
                      value={selectedOrgs}
                      style={{ width: '100%' }}
                      placeholder='Select organization'
                      disabled={loading && !selectedOrgs.length || selectAll}
                      onChange={values => onSelect(values)}
                      filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    >
                      {organizations.map(item => <Option key={item} value={item}>{item}</Option>)}
                    </Select>
                  )}

                  {['designationNumber', 'recognitionNumber', 'title'].includes(key) && (
                    <Tooltip title='Values associated with this field will be based on the selected organization'>
                      <Icon type='exclamation-circle' style={{ fontSize: '18px' }} />
                    </Tooltip>
                  )}

                  {key === 'usage' && (
                    <Values
                      hideLabel
                      text='value'
                      questionId={questionId}
                      values={questionId ? selectedUsages : usages}
                      onChange={values => onUpdateOptions(index, values)}
                    />
                  )}
                </Item>
              </div>
            </div>
          ))}
        </div>
      </Item>
    </>
  );
};
