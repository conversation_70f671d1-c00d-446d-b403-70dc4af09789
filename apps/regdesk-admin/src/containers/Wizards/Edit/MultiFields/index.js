import React, { useState, useEffect } from 'react';
import { Form, Button, Input, Badge, Icon, Checkbox, Select } from 'antd';
import { multiFieldTypes } from '../../utils';
import Options from '../Options';
import Values from '../Values';

const { Item } = Form;
const { Option } = Select;

const formItemLayout = {
  labelCol: { xs: { span: 6 } },
  wrapperCol: { xs: { span: 18 } },
};

const formTailLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 19, offset: 4 },
};

const formOptionsLayout = {
  labelCol: { span: 2 },
  wrapperCol: { span: 18, offset: 3 },
};

/**
 * Multi Fields Question
 * @param {?string} questionId
 * @param {Object[]} multiFields
 * @param {Object} formLayout
 * @param {boolean} disabled
 * @param {function} onChange
 * @returns {JSX.Element}
 */
export default ({ questionId, isEStar, multiFields, formLayout, disabled, onChange }) => {
  const emptyField = { title: '', placeholder: '', type: '', values: [], options: [], hint: '', multiple: false, column: false };
  const [newFields, setNewFields] = useState(multiFields.length ? multiFields : [emptyField]);

  const onAdd = () => {
    const data = [...newFields, emptyField];

    setNewFields(data);
    onChange(data);
  };

  const onUpdate = (key, item, value) => {
    const data = [...newFields];

    const newField = { ...data[key], [item]: value };

    if (item === 'type') {
      newField.values = [];
      newField.options = [];
      newFields.multiple = false;
    }

    data.splice(key, 1, newField);
    setNewFields(data);
    onChange(data);
  };

  const onRemove = key => {
    const data = [...newFields];

    data.splice(key, 1);
    setNewFields(data);
    onChange(data);
  };

  useEffect(() => {
    if ((disabled || questionId) && JSON.stringify(multiFields) !== JSON.stringify(newFields) && multiFields.length) {
      setNewFields(multiFields);
    }
  }, [multiFields]);

  const fields = [
    {
      key: 'title',
      general: true,
      label: 'Title',
      cmp: ({ title }, key) => (
        <Input
          value={title}
          disabled={disabled}
          placeholder='Enter title'
          onChange={e => onUpdate(key, 'title', e.target.value)}
        />
      )
    },
    {
      key: 'placeholder',
      general: true,
      label: 'Placeholder',
      cmp: ({ placeholder, type }, key) => (
        <Input
          value={placeholder}
          disabled={type === 'date-picker' || disabled}
          placeholder='Enter placeholder'
          onChange={e => onUpdate(key, 'placeholder', e.target.value)}
        />
      )
    },
    {
      key: 'type',
      general: true,
      label: 'Type',
      cmp: ({ type }, key) => (
        <Select
          showSearch
          disabled={disabled}
          value={type || undefined}
          style={{ width: '100%' }}
          optionLabelProp='label'
          placeholder='Select type'
          onChange={newType => onUpdate(key, 'type', newType)}
          filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {multiFieldTypes.map(({ label, value }) => (
            <Option key={value} value={value} label={label}>
              {label}
            </Option>
          ))}
        </Select>
      )
    },
    {
      key: 'values',
      label: 'Value',
      cmp: ({ values }, key) => (
        <Values
          hideLabel
          values={values}
          disabled={disabled}
          questionId={questionId}
          layout={formOptionsLayout}
          onChange={items => onUpdate(key, 'values', items)}
        />
      )
    },
    {
      key: 'options',
      label: 'Value',
      cmp: ({ options }, key) => (
        <Options
          hideLabel
          options={options}
          disabled={disabled}
          questionId={questionId}
          layout={formOptionsLayout}
          onChange={items => onUpdate(key, 'options', items)}
        />
      )
    },
    {
      key: 'hint',
      general: true,
      label: 'Hint',
      cmp: ({ hint }, key) => (
        <Input
          value={hint}
          disabled={disabled}
          placeholder='Enter hint'
          onChange={e => onUpdate(key, 'hint', e.target.value)}
        />
      )
    },
    {
      key: 'xmlTagName',
      dependence: isEStar,
      label: 'XML Association',
      cmp: ({ xmlTagName }, key) => (
        <Input
          value={xmlTagName}
          placeholder='Enter XML Association'
          onChange={e => onUpdate(key, 'xmlTagName', e.target.value)}
        />
      )
    },
    {
      key: 'multiple',
      layout: formTailLayout,
      cmp: ({ multiple }, key) => (
        <Checkbox
          checked={multiple}
          disabled={disabled}
          onChange={() => onUpdate(key, 'multiple', !multiple)}
        >
          Allow multiple
        </Checkbox>
      )
    },
    {
      key: 'column',
      layout: formTailLayout,
      cmp: ({ column }, key) => (
        <Checkbox
          checked={column}
          disabled={disabled}
          onChange={() => onUpdate(key, 'column', !column)}
        >
          Column Layout
        </Checkbox>
      )
    },
  ];

  return (
    <>
      <Item label='Options' style={{ margin: 0 }} {...formLayout}>
        <div style={{ marginTop: 30 }}>
          {newFields.map((item, index) => {
            const { _id, type } = item || {};
            const { extra = [] } = multiFieldTypes.find(({ value }) => value === type) || {};

            return (
              <div key={_id || index} style={{ display: 'flex', marginBottom: 16, position: 'relative' }}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: 10, width: '100%', marginBottom: 16 }}>
                  {fields.filter(({ key, general, dependence }) => general || dependence || extra.includes(key)).map(({ key, label, layout, cmp }) => (
                    <Item key={key} label={label} style={{ margin: 0 }} {...(layout || formItemLayout)}>
                      {cmp(item, index)}
                    </Item>
                  ))}
                </div>

                {newFields.length > 1 && !disabled && (
                  <Badge dot={false}>
                    <Icon
                      type='delete'
                      style={{ position: 'absolute', top: 60, fontSize: 18 }}
                      onClick={() => onRemove(index)}
                    />
                  </Badge>
                )}
              </div>
            );
          })}
        </div>

        <Item style={{ margin: '-20px 0 0' }} {...formTailLayout}>
          <Button type='dashed' style={{ width: '100%' }} disabled={disabled} onClick={onAdd}>
            <Icon type='plus' /> Add field
          </Button>
        </Item>
      </Item>
    </>
  );
};
