import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { Col, Row, Spin, Card, Empty, Button, message } from 'antd';
import { linksAreNotEmpty, optionsAreNotEmpty, optionsAreUnique, idHasNotSystemTag, questionHasNotDefaultTitle, customTitlesAreNotEmpty } from '../utils';
import { allCountriesWithEUObj } from '../../../utils/countries';
import Breadcrumb from '../../../components/Breadcrumb';
import EmptyImage from '../../../assets/empty-image.png';
import BufferModal from '../../../components/Form/Buffer';
import EditQuestion from './Question';
import EditSection from './Section';
import Fields from './Fields';
import Tree from './Tree';
import Add from '../Add';
import Form from './Form';
import actions from '../../../actions';
import styles from '../index.less';

const getBufferTitle = ({ wizard, position, sectionId }) => {
  const { sections, country, notifiedBody, applicationType, productType, classification, versionNumber } = wizard || {};
  const items = [];

  if (allCountriesWithEUObj[country]) items.push(allCountriesWithEUObj[country]);
  if (notifiedBody) items.push(notifiedBody.name);
  if (applicationType) items.push(applicationType.name);
  if (productType) items.push(productType.name);
  if (classification) items.push(classification.name);
  if (versionNumber) items.push(versionNumber);

  if (sectionId) {
    const { name: secName } = sections.find(({ _id }) => _id === sectionId) || {};

    if (secName) items.push(secName);
  } else if (position?.length) {
    let section = sections[position[0]] || {};
    const subSectionsPosition = position.slice(1);

    items.push(section.name);

    subSectionsPosition.forEach(index => {
      section = section.questions[index] || {};
      items.push(section.name);
    });
  }

  return items.join(' / ');
};

/**
 * Wizard
 * @param {Object[]} ids
 * @param {Object} match
 * @param {Object[]} buffer
 * @param {boolean} loading
 * @param {string[]} position
 * @param {Object[]} sections
 * @param {string} sectionId
 * @param {string[]} bufferTypes
 * @param {boolean} showSection
 * @param {boolean} showQuestion
 * @param {Object} currentWizard
 * @param {Object} currentSection
 * @param {boolean} showSubSection
 * @param {Object} currentQuestion
 * @param {Object} currentSubSection
 * @returns {JSX.Element}
 */
const Wizard = ({
  ids,
  match,
  buffer,
  loading,
  position,
  sections,
  sectionId,
  bufferTypes,
  showSection,
  showQuestion,
  currentWizard,
  currentSection,
  showSubSection,
  currentQuestion,
  currentSubSection,
}) => {
  const { id: wizardId } = match.params;
  const { dependentSections = [], dependentQuestions = [] } = currentWizard || {};
  const [showModalClone, setShowModalClone] = useState(false);
  const ref = useRef(null);
  const refBuffer = useRef(null);

  const getValue = () => ref.current?.getValue();

  const handleSubmit = () => {
    const data = getValue();
    const { type } = data || {};
    const { _id: currentSectionId } = currentSection || data || {};
    const { subSection: currentSubSectionId } = currentSubSection || data || {};
    const { _id: currentQuestionId } = currentQuestion || data || {};

    if (['section', 'subsection'].includes(type)) {
      if (!data?.name) return message.warn('Please, enter section name');

      if (type === 'section' && currentSectionId) actions.wizards.updateSection(currentSectionId, data);
      else if (type === 'subsection' && currentSubSectionId) actions.wizards.updateSection(currentSubSectionId, data);
      else if (showSection) actions.wizards.addSection(data);
      else if (showSubSection && sectionId) actions.wizards.addSubSection({ ...data, parentId: sectionId });
    } else {
      if (data?.type !== 'products-data' && !data?.id) return message.warn('Please, select id');
      if (!data?.name) return message.warn('Please, enter name');
      if (!data?.type) return message.warn('Please, select type');
      if (idHasNotSystemTag(data, ids)) return message.warn('Please, select another ID');
      if (!linksAreNotEmpty(data?.example?.links)) return message.warn('Please, remove empty links');
      if (!optionsAreUnique(data) || !optionsAreNotEmpty(data)) return message.warn('Please, enter unique and non-empty options');
      if (questionHasNotDefaultTitle(data)) return message.warn('Please, enter default title');
      if (!customTitlesAreNotEmpty(data?.customTitles)) return message.warn('Please, remove empty custom titles');

      if (currentQuestionId) actions.wizards.updateQuestion(currentQuestionId, data);
      else if (showQuestion) actions.wizards.addQuestion({ ...data, parentId: sectionId });
    }

    return null;
  };

  const onSelectBuffer = item => {
    const { _id: currentSectionId } = currentSection || {};
    const { subSection: currentSubSectionId } = currentSubSection || {};
    const { _id: currentQuestionId } = currentQuestion || {};

    const props = { data: item };

    if (sectionId) {
      if (currentSectionId) props.parentId = currentSectionId;
      if (currentSubSectionId) props.parentId = currentSubSectionId;
      if (currentQuestionId) props.parentId = sectionId;
    }

    actions.wizards.updateFromBuffer(props);
  };

  useEffect(() => {
    actions.wizards.getIds();
  }, []);

  useEffect(() => {
    if (wizardId) actions.wizards.getById(wizardId);

    return (() => {
      actions.wizards.clean();
      actions.wizards.change({ currentWizard: null });
    });
  }, [wizardId]);

  const EmptyCard = (
    <Card style={{ height: 500, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      <Empty
        image={EmptyImage}
        imageStyle={{ height: 60 }}
        description={
          <span style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 15 }}>
            Please select menu item
          </span>
        }
      />
    </Card>
  );

  let content = EmptyCard;

  if (currentWizard) {
    const bufferTitleTemplate = getBufferTitle({ wizard: currentWizard });

    const props = {
      getValue,
      onSubmit: handleSubmit,
      onOpenBuffer: () => refBuffer.current.show(true),
      onPushBufferTemplate: () => actions.wizards.pushBuffer(bufferTitleTemplate, 'wizard', sections),
    };

    if (showSection) {
      const { _id: currentSectionId } = currentSection || {};
      const bufferTitle = currentSectionId ? getBufferTitle({ wizard: currentWizard, sectionId: currentSectionId }) : '';

      content = (
        <Form
          type='section'
          current={currentSection}
          canDelete={!dependentSections.includes(currentSectionId)}
          onDelete={() => actions.wizards.deleteSection(currentSectionId)}
          onPushBuffer={() => actions.wizards.pushBuffer(bufferTitle, 'section', getValue())}
          deleteMessage='Are you sure you want to delete the section and all the included questions?'
          {...props}
        >
          <EditSection
            ref={ref}
            type='section'
            currentSection={currentSection}
          />
        </Form>
      );
    } else if (showQuestion) {
      const { _id: currentQuestionId } = currentQuestion || {};
      const bufferTitle = currentQuestionId ? getBufferTitle({ wizard: currentWizard, position }) : '';

      content = (
        <Form
          type='question'
          current={currentQuestion}
          canDelete={!dependentQuestions.includes(currentQuestionId)}
          onDelete={() => actions.wizards.deleteQuestion(currentQuestionId)}
          onPushBuffer={() => actions.wizards.pushBuffer(bufferTitle, 'question', getValue())}
          deleteMessage='Are you sure you want to delete the question?'
          {...props}
        >
          <EditQuestion
            ref={ref}
            ids={ids}
            parentId={sectionId}
            currentWizard={currentWizard}
            currentQuestion={currentQuestion}
          />
        </Form>
      );
    } else if (showSubSection) {
      const { subSection: currentSubSectionId } = currentSubSection || {};
      const bufferTitle = currentSubSectionId ? getBufferTitle({ wizard: currentWizard, position }) : '';

      content = (
        <Form
          type='subsection'
          current={currentSubSection}
          canDelete={!dependentSections.includes(currentSubSectionId)}
          onDelete={() => actions.wizards.deleteSubSection(currentSubSectionId)}
          onPushBuffer={() => actions.wizards.pushBuffer(bufferTitle, 'subsection', getValue())}
          deleteMessage='Are you sure you want to delete the subsection and all the included questions?'
          {...props}
        >
          <EditSection
            ref={ref}
            type='subsection'
            parentId={sectionId}
            currentSection={currentSubSection}
          />
        </Form>
      );
    } else {
      content = EmptyCard;
    }
  }

  return (
    <>
      <Breadcrumb
        routes={[
          { title: 'Dashboard', href: `${config.rootRoute}/` },
          { title: 'Wizards', href: `${config.rootRoute}/wizards` },
          { title: 'Edit' },
        ]}
      />

      <Row gutter={24}>
        <Col xl={8} lg={8} md={24} sm={24} xs={24}>
          <Spin spinning={loading}>
            <Card
              title='Wizard parameters'
              className={styles.fields}
              extra={(
                <>
                  <Button
                    type='link'
                    target='_blank'
                    href={`${config.site}applications/${wizardId}/preview`}
                  >
                    Preview
                  </Button>

                  <Button type='primary' onClick={() => setShowModalClone(true)}>
                    Clone
                  </Button>
                </>
              )}
            >
              <Fields wizard={currentWizard} />
            </Card>
          </Spin>

          <Spin spinning={loading}>
            <Card>
              <Tree sections={sections} loading={loading} dependentSections={dependentSections} dependentQuestions={dependentQuestions} />
            </Card>
          </Spin>
        </Col>

        <Col xl={16} lg={16} md={24} sm={24} xs={24}>
          <Spin spinning={loading}>
            {content}
          </Spin>
        </Col>
      </Row>

      {showModalClone && <Add id={wizardId} show={showModalClone} onClose={() => setShowModalClone(false)} />}

      <BufferModal
        ref={refBuffer}
        buffer={buffer}
        types={bufferTypes}
        onSelect={item => onSelectBuffer(item)}
        onRemove={newBuffer => actions.wizards.change({ buffer: newBuffer })}
      />
    </>
  );
};

export default connect(({ wizards }) => ({
  ids: wizards.ids,
  buffer: wizards.buffer,
  loading: wizards.loading,
  position: wizards.position,
  sections: wizards.sections,
  sectionId: wizards.sectionId,
  bufferTypes: wizards.bufferTypes,
  showSection: wizards.showSection,
  showQuestion: wizards.showQuestion,
  showSubSection: wizards.showSubSection,
  currentSection: wizards.currentSection,
  currentSubSection: wizards.currentSubSection,
  currentWizard: wizards.currentWizard,
  currentQuestion: wizards.currentQuestion,
}))(Wizard);
