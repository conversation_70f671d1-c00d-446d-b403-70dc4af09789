import React, { useState } from 'react';
import { Switch, Tag, Icon } from 'antd';
import CountryFlag from '../../../components/CountryFlag';
import UpdateVersion from './Version';
import actions from '../../../actions';
import styles from '../index.less';

/**
 * Wizard Fields
 * @param {Object} wizard
 * @returns {JSX.Element}
 */
export default ({ wizard }) => {
  const {
    country,
    notifiedBody,
    applicationType,
    productType,
    classification,
    releaseDate,
    versionNumber,
    versionDescription,
    isEStar
  } = wizard || {};

  const [showModalDate, setShowModalDate] = useState(false);
  const [showModalNumber, setShowModalNumber] = useState(false);
  const [showModalDescription, setShowModalDescription] = useState(false);

  const onCloseModal = () => {
    setShowModalDate(false);
    setShowModalNumber(false);
    setShowModalDescription(false);
  };

  const NotSet = <Tag title='Not set'>Not set</Tag>;

  return (
    <>
      <div className={styles.descriptionList}>
        <div className={styles.description}><div className={styles.term}>Country: </div><CountryFlag countryId={country} /></div>
        <div className={styles.description}><div className={styles.term}>Notified Body: </div>{notifiedBody?.name || NotSet}</div>
        <div className={styles.description}><div className={styles.term}>Application Type: </div>{applicationType?.name || NotSet}</div>
        <div className={styles.description}><div className={styles.term}>Product Type: </div>{productType?.name || NotSet}</div>
        <div className={styles.description}><div className={styles.term}>Classification: </div>{classification?.name || NotSet}</div>

        <div className={styles.description}>
          <div className={styles.term}>Release Date: </div>
          {releaseDate || NotSet}
          {!releaseDate && wizard && <a><Icon type='edit' onClick={() => setShowModalDate(true)} /></a>}
        </div>

        <div className={styles.description}>
          <div className={styles.term}>Version Number: </div>
          {versionNumber || NotSet}
          {!versionNumber && wizard && <a><Icon type='edit' onClick={() => setShowModalNumber(true)} /></a>}
        </div>

        <div className={styles.description}>
          <div className={styles.term}>Version Description: </div>
          {versionDescription || NotSet}
          {!versionDescription && wizard && <a><Icon type='edit' onClick={() => setShowModalDescription(true)} /></a>}
        </div>

        <div className={styles.description} style={{ display: 'flex', alignItems: 'center' }}>
          <div className={styles.term}>Enable eSTAR: </div>
          <Switch size='small' disabled={!['USA', 'CAN'].includes(country)} checked={isEStar} onChange={(checked) => actions.wizards.updateWizard({ isEStar: checked })} />
        </div>
      </div>

      {showModalDate && <UpdateVersion show={showModalDate} wizard={wizard} title='Release Date' item='releaseDate' onClose={() => onCloseModal(false)} />}
      {showModalNumber && <UpdateVersion show={showModalNumber} wizard={wizard} title='Version Number' item='versionNumber' onClose={() => onCloseModal(false)} />}
      {showModalDescription && <UpdateVersion show={showModalDescription} wizard={wizard} title='Version Description' item='versionDescription' onClose={() => onCloseModal(false)} />}
    </>
  );
};

