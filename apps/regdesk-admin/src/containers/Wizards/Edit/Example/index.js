import React, { useState, useRef } from 'react';
import { Form, Button, Input, Badge, Icon } from 'antd';
import ModalUpload from '../../../../components/Upload/Modal';

const { Item } = Form;

/**
 * Example text and link
 * @param {?Object} example
 * @param {Object} layout
 * @param {function} onChange
 * @returns {JSX.Element}
 */
export default ({ example, layout, onChange }) => {
  const { text, links } = example || {};
  const [newText, setNewText] = useState(text || '');
  const [newLinks, setNewLinks] = useState(links || []);
  const [index, setIndex] = useState(null);
  const ref = useRef(null);

  const onChangeText = value => {
    setNewText(value);
    onChange({ text: value, links: newLinks });
  };

  const onAddLink = () => {
    const data = [...newLinks, { text: '', link: '' }];

    setNewLinks(data);
    onChange({ text: newText, links: data });
  };

  const onUpdateLink = (key, item, value) => {
    const data = [...newLinks];

    data.splice(key, 1, { ...data[key], [item]: value });
    setNewLinks(data);
    onChange({ text: newText, links: data });
  };

  const onSelectLink = item => {
    onUpdateLink(index, 'link', item);
    setIndex(null);
  };

  const onRemoveLink = key => {
    const data = [...newLinks];

    data.splice(key, 1);
    setNewLinks(data);
    onChange({ text: newText, links: data });
  };

  return (
    <>
      <Item label='Example text' {...layout}>
        <Input
          placeholder='Enter example text'
          value={newText}
          onChange={e => onChangeText(e.target.value)}
        />
      </Item>

      <Item label='Example Links' style={{ margin: 0 }} {...layout}>
        <div style={{ marginTop: newLinks.length && 4 }}>
          {newLinks.map(({ _id, text: itemText, link: itemLink }, key) => (
            <div key={_id || key} style={{ display: 'flex' }}>
              <div style={{ display: 'flex', gap: 10, width: '100%', marginBottom: 16 }}>
                <Input
                  value={itemText}
                  placeholder='Enter text'
                  onChange={e => onUpdateLink(key, 'text', e.target.value)}
                />

                <Input
                  value={itemLink}
                  placeholder='Enter link'
                  style={{ marginTop: 1 }}
                  onChange={e => onUpdateLink(key, 'link', e.target.value)}
                  addonAfter={<Icon type='upload' onClick={() => { setIndex(key); ref.current.showModal(true); }} />}
                />
              </div>

              <div style={{ position: 'relative', width: 0, height: 0 }}>
                <Badge dot={false}>
                  <Icon
                    type='delete'
                    style={{ position: 'absolute', top: -17, left: 8 }}
                    onClick={() => onRemoveLink(key)}
                  />
                </Badge>
              </div>
            </div>
          ))}
        </div>

        <Button type='dashed' style={{ width: '100%' }} onClick={onAddLink}>
          <Icon type='plus' /> Add link
        </Button>
      </Item>

      <ModalUpload ref={ref} onUpload={onSelectLink} />
    </>
  );
};
