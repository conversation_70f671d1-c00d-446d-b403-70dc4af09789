import React, { useState } from 'react';
import { Table, Descriptions, Button, Checkbox, Divider } from 'antd';
import { getConditionName } from '../../utils';
import Dropdown from '../../Dropdown';
import Add from './Add';
import styles from '../../index.less';

/**
 * Conditions
 * @param {boolean} fromQuestion
 * @param {boolean} oneOfEnabled
 * @param {Object[]} conditions
 * @param {?Object[]} dependencies
 * @param {function} onChange
 * @returns {JSX.Element}
 */
export default ({ fromQuestion, oneOfEnabled, conditions, dependencies, onChange }) => {
  const [showModal, setShowModal] = useState(false);
  const [conditionKey, setConditionKey] = useState();

  const onOpenModal = key => {
    setShowModal(true);
    setConditionKey(key);
  };

  const onCloseModal = () => {
    setShowModal(false);
    setConditionKey();
  };

  const onRemove = keys => {
    const newData = [...conditions].map(item => ({ ...item, key: item.key || 0 })).filter(({ key }) => !keys.includes(key));

    onChange('conditions', newData);

    if ([...new Set(newData.map(({ key }) => key))].length <= 1) onChange('oneOfEnabled', false);
  };

  const onAdd = item => {
    onChange('conditions', [...conditions, item]);
    onCloseModal();
  };

  const getData = () => {
    const data = {};

    conditions.forEach((item = {}) => {
      const { key = 0 } = item;

      if (data[key]) data[key].push(item);
      else data[key] = [item];
    });

    return Object.values(data);
  };

  const data = getData();
  const maxKey = Math.max(...conditions.map(({ key = 0 }) => key));

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 30 }}>
        <Descriptions title='Conditions' />

        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 20 }}>
          <Checkbox
            style={{ width: 90 }}
            checked={oneOfEnabled}
            disabled={data.length <= 1}
            onChange={() => onChange('oneOfEnabled', !oneOfEnabled)}
          >
            One Of
          </Checkbox>

          <Button
            size='small'
            icon='plus'
            type='primary'
            onClick={() => onOpenModal(maxKey >= 0 ? maxKey + 1 : 0)}
          >
            Add Condition
          </Button>
        </div>
      </div>

      <Table
        size='small'
        rowKey='id'
        pagination={false}
        dataSource={data}
        columns={[
          {
            title: 'Question',
            render: items => <div className={styles.conditions}>{items.map(({ name }) => <div>{name}</div>)}</div>
          },
          {
            title: 'Condition',
            render: items => <div className={styles.conditions}>{items.map(({ condition }) => <div>{getConditionName(condition)}</div>)}</div>
          },
          {
            title: 'Label',
            render: items => <div className={styles.conditions}>{items.map(({ values }) => <div><Dropdown data={values} title='Labels' /></div>)}</div>
          },
          {
            title: 'Actions',
            render: items => {
              const keys = items.map(({ key = 0 }) => key);

              return (
                <>
                  <a onClick={() => onOpenModal(keys[0])}>Add</a>
                  <Divider type='vertical' />
                  <a onClick={() => onRemove(keys)}>Remove</a>
                </>
              );
            }
          },
        ]}
      />

      {!!dependencies?.length && (
        <Table
          style={{ marginTop: 20 }}
          size='small'
          rowKey='id'
          pagination={false}
          columns={[
            {
              title: 'Title',
              dataIndex: 'title',
            },
            {
              title: 'Type',
              dataIndex: 'type',
            },
          ]}
          dataSource={dependencies}
        />
      )}

      {showModal && <Add conditionKey={conditionKey} show={showModal} fromQuestion={fromQuestion} onClose={onCloseModal} onAdd={onAdd} />}
    </>
  );
};
