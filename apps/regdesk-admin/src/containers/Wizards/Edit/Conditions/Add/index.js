import React from 'react';
import { Modal } from 'antd';
import Form from './Form';

/**
 * Add Condition Modal
 * @param {boolean} show
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ show, onClose, ...props }) => (
  <Modal
    title='Add condition'
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={() => onClose()}
    onClose={() => onClose()}
    width={600}
    zIndex={999}
  >
    <Form onClose={onClose} {...props} />
  </Modal>
);
