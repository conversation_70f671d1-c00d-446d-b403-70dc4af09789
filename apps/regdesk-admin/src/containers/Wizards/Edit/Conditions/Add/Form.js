import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Button, Form, Select } from 'antd';
import { getConditionName } from '../../../utils';
import actions from '../../../../../actions';

const { Item } = Form;
const { Option } = Select;

const formItemLayout = {
  labelCol: { xs: { span: 6 } },
  wrapperCol: { xs: { span: 14 } },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Add Condition
 * @param {number} conditionKey
 * @param {boolean} fromQuestion
 * @param {Object[]} questions
 * @param {function} onAdd
 * @param {function} onClose
 * @returns {JSX.Element}
 */
const ConditionAdd = ({ conditionKey, fromQuestion, questions, onAdd, onClose }) => {
  const [questionId, setQuestionId] = useState('');
  const [condition, setCondition] = useState('');
  const [values, setValues] = useState([]);
  const { type, name, options = [], values: questionValues = [], multiple } = questions.find(({ _id: id }) => id === questionId) || {};
  const conditions = ['EMPTY', 'NOT_EMPTY'];
  const allValues = type === 'select' ? options.map(({ value }) => value) : questionValues;

  useEffect(() => {
    actions.wizards.getQuestions();
  }, []);

  const reset = () => {
    setQuestionId('');
    setCondition('');
    setValues([]);
    onClose();
  };

  if (['checkbox', 'radio', 'select'].includes(type)) {
    conditions.push('EQUALS', 'NOT_EQUALS', 'ONE_OF', 'NOT_ONE_OF');

    if (fromQuestion && ((type === 'select' && !multiple || type === 'radio')
      && allValues.every(value => typeof +value === 'number' && +value > 0))) {
      conditions.push('REPETITIONS_BASED_ON_VALUE');
    }
  }

  const disabled = !questionId || questionId
    && (!condition || ['EQUALS', 'NOT_EQUALS', 'ONE_OF', 'NOT_ONE_OF'].includes(condition) && !values?.length);

  return (
    <>
      <Item required label='Question' {...formItemLayout}>
        <Select
          showSearch
          value={questionId || undefined}
          placeholder='Select question'
          style={{ width: '100%' }}
          onChange={item => { setQuestionId(item); setCondition(''); setValues([]); }}
          optionFilterProp='children'
          filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {questions.map(({ _id: id, name: questionName }) => (
            <Option key={id} value={id}>
              {questionName}
            </Option>
          ))}
        </Select>
      </Item>

      {questionId && (
        <Item required label='Condition' {...formItemLayout}>
          <Select
            showSearch
            value={condition || undefined}
            placeholder='Select condition'
            style={{ width: '100%' }}
            onChange={item => { setCondition(item); setValues([]); }}
          >
            {conditions.map(item => (
              <Option key={item} value={item}>
                {getConditionName(item)}
              </Option>
            ))}
          </Select>
        </Item>
      )}

      {['EQUALS', 'NOT_EQUALS', 'ONE_OF', 'NOT_ONE_OF'].includes(condition) && allValues.length && (
        <Item required label='Label' {...formItemLayout}>
          <Select
            showSearch
            mode='multiple'
            value={values || undefined}
            placeholder='Select label'
            style={{ width: '100%' }}
            onChange={item => setValues(item)}
            optionFilterProp='children'
            filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          >
            {allValues.filter(option => option).map(option => (
              <Option key={option} value={option}>
                {option}
              </Option>
            ))}
          </Select>
        </Item>
      )}

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={() => reset()}>Cancel</Button>

        <Button
          style={{ marginLeft: 8 }}
          type='primary'
          disabled={disabled}
          onClick={() => onAdd({ key: conditionKey, question: questionId, condition, values, type, name })}
        >
          Add
        </Button>
      </Item>
    </>
  );
};

export default connect(({ wizards }) => ({
  questions: wizards.questions
}))(ConditionAdd);
