import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Divider, I<PERSON>, Popconfirm, Empty } from 'antd';
import EmptyImage from '../../../assets/empty-image.png';
import actions from '../../../actions';
import styles from '../index.less';

const { TreeNode } = Tree;

/**
 * Wizard Tree
 * @param {Object[]} sections
 * @param {boolean} loading
 * @param {string[]} dependentSections
 * @param {string[]} dependentQuestions
 * @returns {JSX.Element}
 */
export default ({ sections, loading, dependentSections, dependentQuestions }) => {
  const [selectedKeys, setSelectedKeys] = useState([]);

  const formatName = name => name && name.length > 30 ? `${name.slice(0, 30)}...` : name;

  const stopEvent = e => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  const handleSelect = (key, info) => {
    const { section = {}, question = {}, pos } = info.node.props;
    const { _id: sectionId, subSection } = section;
    const { _id: questionId, subSection: subSectionId } = question;
    const position = pos.split('-').slice(1);
    const props = {
      position,
      currentSection: null,
      currentQuestion: null,
      currentSubSection: null,
      sectionId: null,
      subSectionId: null,
      questionId: null,
      showSection: false,
      showSubSection: false,
      showQuestion: false,
      bufferTypes: ['wizard', 'section', 'subsection', 'question']
    };

    if (sectionId || subSection) {
      props.currentSection = section;
      props.sectionId = subSection || sectionId;
      props.showSection = true;
    }

    if (questionId && subSectionId) {
      props.currentSubSection = question;
      props.subSectionId = subSectionId;
      props.showSection = false;
      props.showSubSection = true;
    }

    if (questionId && !subSectionId) {
      props.currentQuestion = question;
      props.questionId = questionId;
      props.showSection = false;
      props.showQuestion = true;
    }

    setSelectedKeys(key);
    actions.wizards.change(props);
  };

  const setSection = section => {
    const { _id: sectionId } = section;

    if (sectionId) actions.wizards.change({ sectionId, currentSection: section });
  };

  const addSection = () => {
    actions.wizards.clean();
    actions.wizards.change({ showSection: true, bufferTypes: ['wizard', 'section', 'subsection'] });
    setSelectedKeys([]);
  };

  const addSubSection = (e, section) => {
    stopEvent(e);
    actions.wizards.clean();
    setSection(section);
    actions.wizards.change({ showSubSection: true, bufferTypes: ['wizard', 'section', 'subsection'] });
    setSelectedKeys([]);
  };

  const addQuestion = (e, section) => {
    stopEvent(e);
    actions.wizards.clean();
    setSection(section);
    actions.wizards.change({ showQuestion: true, bufferTypes: ['question'] });
    setSelectedKeys([]);
  };

  const getTreeQuestions = section => (
    section.questions.map((question) => {
      const { name, questions, subSection: subSectionId, _id: questionId, needUpdate } = question;
      const canDeleteQuestion = !dependentQuestions.includes(questionId);
      const canDeleteSubSection = !dependentSections.includes(subSectionId);

      return (
        <TreeNode
          key={questionId}
          title={
            <span style={{ color: subSectionId && !questions?.length && '#ff9800' }} className='b-hover'>
              {formatName(name)}

              {subSectionId && (
                <span>
                  <a onClick={e => addSubSection(e, { _id: subSectionId, ...questions })}>
                    <Tooltip title='Add New Layer'>
                      <Icon type='branches' style={{ fontSize: 14, marginLeft: 10 }} />
                    </Tooltip>
                  </a>

                  <Divider type='vertical' />

                  <a onClick={e => addQuestion(e, { _id: subSectionId, ...questions })}>
                    <Tooltip title='Add New Question'>
                      <Icon type='question-circle' style={{ fontSize: 14 }} />
                    </Tooltip>
                  </a>

                  <Divider type='vertical' />

                  <Tooltip title={!canDeleteSubSection && 'Section is used as a condition. Please remove the condition first!'}>
                    <Popconfirm
                      okText='Confirm'
                      cancelText='Cancel'
                      placement='topLeft'
                      disabled={!canDeleteSubSection}
                      title='Are you sure you want to delete the subsection?'
                      onConfirm={e => { stopEvent(e); actions.wizards.deleteSubSection(subSectionId); }}
                    >
                      <a className={!canDeleteSubSection ? styles.disabled : ''} onClick={stopEvent}>
                        <Tooltip title={canDeleteSubSection && 'Delete'}>
                          <Icon type='minus-circle' style={{ fontSize: 14 }} />
                        </Tooltip>
                      </a>
                    </Popconfirm>
                  </Tooltip>
                </span>
              )}

              {!subSectionId && (
                <>
                  <Tooltip title={!canDeleteQuestion && 'Question is used as a condition. Please remove the condition first!'}>
                    <Popconfirm
                      okText='Confirm'
                      cancelText='Cancel'
                      placement='topLeft'
                      disabled={!canDeleteQuestion}
                      title='Are you sure you want to delete the question?'
                      onConfirm={e => { stopEvent(e); actions.wizards.deleteQuestion(questionId); }}
                    >
                      <a className={!canDeleteQuestion ? styles.disabled : ''} onClick={stopEvent}>
                        <Tooltip title={canDeleteQuestion && 'Delete'}>
                          <Icon type='minus-circle' style={{ fontSize: 14, marginLeft: 10 }} />
                        </Tooltip>
                      </a>
                    </Popconfirm>
                  </Tooltip>

                  {needUpdate && (
                    <Tooltip title='Need to update ID'>
                      <Icon type='exclamation-circle' theme='filled' style={{ color: '#FF5722', marginLeft: 6 }} />
                    </Tooltip>
                  )}
                </>
              )}
            </span>
          }
          question={question}
          section={section}
        >
          {questions && getTreeQuestions(question)}
        </TreeNode>
      );
    })
  );

  const isEqual = (arr1, arr2) => arr1.every((val, index) => val === arr2[index]);

  const moveItem = (arr, from, to) => arr.splice(to, 0, arr.splice(from, 1)[0]);

  const onDrop = info => {
    const { dropPosition, node, dropToGap, dragNode: { props: { pos, eventKey, section } } } = info;
    const dragNodePos = pos.split('-').slice(1);
    const nodePos = node.props.pos.split('-').slice(1);
    const dragNodeLevel = dragNodePos.length - 1;
    const nodeLevel = nodePos.length - 1;
    const oldIndex = +dragNodePos[dragNodeLevel];
    const newIndex = +nodePos[nodeLevel];
    const oldSection = section;
    const newSection = node.props.section;
    const { _id: oldSectionId } = oldSection;
    const { _id: newSectionId } = newSection;

    if (dragNodeLevel === 0 && nodeLevel === 0 && dropToGap) {
      const oldSectionIds = sections.map(({ _id }) => _id);
      const newSectionIds = [...oldSectionIds];

      moveItem(newSectionIds, oldIndex, newIndex);

      if (dropPosition !== oldIndex && !isEqual(oldSectionIds, newSectionIds)) {
        actions.wizards.updateWizard({ sectionsIds: newSectionIds });
      }
    } else if (dragNodeLevel !== 0) {
      if (dropToGap) {
        const oldQuestionIds = oldSection.questions.map(({ _id }) => _id);
        const newQuestionIds = newSection.questions.map(({ _id }) => _id);
        const oldParentId = oldSection.subSection || oldSectionId;
        const newParentId = newSection.subSection || newSectionId;

        if (isEqual(oldQuestionIds, newQuestionIds)) moveItem(newQuestionIds, oldIndex, newIndex);
        else newQuestionIds.splice(newIndex, 0, eventKey);

        oldQuestionIds.splice(oldIndex, 1);

        actions.wizards.updateWizard({ oldParentId, newParentId, oldQuestionIds, newQuestionIds });
      } else if (!dropToGap && node.props.question?.type === 'subsection') {
        const oldData = oldSection.questions.map(({ _id }) => _id);
        const oldQuestionIds = [...oldData];
        const oldParentId = oldSection.subSection || oldSectionId;
        const { question } = node.props;
        const newQuestionIds = question.questions.map(({ _id }) => _id);
        const newParentId = question.subSection;

        newQuestionIds.push(eventKey);
        oldQuestionIds.splice(oldIndex, 1);

        if (oldParentId !== newParentId) {
          actions.wizards.updateWizard({ oldParentId, newParentId, oldQuestionIds, newQuestionIds });
        }
      } else if (!dropToGap && newSection && nodeLevel === 0) {
        const oldData = newSection.questions.map(({ _id }) => _id);
        const newQuestionIds = [...oldData];
        const oldQuestionIds = oldSection.questions.map(({ _id }) => _id);
        const oldParentId = oldSection.subSection || oldSectionId;
        const newParentId = newSectionId;

        newQuestionIds.push(eventKey);
        oldQuestionIds.splice(oldIndex, 1);

        if (oldParentId !== newParentId) {
          actions.wizards.updateWizard({ oldParentId, newParentId, oldQuestionIds, newQuestionIds });
        }
      }
    }
  };

  return (
    <div>
      <Button
        type='primary'
        icon='plus'
        onClick={addSection}
        style={{ width: '100%' }}
      >
        Add Section
      </Button>

      {!sections.length && (
        <Empty
          style={{ marginTop: 20 }}
          image={EmptyImage}
          imageStyle={{ height: 60 }}
          description={
            <span style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 15 }}>
              No sections yet
            </span>
          }
        />
      )}

      {!loading && (
        <Tree
          showLine
          draggable
          defaultExpandAll
          onDrop={onDrop}
          onSelect={handleSelect}
          style={{ marginTop: 20 }}
          selectedKeys={selectedKeys}
        >
          {sections.map((section) => {
            const { name, _id: sectionId } = section;
            const canDeleteSection = !dependentSections.includes(sectionId);

            return (
              <TreeNode
                key={sectionId}
                section={section}
                title={
                  <span className='b-hover'>
                    {formatName(name)}

                    <a onClick={e => addSubSection(e, section)}>
                      <Tooltip title='Add New Layer'>
                        <Icon type='branches' style={{ fontSize: 14, marginLeft: 10 }} />
                      </Tooltip>
                    </a>

                    <Divider type='vertical' />

                    <a onClick={e => addQuestion(e, section)}>
                      <Tooltip title='Add New Question'>
                        <Icon type='question-circle' style={{ fontSize: 14 }} />
                      </Tooltip>
                    </a>

                    <Divider type='vertical' />

                    <Tooltip title={!canDeleteSection && 'Section is used as a condition. Please remove the condition first!'}>
                      <Popconfirm
                        okText='Confirm'
                        cancelText='Cancel'
                        placement='topLeft'
                        disabled={!canDeleteSection}
                        title='Are you sure you want to delete the section and all the included questions?'
                        onConfirm={e => { stopEvent(e); actions.wizards.deleteSection(sectionId); }}
                      >
                        <a className={!canDeleteSection ? styles.disabled : ''} onClick={stopEvent}>
                          <Tooltip title={canDeleteSection && 'Delete'}>
                            <Icon type='minus-circle' style={{ fontSize: 14 }} />
                          </Tooltip>
                        </a>
                      </Popconfirm>
                    </Tooltip>
                  </span>
                }
              >
                {getTreeQuestions(section)}
              </TreeNode>
            );
          })}
        </Tree>
      )}
    </div>
  );
};
