import React, { useState, useEffect } from 'react';
import { Form, Button, Input, Badge, Icon, Checkbox, Select, Tooltip } from 'antd';
import { productsDataSources } from '../../utils';

const { Item } = Form;
const { Option } = Select;

const formItemLayout = {
  labelCol: { xs: { span: 6 } },
  wrapperCol: { xs: { span: 18 } },
};

const formTailLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 19, offset: 4 },
};

/**
 * Products Data Question
 * @param {string} questionId
 * @param {Object[]} productsData
 * @param {Object} formLayout
 * @param {function} onChange
 * @returns {JSX.Element}
 */
export default ({ questionId, isEStar, productsData, formLayout, onChange }) => {
  const emptyField = { title: '', placeholder: '', source: '', hint: '', hide: false };
  const [newFields, setNewFields] = useState(productsData.length ? productsData : [emptyField]);

  const onAdd = () => {
    const data = [...newFields, emptyField];

    setNewFields(data);
    onChange(data);
  };

  const onUpdate = (key, item, value) => {
    const data = [...newFields];

    const newField = { ...data[key], [item]: value };

    if (item === 'type') newFields.hide = false;

    data.splice(key, 1, newField);
    setNewFields(data);
    onChange(data);
  };

  const onRemove = key => {
    const data = [...newFields];

    data.splice(key, 1);
    setNewFields(data);
    onChange(data);
  };

  useEffect(() => {
    if (questionId) setNewFields(productsData.length ? productsData : [emptyField]);
  }, [productsData]);

  const fields = [
    {
      key: 'title',
      general: true,
      label: 'Title',
      cmp: ({ title }, key) => (
        <Input
          value={title}
          placeholder='Enter title'
          onChange={e => onUpdate(key, 'title', e.target.value)}
        />
      )
    },
    {
      key: 'placeholder',
      general: true,
      label: 'Placeholder',
      cmp: ({ placeholder }, key) => (
        <Input
          value={placeholder}
          placeholder='Enter placeholder'
          onChange={e => onUpdate(key, 'placeholder', e.target.value)}
        />
      )
    },
    {
      key: 'Source',
      general: true,
      label: 'Source',
      cmp: ({ source }, key) => (
        <Select
          showSearch
          value={source || undefined}
          style={{ width: '100%' }}
          optionLabelProp='label'
          placeholder='Select source'
          onChange={newSource => onUpdate(key, 'source', newSource)}
          filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {productsDataSources.map(({ label, value }) => (
            <Option key={value} value={value} label={label}>
              {label}
            </Option>
          ))}
        </Select>
      )
    },
    {
      key: 'hint',
      general: true,
      label: 'Hint',
      cmp: ({ hint }, key) => (
        <Input
          value={hint}
          placeholder='Enter hint'
          onChange={e => onUpdate(key, 'hint', e.target.value)}
        />
      )
    },
    {
      key: 'xmlTagName',
      dependence: isEStar,
      label: 'XML Association',
      cmp: ({ xmlTagName }, key) => (
        <Input
          value={xmlTagName}
          placeholder='Enter XML Association'
          onChange={e => onUpdate(key, 'xmlTagName', e.target.value)}
        />
      )
    },
    {
      key: 'hide',
      layout: formTailLayout,
      cmp: ({ hide }, key) => (
        <Checkbox
          checked={hide}
          onChange={() => onUpdate(key, 'hide', !hide)}
        >
          <Tooltip title='Will hide field when it is not applicable to the application based on products data'>
            Hide when N/A
          </Tooltip>
        </Checkbox>
      )
    },
  ];

  return (
    <Item label='Options' style={{ margin: 0 }} {...formLayout}>
      <div style={{ marginTop: 30 }}>
        {newFields.map((item, index) => {
          const { _id, source } = item || {};
          const { canHide } = productsDataSources.find(({ value }) => value === source) || {};

          return (
            <div key={_id || index} style={{ display: 'flex', marginBottom: 16, position: 'relative' }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 10, width: '100%', marginBottom: 16 }}>
                {fields.filter(({ key, general, dependence }) => general || dependence || canHide && key === 'hide').map(({ key, label, layout, cmp }) => (
                  <Item key={key} label={label} style={{ margin: 0 }} {...(layout || formItemLayout)}>
                    {cmp(item, index)}
                  </Item>
                ))}
              </div>

              {newFields.length > 1 && (
                <Badge dot={false}>
                  <Icon
                    type='delete'
                    style={{ position: 'absolute', top: 60, fontSize: 18 }}
                    onClick={() => onRemove(index)}
                  />
                </Badge>
              )}
            </div>
          );
        })}
      </div>

      <Item style={{ margin: '-20px 0 0' }} {...formTailLayout}>
        <Button type='dashed' style={{ width: '100%' }} onClick={onAdd}>
          <Icon type='plus' /> Add field
        </Button>
      </Item>
    </Item>
  );
};
