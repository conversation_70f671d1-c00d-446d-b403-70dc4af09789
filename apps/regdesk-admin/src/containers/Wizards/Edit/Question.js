import React, { useReducer, forwardRef, useImperativeHandle, useEffect, useMemo } from 'react';
import { Form, Button, Input, Select, Badge, Checkbox, InputNumber, Tooltip } from 'antd';
import { questionsScopes, questionsTypes, dataForStandards, dataForClassifications } from '../utils';
import MDEditor from '../../../components/MarkDownEditor';
import Classifications from './Classifications';
import ProductsData from './ProductsData';
import MultiFields from './MultiFields';
import Conditions from './Conditions';
import Standards from './Standards';
import Example from './Example';
import Options from './Options';
import Values from './Values';
import styles from '../index.less';

const { Option } = Select;
const { Item } = Form;

const formItemLayout = {
  labelCol: { xs: { span: 4 } },
  wrapperCol: { xs: { span: 16 } },
};

const formTailLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16, offset: 4 },
};

const initialState = {
  _id: '',
  id: null,
  name: '',
  prefix: '',
  type: '',
  newLineEnabled: false,
  buttonName: '',
  customTitlesEnabled: false,
  defaultTitle: '',
  customTitles: [],
  orderByNumberEnabled: false,
  selectAllStandards: false,
  selectAllClassifications: false,
  multiFields: [],
  productsData: [],
  standards: [],
  standardData: [],
  classifications: [],
  classificationData: [],
  multiple: false,
  maxFiles: 1,
  uploadEnabled: false,
  column: false,
  values: [],
  options: [],
  format: '',
  showTime: false,
  placeholder: '',
  scope: 'N/A',
  example: null,
  hint: '',
  xmlTagName: undefined,
  required: false,
  description: '',
  descriptionExport: '',
  oneOfEnabled: false,
  conditions: [],
};

const init = state => ({ ...initialState, ...state });

const reducer = (state, action) => {
  const { type, payload } = action || {};

  switch (type) {
    case 'change':
      return { ...state, ...payload };
    case 'reset':
      return { ...initialState };
    default:
      return { ...state };
  }
};

const getDependencies = (wizard, id) => {
  const { sections = [], subSections = [], allConditions = [] } = wizard || {};
  const allSections = [...sections, ...subSections];
  const questions = [];

  allSections.forEach(item => {
    const listQuestions = item.questions || [];

    questions.push(...listQuestions);
  });

  return allConditions
    .filter(({ questionId }) => questionId === id)
    .map(({ parentId }) => {
      const section = sections.find(({ _id: sectionId }) => sectionId === parentId);
      const subSection = questions.find(({ subSection: subSectionId, type: questionType }) => questionType === 'subsection' && subSectionId === parentId);
      const question = questions.find(({ _id: questionId, type: questionType }) => questionType !== 'subsection' && questionId === parentId);

      if (section) return ({ title: section.name, type: 'Section' });
      if (subSection) return ({ title: subSection.name, type: 'Sub Section' });
      if (question) return ({ title: question.name, type: 'Question' });

      return null;
    })
    .filter(item => item)
    .map((item, key) => ({ ...item, id: key }));
};

/**
 * Wizard Question
 * @param {Object[]} ids
 * @param {?string} parentId
 * @param {Object} currentWizard
 * @param {?Object} currentQuestion
 * @param {Object} ref
 * @returns {JSX.Element}
 */
export default forwardRef(({ ids, parentId, currentWizard, currentQuestion }, ref) => {
  const [activePrefix, dispatchPrefix] = useReducer(active => !active, !!currentQuestion?.prefix);
  const [state, dispatch] = useReducer(reducer, currentQuestion, init);
  const { id, name, prefix, type, newLineEnabled, buttonName, customTitlesEnabled, defaultTitle, customTitles, orderByNumberEnabled, selectAllStandards, selectAllClassifications, multiFields, productsData, standards, standardData, classifications, classificationData, multiple, maxFiles, uploadEnabled, column, values, options, format, showTime, placeholder, scope, example, hint, xmlTagName, required, description, descriptionExport, oneOfEnabled, conditions } = state;
  const { _id: questionId } = currentQuestion || {};
  const { isEStar } = currentWizard || {};
  const dependencies = useMemo(() => getDependencies(currentWizard, questionId), [questionId]);
  const { extra, defaultFormat } = questionsTypes.find(({ value }) => value === type) || {};
  const { systemTag: tag, autofillQuestion } = ids.find(({ _id }) => _id === id) || {};
  const { name: tagName, description: tagDescription, scope: tagScope, fileType: tagFileType } = tag || {};
  const { multiFields: autofillMultiFields } = autofillQuestion || {};
  const typeHasUploadOption = type === 'upload' || type === 'markdown-editor' && uploadEnabled;
  const typeHasCustomTitles = ['multi-field', 'products-data'].includes(type);
  const typeIsProductsData = type === 'products-data';
  const multiFieldsFromAutofill = autofillQuestion && id && type === 'multi-field' && tagScope === scope;

  useImperativeHandle(
    ref,
    () => ({ getValue: () => ({ ...state, format: format || defaultFormat || '' }) }),
    [id, name, prefix, type, newLineEnabled, buttonName, customTitlesEnabled, defaultTitle, customTitles, orderByNumberEnabled, selectAllStandards, selectAllClassifications, multiFields, productsData, standards, standardData, classifications, classificationData, multiple, maxFiles, uploadEnabled, column, values, options, format, showTime, placeholder, scope, example, hint, xmlTagName, required, description, descriptionExport, oneOfEnabled, conditions]
  );

  const onChange = (item, value) => dispatch({ type: 'change', payload: { [item]: value } });

  useEffect(() => {
    if (multiFieldsFromAutofill) onChange('multiFields', autofillMultiFields);
    if (type === 'standards' && !standards?.length) onChange('standards', dataForStandards);
    if (type === 'classifications' && !classifications?.length) onChange('classifications', dataForClassifications);
  }, [id, type, scope]);

  useEffect(() => {
    dispatch({ type: 'change', payload: currentQuestion });

    return () => dispatch({ type: 'reset' });
  }, [parentId, currentQuestion]);

  const fields = [
    {
      key: 'id',
      requiredField: !typeIsProductsData,
      general: true,
      label: 'ID',
      validation: id && typeHasUploadOption && !tag && {
        validateStatus: 'error',
        help: 'Selected ID has no associated tag!'
      },
      cmp: (
        <Select
          showSearch
          value={id || undefined}
          optionLabelProp='label'
          placeholder='Select ID'
          style={{ width: '100%' }}
          disabled={typeIsProductsData}
          onChange={value => onChange('id', value)}
          filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {ids.map(({ _id, name: idName, systemTag }) => {
            const disabled = typeHasUploadOption && !systemTag;

            return (
              <Option key={_id} value={_id} label={idName} disabled={disabled}>
                <Tooltip title={disabled && 'Tag is missing'} placement='topLeft'>
                  <div className={disabled ? styles.disabled : ''}>
                    {idName}
                  </div>
                </Tooltip>
              </Option>
            );
          })}
        </Select>
      )
    },
    {
      key: 'tag',
      layout: {},
      dependence: tag && typeHasUploadOption,
      cmp: (
        <>
          <Item label='Tag Name' style={{ margin: '-10px 0 10px' }} {...formItemLayout}>{tagName}</Item>
          <Item label='Tag Scope' style={{ marginBottom: 10 }} {...formItemLayout}>{tagScope}</Item>
          <Item label='Tag File Type' style={{ marginBottom: 10 }} {...formItemLayout}>{tagFileType}</Item>
          <Item label='Tag Description' style={{ marginBottom: -10 }} {...formItemLayout}>{tagDescription}</Item>
        </>
      )
    },
    {
      key: 'name',
      requiredField: true,
      general: true,
      label: 'Name',
      cmp: (
        <Input
          value={name}
          placeholder='Enter name'
          onChange={e => onChange('name', e.target.value)}
        />
      )
    },
    {
      key: 'prefix',
      label: 'Prefix',
      general: true,
      cmp: (
        <>
          <Input
            value={prefix}
            disabled={!activePrefix}
            placeholder='Enter prefix'
            onChange={e => onChange('prefix', e.target.value)}
          />

          <Badge dot={false}>
            <Button
              style={{ fontSize: 14, position: 'absolute', top: -22, left: 8 }}
              onClick={() => { dispatchPrefix(); onChange('prefix', ''); }}
            >
              {!activePrefix ? 'Enable' : 'Disable'}
            </Button>
          </Badge>
        </>
      )
    },
    {
      key: 'type',
      requiredField: true,
      general: true,
      label: 'Type',
      cmp: (
        <Select
          showSearch
          value={type || undefined}
          optionLabelProp='label'
          placeholder='Select type'
          style={{ width: '100%' }}
          onChange={value => {
            onChange('type', value);

            if (value === 'products-data') onChange('id');
          }}
          filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {questionsTypes.map(({ value, label }) => {
            const disabled = !id && value === 'multi-field';

            return (
              <Option key={value} value={value} label={label} disabled={disabled}>
                <Tooltip title={disabled && 'Add Question ID first!'} placement='topLeft'>
                  <div className={disabled ? styles.disabled : ''}>
                    {label}
                  </div>
                </Tooltip>
              </Option>
            );
          })}
        </Select>
      )
    },
    {
      key: 'newLineEnabled',
      layout: formTailLayout,
      cmp: (
        <>
          <Checkbox
            checked={newLineEnabled}
            onChange={() => {
              onChange('newLineEnabled', !newLineEnabled);
              onChange('buttonName', '');
              onChange('customTitlesEnabled', false);
              onChange('defaultTitle', '');
            }}
          >
            Add New Line Item (User)
          </Checkbox>

          <Badge dot={false}>
            <Input
              value={buttonName}
              disabled={!newLineEnabled}
              placeholder='Enter button name'
              style={{ width: 200, position: 'absolute', top: -22, left: 8 }}
              onChange={e => onChange('buttonName', e.target.value)}
            />
          </Badge>
        </>
      )
    },
    {
      key: 'customTitlesEnabled',
      layout: formTailLayout,
      dependence: newLineEnabled && typeHasCustomTitles,
      cmp: (
        <Checkbox
          checked={customTitlesEnabled}
          onChange={() => {
            onChange('customTitlesEnabled', !customTitlesEnabled);
            onChange('defaultTitle', '');
            onChange('customTitles', []);
          }}
        >
          Enable Custom Titles
        </Checkbox>
      )
    },
    {
      key: 'defaultTitle',
      requiredField: true,
      dependence: newLineEnabled && customTitlesEnabled && typeHasCustomTitles,
      label: 'Default Title',
      cmp: (
        <Input
          value={defaultTitle}
          placeholder='Enter default title'
          onChange={e => onChange('defaultTitle', e.target.value)}
        />
      )
    },
    {
      key: 'customTitles',
      label: 'Custom Title',
      dependence: newLineEnabled && customTitlesEnabled && typeHasCustomTitles,
      cmp: (
        <Values
          hideLabel
          text='custom title'
          values={customTitles}
          questionId={questionId}
          disabled={orderByNumberEnabled}
          onChange={items => onChange('customTitles', items)}
        />
      )
    },
    {
      key: 'orderByNumberEnabled',
      layout: formTailLayout,
      dependence: newLineEnabled && customTitlesEnabled && typeHasCustomTitles,
      cmp: (
        <Checkbox
          checked={orderByNumberEnabled}
          disabled={!!customTitles.length}
          onChange={() => {
            onChange('orderByNumberEnabled', !orderByNumberEnabled);
            onChange('customTitles', []);
          }}
        >
          Order by Number
        </Checkbox>
      )
    },
    {
      key: 'multiFields',
      layout: {},
      cmp: (
        <MultiFields
          questionId={questionId}
          isEStar={isEStar}
          multiFields={multiFields}
          formLayout={formItemLayout}
          disabled={multiFieldsFromAutofill}
          onChange={items => onChange('multiFields', items)}
        />
      )
    },
    {
      key: 'productsData',
      layout: {},
      cmp: (
        <ProductsData
          questionId={questionId}
          isEStar={isEStar}
          productsData={productsData}
          formLayout={formItemLayout}
          onChange={items => onChange('productsData', items)}
        />
      )
    },
    {
      key: 'standards',
      layout: {},
      cmp: (
        <Standards
          questionId={questionId}
          isEStar={isEStar}
          standards={standards}
          standardData={standardData}
          formLayout={formItemLayout}
          selectAll={selectAllStandards}
          onChange={items => onChange('standards', items)}
          onChangeData={items => onChange('standardData', items)}
          onChangeSelectAll={() => onChange('selectAllStandards', !selectAllStandards)}
        />
      )
    },
    {
      key: 'classifications',
      layout: {},
      cmp: (
        <Classifications
          questionId={questionId}
          isEStar={isEStar}
          classifications={classifications}
          classificationData={classificationData}
          formLayout={formItemLayout}
          selectAll={selectAllClassifications}
          onChange={items => onChange('classifications', items)}
          onChangeData={items => onChange('classificationData', items)}
          onChangeSelectAll={() => onChange('selectAllClassifications', !selectAllClassifications)}
        />
      )
    },
    {
      key: 'uploadEnabled',
      layout: formTailLayout,
      cmp: (
        <Checkbox
          checked={uploadEnabled}
          onChange={() => onChange('uploadEnabled', !uploadEnabled)}
        >
          Upload Option
        </Checkbox>
      )
    },
    {
      key: 'multiple',
      layout: formTailLayout,
      dependence: typeHasUploadOption,
      cmp: (
        <Checkbox
          checked={multiple}
          onChange={() => onChange('multiple', !multiple)}
        >
          {typeHasUploadOption ? 'Limit Files' : 'Allow multiple'}
        </Checkbox>
      )
    },
    {
      key: 'maxFiles',
      label: 'Max files',
      layout: formItemLayout,
      dependence: multiple && typeHasUploadOption,
      cmp: (
        <InputNumber
          min={0}
          value={maxFiles}
          style={{ width: '100%' }}
          placeholder='Enter max files number'
          onChange={value => onChange('maxFiles', value)}
        />
      )
    },
    {
      key: 'column',
      layout: formTailLayout,
      cmp: (
        <Checkbox
          checked={column}
          onChange={() => onChange('column', !column)}
        >
          Column Layout
        </Checkbox>
      )
    },
    {
      key: 'options',
      layout: {},
      cmp: (
        <Options
          options={options}
          layout={formItemLayout}
          questionId={questionId}
          onChange={items => onChange('options', items)}
        />
      )
    },
    {
      key: 'values',
      layout: {},
      cmp: (
        <Values
          values={values}
          layout={formItemLayout}
          questionId={questionId}
          onChange={items => onChange('values', items)}
        />
      )
    },
    {
      key: 'format',
      label: 'Format',
      cmp: (
        <Input
          value={format}
          placeholder={`Enter format (ex. ${defaultFormat})`}
          onChange={e => onChange('format', e.target.value)}
        />
      )
    },
    {
      key: 'showTime',
      layout: formTailLayout,
      cmp: (
        <Checkbox
          checked={showTime}
          onChange={() => onChange('showTime', !showTime)}
        >
          Show Time
        </Checkbox>
      )
    },
    {
      key: 'placeholder',
      label: 'Placeholder',
      cmp: (
        <Input
          value={placeholder}
          placeholder='Enter placeholder'
          onChange={e => onChange('placeholder', e.target.value)}
        />
      )
    },
    {
      key: 'scope',
      label: 'Scope',
      cmp: (
        <Select
          showSearch
          value={scope}
          style={{ width: '100%' }}
          placeholder='Select scope'
          onChange={value => onChange('scope', value)}
          filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {questionsScopes.map(value => (
            <Option key={value} value={value}>
              {value}
            </Option>
          ))}
        </Select>
      )
    },
    {
      key: 'example',
      layout: {},
      cmp: (
        <Example
          example={example}
          layout={formItemLayout}
          onChange={item => onChange('example', item)}
        />
      )
    },
    {
      key: 'xmlTagName',
      dependence: isEStar,
      label: 'XML Association',
      cmp: (
        <Input
          value={xmlTagName}
          placeholder='Enter XML Association'
          onChange={e => onChange('xmlTagName', e.target.value)}
        />
      )
    },
    {
      key: 'required',
      general: true,
      layout: formTailLayout,
      cmp: (
        <Checkbox
          checked={required}
          onChange={() => onChange('required', !required)}
        >
          The question is required
        </Checkbox>
      )
    },
  ].filter(({ key, general, dependence }) => general || extra?.includes(key) || dependence);

  return (
    <>
      {fields.map(({ key, requiredField, label, layout, validation, cmp }) => (
        <Item key={key} required={requiredField} label={label} {...(layout || formItemLayout)} {...validation}>
          {cmp}
        </Item>
      ))}

      <MDEditor
        value={hint}
        label='Hint'
        height={300}
        setValue={value => onChange('hint', value)}
      />

      <MDEditor
        value={description}
        label='Description (UI)'
        setValue={value => onChange('description', value)}
      />

      {type !== 'upload' && (
        <MDEditor
          value={descriptionExport}
          label='Description (Export)'
          setValue={value => onChange('descriptionExport', value)}
          extra={
            <Button
              type='link'
              style={{ padding: '0 5px' }}
              onClick={() => onChange('descriptionExport', description)}
            >
              Copy Data from Editor
            </Button>
          }
        />
      )}

      <Conditions fromQuestion oneOfEnabled={oneOfEnabled} conditions={conditions} dependencies={dependencies} onChange={onChange} />
    </>
  );
});
