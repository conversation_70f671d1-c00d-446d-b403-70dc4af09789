import React from 'react';
import { Button, Table, Select } from 'antd';

const { Option } = Select;

/**
 * Wizard List
 * @param {boolean} loading
 * @param {string} type
 * @param {Object[]} list
 * @param {Object} filtersData
 * @returns {JSX.Element}
 */
export default ({ loading = false, type, list = [], filtersData }) => {
  const getColumns = () => {
    const {
      existsCountries = [],
      existsNotifiedBodies = [],
      existsApplicationsTypes = [],
      existsProductsTypes = [],
      existsClassifications = [],
      existsVersionNumbers = [],
      existsSections = [],
      existsQuestions = [],
      existsQuestionTypes = []
    } = filtersData || {};

    const filterDropdown = ({ selectedKeys, setSelectedKeys, confirm, clearFilters, data, title }) => (
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', padding: 8 }}>
        <Select
          showSearch
          value={selectedKeys[0]}
          style={{ width: 188, marginBottom: 8, display: 'block' }}
          placeholder={`Select ${title}`}
          onChange={name => {
            setSelectedKeys(name ? [name] : []);
            confirm();
          }}
        >
          {data.map(name => (
            <Option key={name} value={name}>
              {name}
            </Option>
          ))}
        </Select>

        <Button
          size='small'
          style={{ width: 90 }}
          onClick={() => { clearFilters(); confirm(); setSelectedKeys([]); }}
        >
          Reset
        </Button>
      </div>
    );

    const columns = [
      {
        title: 'Country',
        dataIndex: 'country',
        filterDropdown: props => filterDropdown({ ...props, data: existsCountries, title: 'Country' }),
        onFilter: (value, { country }) => country === value
      },
      {
        title: 'Notified Body',
        dataIndex: 'notifiedBody',
        filterDropdown: props => filterDropdown({ ...props, data: existsNotifiedBodies, title: 'Notified Body' }),
        onFilter: (value, { notifiedBody }) => notifiedBody === value
      },
      {
        title: 'Application Type',
        dataIndex: 'applicationType',
        filterDropdown: props => filterDropdown({ ...props, data: existsApplicationsTypes, title: 'Application Type' }),
        onFilter: (value, { applicationType }) => applicationType === value
      },
      {
        title: 'Product Type',
        dataIndex: 'productType',
        filterDropdown: props => filterDropdown({ ...props, data: existsProductsTypes, title: 'Product Type' }),
        onFilter: (value, { productType }) => productType === value
      },
      {
        title: 'Classification',
        dataIndex: 'classification',
        filterDropdown: props => filterDropdown({ ...props, data: existsClassifications, title: 'Classification' }),
        onFilter: (value, { classification }) => classification === value
      },
      {
        title: 'Version Number',
        dataIndex: 'versionNumber',
        filterDropdown: props => filterDropdown({ ...props, data: existsVersionNumbers, title: 'Version Number' }),
        onFilter: (value, { versionNumber }) => versionNumber === value
      },
      {
        title: 'Section/Sub Section',
        dataIndex: 'section',
        filterDropdown: props => filterDropdown({ ...props, data: existsSections, title: 'Section' }),
        onFilter: (value, { section }) => section === value
      },
      {
        title: 'Question',
        dataIndex: 'name',
        width: 150,
        filterDropdown: props => filterDropdown({ ...props, data: existsQuestions, title: 'Question' }),
        onFilter: (value, { name }) => name === value
      }
    ];

    if (type === 'wizardId') {
      columns.unshift({
        title: 'ID',
        dataIndex: 'id',
        width: 150
      });

      columns.push({
        title: 'Question Type',
        dataIndex: 'type',
        filterDropdown: props => filterDropdown({ ...props, data: existsQuestionTypes, title: 'Question Type' }),
        onFilter: (value, { type: questionType }) => questionType === value
      });
    }

    return columns;
  };

  return (
    <>
      <Table
        rowKey='_id'
        size='middle'
        bordered
        loading={loading}
        columns={getColumns()}
        dataSource={list}
        pagination={{
          defaultPageSize: 5,
          size: 'Pagination',
        }}
      />
    </>
  );
};
