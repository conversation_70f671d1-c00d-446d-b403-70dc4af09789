import React from 'react';
import { Modal } from 'antd';
import Table from './Table';

/**
 * Wizard List Modal
 * @param {boolean} show
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ show, onClose, ...props }) => (
  <Modal
    title='Wizard List'
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={onClose}
    onClose={onClose}
    width={1200}
    zIndex={999}
  >
    <Table {...props} />
  </Modal>
);
