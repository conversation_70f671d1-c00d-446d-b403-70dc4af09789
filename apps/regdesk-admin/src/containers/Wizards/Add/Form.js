import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { Button, Form, Input, Select, Icon, Tooltip, Badge } from 'antd';
import { allCountriesWithEU } from '../../../utils/countries';
import AddSuggestion from '../Suggestion';
import history from '../../../utils/history';
import actions from '../../../actions';

const { Item } = Form;
const { Option } = Select;
const { TextArea } = Input;

const formItemLayout = {
  labelCol: { xs: { span: 6 }, },
  wrapperCol: { xs: { span: 14 }, },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Add/Clone Wizard
 * @param {?string} id
 * @param {Object[]} notifiedBodies
 * @param {Object[]} applicationTypes
 * @param {Object[]} productTypes
 * @param {Object[]} classifications
 * @param {string[]} permissions
 * @param {string[]} availableCountries
 * @param {boolean} limitCountries
 * @param {function} onClose
 * @returns {JSX.Element}
 */
const WizardAdd = ({
  id,
  notifiedBodies,
  applicationTypes,
  productTypes,
  classifications,
  permissions,
  availableCountries,
  limitCountries,
  onClose
}) => {
  const [showModalAppType, setShowModalAppType] = useState(false);
  const [showModalProdType, setShowModalProdType] = useState(false);
  const [showModalNotBody, setShowModalNotBody] = useState(false);
  const [country, setCountry] = useState(null);
  const [notifiedBody, setNotifiedBody] = useState(null);
  const [applicationType, setApplicationType] = useState(null);
  const [productType, setProductType] = useState(null);
  const [classification, setClassification] = useState(null);
  const [versionNumber, setVersionNumber] = useState('');
  const [releaseDate, setReleaseDate] = useState('');
  const [versionDescription, setVersionDescription] = useState('');
  const [suggestion, setSuggestion] = useState(null);

  const accessEdit = permissions.includes('accessWizardAddParams');
  const isEU = country === 'EU';
  let canSave = country && applicationType && productType && classification && versionNumber && releaseDate;
  let allCountries = allCountriesWithEU;

  if (limitCountries) allCountries = allCountries.filter(({ alpha3code }) => availableCountries.includes(alpha3code));
  if (isEU) canSave &&= notifiedBody;

  useEffect(() => {
    actions.wizards.getSuggestions();

    return (() => {
      actions.wizards.change({
        notifiedBodies: [],
        applicationTypes: [],
        productTypes: [],
        classifications: [],
      });
    });
  }, []);

  useEffect(() => {
    if (country) actions.wizards.getClassificationsByCountry(country);
  }, [country]);

  const onCloseModal = () => {
    setShowModalAppType(false);
    setShowModalProdType(false);
    setShowModalNotBody(false);
    setSuggestion(null);
  };

  const onAdd = () => {
    const params = {
      country,
      notifiedBody,
      applicationType,
      productType,
      classification,
      versionNumber,
      releaseDate,
      versionDescription
    };

    if (id) params.wizardId = id;

    actions.wizards[id ? 'clone' : 'add'](params)
      .then(({ wizardId }) => history.push(`${config.rootRoute}/wizards/${wizardId}`));

    onClose();
  };

  return (
    <>
      <Item required label='Country' {...formItemLayout}>
        <Select
          showSearch
          style={{ width: ' 100%' }}
          placeholder='Select country'
          value={country || undefined}
          onChange={idc => { setCountry(idc); setNotifiedBody(null); setClassification(null); }}
          optionFilterProp='children'
          filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {allCountries.map(({ alpha3code, name }) => (
            <Option key={alpha3code} value={alpha3code}>
              {name}
            </Option>
          ))}
        </Select>
      </Item>

      <Item required label='Notified Body' {...formItemLayout}>
        <Select
          showSearch
          disabled={!isEU}
          style={{ width: '100%' }}
          value={notifiedBody || undefined}
          placeholder='Select notified body (only for EU)'
          onChange={body => setNotifiedBody(body)}
          optionLabelProp='label'
          filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {notifiedBodies?.map(({ _id, name }) => (
            <Option key={_id} value={_id} label={name}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                {name}

                <a>
                  <Icon
                    type='edit'
                    onClick={e => { e.stopPropagation(); setSuggestion({ _id, name }); setShowModalNotBody(true); }}
                  />
                </a>
              </div>
            </Option>
          ))}
        </Select>

        <Badge dot={false}>
          <a disabled={!isEU}>
            <Icon
              type='plus'
              style={{ fontSize: '18px', position: 'absolute', top: -15, left: 8 }}
              onClick={() => setShowModalNotBody(true)}
            />
          </a>
        </Badge>
      </Item>

      <Item required label='Application Type' {...formItemLayout}>
        <Select
          showSearch
          style={{ width: '100%' }}
          value={applicationType || undefined}
          placeholder='Select application type'
          onChange={type => setApplicationType(type)}
          optionLabelProp='label'
          filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {applicationTypes?.map(({ _id, name }) => (
            <Option key={_id} value={_id} label={name}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                {name}

                {accessEdit && (
                  <a>
                    <Icon
                      type='edit'
                      onClick={e => { e.stopPropagation(); setSuggestion({ _id, name }); setShowModalAppType(true); }}
                    />
                  </a>
                )}
              </div>
            </Option>
          ))}
        </Select>

        {accessEdit && (
          <Badge dot={false}>
            <a>
              <Icon
                type='plus'
                style={{ fontSize: '18px', position: 'absolute', top: -15, left: 8 }}
                onClick={() => setShowModalAppType(true)}
              />
            </a>
          </Badge>
        )}
      </Item>

      <Item required label='Product Type' {...formItemLayout}>
        <Select
          showSearch
          style={{ width: '100%' }}
          value={productType || undefined}
          placeholder='Select product type'
          onChange={type => setProductType(type)}
          optionLabelProp='label'
          filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {productTypes?.map(({ _id, name }) => (
            <Option key={_id} value={_id} label={name}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                {name}

                {accessEdit && (
                  <a>
                    <Icon
                      type='edit'
                      onClick={e => { e.stopPropagation(); setSuggestion({ _id, name }); setShowModalProdType(true); }}
                    />
                  </a>
                )}
              </div>
            </Option>
          ))}
        </Select>

        {accessEdit && (
          <Badge dot={false}>
            <a>
              <Icon
                type='plus'
                style={{ fontSize: '18px', position: 'absolute', top: -15, left: 8 }}
                onClick={() => setShowModalProdType(true)}
              />
            </a>
          </Badge>
        )}
      </Item>

      <Item required label='Classification' {...formItemLayout}>
        <Select
          showSearch
          style={{ width: '100%' }}
          value={classification || undefined}
          placeholder='Select classification'
          onChange={type => setClassification(type)}
          optionFilterProp='children'
          filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {classifications?.map(({ _id, name }) => (
            <Option key={_id} value={_id}>
              {name}
            </Option>
          ))}
        </Select>

        <Badge dot={false}>
          <Tooltip title='Available classifications are specific to the selected country above'>
            <Icon
              type='exclamation-circle'
              style={{ fontSize: '18px', position: 'absolute', top: -15, left: 8 }}
            />
          </Tooltip>
        </Badge>
      </Item>

      <Item required label='Version Number' {...formItemLayout}>
        <Input
          value={versionNumber}
          placeholder='Enter version number'
          onChange={e => setVersionNumber(e.target.value)}
        />

        <Badge dot={false}>
          <Tooltip title='Version Number of the template in RegDesk'>
            <Icon
              type='exclamation-circle'
              style={{ fontSize: '18px', position: 'absolute', top: -15, left: 8 }}
            />
          </Tooltip>
        </Badge>
      </Item>

      <Item required label='Release Date' {...formItemLayout}>
        <Input
          value={releaseDate}
          placeholder='Enter release date'
          onChange={e => setReleaseDate(e.target.value)}
        />
      </Item>

      <Item label='Version Description' {...formItemLayout}>
        <TextArea
          rows={3}
          value={versionDescription}
          placeholder='Enter version description'
          onChange={e => setVersionDescription(e.target.value)}
        />
      </Item>

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={() => onClose()}>Cancel</Button>

        <Button
          type='primary'
          disabled={!canSave}
          style={{ marginLeft: 8 }}
          onClick={() => onAdd()}
        >
          Save
        </Button>
      </Item>

      {showModalAppType && (
        <AddSuggestion
          type='applicationType'
          show={showModalAppType}
          suggestion={suggestion}
          onClose={() => onCloseModal()}
        />
      )}

      {showModalProdType && (
        <AddSuggestion
          type='productType'
          show={showModalProdType}
          suggestion={suggestion}
          onClose={() => onCloseModal()}
        />
      )}

      {showModalNotBody && (
        <AddSuggestion
          type='notifiedBody'
          show={showModalNotBody}
          suggestion={suggestion}
          onClose={() => onCloseModal()}
        />
      )}
    </>
  );
};

export default connect(({ wizards, account }) => ({
  filtersData: wizards.filtersData,
  notifiedBodies: wizards.notifiedBodies,
  applicationTypes: wizards.applicationTypes,
  productTypes: wizards.productTypes,
  classifications: wizards.classifications,
  permissions: account.adminPermissions,
  availableCountries: account.adminCountriesForWizards,
  limitCountries: account.adminLimitCountriesForWizards,
}))(WizardAdd);
