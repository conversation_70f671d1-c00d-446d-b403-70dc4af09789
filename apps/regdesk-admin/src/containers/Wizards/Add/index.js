import React from 'react';
import { Modal } from 'antd';
import Form from './Form';

/**
 * Add/Clone Wizard Modal
 * @param {string} id
 * @param {boolean} show
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ id, show, onClose, ...props }) => (
  <Modal
    title={`${id ? 'Clone' : 'Create New'} Wizard`}
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={() => onClose()}
    onClose={() => onClose()}
    width={700}
    zIndex={999}
  >
    <Form id={id} onClose={onClose} {...props} />
  </Modal>
);
