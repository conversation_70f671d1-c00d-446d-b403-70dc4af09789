import React, { useState } from 'react';
import { Button, Form, Input } from 'antd';
import actions from '../../../actions';

const { Item } = Form;

const formItemLayout = {
  labelCol: { xs: { span: 6 }, },
  wrapperCol: { xs: { span: 14 }, },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Add/Update Suggestion
 * @param {string} type
 * @param {?Object} suggestion
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ type, suggestion, onClose }) => {
  const { _id: id, name } = suggestion || {};
  const [newName, setName] = useState(name || '');

  const reset = () => {
    setName('');
    onClose();
  };

  const onAdd = () => {
    const newSuggestion = { name: newName, type };

    if (id) actions.wizards.updateSuggestion(id, newSuggestion);
    else actions.wizards.addSuggestion(newSuggestion);

    reset();
  };

  return (
    <>
      <Item required label='Name' {...formItemLayout}>
        <Input
          autoFocus
          placeholder='Enter name'
          value={newName}
          onChange={e => setName(e.target.value)}
        />
      </Item>

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={() => reset()}>Cancel</Button>

        <Button
          style={{ marginLeft: 8 }}
          type='primary'
          disabled={!newName}
          onClick={() => onAdd()}
        >
          {id ? 'Update' : 'Add'}
        </Button>
      </Item>
    </>
  );
};
