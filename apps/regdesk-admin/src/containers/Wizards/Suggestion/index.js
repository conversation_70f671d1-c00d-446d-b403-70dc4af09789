import React from 'react';
import { Modal } from 'antd';
import Form from './Form';

/**
 * Add/Update Suggestion Modal
 * @param {boolean} show
 * @param {string} type
 * @param {?Object} suggestion
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ show, type, suggestion, onClose }) => {
  let title = '';

  if (suggestion) title += 'Update ';
  else title += 'Create New ';

  if (type === 'applicationType') title += 'Application Type';
  if (type === 'productType') title += 'Product Type';
  if (type === 'notifiedBody') title += 'Notified Body';

  return (
    <Modal
      title={title}
      visible={show}
      footer={null}
      centered
      destroyOnClose
      onCancel={() => onClose()}
      onClose={() => onClose()}
      width={600}
      zIndex={999}
    >
      <Form type={type} onClose={onClose} suggestion={suggestion} />
    </Modal>
  );
};
