import React from 'react';
import { Modal } from 'antd';
import Form from './Form';

/**
 * Add System Tag Modal
 * @param {boolean} show
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ show, onClose }) => (
  <Modal
    title='Create New System Tag'
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={() => onClose()}
    onClose={() => onClose()}
    width={700}
    zIndex={999}
  >
    <Form onClose={onClose} />
  </Modal>
);
