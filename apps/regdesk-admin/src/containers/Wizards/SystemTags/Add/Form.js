import React, { useState } from 'react';
import { Button, Form, Input } from 'antd';
import actions from '../../../../actions';

const { Item } = Form;
const { TextArea } = Input;

const formItemLayout = {
  labelCol: { xs: { span: 6 }, },
  wrapperCol: { xs: { span: 14 }, },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Add System Tag
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ onClose }) => {
  const [newName, setNewName] = useState('');
  const [newDescription, setNewDescription] = useState('');

  const onReset = () => {
    setNewName('');
    setNewDescription('');
    onClose();
  };

  const onAdd = () => {
    actions.wizards.addTag({ name: newName, description: newDescription });
    onReset();
  };

  return (
    <>
      <Item required label='Name' {...formItemLayout}>
        <Input
          autoFocus
          placeholder='Enter name'
          value={newName}
          onChange={e => setNewName(e.target.value)}
        />
      </Item>

      <Item label='Description' {...formItemLayout}>
        <TextArea
          rows={3}
          placeholder='Enter description'
          value={newDescription}
          onChange={e => setNewDescription(e.target.value)}
        />
      </Item>

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={onReset}>Cancel</Button>

        <Button
          style={{ marginLeft: 8 }}
          type='primary'
          disabled={!newName}
          onClick={onAdd}
        >
          Add
        </Button>
      </Item>
    </>
  );
};
