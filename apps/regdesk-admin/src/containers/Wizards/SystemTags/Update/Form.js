import React, { useState } from 'react';
import { Button, Form, Select, Input, InputNumber } from 'antd';
import { tagScopes, systemTagFileTypes } from '../../utils';
import actions from '../../../../actions';

const { Item } = Form;
const { Option } = Select;
const { TextArea } = Input;

const formItemLayout = {
  labelCol: { xs: { span: 6 }, },
  wrapperCol: { xs: { span: 14 }, },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Update System Tag
 * @param {Object} tag
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ tag, onClose }) => {
  const { _id, name, description, scope, fileType, maxFileSize, fileNameLengthLimit } = tag || {};
  const [newName, setNewName] = useState(name || '');
  const [newDescription, setNewDescription] = useState(description || '');
  const [newScope, setNewScope] = useState(scope || 'N/A');
  const [newFileType, setNewFileType] = useState(fileType ? fileType.split(',') : null);
  const [newMaxSize, setNewMaxSize] = useState(maxFileSize || null);
  const [newNameLimit, setNewNameLimit] = useState(fileNameLengthLimit || null);

  const reset = () => {
    setNewName('');
    setNewDescription('');
    setNewScope('NA');
    setNewFileType(null);
    onClose();
  };

  const onUpdate = () => {
    const newData = {
      name: newName,
      description: newDescription,
      scope: newScope,
      fileType: newFileType?.toString() || '',
      maxFileSize: newMaxSize,
      fileNameLengthLimit: newNameLimit
    };

    if (_id) actions.wizards.updateTag(_id, newData);
    reset();
  };

  return (
    <>
      <Item required label='Name' {...formItemLayout}>
        <Input
          autoFocus
          placeholder='Enter name'
          value={newName}
          onChange={e => setNewName(e.target.value)}
        />
      </Item>

      <Item label='Description' {...formItemLayout}>
        <TextArea
          rows={3}
          placeholder='Enter description'
          value={newDescription}
          onChange={e => setNewDescription(e.target.value)}
        />
      </Item>

      <Item label='Scope' {...formItemLayout}>
        <Select
          showSearch
          value={newScope || undefined}
          style={{ width: '100%' }}
          optionLabelProp='label'
          placeholder='Select scope'
          onChange={value => setNewScope(value)}
          filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {tagScopes.map(value => (
            <Option key={value} value={value} label={value}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                {value}
              </div>
            </Option>
          ))}
        </Select>
      </Item>

      <Item label='File Type' {...formItemLayout}>
        <Select
          mode='multiple'
          showSearch
          value={newFileType || undefined}
          style={{ width: '100%' }}
          optionLabelProp='label'
          placeholder='Select file type'
          onChange={value => setNewFileType(value)}
          filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {systemTagFileTypes?.map((type) => (
            <Option key={type} value={type} label={type}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                {type}
              </div>
            </Option>
          ))}
        </Select>
      </Item>

      <Item label='Max file size (Kb)' {...formItemLayout}>
        <InputNumber
          min={0}
          placeholder='Enter max file size'
          defaultValue={newMaxSize}
          style={{ width: '100%' }}
          onChange={value => setNewMaxSize(value)}
        />
      </Item>

      <Item label='Title Characters Limit' {...formItemLayout}>
        <InputNumber
          min={0}
          placeholder='Enter characters limit'
          defaultValue={newNameLimit}
          style={{ width: '100%' }}
          onChange={value => setNewNameLimit(value)}
        />
      </Item>

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={() => reset()}>Cancel</Button>

        <Button
          style={{ marginLeft: 8 }}
          type='primary'
          disabled={!newName}
          onClick={onUpdate}
        >
          Update
        </Button>
      </Item>
    </>
  );
};
