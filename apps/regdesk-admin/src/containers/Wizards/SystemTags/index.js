import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { Button, Table, Card, Input, Tooltip, Menu, Dropdown, Icon, Popconfirm } from 'antd';
import Breadcrumb from '../../../components/Breadcrumb';
import { tagScopes, systemTagFileTypes } from '../utils';
import Update from './Update';
import Add from './Add';
import actions from '../../../actions';
import styles from '../index.less';

const { Item } = Menu;

/**
 * System Tags List
 * @param {Object[]} list
 * @param {boolean} loading
 * @param {Object} pagination
 * @param {string[]} permissions
 * @returns {JSX.Element}
 */
const SystemTags = ({ list, loading, pagination, permissions }) => {
  const [tag, setTag] = useState(null);
  const [showModalAdd, setShowModalAdd] = useState(false);
  const [showModalUpdate, setShowModalUpdate] = useState(false);

  const accessEdit = permissions.includes('accessWizardUpdateTag');

  useEffect(() => {
    actions.wizards.getTags();

    return () => actions.wizards.change({ pagination: {} });
  }, []);

  const onTableChange = (newPagination, newFilters) => actions.wizards.getTags({ pagination: newPagination, filters: newFilters });

  const getColumns = () => {
    const filterDropdown = ({ selectedKeys, setSelectedKeys, confirm, clearFilters }, text) => (
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', padding: 8 }}>
        <Input
          showSearch
          value={selectedKeys[0]}
          style={{ width: 188, marginBottom: 8, display: 'block' }}
          placeholder={`Search ${text}`}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => confirm()}
        />

        <Button
          size='small'
          style={{ width: 90 }}
          onClick={() => { clearFilters(); confirm(); setSelectedKeys([]); }}
        >
          Reset
        </Button>
      </div>
    );

    return ([
      {
        title: 'Tag',
        dataIndex: 'name',
        filterDropdown: props => filterDropdown(props, 'tag')
      },
      {
        title: 'Tag Description',
        dataIndex: 'description',
        filterDropdown: props => filterDropdown(props, 'description')
      },
      {
        title: 'Scope',
        dataIndex: 'scope',
        filters: tagScopes.map(value => ({ text: value, value })),
      },
      {
        title: 'File Type',
        dataIndex: 'fileType',
        filters: systemTagFileTypes.map(value => ({ text: value, value })),
      },
      {
        title: 'Max file size (Kb)',
        dataIndex: 'maxFileSize',
      },
      {
        title: 'Title Characters Limit',
        dataIndex: 'fileNameLengthLimit',
      },
      {
        title: 'Actions',
        width: 100,
        render: item => {
          const { _id, released, isUsed } = item;

          const menu = (
            <Menu>
              <Item disabled={isUsed} onClick={() => { if (!isUsed) setShowModalUpdate(true); setTag(item); }}>Edit</Item>
              <Item disabled={released} onClick={() => { if (!released) actions.wizards.releaseTag(_id); }}>{released ? 'Released' : 'Release'}</Item>

              {!released && (
                <Item>
                  <Popconfirm
                    title='Are you sure you would like to remove this system tag?'
                    onConfirm={() => actions.wizards.deleteTag(_id)}
                    okText='Yes'
                    cancelText='No'
                  >
                    <a>Delete</a>
                  </Popconfirm>
                </Item>
              )}
            </Menu>
          );

          if (accessEdit) {
            return (
              <Dropdown overlay={menu}>
                <a className='ant-dropdown-link'>
                  Actions <Icon type='down' />
                </a>
              </Dropdown>
            );
          }

          return null;
        }
      },
    ]);
  };

  const onCloseModal = () => {
    setShowModalAdd(false);
    setShowModalUpdate(false);
    setTag(null);
  };

  return (
    <>
      <Breadcrumb
        routes={[
          { title: 'Dashboard', href: `${config.rootRoute}/` },
          { title: 'Wizards', href: `${config.rootRoute}/wizards` },
          { title: 'System Tags' },
        ]}
      />

      <Card>
        <div className={styles.btnWrapper}>
          <Tooltip title={!accessEdit && 'No permissions'}>
            <Button
              icon='plus'
              type='primary'
              onClick={() => setShowModalAdd(true)}
              disabled={!accessEdit}
            >
              Add System Tag
            </Button>
          </Tooltip>
        </div>

        <Table
          rowKey='_id'
          size='middle'
          bordered
          loading={loading}
          columns={getColumns()}
          dataSource={list}
          pagination={{
            defaultPageSize: 10,
            size: 'Pagination',
            ...pagination,
          }}
          onChange={onTableChange}
        />
      </Card>

      {showModalAdd && <Add show={showModalAdd} onClose={() => onCloseModal()} />}
      {showModalUpdate && <Update show={showModalUpdate} tag={tag} onClose={() => onCloseModal()} />}
    </>
  );
};

export default connect(({ wizards, account }) => ({
  list: wizards.tags,
  loading: wizards.loading,
  pagination: wizards.pagination,
  permissions: account.adminPermissions
}))(SystemTags);
