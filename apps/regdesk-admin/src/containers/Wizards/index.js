import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { Button, Table, Card, Popconfirm, Icon, Select, Dropdown, Menu, Tooltip, Checkbox } from 'antd';
import { allCountriesWithEU } from '../../utils/countries';
import Breadcrumb from '../../components/Breadcrumb';
import CountryFlag from '../../components/CountryFlag';
import Add from './Add';
import history from '../../utils/history';
import actions from '../../actions';
import styles from './index.less';

const { Option } = Select;
const { Item } = Menu;

/**
 * Wizards List
 * @param {Object[]} list
 * @param {boolean} loading
 * @param {Object} pagination
 * @param {Object} filtersData
 * @param {string[]} permissions
 * @param {boolean} limitCountries
 * @param {string[]} availableCountries
 * @returns {JSX.Element}
 */
const Wizards = ({
  list,
  loading,
  pagination,
  filtersData,
  permissions,
  limitCountries,
  availableCountries,
}) => {
  const [showModalAdd, setShowModalAdd] = useState(false);
  const [showModalClone, setShowModalClone] = useState(false);
  const [wizardId, setWizardId] = useState(null);
  const { total = 0 } = pagination;

  const [needUpdate, setNeedUpdate] = useState(false);

  let allCountries = allCountriesWithEU;

  if (limitCountries) allCountries = allCountries.filter(({ alpha3code }) => availableCountries.includes(alpha3code));

  useEffect(() => {
    actions.wizards.get();
    actions.wizards.getFiltersData();

    return () => actions.wizards.change({ pagination: {} });
  }, []);

  const onTableChange = (newPagination, newFilters) => actions.wizards.get({ pagination: newPagination, filters: { ...newFilters, needUpdate } });

  const getColumns = () => {
    const {
      existsCountries = [],
      existsNotifiedBodies = [],
      existsApplicationsTypes = [],
      existsProductsTypes = [],
      existsClassifications = [],
    } = filtersData || {};

    return [
      {
        title: 'Country',
        dataIndex: 'country',
        filterDropdown: ({ selectedKeys, setSelectedKeys, confirm, clearFilters }) => (
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', padding: 8 }}>
            <Select
              showSearch
              value={selectedKeys[0]}
              style={{ width: 188, marginBottom: 8, display: 'block' }}
              placeholder='Search country'
              onChange={name => {
                setSelectedKeys(name ? [name] : []);
                confirm();
              }}
              optionFilterProp='children'
              filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
            >
              {allCountries
                .filter(({ alpha3code }) => existsCountries.includes(alpha3code))
                .map(({ alpha3code, name }) => (
                  <Option key={alpha3code} value={alpha3code}>
                    {name}
                  </Option>
                ))
              }
            </Select>

            <Button
              size='small'
              style={{ width: 90 }}
              onClick={() => { clearFilters(); confirm(); setSelectedKeys([]); }}
            >
              Reset
            </Button>
          </div>
        ),
        render: country => <CountryFlag countryId={country} />,
      },
      {
        title: 'Notified body (for EU only)',
        dataIndex: 'notifiedBody',
        filters: existsNotifiedBodies.map(({ _id, name }) => ({ text: name, value: _id })),
        render: item => <span>{item?.name || ''}</span>,
      },
      {
        title: 'Application type',
        dataIndex: 'applicationType',
        filters: existsApplicationsTypes.map(({ _id, name }) => ({ text: name, value: _id })),
        render: item => <span>{item?.name || ''}</span>,
      },
      {
        title: 'Product type',
        dataIndex: 'productType',
        filters: existsProductsTypes.map(({ _id, name }) => ({ text: name, value: _id })),
        render: item => <span>{item?.name || ''}</span>,
      },
      {
        title: 'Classification',
        dataIndex: 'classification',
        filters: existsClassifications.map(({ _id, name }) => ({ text: name, value: _id })),
        render: item => <span>{item?.name || ''}</span>,
      },
      {
        title: 'Version Number',
        dataIndex: 'versionNumber',
      },
      {
        title: 'Release Date',
        dataIndex: 'releaseDate',
      },
      {
        title: 'Version Description',
        dataIndex: 'versionDescription',
      },
      {
        title: 'Date Released',
        dataIndex: 'releasedDate',
        render: item => <span>{!!item && new Date(item)?.toLocaleDateString()}</span>
      },
      {
        title: 'Actions',
        width: 120,
        render: ({ _id, released, withOpenLayer, needUpdate: needUpdateParam }) => {
          const menu = (
            <Menu>
              <Item><a onClick={() => { setShowModalClone(true); setWizardId(_id); }}>Clone</a></Item>
              <Item><a onClick={() => history.push(`wizards/${_id}`)}>Edit</a></Item>
              <Item><a onClick={() => actions.wizards.updateRelease(_id, released)}>{released ? 'Unrelease' : 'Release'}</a></Item>

              {permissions.includes('accessWizardRemove') && (
                <Item>
                  <Popconfirm
                    title='Delete the wizard?'
                    onConfirm={() => actions.wizards.delete(_id)}
                    okText='Yes'
                    cancelText='No'
                  >
                    <a>Delete</a>
                  </Popconfirm>
                </Item>
              )}
            </Menu>
          );

          return (
            <>
              <Dropdown overlay={menu}>
                <a className='ant-dropdown-link'>
                  Actions <Icon type='down' />
                </a>
              </Dropdown>

              {withOpenLayer && (
                <Tooltip title='Open layer found!'>
                  <Icon type='warning' style={{ color: '#FAAD14', marginLeft: 6 }} />
                </Tooltip>
              )}

              {needUpdateParam && (
                <Tooltip title='This wizard has questions that need to be updated'>
                  <Icon type='exclamation-circle' theme='filled' style={{ color: '#FF5722', marginLeft: 6 }} />
                </Tooltip>
              )}
            </>
          );
        }
      },
    ];
  };

  return (
    <>
      <Breadcrumb
        routes={[
          { title: 'Dashboard', href: `${config.rootRoute}/` },
          { title: 'Wizards' },
        ]}
      />

      <Card>
        <div className={styles.btnWrapper}>
          <Checkbox onChange={e => { actions.wizards.get({ filters: { needUpdate: e.target.checked } }); setNeedUpdate(e.target.checked); }}>Need Update</Checkbox>

          <Button
            icon='plus'
            type='primary'
            onClick={() => setShowModalAdd(true)}
          >
            Create New Wizard
          </Button>

          <Button
            style={{ marginLeft: 10 }}
            onClick={() => history.push('wizards/classifications')}
          >
            Classifications
          </Button>

          <Button
            style={{ marginLeft: 10 }}
            onClick={() => history.push('wizards/ids')}
          >
            Wizard IDs
          </Button>

          <Button
            style={{ marginLeft: 10 }}
            onClick={() => history.push('wizards/tags')}
          >
            DMS System Tags
          </Button>

          <Button
            style={{ marginLeft: 10 }}
            onClick={() => history.push('wizards/questions')}
          >
            Questions
          </Button>
        </div>

        <Table
          size='middle'
          rowKey='_id'
          bordered
          loading={loading}
          columns={getColumns()}
          dataSource={list}
          pagination={{
            showTotal: () => `Total ${total} items`,
            defaultPageSize: 10,
            size: 'Pagination',
            ...pagination,
          }}
          onChange={onTableChange}
        />
      </Card>

      {showModalAdd && <Add show={showModalAdd} onClose={() => setShowModalAdd(false)} />}
      {showModalClone && <Add id={wizardId} show={showModalClone} onClose={() => setShowModalClone(false)} />}
    </>
  );
};

export default connect(({ wizards, account }) => ({
  list: wizards.list,
  loading: wizards.loading,
  pagination: wizards.pagination,
  filtersData: wizards.filtersData,
  permissions: account.adminPermissions,
  availableCountries: account.adminCountriesForWizards,
  limitCountries: account.adminLimitCountriesForWizards,
}))(Wizards);
