.btnWrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20px;
}

.btn {
  width: 100%;

  button {
    width: 100%;
  }
}

.disabled {
  cursor: not-allowed;
  color: #888 !important;

  &:hover {
    color: inherit;
  }
}

.dropdown {
  .name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    max-width: 150px;
  }

  .list {
    max-height: 300px;
    overflow-y: auto;
  }
}

.cardTitle {
  display: flex;
  justify-content: space-between;
}

.dynamicDeleteButton {
  font-size: 24px;
  color: #999;
  transition: all 0.3s;
}

.descriptionList {
  .description {
    padding-bottom: 16px;

    &:last-child {
      padding-bottom: 0;
    }

    .term {
      display: inline-block;
      line-height: 22px;
      font-weight: 500;
      margin-right: 8px;
      color: rgba(0, 0, 0, 85%);
      white-space: nowrap;
    }
  }
}

.fields {
  margin-bottom: 24px;

  :global {
    .ant-card-extra {
      padding: 0;
    }
  }
}

.cloneItem {
  display: grid;
  grid-template-columns: repeat(4, minmax(100px, 1fr));
  margin-bottom: 15px;

  .firstRemoveIcon {
    align-self: flex-end;
    bottom: 6px;
  }

  :global {
    .ant-form-item {
      margin-bottom: 0;
    }
  }
}

@media (max-width: 800px) {
  .cloneItem {
    display: block;
  }
}

.conditions {
  div:not(:last-child) {
    margin-bottom: 10px;
  }
}
