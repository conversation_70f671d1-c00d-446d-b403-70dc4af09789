import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { Row, Col, Button, Table, Card, Select, Input, Switch, Tooltip } from 'antd';
import { allCountriesWithEU, allCountriesWithEUObj } from '../../../utils/countries';
import Breadcrumb from '../../../components/Breadcrumb';
import Dropdown from '../Dropdown';
import Update from './Update';
import Add from './Add';
import actions from '../../../actions';
import styles from '../index.less';

const { Option } = Select;

/**
 * Classifications List
 * @param {Object[]} list
 * @param {boolean} loading
 * @param {string[]} permissions
 * @returns {JSX.Element}
 */
const WizardClass = ({ list, loading, permissions }) => {
  const [country, setCountry] = useState(null);
  const [searchName, setSearchName] = useState('');
  const [showModalAdd, setShowModalAdd] = useState(false);
  const [searchCountries, setSearchCountries] = useState([]);
  const [classification, setClassification] = useState(null);
  const [groupByCountry, setGroupByCountry] = useState(false);
  const [showModalUpdate, setShowModalUpdate] = useState(false);
  const [showWithCountries, setShowWithCountries] = useState(false);
  const [showWithClassification, setShowWithClassification] = useState(false);

  const accessEdit = permissions.includes('accessWizardAddParams');

  useEffect(() => {
    actions.wizards.getClassifications();
  }, []);

  const getColumns = () => {
    let columns = [];

    if (groupByCountry) {
      columns = [
        {
          title: 'Country',
          dataIndex: 'id',
          render: id => allCountriesWithEUObj[id]
        },
        {
          title: 'Classifications',
          width: 360,
          dataIndex: 'classifications',
          render: data => <Dropdown data={data} title='Classifications' />
        },
        {
          title: 'Actions',
          width: 160,
          render: ({ id, classifications }) => (
            <Tooltip title={!accessEdit && 'No permissions'}>
              <a
                className={!accessEdit && styles.disabled}
                onClick={() => {
                  if (accessEdit) {
                    setShowModalUpdate(true);
                    setCountry(({ id, classifications: classifications.map(({ _id }) => _id) }));
                  }
                }}
              >
                Update
              </a>
            </Tooltip>
          )
        },
      ];
    } else {
      columns = [
        {
          title: 'Classification List',
          dataIndex: 'name',
        },
        {
          title: 'Countries',
          width: 360,
          dataIndex: 'countries',
          render: data => <Dropdown data={data.filter(idc => allCountriesWithEUObj[idc])} title='Countries' showCountries />
        },
        {
          title: 'Actions',
          width: 160,
          render: item => (
            <Tooltip title={!accessEdit && 'No permissions'}>
              <a
                className={!accessEdit && styles.disabled}
                onClick={() => {
                  if (accessEdit) {
                    setShowModalAdd(true);
                    setClassification(item);
                  }
                }}
              >
                Update
              </a>
            </Tooltip>
          ),
        },
      ];
    }

    return columns;
  };

  const getData = () => {
    let data = list;

    if (groupByCountry) {
      data = allCountriesWithEU
        .map(({ alpha3code }) => ({
          id: alpha3code,
          classifications: list
            .filter(({ countries }) => countries.includes(alpha3code))
            .map(({ _id, name }) => ({ _id, name }))
        }))
        .filter(({ id, classifications }) => {
          let value = true;

          if (showWithClassification) value &&= classifications.length;
          if (searchName.length) value &&= classifications.some(({ name }) => name.toLowerCase().includes(searchName.toLowerCase()));
          if (searchCountries.length) value &&= searchCountries.includes(id);

          return value;
        });
    } else {
      data = list
        .filter(({ name, countries }) => {
          let value = true;

          if (showWithCountries) value &&= countries.length;
          if (searchName.length) value &&= name.toLowerCase().includes(searchName.toLowerCase());
          if (searchCountries.length) value &&= searchCountries.every(val => countries.includes(val));

          return value;
        });
    }

    return data;
  };

  const onCloseModal = () => {
    setShowModalAdd(false);
    setShowModalUpdate(false);
    setClassification(null);
    setCountry(null);
  };

  return (
    <>
      <Breadcrumb
        routes={[
          { title: 'Dashboard', href: `${config.rootRoute}/` },
          { title: 'Wizards', href: `${config.rootRoute}/wizards` },
          { title: 'Classifications' },
        ]}
      />

      <Row gutter={24}>
        <Col xl={6} lg={6} md={24} sm={24} xs={24}>
          <Card title='Classifications' style={{ marginBottom: 24 }}>
            <Tooltip title={!accessEdit && 'No permissions'}>
              <Button
                icon='plus'
                size='large'
                type='primary'
                className={styles.btn}
                onClick={() => setShowModalAdd(true)}
                disabled={!accessEdit}
              >
                Add New Classification
              </Button>
            </Tooltip>
          </Card>

          <Card
            title='Filters'
            style={{ marginBottom: 24 }}
            bodyStyle={{ display: 'flex', flexDirection: 'column' }}
          >
            <Select
              value={searchCountries}
              size='large'
              mode='multiple'
              style={{ marginBottom: 24 }}
              placeholder='Select country'
              onChange={value => setSearchCountries(value)}
              optionFilterProp='children'
              filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
            >
              {allCountriesWithEU.map(({ alpha3code, name }) =>
                <Option key={alpha3code} value={alpha3code}>
                  {name}
                </Option>
              )}
            </Select>

            <Input
              size='large'
              style={{ marginBottom: 24 }}
              placeholder='Search by classification name'
              onChange={e => setSearchName(e.target.value)}
            />

            <div>
              <Switch checked={groupByCountry} onChange={() => setGroupByCountry(!groupByCountry)} />
              <span style={{ margin: 10 }}>Group By Country</span>
            </div>

            {!groupByCountry && (
              <div style={{ marginTop: 16 }}>
                <Switch checked={showWithCountries} onChange={() => setShowWithCountries(!showWithCountries)} />
                <span style={{ marginLeft: 10 }}>With Countries</span>
              </div>
            )}

            {groupByCountry && (
              <div style={{ marginTop: 16 }}>
                <Switch checked={showWithClassification} onChange={() => setShowWithClassification(!showWithClassification)} />
                <span style={{ marginLeft: 10 }}>With Classification</span>
              </div>
            )}
          </Card>
        </Col>

        <Col xl={18} lg={18} md={24} sm={24} xs={24}>
          <Card>
            <Table
              rowKey={groupByCountry ? 'id' : '_id'}
              bordered
              loading={loading}
              columns={getColumns()}
              dataSource={getData()}
            />
          </Card>
        </Col>
      </Row>

      {showModalAdd && <Add show={showModalAdd} classification={classification} onClose={() => onCloseModal()} />}
      {showModalUpdate && <Update show={showModalUpdate} list={list} country={country} onClose={() => onCloseModal()} />}
    </>
  );
};

export default connect(({ wizards, account }) => ({
  list: wizards.classifications,
  loading: wizards.loading,
  permissions: account.adminPermissions
}))(WizardClass);
