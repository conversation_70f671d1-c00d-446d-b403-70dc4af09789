import React from 'react';
import { Modal } from 'antd';
import { allCountriesWithEUObj } from '../../../../utils/countries';
import Form from './Form';

/**
 * Update Classifications Modal
 * @param {boolean} show
 * @param {Object} country
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ show, country, onClose, ...props }) => (
  <Modal
    title={`Update Classifications for ${allCountriesWithEUObj[country.id]}`}
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={() => onClose()}
    onClose={() => onClose()}
    width={600}
    zIndex={999}
  >
    <Form onClose={onClose} country={country} {...props} />
  </Modal>
);
