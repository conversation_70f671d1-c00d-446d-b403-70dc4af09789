import React, { useState } from 'react';
import { Button, Form, Select } from 'antd';
import actions from '../../../../actions';

const { Item } = Form;
const { Option } = Select;

const formItemLayout = {
  labelCol: { xs: { span: 6 }, },
  wrapperCol: { xs: { span: 14 }, },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Update Classifications for country
 * @param {Object} country
 * @param {Object[]} list
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ country, list, onClose }) => {
  const { id, classifications } = country || {};
  const [newClassifications, setNewClassifications] = useState(classifications || []);

  const reset = () => {
    setNewClassifications([]);
    onClose();
  };

  const onUpdate = () => {
    if (id) actions.wizards.updateClassifications(id, { classificationIds: newClassifications });

    reset();
  };

  return (
    <>
      <Item label='Classifications' {...formItemLayout}>
        <Select
          value={newClassifications}
          mode='multiple'
          style={{ width: '100%' }}
          placeholder='Select classifications'
          onChange={value => setNewClassifications(value)}
          optionFilterProp='children'
          filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {list.map(({ _id, name }) =>
            <Option key={_id} value={_id}>
              {name}
            </Option>
          )}
        </Select>
      </Item>

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={() => reset()}>Cancel</Button>

        <Button
          style={{ marginLeft: 8 }}
          type='primary'
          onClick={() => onUpdate()}
        >
          Update
        </Button>
      </Item>
    </>
  );
};
