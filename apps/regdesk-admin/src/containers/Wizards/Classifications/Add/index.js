import React from 'react';
import { Modal } from 'antd';
import Form from './Form';

/**
 * Add/Update Classification Modal
 * @param {boolean} show
 * @param {?Object} classification
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ show, classification, onClose, ...props }) => (
  <Modal
    title={classification ? 'Update Classification' : 'Create New Classification'}
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={() => onClose()}
    onClose={() => onClose()}
    width={600}
    zIndex={999}
  >
    <Form onClose={onClose} classification={classification} {...props} />
  </Modal>
);
