import React, { useState } from 'react';
import { Button, Form, Input, Select } from 'antd';
import { allCountriesWithEU } from '../../../../utils/countries';
import actions from '../../../../actions';

const { Item } = Form;
const { Option } = Select;

const formItemLayout = {
  labelCol: { xs: { span: 6 }, },
  wrapperCol: { xs: { span: 14 }, },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Add/Update Classification
 * @param {?Object} classification
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ classification, onClose }) => {
  const { _id: id, name, countries } = classification || {};
  const [newName, setNewName] = useState(name || '');
  const [newCountries, setNewCountries] = useState(countries || []);

  const reset = () => {
    setNewName('');
    setNewCountries([]);
    onClose();
  };

  const onAdd = () => {
    const newClassification = { name: newName, countries: newCountries };

    if (id) actions.wizards.updateClassification(id, newClassification);
    else actions.wizards.addClassification(newClassification);

    reset();
  };

  return (
    <>
      <Item required label='Name' {...formItemLayout}>
        <Input
          autoFocus
          placeholder='Enter name'
          value={newName}
          onChange={e => setNewName(e.target.value)}
        />
      </Item>

      <Item label='Countries' {...formItemLayout}>
        <Select
          value={newCountries}
          mode='multiple'
          style={{ width: '100%' }}
          placeholder='Select country'
          onChange={value => setNewCountries(value)}
          optionFilterProp='children'
          filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {allCountriesWithEU.map(({ alpha3code, name: countryName }) =>
            <Option key={alpha3code} value={alpha3code}>
              {countryName}
            </Option>
          )}
        </Select>
      </Item>

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={() => reset()}>Cancel</Button>

        <Button
          style={{ marginLeft: 8 }}
          type='primary'
          disabled={!newName}
          onClick={() => onAdd()}
        >
          {id ? 'Update' : 'Add'}
        </Button>
      </Item>
    </>
  );
};
