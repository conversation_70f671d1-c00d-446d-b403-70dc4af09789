import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { Button, Table, Card, Input, Tooltip, Select, Menu, Dropdown, Icon, Popconfirm, Checkbox } from 'antd';
import Breadcrumb from '../../../components/Breadcrumb';
import { tagScopes, systemTagFileTypes } from '../utils';
import WizardList from '../WizardList';
import Update from './Update';
import Add from './Add';
import actions from '../../../actions';
import styles from '../index.less';

const { Option } = Select;
const { Item } = Menu;

/**
 * IDs List
 * @param {Object[]} list
 * @param {Object[]} tags
 * @param {boolean} loading
 * @param {Object} pagination
 * @param {string[]} permissions
 * @returns {JSX.Element}
 */
const WizardIDs = ({ list, tags, loading, pagination, permissions }) => {
  const [identifier, setIdentifier] = useState(null);
  const [showModalAdd, setShowModalAdd] = useState(false);
  const [showModalUpdate, setShowModalUpdate] = useState(false);
  const [showModalWizardList, setShowModalWizardList] = useState(false);
  const [wizardData, setWizardData] = useState({ list: [], filtersData: {} });
  const [needUpdate, setNeedUpdate] = useState(false);

  const accessEdit = permissions.includes('accessWizardUpdateID');

  useEffect(() => {
    actions.wizards.getIdentifiers();
    actions.wizards.getAllTags();

    return () => actions.wizards.change({ pagination: {} });
  }, []);

  const onOpenWizardList = item => {
    const { _id } = item;

    setIdentifier(item);
    setShowModalWizardList(true);

    actions.wizards
      .getWizardData(_id)
      .then(({ list: wizardList, filtersData }) => setWizardData({ list: wizardList, filtersData }));
  };

  const onTableChange = (newPagination, newFilters) => actions.wizards.getIdentifiers({ pagination: newPagination, filters: { ...newFilters, needUpdate } });

  const getColumns = () => ([
    {
      title: 'ID',
      dataIndex: 'name',
      width: 250,
      filterDropdown: ({ selectedKeys, setSelectedKeys, confirm, clearFilters }) => (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', padding: 8 }}>
          <Input
            showSearch
            value={selectedKeys[0]}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
            placeholder='Search ID'
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />

          <Button
            size='small'
            style={{ width: 90 }}
            onClick={() => { clearFilters(); confirm(); setSelectedKeys([]); }}
          >
            Reset
          </Button>
        </div>
      )
    },
    {
      title: 'Tag',
      dataIndex: 'systemTagName',
      filterDropdown: ({ selectedKeys, setSelectedKeys, confirm, clearFilters }) => (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', padding: 8 }}>
          <Select
            showSearch
            value={selectedKeys[0]}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
            placeholder='Search tag'
            onChange={name => {
              setSelectedKeys(name ? [name] : []);
              confirm();
            }}
            filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          >
            {tags.map(({ _id, name }) => (
              <Option key={_id} value={_id}>
                {name}
              </Option>
            ))}
          </Select>

          <Button
            size='small'
            style={{ width: 90 }}
            onClick={() => { clearFilters(); confirm(); setSelectedKeys([]); }}
          >
            Reset
          </Button>
        </div>
      )
    },
    {
      title: 'Tag Description',
      dataIndex: 'systemTagDescription',
      filterDropdown: ({ selectedKeys, setSelectedKeys, confirm, clearFilters }) => (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', padding: 8 }}>
          <Input
            showSearch
            value={selectedKeys[0]}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
            placeholder='Search tag description'
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />

          <Button
            size='small'
            style={{ width: 90 }}
            onClick={() => { clearFilters(); confirm(); setSelectedKeys([]); }}
          >
            Reset
          </Button>
        </div>
      )
    },
    {
      title: 'Upload Option',
      dataIndex: 'uploadEnabled',
      filters: [{ text: 'Yes', value: true }, { text: 'No', value: false }].map(({ text, value }) => ({ text, value })),
      filterMultiple: false,
      render: upload => upload ? 'Yes' : 'No'
    },
    {
      title: 'Scope',
      dataIndex: 'systemTagScope',
      filters: tagScopes.map(value => ({ text: value, value })),
    },
    {
      title: 'File Type',
      dataIndex: 'systemTagFileType',
      filters: systemTagFileTypes.map(value => ({ text: value, value })),
    },
    {
      title: 'ID Association',
      width: 110,
      render: item => item.isUsed && <Button onClick={() => onOpenWizardList(item)}>Wizard List</Button>
    },

    {
      title: 'Actions',
      width: 100,
      render: item => {
        const { _id, disabled, isUsed, needUpdate: needUpdateParam } = item;

        const menu = (
          <Menu>
            <Item onClick={() => { setShowModalUpdate(true); setIdentifier(item); }}>Edit</Item>

            <Item disabled={isUsed}>
              <Tooltip title={isUsed && 'ID used in Wizard Templates!'}>
                <Popconfirm
                  okText='Yes'
                  cancelText='No'
                  placement='topLeft'
                  title={`Are you sure you would like to ${disabled ? 'enable' : 'disable'} this ID?`}
                  onConfirm={() => actions.wizards.disableIdentifier(_id)}
                  disabled={isUsed}
                >
                  <a className={isUsed ? styles.disabled : ''}>{disabled ? 'Enable' : 'Disable'}</a>
                </Popconfirm>
              </Tooltip>
            </Item>

            <Item disabled={isUsed}>
              <Tooltip title={isUsed && 'ID used in Wizard Templates!'}>
                <Popconfirm
                  okText='Yes'
                  cancelText='No'
                  placement='topLeft'
                  title='Are you sure you would like to remove this ID?'
                  onConfirm={() => actions.wizards.deleteIdentifier(_id)}
                  disabled={isUsed}
                >
                  <a className={isUsed ? styles.disabled : ''}>Delete</a>
                </Popconfirm>
              </Tooltip>
            </Item>
          </Menu>
        );

        const warning = needUpdateParam && (
          <Tooltip title='Need to add a tag'>
            <Icon type='exclamation-circle' theme='filled' style={{ color: '#FF5722', marginLeft: 6 }} />
          </Tooltip>
        );

        if (accessEdit) {
          return (
            <>
              <Dropdown overlay={menu}>
                <a className='ant-dropdown-link'>
                  Actions <Icon type='down' />
                </a>
              </Dropdown>

              {warning}
            </>
          );
        }

        return warning;
      }
    },
  ]);

  const onCloseModal = () => {
    setShowModalAdd(false);
    setShowModalUpdate(false);
    setShowModalWizardList(false);
    setIdentifier(null);
  };

  return (
    <>
      <Breadcrumb
        routes={[
          { title: 'Dashboard', href: `${config.rootRoute}/` },
          { title: 'Wizards', href: `${config.rootRoute}/wizards` },
          { title: 'IDs' },
        ]}
      />

      <Card>
        <div className={styles.btnWrapper}>
          <Checkbox onChange={e => { actions.wizards.getIdentifiers({ filters: { needUpdate: e.target.checked } }); setNeedUpdate(e.target.checked); }}>
            Need Update
          </Checkbox>

          <Tooltip title={!accessEdit && 'No permissions'}>
            <Button
              icon='plus'
              type='primary'
              onClick={() => setShowModalAdd(true)}
              disabled={!accessEdit}
            >
              Add New ID
            </Button>
          </Tooltip>
        </div>

        <Table
          rowKey='_id'
          size='middle'
          bordered
          loading={loading}
          columns={getColumns()}
          dataSource={list}
          pagination={{
            defaultPageSize: 10,
            size: 'Pagination',
            ...pagination,
          }}
          onChange={onTableChange}
        />
      </Card>

      {showModalAdd && <Add show={showModalAdd} onClose={onCloseModal} />}
      {showModalUpdate && <Update show={showModalUpdate} identifier={identifier} tags={tags} onClose={onCloseModal} />}
      {showModalWizardList && <WizardList show={showModalWizardList} loading={loading} type='wizardId' list={wizardData.list} filtersData={wizardData.filtersData} onClose={onCloseModal} />}
    </>
  );
};

export default connect(({ wizards, account }) => ({
  list: wizards.identifiers,
  tags: wizards.tags,
  loading: wizards.loading,
  pagination: wizards.pagination,
  permissions: account.adminPermissions
}))(WizardIDs);
