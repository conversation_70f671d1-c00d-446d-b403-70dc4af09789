import React, { useState } from 'react';
import { Button, Form, Select, Input, Checkbox } from 'antd';
import actions from '../../../../actions';

const { Item } = Form;
const { Option } = Select;

const formItemLayout = {
  labelCol: { xs: { span: 6 }, },
  wrapperCol: { xs: { span: 14 }, },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Update Wizard ID
 * @param {Object} identifier
 * @param {Object[]} tags
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ identifier, tags, onClose }) => {
  const { _id: id, name, systemTag, uploadEnabled } = identifier || {};
  const { _id: systemTagId } = systemTag || {};
  const [newIDName, setNewIDName] = useState(name || '');
  const [newTag, setNewTag] = useState(systemTagId || null);
  const [newUploadEnabled, setNewUploadEnabled] = useState(!!uploadEnabled);

  const onReset = () => {
    setNewIDName('');
    setNewTag('');
    onClose();
  };

  const onUpdate = () => {
    if (id) actions.wizards.updateIdentifier(id, { name: newIDName, tagId: newTag, uploadEnabled: newUploadEnabled });
    onReset();
  };

  return (
    <>
      <Item required label='ID name' {...formItemLayout}>
        <Input
          autoFocus
          value={newIDName}
          placeholder='Enter name'
          onChange={e => setNewIDName(e.target.value)}
        />
      </Item>

      <Item label='Tag' {...formItemLayout}>
        <Select
          showSearch
          value={newTag || undefined}
          optionLabelProp='label'
          placeholder='Select tag'
          style={{ width: '100%' }}
          onChange={tagId => setNewTag(tagId)}
          filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {tags?.map(({ _id: tagId, name: tagName }) => (
            <Option key={tagId} value={tagId} label={tagName}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                {tagName}
              </div>
            </Option>
          ))}
        </Select>
      </Item>

      <Item {...formTailLayout}>
        <Checkbox checked={newUploadEnabled} onChange={(e) => setNewUploadEnabled(e.target.checked)}>Upload Option</Checkbox>
      </Item>

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={onReset}>Cancel</Button>

        <Button
          style={{ marginLeft: 8 }}
          type='primary'
          disabled={!newIDName}
          onClick={onUpdate}
        >
          Update
        </Button>
      </Item>
    </>
  );
};
