import React from 'react';
import { Modal } from 'antd';
import Form from './Form';

/**
 * Update Wizard ID Modal
 * @param {boolean} show
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ show, onClose, ...props }) => (
  <Modal
    title='Update ID'
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={() => onClose()}
    onClose={() => onClose()}
    width={600}
    zIndex={999}
  >
    <Form onClose={onClose} {...props} />
  </Modal>
);
