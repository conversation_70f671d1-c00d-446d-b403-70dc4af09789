import React, { useState } from 'react';
import { Button, Form, Input } from 'antd';
import actions from '../../../../actions';

const { Item } = Form;

const formItemLayout = {
  labelCol: { xs: { span: 6 }, },
  wrapperCol: { xs: { span: 14 }, },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Add Wizard ID
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ onClose }) => {
  const [newIDName, setNewIDName] = useState('');

  const reset = () => {
    setNewIDName('');
    onClose();
  };

  const onAdd = () => {
    actions.wizards.addIdentifier({ name: newIDName });
    reset();
  };

  return (
    <>
      <Item required label='ID name' {...formItemLayout}>
        <Input
          autoFocus
          value={newIDName}
          placeholder='Enter name'
          onChange={e => setNewIDName(e.target.value)}
        />
      </Item>

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={() => reset()}>Cancel</Button>

        <Button
          style={{ marginLeft: 8 }}
          type='primary'
          disabled={!newIDName}
          onClick={() => onAdd()}
        >
          Add
        </Button>
      </Item>
    </>
  );
};
