import React from 'react';
import { Modal } from 'antd';
import Form from './Form';

/**
 * Add Wizard ID Modal
 * @param {boolean} show
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ show, onClose }) => (
  <Modal
    title='Create New ID'
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={() => onClose()}
    onClose={() => onClose()}
    width={600}
    zIndex={999}
  >
    <Form onClose={onClose} />
  </Modal>
);
