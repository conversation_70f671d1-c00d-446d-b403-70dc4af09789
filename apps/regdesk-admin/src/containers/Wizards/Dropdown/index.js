import React from 'react';
import { Dropdown, Icon, Menu, Tag } from 'antd';
import { allCountriesWithEUObj } from '../../../utils/countries';
import styles from '../index.less';

/**
 * Dropdown
 * @param {?Object[]} data
 * @param {?string} title
 * @param {boolean} showCountries
 * @returns {JSX.Element}
 */
export default ({ data = [], title = '', showCountries }) => {
  if (data && data.length === 0) return <Tag>No data</Tag>;
  if (data.length === 1) return <Tag color='#108ee9'>{showCountries ? allCountriesWithEUObj[data[0]] : data[0]?.name || data[0]}</Tag>;

  const menu = (
    <Menu className={styles.list}>
      {data.map((item) => (
        <Menu.Item key={item._id || item}>
          {showCountries ? allCountriesWithEUObj[item] : item?.name || item}
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <Dropdown overlay={menu} className={styles.dropdown}>
      <a>{title} <Icon type='down' /></a>
    </Dropdown>
  );
};

