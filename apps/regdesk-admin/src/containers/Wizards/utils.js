export const optionsAreUnique = ({ type, values = [], options = [] }) => {
  if (type === 'select' && options.length > 1) {
    const uniqueLabels = new Set(options.filter(({ label }) => label).map(({ label }) => label));
    const uniqueValues = new Set(options.filter(({ value }) => value).map(({ value }) => value));

    if (uniqueLabels.size !== options.filter(({ label }) => label).length || uniqueValues.size !== options.length) return false;
  } else if (values.length > 1) {
    const uniqueValues = new Set(values.filter(value => value));

    if (uniqueValues.size !== values.length) return false;
  }

  return true;
};

export const optionsAreNotEmpty = ({ type, values = [], options = [] }) => {
  if (type === 'select') return !options.length || !options.find(({ value }) => !value);

  return !values.length || !values.some(value => !value);
};

export const linksAreNotEmpty = (links = []) => !links.length || !links.find(({ text, link }) => !text || !link);

export const idHasNotSystemTag = ({ id, type, uploadEnabled }, ids = []) => (
  (type === 'upload' || type === 'markdown-editor' && uploadEnabled) && !ids.find(({ _id }) => _id === id)?.systemTag
);

export const questionHasNotDefaultTitle = ({ type, customTitlesEnabled, defaultTitle }) => (
  ['multi-field', 'products-data'].includes(type) && customTitlesEnabled && !defaultTitle
);

export const customTitlesAreNotEmpty = (titles = []) => !titles.length || !titles.some(title => !title);

export const questionsScopes = ['Global', 'Country', 'Product', 'N/A'];

export const questionsTypes = [
  { label: 'Mark down editor', value: 'markdown-editor', extra: ['id', 'uploadEnabled', 'example', 'scope'] },
  { label: 'Upload', value: 'upload', extra: ['id', 'multiple', 'example'] },
  { label: 'Checkbox', value: 'checkbox', extra: ['id', 'column', 'values', 'scope'] },
  { label: 'Date picker', value: 'date-picker', defaultFormat: 'YYYY-MM-DD', extra: ['id', 'format', 'showTime', 'scope'] },
  { label: 'Input', value: 'input', extra: ['id', 'placeholder', 'example', 'scope'] },
  { label: 'Input number', value: 'input-number', extra: ['id', 'placeholder', 'scope'] },
  { label: 'Select', value: 'select', extra: ['id', 'placeholder', 'multiple', 'options', 'example', 'scope'] },
  { label: 'Radio', value: 'radio', extra: ['id', 'column', 'values', 'scope'] },
  { label: 'Month picker', value: 'month-picker', defaultFormat: 'YYYY-MM', extra: ['id', 'format', 'showTime', 'scope'] },
  { label: 'Range picker', value: 'range-picker', defaultFormat: 'YYYY-MM-DD HH:mm:ss', extra: ['id', 'format', 'showTime', 'scope'] },
  { label: 'Switch', value: 'switch', extra: ['id', 'scope'] },
  { label: 'Text area', value: 'textarea', extra: ['id', 'placeholder', 'example', 'scope'] },
  { label: 'Time picker', value: 'time-picker', defaultFormat: 'HH:mm:ss', extra: ['id', 'format', 'scope'] },
  { label: 'Multi Field', value: 'multi-field', extra: ['id', 'newLineEnabled', 'multiFields', 'example', 'scope'] },
  { label: 'Products Data', value: 'products-data', extra: ['newLineEnabled', 'productsData', 'example'] },
  { label: 'Standards', value: 'standards', extra: ['id', 'newLineEnabled', 'selectAllStandards', 'standards', 'example', 'scope'] },
  { label: 'Classifications', value: 'classifications', extra: ['id', 'newLineEnabled', 'selectAllClassifications', 'classifications', 'example', 'scope'] },
];

export const autofillQuestionsTypes = [
  { label: 'Multi Field', value: 'multi-field' },
];

export const multiFieldTypes = [
  { label: 'Input', value: 'input' },
  { label: 'Text area', value: 'textarea' },
  { label: 'Select', value: 'select', extra: ['options', 'multiple'] },
  { label: 'Country List', value: 'country-list', extra: ['multiple'] },
  { label: 'Checkbox', value: 'checkbox', extra: ['values', 'column'] },
  { label: 'Radio', value: 'radio', extra: ['values', 'column'] },
  { label: 'Date picker', value: 'date-picker' },
];

export const productsDataSources = [
  { label: 'Product/Family Name', value: 'productName' },
  { label: 'Product/Family Description', value: 'productDescription' },
  { label: 'Products', value: 'products', canHide: true },
  { label: 'SKU Number', value: 'skuNumber', canHide: true },
  { label: 'Product Version', value: 'productVersion', canHide: true },
  { label: 'Product GMDN Code', value: 'productGmdn', canHide: true },
  { label: 'Product Manufacturer', value: 'productManufacturer', canHide: true },
  { label: 'Product EMDN-Code', value: 'productEmdn', canHide: true },
  { label: 'Product Category', value: 'productCategory', canHide: true },
  { label: 'Product Classification', value: 'productClassification', canHide: true },
];

export const dataForStandards = [
  {
    key: 'organization',
    title: 'Organization',
    type: 'select',
    placeholder: 'Organization',
    hint: 'Standards Developing Organization',
  },
  {
    key: 'designationNumber',
    title: 'Designation Number and Edition/Date',
    type: 'select',
    placeholder: 'Designation Number and Edition/Date',
    hint: 'Designation number and edition date',
  },
  {
    key: 'recognitionNumber',
    title: 'Recognition Number',
    type: 'select',
    placeholder: 'Recognition #',
    hint: 'FDA Recognition number, if available',
  },
  {
    key: 'title',
    title: 'Title',
    type: 'select',
    placeholder: 'Title',
    hint: 'Title',
  },
  {
    key: 'usage',
    title: 'Are you using this standard for general use, or are you declaring conformity to it?',
    type: 'select',
    placeholder: '',
    hint: 'Are you using this standard for general use, or are you declaring conformity to it?',
  },
];

export const dataForClassifications = [
  {
    key: 'medicalSpecialty',
    title: 'Medical Specialty',
    type: 'select',
    hint: 'Medical Specialty',
  },
  {
    key: 'regulation',
    title: 'Regulation',
    type: 'select',
    hint: 'Regulation',
  },
  {
    key: 'productCode',
    title: 'Product Code',
    type: 'select',
    hint: 'Product Code',
  },
  {
    key: 'associatedProductCode',
    title: 'Associated Product Code(s)',
    type: 'input',
    hint: 'Associated Product Code(s)',
  },
];

export const usages = ['General Use', 'Declaration of Conformity', 'Declaration of Conformity with ASCA'];

export const fileTypes = [
  { label: 'Any', value: 'any' },
  { label: 'Image', value: 'image' },
  { label: 'Pdf', value: 'pdf' },
];

export const getConditionName = name => name.split('_').map(word => word[0].toUpperCase() + word.substring(1).toLowerCase()).join(' ');

export const tagScopes = ['Product', 'Country', 'N/A'];

export const systemTagFileTypes = [ 'PDF', 'IMAGE', 'DOCX', 'XLSX', 'PPTX', 'VIDEO', 'AUDIO', 'COMPRESSED', 'ANY'];
