import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { Button, Table, Card, Input, Divider, Popconfirm, Tooltip } from 'antd';
import WizardList from '../../WizardList';
import Add from './Add';
import actions from '../../../../actions';
import styles from '../../index.less';

/**
 * Classification Questions List
 * @param {Object[]} list
 * @param {boolean} loading
 * @returns {JSX.Element}
 */
const WizardClassificationQuestions = ({ list, loading, pagination }) => {
  const [classification, setClassification] = useState(null);
  const [showModalAdd, setShowModalAdd] = useState(false);
  const [showModalWizardList, setShowModalWizardList] = useState(false);
  const [wizardData, setWizardData] = useState({ list: [], filtersData: {} });

  useEffect(() => {
    actions.wizards.getClassificationQuestions();

    return () => actions.wizards.change({ pagination: {} });
  }, []);

  const onOpenWizardList = item => {
    const { _id } = item;

    setClassification(item);
    setShowModalWizardList(true);

    actions.wizards
      .getWizardDataClassificationQuestions(_id)
      .then(({ list: wizardList, filtersData }) => setWizardData({ list: wizardList, filtersData }));
  };

  const onTableChange = (newPagination, newFilters) => actions.wizards.getClassificationQuestions({ pagination: newPagination, filters: newFilters });

  const getColumns = () => {
    const filterDropdown = ({ selectedKeys, setSelectedKeys, confirm, clearFilters }) => (
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', padding: 8 }}>
        <Input
          showSearch
          value={selectedKeys[0]}
          style={{ width: 188, marginBottom: 8, display: 'block' }}
          placeholder='Search...'
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => confirm()}
        />

        <Button
          size='small'
          style={{ width: 90 }}
          onClick={() => { clearFilters(); confirm(); setSelectedKeys([]); }}
        >
          Reset
        </Button>
      </div>
    );

    return ([
      {
        title: 'Medical Specialty',
        dataIndex: 'medicalSpecialty',
        filterDropdown: props => filterDropdown(props)
      },
      {
        title: 'Regulation',
        dataIndex: 'regulation',
        filterDropdown: props => filterDropdown(props)
      },
      {
        title: 'Product Code',
        dataIndex: 'productCode',
        filterDropdown: props => filterDropdown(props)
      },
      {
        title: 'Wizard Association',
        width: 110,
        render: item => item.isUsed && <Button onClick={() => onOpenWizardList(item)}>Wizard List</Button>
      },
      {
        title: 'Actions',
        width: 130,
        render: item => {
          const { _id, isUsed } = item;

          return (
            <>
              <a onClick={() => { setShowModalAdd(true); setClassification(item); }}>
                Update
              </a>

              <Divider type='vertical' />

              <Tooltip title={isUsed && 'Classification used in Wizard Templates!'} placement='topLeft'>
                <Popconfirm
                  okText='Yes'
                  cancelText='No'
                  disabled={isUsed}
                  placement='topLeft'
                  title='Are you sure you would like to remove this Classification?'
                  onConfirm={() => actions.wizards.deleteClassificationQuestion(_id)}
                >
                  <a className={isUsed ? styles.disabled : ''}>Remove</a>
                </Popconfirm>
              </Tooltip>
            </>
          );
        },
      },
    ]);
  };

  const onCloseModal = () => {
    setShowModalAdd(false);
    setShowModalWizardList(false);
    setClassification(null);
  };

  return (
    <>
      <Card>
        <div className={styles.btnWrapper} style={{ justifyContent: 'space-between' }}>
          <div style={{ fontWeight: 500 }}>This table is applicable to CLASSIFICATION question type only</div>

          <Button
            icon='plus'
            type='primary'
            onClick={() => setShowModalAdd(true)}
          >
            Add New Classification
          </Button>
        </div>

        <Table
          rowKey='_id'
          size='middle'
          bordered
          loading={loading}
          columns={getColumns()}
          dataSource={list}
          pagination={{
            defaultPageSize: 10,
            size: 'Pagination',
            ...pagination,
          }}
          onChange={onTableChange}
        />
      </Card>

      {showModalAdd && <Add show={showModalAdd} classification={classification} onClose={() => onCloseModal()} />}
      {showModalWizardList && <WizardList show={showModalWizardList} loading={loading} type='classification' list={wizardData.list} filtersData={wizardData.filtersData} onClose={onCloseModal} />}
    </>
  );
};

export default connect(({ wizards }) => ({
  list: wizards.classificationQuestions,
  loading: wizards.loading,
  pagination: wizards.pagination,
}))(WizardClassificationQuestions);
