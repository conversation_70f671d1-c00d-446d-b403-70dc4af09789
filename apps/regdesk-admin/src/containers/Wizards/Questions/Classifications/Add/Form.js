import React, { useReducer, useEffect } from 'react';
import { Button, Form, Input, Select } from 'antd';
import actions from '../../../../../actions';
import api from '../../../../../utils/api';

const { Item } = Form;
const { Option } = Select;

const formItemLayout = {
  labelCol: { xs: { span: 6 } },
  wrapperCol: { xs: { span: 14 } },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

const initialState = {
  loading: true,
  specialties: [],
  newMedicalSpecialty: '',
  medicalSpecialty: '',
  regulation: '',
  productCode: '',
};

const init = state => ({ ...initialState, ...state });

const reducer = (state, action) => {
  const { type, payload } = action || {};

  switch (type) {
    case 'startLoading':
      return { ...state, loading: true };
    case 'endLoading':
      return { ...state, loading: false };
    case 'change':
      return { ...state, ...payload };
    case 'reset':
      return { ...initialState };
    default:
      return { ...state };
  }
};

/**
 * Add/Update Classification Question
 * @param {?Object} classification
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ classification, onClose }) => {
  const { _id: id } = classification || {};
  const [state, dispatch] = useReducer(reducer, classification || {}, init);
  const { loading, specialties, newMedicalSpecialty, medicalSpecialty, regulation, productCode } = state;

  useEffect(() => {
    dispatch({ type: 'startLoading' });

    api.wizards
      .getClassificationQuestionsData({ fieldName: 'medicalSpecialty' })
      .then(({ data }) => dispatch({ type: 'change', payload: { specialties: data } }))
      .finally(() => dispatch({ type: 'endLoading' }));

  }, []);

  const reset = () => {
    dispatch({ type: 'reset' });
    onClose();
  };

  const onAdd = () => {
    const newClassification = { medicalSpecialty, regulation, productCode };

    if (id) actions.wizards.updateClassificationQuestion(id, newClassification);
    else actions.wizards.addClassificationQuestion(newClassification);

    reset();
  };

  const onAddSpecialty = open => {
    if (!open && newMedicalSpecialty && !specialties.includes(newMedicalSpecialty)) {
      dispatch({
        type: 'change',
        payload: {
          specialties: [...specialties, newMedicalSpecialty],
          medicalSpecialty: newMedicalSpecialty,
          newMedicalSpecialty: ''
        },
      });
    }
  };

  return (
    <>
      <Item required label='Medical Specialty' {...formItemLayout}>
        <Select
          showSearch
          loading={loading}
          disabled={loading}
          style={{ width: '100%' }}
          placeholder='Medical Specialty...'
          value={medicalSpecialty || undefined}
          onDropdownVisibleChange={onAddSpecialty}
          onChange={value => dispatch({ type: 'change', payload: { medicalSpecialty: value } })}
          onSearch={value => dispatch({ type: 'change', payload: { newMedicalSpecialty: value } })}
          filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {specialties.map(item => <Option key={item} value={item}>{item}</Option>)}
        </Select>
      </Item>

      <Item required label='Regulation' {...formItemLayout}>
        <Input
          value={regulation}
          placeholder='Regulation...'
          onChange={e => dispatch({ type: 'change', payload: { regulation: e.target.value } })}
        />
      </Item>

      <Item required label='Product Code' {...formItemLayout}>
        <Input
          value={productCode}
          placeholder='Product Code...'
          onChange={e => dispatch({ type: 'change', payload: { productCode: e.target.value } })}
        />
      </Item>

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={() => reset()}>Cancel</Button>

        <Button
          type='primary'
          style={{ marginLeft: 8 }}
          disabled={loading || !medicalSpecialty || !regulation || !productCode}
          onClick={() => onAdd()}
        >
          {id ? 'Update' : 'Add'}
        </Button>
      </Item>
    </>
  );
};
