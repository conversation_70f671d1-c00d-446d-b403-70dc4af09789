import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { Row, Col, Button, Table, Card, Input, Divider, Popconfirm, Tooltip } from 'antd';
import { autofillQuestionsTypes } from '../../utils';
import Add from './Add';
import actions from '../../../../actions';
import styles from '../../index.less';

/**
 * Autofill Questions List
 * @param {string[]} ids
 * @param {Object[]} list
 * @param {boolean} loading
 * @returns {JSX.Element}
 */
const WizardQuestions = ({ ids, list, loading }) => {
  const [searchId, setSearchId] = useState('');
  const [question, setQuestion] = useState(null);
  const [showModalAdd, setShowModalAdd] = useState(false);

  useEffect(() => {
    actions.wizards.getIds();
    actions.wizards.getAutofillQuestions();
  }, []);

  const getColumns = () => {
    const columns = [
      {
        title: 'Type',
        dataIndex: 'type',
        render: type => autofillQuestionsTypes.find(({ value }) => value === type)?.label
      },
      {
        title: 'ID',
        dataIndex: 'id.name',
      },
      {
        title: 'Actions',
        width: 200,
        render: item => {
          const { _id, isUsed } = item;

          return (
            <>
              <a onClick={() => { setShowModalAdd(true); setQuestion(item); }}>
                Update
              </a>

              <Divider type='vertical' />

              <Tooltip title={isUsed && 'ID used in Wizard Templates!'}>
                <Popconfirm
                  okText='Yes'
                  cancelText='No'
                  placement='topLeft'
                  title='Are you sure you would like to remove this Question?'
                  onConfirm={() => actions.wizards.deleteAutofillQuestion(_id)}
                  disabled={isUsed}
                >
                  <a className={isUsed ? styles.disabled : ''}>Remove</a>
                </Popconfirm>
              </Tooltip>
            </>
          );
        },
      },
    ];

    return columns;
  };

  const onCloseModal = () => {
    setShowModalAdd(false);
    setQuestion(null);
  };

  return (
    <>
      <Row gutter={24}>
        <Col xl={6} lg={6} md={24} sm={24} xs={24}>
          <Card title='Questions' style={{ marginBottom: 24 }}>
            <Button
              icon='plus'
              size='large'
              type='primary'
              className={styles.btn}
              onClick={() => setShowModalAdd(true)}
            >
              Add New Question
            </Button>
          </Card>

          <Card
            title='Filters'
            style={{ marginBottom: 24 }}
            bodyStyle={{ display: 'flex', flexDirection: 'column' }}
          >
            <Input
              size='large'
              placeholder='Search by ID'
              onChange={e => setSearchId(e.target.value)}
            />
          </Card>
        </Col>

        <Col xl={18} lg={18} md={24} sm={24} xs={24}>
          <Card>
            <Table
              rowKey='_id'
              bordered
              loading={loading}
              columns={getColumns()}
              title={() => <div style={{ fontWeight: 500 }}>This table is applicable to MULTI FIELD question type only</div>}
              dataSource={list.filter(({ id: { name } }) => name.toLowerCase().includes(searchId.toLowerCase()))}
            />
          </Card>
        </Col>
      </Row>

      {showModalAdd && <Add show={showModalAdd} ids={ids} question={question} onClose={() => onCloseModal()} />}
    </>
  );
};

export default connect(({ wizards }) => ({
  ids: wizards.ids,
  list: wizards.autofillQuestions,
  loading: wizards.loading,
}))(WizardQuestions);
