import React, { useState } from 'react';
import { Button, Form, Tooltip, Select } from 'antd';
import { autofillQuestionsTypes } from '../../../utils';
import MultiFields from '../../../Edit/MultiFields';
import actions from '../../../../../actions';
import styles from '../../../index.less';

const { Item } = Form;
const { Option } = Select;

const formItemLayout = {
  labelCol: { xs: { span: 4 } },
  wrapperCol: { xs: { span: 16 } },
};

const formTailLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16, offset: 4 },
};

/**
 * Add/Update Autofill Question
 * @param {string[]} ids
 * @param {?Object} question
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ ids, question, onClose }) => {
  const { _id: questionId, id: identifier, type, multiFields } = question || {};
  const { _id: id } = identifier || {};
  const [newId, setNewId] = useState(id || '');
  const [newType, setNewType] = useState(type || '');
  const [newMultiFields, setNewMultiFields] = useState(multiFields || []);

  const reset = () => {
    setNewId('');
    setNewType('');
    setNewMultiFields([]);
    onClose();
  };

  const onAdd = () => {
    const newQuestion = { id: newId, type: newType, multiFields: newMultiFields };

    if (questionId) actions.wizards.updateAutofillQuestion(questionId, newQuestion);
    else actions.wizards.addAutofillQuestion(newQuestion);

    reset();
  };

  return (
    <>
      <Item required label='Type' {...formItemLayout}>
        <Select
          showSearch
          disabled={!!questionId}
          value={newType || undefined}
          placeholder='Select type'
          style={{ width: '100%' }}
          onChange={value => setNewType(value)}
          filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {autofillQuestionsTypes.map(({ value, label }) => (
            <Option key={value} value={value}>
              {label}
            </Option>
          ))}
        </Select>
      </Item>

      {newType && (
        <>
          <Item required label='ID' {...formItemLayout}>
            <Select
              showSearch
              disabled={!!questionId}
              value={newId || undefined}
              optionLabelProp='label'
              placeholder='Select ID'
              style={{ width: '100%' }}
              onChange={value => setNewId(value)}
              filterOption={(input, option) => option.props.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
            >
              {ids.map(({ _id, name, systemTag, isUsed }) => (
                <Option key={_id} value={_id} label={name} disabled={!systemTag || isUsed}>
                  <Tooltip title={!systemTag && 'Tag is missing' || isUsed && 'ID already associated with a Question!'} placement='topLeft'>
                    <div className={(!systemTag || isUsed) && styles.disabled}>{name}</div>
                  </Tooltip>
                </Option>
              ))}
            </Select>
          </Item>

          <Item>
            <MultiFields
              multiFields={newMultiFields}
              formLayout={formItemLayout}
              onChange={fields => setNewMultiFields(fields)}
            />
          </Item>
        </>
      )}

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={() => reset()}>Cancel</Button>

        <Button
          style={{ marginLeft: 8 }}
          type='primary'
          disabled={!newId || !newType}
          onClick={() => onAdd()}
        >
          {id ? 'Update' : 'Add'}
        </Button>
      </Item>
    </>
  );
};
