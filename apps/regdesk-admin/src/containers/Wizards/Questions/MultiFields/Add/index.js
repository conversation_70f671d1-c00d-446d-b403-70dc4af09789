import React from 'react';
import { Modal } from 'antd';
import Form from './Form';

/**
 * Add/Update Autofill Question Modal
 * @param {boolean} show
 * @param {?Object} question
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ show, question, onClose, ...props }) => (
  <Modal
    title={question ? 'Update Question' : 'Create New Question'}
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={() => onClose()}
    onClose={() => onClose()}
    width={800}
    zIndex={999}
  >
    <Form onClose={onClose} question={question} {...props} />
  </Modal>
);
