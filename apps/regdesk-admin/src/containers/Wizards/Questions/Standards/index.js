import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { Button, Table, Card, Input, Divider, Popconfirm, Tooltip } from 'antd';
import WizardList from '../../WizardList';
import Add from './Add';
import actions from '../../../../actions';
import styles from '../../index.less';

/**
 * Standard Questions List
 * @param {Object[]} list
 * @param {boolean} loading
 * @returns {JSX.Element}
 */
const WizardStandardQuestions = ({ list, loading, pagination }) => {
  const [standard, setStandard] = useState(null);
  const [showModalAdd, setShowModalAdd] = useState(false);
  const [showModalWizardList, setShowModalWizardList] = useState(false);
  const [wizardData, setWizardData] = useState({ list: [], filtersData: {} });

  useEffect(() => {
    actions.wizards.getStandardQuestions();

    return () => actions.wizards.change({ pagination: {} });
  }, []);

  const onOpenWizardList = item => {
    const { _id } = item;

    setStandard(item);
    setShowModalWizardList(true);

    actions.wizards
      .getWizardDataStandardQuestions(_id)
      .then(({ list: wizardList, filtersData }) => setWizardData({ list: wizardList, filtersData }));
  };

  const onTableChange = (newPagination, newFilters) => actions.wizards.getStandardQuestions({ pagination: newPagination, filters: newFilters });

  const getColumns = () => {
    const filterDropdown = ({ selectedKeys, setSelectedKeys, confirm, clearFilters }) => (
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', padding: 8 }}>
        <Input
          showSearch
          value={selectedKeys[0]}
          style={{ width: 188, marginBottom: 8, display: 'block' }}
          placeholder='Search...'
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => confirm()}
        />

        <Button
          size='small'
          style={{ width: 90 }}
          onClick={() => { clearFilters(); confirm(); setSelectedKeys([]); }}
        >
          Reset
        </Button>
      </div>
    );

    return ([
      {
        title: 'Standard Developing Organization',
        dataIndex: 'organization',
        filterDropdown: props => filterDropdown(props)
      },
      {
        title: 'Standard Designation Number/Date',
        dataIndex: 'designationNumber',
        filterDropdown: props => filterDropdown(props)
      },
      {
        title: 'Recognition Number',
        dataIndex: 'recognitionNumber',
        filterDropdown: props => filterDropdown(props)
      },
      {
        title: 'Title of Standard',
        dataIndex: 'title',
        filterDropdown: props => filterDropdown(props)
      },
      {
        title: 'Wizard Association',
        width: 110,
        render: item => item.isUsed && <Button onClick={() => onOpenWizardList(item)}>Wizard List</Button>
      },
      {
        title: 'Actions',
        width: 130,
        render: item => {
          const { _id, isUsed } = item;

          return (
            <>
              <a onClick={() => { setShowModalAdd(true); setStandard(item); }}>
                Update
              </a>

              <Divider type='vertical' />

              <Tooltip title={isUsed && 'Standard used in Wizard Templates!'} placement='topLeft'>
                <Popconfirm
                  okText='Yes'
                  cancelText='No'
                  disabled={isUsed}
                  placement='topLeft'
                  title='Are you sure you would like to remove this Standard?'
                  onConfirm={() => actions.wizards.deleteStandardQuestion(_id)}
                >
                  <a className={isUsed ? styles.disabled : ''}>Remove</a>
                </Popconfirm>
              </Tooltip>
            </>
          );
        },
      },
    ]);
  };

  const onCloseModal = () => {
    setShowModalAdd(false);
    setShowModalWizardList(false);
    setStandard(null);
  };

  return (
    <>
      <Card>
        <div className={styles.btnWrapper} style={{ justifyContent: 'space-between' }}>
          <div style={{ fontWeight: 500 }}>This table is applicable to STANDARDS question type only</div>

          <Button
            icon='plus'
            type='primary'
            onClick={() => setShowModalAdd(true)}
          >
            Add New Standard
          </Button>
        </div>

        <Table
          rowKey='_id'
          size='middle'
          bordered
          loading={loading}
          columns={getColumns()}
          dataSource={list}
          pagination={{
            defaultPageSize: 10,
            size: 'Pagination',
            ...pagination,
          }}
          onChange={onTableChange}
        />
      </Card>

      {showModalAdd && <Add show={showModalAdd} standard={standard} onClose={() => onCloseModal()} />}
      {showModalWizardList && <WizardList show={showModalWizardList} loading={loading} type='standard' list={wizardData.list} filtersData={wizardData.filtersData} onClose={onCloseModal} />}
    </>
  );
};

export default connect(({ wizards }) => ({
  list: wizards.standardQuestions,
  loading: wizards.loading,
  pagination: wizards.pagination,
}))(WizardStandardQuestions);
