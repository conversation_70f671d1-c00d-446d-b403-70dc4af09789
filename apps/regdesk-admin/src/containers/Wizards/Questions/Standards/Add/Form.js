import React, { useReducer, useEffect } from 'react';
import { Button, Form, Input, Select } from 'antd';
import actions from '../../../../../actions';
import api from '../../../../../utils/api';

const { Item } = Form;
const { Option } = Select;

const formItemLayout = {
  labelCol: { xs: { span: 6 } },
  wrapperCol: { xs: { span: 14 } },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

const initialState = {
  loading: true,
  organizations: [],
  newOrganization: '',
  organization: '',
  designationNumber: '',
  recognitionNumber: '',
  title: '',
};

const init = state => ({ ...initialState, ...state });

const reducer = (state, action) => {
  const { type, payload } = action || {};

  switch (type) {
    case 'startLoading':
      return { ...state, loading: true };
    case 'endLoading':
      return { ...state, loading: false };
    case 'change':
      return { ...state, ...payload };
    case 'reset':
      return { ...initialState };
    default:
      return { ...state };
  }
};

/**
 * Add/Update Standard Question
 * @param {?Object} standard
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ standard, onClose }) => {
  const { _id: id } = standard || {};
  const [state, dispatch] = useReducer(reducer, standard || {}, init);
  const { loading, organizations, newOrganization, organization, designationNumber, recognitionNumber, title } = state;

  useEffect(() => {
    dispatch({ type: 'startLoading' });

    api.wizards
      .getStandardQuestionsData({ fieldName: 'organization' })
      .then(({ data }) => dispatch({ type: 'change', payload: { organizations: data } }))
      .finally(() => dispatch({ type: 'endLoading' }));

  }, []);

  const reset = () => {
    dispatch({ type: 'reset' });
    onClose();
  };

  const onAdd = () => {
    const newStandard = { organization, designationNumber, recognitionNumber, title };

    if (id) actions.wizards.updateStandardQuestion(id, newStandard);
    else actions.wizards.addStandardQuestion(newStandard);

    reset();
  };

  const onAddOrganization = open => {
    if (!open && newOrganization && !organizations.includes(newOrganization)) {
      dispatch({
        type: 'change',
        payload: {
          organizations: [...organizations, newOrganization],
          organization: newOrganization,
          newOrganization: ''
        },
      });
    }
  };

  return (
    <>
      <Item required label='Organization' {...formItemLayout}>
        <Select
          showSearch
          loading={loading}
          disabled={loading}
          style={{ width: '100%' }}
          placeholder='Organization...'
          value={organization || undefined}
          onDropdownVisibleChange={onAddOrganization}
          onChange={value => dispatch({ type: 'change', payload: { organization: value } })}
          onSearch={value => dispatch({ type: 'change', payload: { newOrganization: value } })}
          filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {organizations.map(item => <Option key={item} value={item}>{item}</Option>)}
        </Select>
      </Item>

      <Item required label='Designation Number/Date' {...formItemLayout}>
        <Input
          value={designationNumber}
          placeholder='Designation Number/Date...'
          onChange={e => dispatch({ type: 'change', payload: { designationNumber: e.target.value } })}
        />
      </Item>

      <Item required label='Recognition Number' {...formItemLayout}>
        <Input
          value={recognitionNumber}
          placeholder='Recognition Number...'
          onChange={e => dispatch({ type: 'change', payload: { recognitionNumber: e.target.value } })}
        />
      </Item>

      <Item required label='Title' {...formItemLayout}>
        <Input
          value={title}
          placeholder='Title...'
          onChange={e => dispatch({ type: 'change', payload: { title: e.target.value } })}
        />
      </Item>

      <Item style={{ marginBottom: 0 }} {...formTailLayout}>
        <Button onClick={() => reset()}>Cancel</Button>

        <Button
          style={{ marginLeft: 8 }}
          type='primary'
          disabled={loading || !organization || !designationNumber || !recognitionNumber || !title}
          onClick={() => onAdd()}
        >
          {id ? 'Update' : 'Add'}
        </Button>
      </Item>
    </>
  );
};
