import React from 'react';
import { Modal } from 'antd';
import Form from './Form';

/**
 * Add/Update Standard Question Modal
 * @param {boolean} show
 * @param {?Object} standard
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ show, standard, onClose, ...props }) => (
  <Modal
    title={standard ? 'Update Standard' : 'Create New Standard'}
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={() => onClose()}
    onClose={() => onClose()}
    width={850}
    zIndex={999}
  >
    <Form onClose={onClose} standard={standard} {...props} />
  </Modal>
);
