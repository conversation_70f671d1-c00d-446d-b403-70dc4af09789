import React, { useState } from 'react';
import config from 'config';
import { Tabs } from 'antd';
import Breadcrumb from '../../../components/Breadcrumb';
import MultiFields from './MultiFields';
import Standards from './Standards';
import Classifications from './Classifications';

const { TabPane } = Tabs;

/**
 * Questions Tabs
 * @returns {JSX.Element}
 */
export default () => {
  const [activeTab, setActiveTab] = useState('1');

  return (
    <>
      <Breadcrumb
        routes={[
          { title: 'Dashboard', href: `${config.rootRoute}/` },
          { title: 'Wizards', href: `${config.rootRoute}/wizards` },
          { title: 'Questions' },
        ]}
      />

      <Tabs
        defaultActiveKey='1'
        activeKey={activeTab}
        style={{ width: '100%' }}
        onChange={tab => setActiveTab(tab)}
      >
        <TabPane tab='Multi Field' key='1'>
          <MultiFields />
        </TabPane>

        <TabPane tab='Standards' key='2'>
          <Standards />
        </TabPane>

        <TabPane tab='Classification' key='3'>
          <Classifications />
        </TabPane>
      </Tabs>
    </>
  );
};
