import React, { Component } from 'react';
import { Divider, Form, Button, Input, message, Select, Row, Modal, Card, Table, Popconfirm } from 'antd';
import config from 'config';
import { connect } from 'react-redux';
import Breadcrumb from '../../components/Breadcrumb';
import actions from '../../actions';

const FormItem = Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const moduleOptions = [
  { value: 'tracking', label: 'tracking' },
  { value: 'dct', label: 'dct' },
  { value: 'standard', label: 'standard' },
  { value: 'product', label: 'product' },
];

@connect(({ grafanaDashboards }) => ({
  list: grafanaDashboards.list,
}))
class GrafanaDashboards extends Component {
  constructor(props) {
    super(props);

    this.state = {
      visible: false,
      addType: '',
      editData: {
        createdAt: '',
        url: '',
        module: '',
        report: ''
      }
    };
  }

  componentDidMount() {
    actions.grafanaDashboards.get();
  }

  editGrafanaDashboard = (record) => this.setState({ addType: 'edit', visible: true, editData: record });

  openAddGrafanaDashboard = () => this.setState({ addType: 'add', visible: true });

  getColumns = () => [
    {
      title: 'Module',
      dataIndex: 'module',
      render: (module) => <span>{module}</span>,
    },
    {
      title: 'Url',
      dataIndex: 'url',
      render: (url) => <span>{url}</span>,
    },
    {
      title: 'Report',
      dataIndex: 'report',
      render: (report) => <span>{report}</span>,
    },
    {
      title: 'Actions',
      dataIndex: 'key',
      render: (code, item) => {
        const { _id } = item;

        return (
          <div>
            <a onClick={() => this.editGrafanaDashboard(item)}>
              Edit
            </a>

            <Divider type='vertical' />

            <Popconfirm
              title='Remove this Grafana Dashboard?'
              onConfirm={() => this.remove(_id)}
              onCancel={() => {}}
              okText='Yes'
              cancelText='No'
            >
              <a>
                Delete
              </a>
            </Popconfirm>
          </div>
        );
      },
    },
  ]

  handleOk = () => {
    const { addType, editData } = this.state;
    const { url, module, report } = editData;

    if (!url) return message.error('Url should not be empty!');
    if (!module) return message.error('Module should not be empty!');
    if (!report) return message.error('Report should not be empty!');

    const action = addType === 'edit' ? 'update' : 'add';
    const successMessage = addType === 'edit' ? 'Update success' : 'Add success!';

    return actions.grafanaDashboards[action](editData)
      .then(() => {
        message.success(successMessage);
        actions.grafanaDashboards.get();

        this.setState({ 
          visible: false,
          editData: {
            createdAt: '',
            url: '',
            module: '',
            report: ''
          }
        });
      });
  };

  handleChange = (e) => {
    const { name, value } = e.target;
    
    this.setState(prevState => ({
      editData: {
        ...prevState.editData,
        [name]: value
      }
    }));
  };

  handleChangeModuleOption = (e) => {
    this.setState((prevState) => ({ editData: { ...prevState.editData, module: e } }));
  };

  remove = (id) => {
    actions.grafanaDashboards.delete(id)
      .then(() => {
        message.success('Successfully removed');
        actions.grafanaDashboards.get();
      })
      .catch(() => {
        message.error('Dashboard is not removed');
      });
  }

  render() {
    const { list, pagination } = this.props;
    const { visible, editData: { report, module, url } } = this.state;

    return (
      <div>
        <Breadcrumb routes={[{ title: 'Dashboard', href: `${config.rootRoute}/` }, { title: 'Grafana Dashboards' }]} />
        
        <Card bordered={false}>
          <Row>
            <Button
              style={{ marginLeft: '20px' }}
              type='primary'
              onClick={this.openAddGrafanaDashboard}
            >
              Add
            </Button>
          </Row>
        </Card>
        
        <Card>
          <Table
            bordered
            columns={this.getColumns()}
            dataSource={list}
            rowKey='_id'
            pagination={{
              defaultPageSize: 10,
              size: 'Pagination',
              ...pagination,
            }}
          />
        </Card>
        
        {visible && (
          <Modal
            visible
            title='Edit Detail'
            onCancel={() => this.setState({ visible: false })}
            onOk={this.handleOk}
            width='600px'
          >
            <Form>
              <FormItem label='Report' {...formItemLayout}>
                <Input
                  placeholder='Report'
                  name='report'
                  value={report || ''}
                  onChange={this.handleChange}
                />
              </FormItem>
              
              <FormItem label='Module' {...formItemLayout}>
                <Select
                  value={module}
                  optionLabelProp='label'
                  placeholder='Select Module'
                  style={{ width: '100%' }}
                  onChange={(e) => this.handleChangeModuleOption(e)}
                >
                  {moduleOptions.map((item) => (
                    <Select.Option key={item.value} label={item.label} value={item.value}>{item.value}</Select.Option>
                  ))}
                </Select>
              </FormItem>
              
              <FormItem label='Url' {...formItemLayout}>
                <TextArea
                  name='url'
                  value={url}
                  autoSize={{ minRows: 2 }}
                  onChange={this.handleChange}
                />
              </FormItem>
            </Form>
          </Modal>
        )}
      </div>
    );
  }
}

export default GrafanaDashboards;