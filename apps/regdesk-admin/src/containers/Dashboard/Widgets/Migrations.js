import React, { useEffect } from 'react';
import { Icon, Card, Spin, Tag, Input, Table, Button } from 'antd';
import { connect } from 'react-redux';
import moment from 'moment';
import actions from '../../../actions';
import styles from '../index.less';

const statuses = {
  completed: { name: 'Completed', color: 'green' },
  executing: { name: 'Executing', color: 'orange' },
  error: { name: 'Error', color: 'volcano' },
};

const Migrations = ({ list, pagination, loading }) => {
  useEffect(() => {
    actions.widget.getMigrations();
  }, []);

  const getColumns = () => ([
    {
      title: 'Name',
      dataIndex: 'name',
      filterDropdown: ({ selectedKeys, setSelectedKeys, confirm, clearFilters }) => (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', padding: 8 }}>
          <Input
            showSearch
            value={selectedKeys[0]}
            style={{ width: 240, marginBottom: 8, display: 'block' }}
            placeholder='Search by name'
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
          />

          <Button
            size='small'
            style={{ width: 90 }}
            onClick={() => { clearFilters(); confirm(); setSelectedKeys([]); }}
          >
            Reset
          </Button>
        </div>
      )
    },
    {
      title: 'Date ',
      dataIndex: 'createdAt',
      width: 250,
      render: date => moment(date).getUTC()
    },
    {
      title: 'Status',
      dataIndex: 'status',
      filters: Object.entries(statuses).map(([value, { name: text }]) => ({ text, value })),
      width: 250,
      render: status => {
        if (!status) return <Tag>Not set</Tag>;

        const { name, color } = statuses[status] || {};

        return <Tag color={color}>{name}</Tag>;
      }
    },
  ]);

  const onTableChange = (newPagination, newFilters) => actions.widget.getMigrations({ pagination: newPagination, filters: newFilters });

  return (
    <Spin spinning={loading}>
      <Card className={styles.widget}>
        <div className={styles.title}>
          <Icon type='database' className={styles.icon} />Migrations
        </div>

        <Table
          rowKey='_id'
          size='middle'
          bordered
          loading={loading}
          columns={getColumns()}
          dataSource={list}
          pagination={{
            defaultPageSize: 10,
            size: 'Pagination',
            ...pagination,
          }}
          onChange={onTableChange}
        />
      </Card>
    </Spin>
  );
};

export default connect(({ widget }) => ({
  list: widget.migrations,
  filters: widget.filtersMigrations,
  pagination: widget.paginationMigrations,
  loading: widget.loadingMigrations
}))(Migrations);
