import React, { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Spin, Tag, Empty, <PERSON><PERSON>, <PERSON><PERSON>, Too<PERSON><PERSON>, Popconfirm } from 'antd';
import { connect } from 'react-redux';
import moment from 'moment';
import actions from '../../../actions';
import styles from '../index.less';

const timersInfo = {
  trackingNeedMoreInfo: {
    title: 'Tracking - Need More Info - Deadline Date',
    description: 'Starts from 21 days left. Recurrent notifications every 7 days. Notification on the deadline date.'
  },
  trackingTimer: {
    title: 'Tracking - Approved - Expiry Date',
    description: 'Starts from 364 days. Recurrent notifications every 7/14/28 days. Notification on the expiry date.',
  },
  deadlineTimer: {
    title: 'Checklist - Requirement - Deadline Date',
    description: `
      Starts from 182 days. Recurrent notifications every 7 days. Notification for the last 3, 2 and 1 days left.
      Notification on the deadline date. Notification for 1 and 4 days passed the deadline date.
    `,
  },
  checklistExpiryDate: {
    title: 'Checklist - Requirement - Expiry Date',
    description: 'Notification for 90/60/30/7 days left. Notification on the expiry date.'
  },
  dmsDocExpTimer: {
    title: 'DMS - Document - Expiry Date',
    description: 'Notification for 364/180/90 days left. Notification on the expiry date.',
  },
  assessmentTimer: {
    title: 'STA/GUI - Assessment - Deadline Date',
    description: 'Starts from 364 days. Recurrent notifications every 7/14/28 days. Notification on the expiry date.',
  },
  standardsTimer: {
    title: 'Standards/Guidance - Updates',
    description: 'Blinking function applied to standards/guidances added to watchlist/custom sections on a monthly basis.',
  },
  legislationAssessmentTimer: {
    title: 'Legislation - Assessment - Deadline Date',
    description: 'Starts from 364 days. Recurrent notifications every 7/14/28 days. Notification on the expiry date.',
  },
  legislationTimer: {
    title: 'Legislation - Updates',
    description: 'Blinking function applied to legislations added to watchlist/custom sections on a monthly basis.',
  },
  applicationUsageTimer: {
    title: 'Application - Packages',
    description: `
      Sends banner message when there are 90 days left of the package expiry date and when the package expires.
      When there are 10 applications left in the package or when package consumption happens
    `,
  },
  updatesTimer: {
    title: 'Banner Messages',
    description: 'Sends banner messages based on a "Start Date", and removes banner messages based on an "End Date"',
  },
  notifAutoClear: {
    title: 'Auto Clear Notifications',
    description: `
      Clears web push notifications after 1/2/3/5/7/10/14/20/21/30/90/365 have passed from the moment changes are saved.
      Clear process is recurrent.
    `,
  },
  systemTagsTimer: {
    title: 'DMS - System Tags',
    description: 'System Tags in DMS sync',
  },
  ecExpTimer: {
    title: 'Bausch - Products - EC Cert Exp Date',
    description: 'Starts from 364 days. Recurrent notifications every 7/14/28 days. Notification on the expiry date.',
  },
  maExpTimer: {
    title: 'Bausch - Products - MA Exp Date',
    description: 'Starts from 364 days. Recurrent notifications every 7/14/28 days. Notification on the expiry date.',
  }
};

const Timer = ({ timer, loading }) => {
  const { data = {}, updatedAt, performedBy } = timer || {};
  const duration = moment.duration(moment(new Date()).diff(updatedAt)).asHours();

  useEffect(() => {
    actions.widget.getTimer();
  }, []);

  let cmp = <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
  const alertMessage = (
    <div>
      <span>Last Update: {moment(updatedAt).getFullUTC()}</span>
      <br />
      <span>Performed By: {performedBy?.email || 'Daily Timer'}</span>
    </div>
  );

  if (timer) {
    cmp = (
      <>
        <Alert message={alertMessage} type={duration > 24 ? 'error' : 'success'} />

        <ul className={styles.list}>
          {Object.keys(timersInfo)
            .filter(key => Object.keys(data).includes(key))
            .map(key => {
              const { title, description } = timersInfo[key];
              const timerStatus = data[key];

              return (
                <li key={key} className={styles.item}>
                  <span className={styles.tag}>
                    {timerStatus === 'success' ? <Tag color='green'>Success</Tag> : <Tag color='volcano'>Error</Tag>}
                  </span>

                  <Tooltip title={description}>
                    <span className={styles.name}>{title}</span>
                  </Tooltip>
                </li>
              );
            })}
        </ul>
      </>
    );
  }

  return (
    <Spin spinning={loading}>
      <Card className={styles.widget}>
        <span className={styles.timer}>
          <div className={styles.title}>
            <Icon type='bell' className={styles.icon} />Timer
          </div>

          {cmp}

          <Popconfirm
            okText='Yes'
            cancelText='No'
            disabled={loading}
            title='Manually Run Timer?'
            onConfirm={() => actions.widget.runTimer({ isManualExecution: true })}
          >
            <Tooltip title='Timer Complete'>
              <Button type='primary' className={styles.btn}>
                Run Timer
              </Button>
            </Tooltip>
          </Popconfirm>
        </span>
      </Card>
    </Spin>
  );
};

export default connect(({ widget }) => ({
  timer: widget.timer,
  loading: widget.loadingTimer
}))(Timer);
