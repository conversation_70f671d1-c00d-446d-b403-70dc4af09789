import React from 'react';
import { Row, Col } from 'antd';
import Timer from './Timer';
import Services from './Services';
import Migrations from './Migrations';
import styles from '../index.less';

export default ({ permissions }) => (
  <div className={styles.container}>
    <Row gutter={24}>
      {(permissions.includes('widget-services') || permissions.includes('widget-timer')) && (
        <Col xl={7} lg={7} md={12} sm={24} xs={24}>
          {permissions.includes('widget-services') && <Services />}
          {permissions.includes('widget-timer') && <Timer />}
        </Col>
      )}

      {permissions.includes('widget-migrations') && (
        <Col xl={17} lg={17} md={24} sm={24} xs={24}>
          <Migrations />
        </Col>
      )}
    </Row>
  </div>
);
