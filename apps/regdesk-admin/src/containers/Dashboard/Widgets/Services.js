import React, { useEffect } from 'react';
import { Icon, Card, Spin, Tag, Empty, Tooltip } from 'antd';
import { connect } from 'react-redux';
import actions from '../../../actions';
import styles from '../index.less';

const Services = ({ list, loading }) => {
  useEffect(() => {
    actions.widget.getServices();
  }, []);

  let cmp = <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;

  if (list.length) {
    cmp = (
      <div className={styles.services}>
        <ul className={styles.list}>
          {list.map(({ msg, info, success }) => (
            <li key={msg} className={styles.item}>
              <span className={styles.name}>
                {msg}

                {info && (
                  <Tooltip overlayStyle={{ maxWidth: 'min-content' }} title={info.split(',').map(text => <div style={{ whiteSpace: 'nowrap' }}>{text}</div>)}>
                    <Icon type='info-circle' />
                  </Tooltip>
                )}
              </span>

              <span className={styles.tag}>
                {success ? <Tag color='green'>Ok</Tag> : <Tag color='volcano'>Failed</Tag>}
              </span>
            </li>
          ))}
        </ul>
      </div>
    );
  }

  return (
    <Spin spinning={loading}>
      <Card className={styles.widget}>
        <div className={styles.title}>
          <Icon type='setting' className={styles.icon} />Services
        </div>

        {cmp}
      </Card>
    </Spin>
  );
};

export default connect(({ widget }) => ({
  list: widget.services,
  loading: widget.loadingServices
}))(Services);
