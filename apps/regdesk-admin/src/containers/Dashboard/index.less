.containerEmpty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: calc(100vh - 181px);

  .icon {
    font-size: 46px;
    color: rgb(132, 132, 132);
  }

  h2 {
    margin-top: 15px;
    color: rgb(132, 132, 132);
  }
}

.container {
  min-height: calc(100vh - 181px);

  .widget {
    margin-bottom: 24px;

    .title {
      color: rgba(0, 0, 0, 0.6);
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 24px;

      .icon {
        margin-right: 8px;
      }
    }

    .timer {
      .list {
        margin: 10px 0 0;
        padding: 0;
        list-style: none;

        .item {
          padding-top: 10px;

          .tag {
            width: 60px;
            display: inline-block
          }

          .name {
            margin-left: 12px;
            font-weight: 500;
          }
        }
      }

      .btn {
        width: 100%;
        margin-top: 18px;
      }
    }

    .services {
      .list {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin: 10px 0 0;
        padding: 0;
        list-style: none;

        .item {
          display: flex;
          justify-content: space-between;
          gap: 10px;

          .tag {
            display: inline-block;

            :global {
              .ant-tag {
                margin: 0;
              }
            }
          }

          .name {
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }
      }
    }
  }
}
