import React from 'react';
import { Icon } from 'antd';
import { connect } from 'react-redux';
import Widgets from './Widgets';
import styles from './index.less';

const Dashboard = ({ permissions }) => {
  const existWidgets = permissions.some(permission => permission.includes('widget'));

  if (existWidgets) return <Widgets permissions={permissions} />;

  return (
    <div className={styles.containerEmpty}>
      <Icon type='deployment-unit' className={styles.icon} />
      <h2>Dashboard</h2>
    </div>
  );
};

export default connect(({ account }) => ({
  permissions: account.adminPermissions
}))(Dashboard);

