.notifications {
  .btnWrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .filters {
      display: flex;
      gap: 10px;

      .select {
        width: 250px;
      }

      .search {
        width: 300px;
      }

      .datePicker {
        width: 350px;
      }
    }
  }

  .table {
    .column {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 3px;
    }

    .iconSuccess {
      color: #87d068;
    }

    .iconError {
      color: #f50;
    }
  }
}

.form {
  .select {
    width: 100%;
  }

  .colors {
    display: flex;
    gap: 10px;
    padding-top: 7px;

    .color {
      border-radius: 6px;
      width: 25px;
      height: 25px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .red {
      border: 1px solid #FF4D4F;
      background-color: #FFF1F0;
    }

    .orange {
      border: 1px solid #FAAD14;
      background-color: #FFFBE6;
    }

    .green {
      border: 1px solid #52C41A;
      background-color: #F6FFED;
    }

    .blue {
      border: 1px solid #1890FF;
      background-color: #E6F7FF;
    }
  }

  .alert {
    display: flex;
    flex-wrap: wrap;

    .title {
      margin-right: 14px;
      font-weight: 700;
    }

    .country {
      margin-right: 8px;
    }

    .url {
      margin-left: 12px;
      font-weight: 600;

      .link {
        color: #1890ff;
      }
    }
  }

  .btns {
    margin-bottom: 0;

    :global {
      .ant-form-item-children {
        display: flex;
        gap: 8px;
      }
    }
  }
}
