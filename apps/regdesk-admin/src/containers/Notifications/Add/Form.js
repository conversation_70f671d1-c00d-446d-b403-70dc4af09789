import React, { useState } from 'react';
import moment from 'moment';
import classNames from 'classnames';
import { Link } from 'react-router-dom';
import { Button, Form, Input, Select, Switch, DatePicker, Alert } from 'antd';
import { FULL_DATE_FORMAT, FULL_TIME_FORMAT } from '../../../utils/date';
import { allCountriesWithEU } from '../../../utils/countries';
import CountryFlag from '../../../components/CountryFlag';
import ManualDatePicker from '../../../components/ManualDatePicker';
import actions from '../../../actions';
import styles from '../index.less';

const { Item } = Form;
const { Option } = Select;
const { TextArea } = Input;

const formItemLayout = {
  labelCol: { xs: { span: 6 }, },
  wrapperCol: { xs: { span: 14 }, },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Add/Update Notification
 * @param {boolean} isBanner
 * @param {Object} notification
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ isBanner, notification, onClose }) => {
  const { _id: id } = notification;
  const [createdAt, setCreatedAt] = useState(notification.createdAt || new Date());
  const [startDateAt, setStartDateAt] = useState(notification.startDateAt);
  const [endDateAt, setEndDateAt] = useState(notification.endDateAt);
  const [country, setCountry] = useState(notification.country);
  const [title, setTitle] = useState(notification.title);
  const [content, setContent] = useState(notification.content);
  const [url, setUrl] = useState(notification.url);
  const [publishedAt, setPublishedAt] = useState(notification.publishedAt);
  const [released, setReleased] = useState(notification.is_released === 1 || false);
  const [publishNow, setPublishNow] = useState(false);
  const [color, setColor] = useState(notification.color);

  const dateFormat = isBanner ? FULL_TIME_FORMAT : FULL_DATE_FORMAT;

  const colors = [
    { name: 'red', type: 'error' },
    { name: 'orange', type: 'warning' },
    { name: 'green', type: 'success' },
    { name: 'blue', type: 'info' }
  ];

  const onAdd = () => {
    const params = {
      isBanner,
      createdAt,
      startDateAt,
      endDateAt,
      title,
      content,
      country,
      url,
      publishedAt,
      released,
      publishNow,
      color
    };

    (id ? () => actions.notifications.update(id, params) : () => actions.notifications.add(params))();

    onClose();
  };

  return (
    <>
      <Item required label='Create Date' {...formItemLayout}>
        <ManualDatePicker
          allowClear={false}
          format={dateFormat}
          showTime={isBanner}
          className={styles.select}
          value={moment(createdAt)}
          onChange={date => setCreatedAt(date)}
        />
      </Item>

      {isBanner && (
        <>
          <Item label='Start Date' {...formItemLayout}>
            <DatePicker
              allowClear={false}
              format={FULL_DATE_FORMAT}
              className={styles.select}
              value={startDateAt ? moment(startDateAt) : null}
              onChange={date => setStartDateAt(date)}
            />
          </Item>

          <Item label='End Date' {...formItemLayout}>
            <DatePicker
              allowClear={false}
              format={FULL_DATE_FORMAT}
              className={styles.select}
              value={endDateAt ? moment(endDateAt) : null}
              onChange={date => setEndDateAt(date)}
            />
          </Item>
        </>
      )}

      <Item required label='Title' {...formItemLayout}>
        <Input
          value={title}
          placeholder='Enter title'
          onChange={e => setTitle(e.target.value)}
        />
      </Item>

      <Item required={!isBanner} label='Content' {...formItemLayout}>
        <TextArea
          value={content}
          rows={isBanner ? 2 : 4}
          placeholder='Enter content'
          onChange={e => setContent(e.target.value)}
        />
      </Item>

      <Item label='Country' {...formItemLayout}>
        <Select
          showSearch
          value={country}
          className={styles.select}
          placeholder='Select country'
          onChange={idc => setCountry(idc)}
          filterOption={(input, option) => option.props.children.toLowerCase().includes(input.toLowerCase())}
        >
          {allCountriesWithEU.map(({ alpha3code, name }) => (
            <Option key={alpha3code} value={alpha3code}>
              {name}
            </Option>
          ))}
        </Select>
      </Item>

      <Item label='Url' {...formItemLayout}>
        <Input
          value={url}
          placeholder='Enter url'
          onChange={e => setUrl(e.target.value)}
        />
      </Item>

      {!isBanner && (
        <Item label='Published Date' {...formItemLayout}>
          <ManualDatePicker
            format={FULL_DATE_FORMAT}
            placeholder='Select date'
            className={styles.select}
            value={publishedAt ? moment(publishedAt) : null}
            onChange={date => setPublishedAt(date)}
          />
        </Item>
      )}

      <Item label='Release' {...formItemLayout}>
        <Switch checked={released} onChange={() => setReleased(!released)} />
      </Item>

      {isBanner && (
        <>
          <Item label='Color' {...formItemLayout}>
            <div className={styles.colors}>
              {colors.map(({ name, type }) => <div className={classNames(styles.color, styles[name])} onClick={() => setColor(type)}>{color === type ? '✔️' : ''}</div>)}
            </div>
          </Item>

          {color && (
            <Item label='Preview' {...formItemLayout}>
              <Alert
                type={color}
                message={
                  <div className={styles.alert}>
                    <div className={styles.title}>{title || 'Sample text'}</div>
                    {country && <div className={styles.country}><CountryFlag countryId={country} /></div>}

                    <div>
                      {content || 'Sample content'}
                      {url && <span className={styles.url}><Link to={{ pathname: `${url}/` }} target='_blank'>{url}</Link></span>}
                    </div>
                  </div>
                }
              />
            </Item>
          )}
        </>
      )}

      {!id && !isBanner && (
        <Item label='Publish Now' {...formItemLayout}>
          <Switch disabled={!released} checked={publishNow} onChange={() => setPublishNow(!publishNow)} />
        </Item>
      )}

      <Item className={styles.btns} {...formTailLayout}>
        <Button onClick={() => onClose()}>Cancel</Button>
        <Button type='primary' disabled={!createdAt || !title || !isBanner && !content} onClick={() => onAdd()}>Save</Button>
      </Item>
    </>
  );
};
