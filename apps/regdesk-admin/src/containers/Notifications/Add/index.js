import React from 'react';
import { Modal } from 'antd';
import Form from './Form';
import styles from '../index.less';

/**
 * Add/Update Notification Modal
 * @param {?Object} notification
 * @param {boolean} show
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ notification, show, onClose, ...props }) => (
  <Modal
    title={`${notification ? 'Update' : 'Create New'} Notification`}
    className={styles.form}
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={onClose}
    onClose={onClose}
    width={1000}
    zIndex={999}
  >
    <Form notification={notification || {}} onClose={onClose} {...props} />
  </Modal>
);
