import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import moment from 'moment';
import { Button, Table, Card, Icon, Dropdown, Menu, Input, Tabs, Modal } from 'antd';
import Breadcrumb from '../../components/Breadcrumb';
import ManualDatePicker from '../../components/ManualDatePicker';
import Add from './Add';
import actions from '../../actions';
import styles from './index.less';

const { Item } = Menu;
const { Search } = Input;
const { TabPane } = Tabs;
const { confirm } = Modal;
const { RangePicker } = ManualDatePicker;

/**
 * Notifications List
 * @param {Object[]} list
 * @param {boolean} loading
 * @param {Object} pagination
 * @returns {JSX.Element}
 */
const Notifications = ({ list, loading, pagination }) => {
  const tabs = ['regular', 'banner'];
  const timerRef = useRef(null);
  const [tab, setTab] = useState(tabs[0]);
  const [showModal, setShowModal] = useState(false);
  const [notification, setNotification] = useState(null);
  const { total = 0 } = pagination;
  const isBanner = tab === 'banner';

  useEffect(() => {
    actions.notifications.get({ pagination: {}, filters: { isBanner } });
  }, [tab]);

  const getColumns = () => {
    const columns = [
      {
        title: 'Create date',
        dataIndex: 'createdAt',
        width: 120,
        render: date => moment(date).getUTC(),
      },
      {
        title: 'Title',
        dataIndex: 'title',
      },
      {
        title: 'Content',
        dataIndex: 'content',
      },
      {
        title: 'Released',
        dataIndex: 'is_released',
        width: 90,
        render: released => (
          <div className={styles.column}>
            {released ? <Icon className={styles.iconSuccess} type='check-circle' /> : <Icon className={styles.iconError} type='close-circle' />}
          </div>
        )
      },
      {
        title: 'Actions',
        width: 110,
        render: item => {
          const { _id, is_released: released } = item || {};

          const menu = (
            <Menu>
              <Item><a onClick={() => { setShowModal(true); setNotification(item); }}>Edit</a></Item>
              <Item><a onClick={() => actions.notifications.updateRelease(_id, released)}>{released ? 'Unrelease' : 'Release'}</a></Item>
              {!isBanner && released && <Item><a onClick={() => actions.notifications.push(_id)}>Push</a></Item>}
              <Item><a onClick={() => actions.notifications[!isBanner ? 'test' : 'testBanner'](_id)}>Test</a></Item>

              <Item>
                <a
                  onClick={() => {
                    confirm({
                      okText: 'Yes',
                      okType: 'danger',
                      cancelText: 'No',
                      title: 'Are you sure delete this Notification?',
                      content: 'Before you delete the Notification, please confirm that it\'s useless',
                      onOk: () => actions.notifications.delete(_id)
                    });
                  }}
                >
                  Delete
                </a>
              </Item>
            </Menu>
          );

          return (
            <Dropdown overlay={menu} className={styles.column}>
              <a className='ant-dropdown-link'>
                Actions <Icon type='down' />
              </a>
            </Dropdown>
          );
        }
      },
    ];

    if (isBanner) {
      columns.splice(1, 0, {
        title: 'Start date',
        dataIndex: 'startDateAt',
        width: 120,
        render: date => date && moment(date).getUTC(),
      });
    }

    return columns;
  };

  return (
    <>
      <Breadcrumb routes={[{ title: 'Dashboard', href: `${config.rootRoute}/` }, { title: 'Notification Center' }]} />

      <Card className={styles.notifications}>
        <Tabs activeKey={tab} type='card' onChange={key => setTab(key)}>
          {tabs.map(tabName => (
            <TabPane tab={tabName} key={tabName.toLowerCase()}>
              <div className={styles.btnWrapper}>
                <div className={styles.filters}>
                  <Search
                    allowClear
                    className={styles.search}
                    placeholder='Search by text...'
                    onChange={e => {
                      const search = e.target.value;

                      clearTimeout(timerRef.current);
                      timerRef.current = setTimeout(() => actions.notifications.filter({ search }), 600);
                    }}
                  />

                  <RangePicker className={styles.datePicker} onChange={dateRange => actions.notifications.filter({ dateRange })} />
                </div>

                <Button icon='plus' type='primary' onClick={() => setShowModal(true)}>
                  Create New Notification
                </Button>
              </div>

              <Table
                bordered
                rowKey='_id'
                size='middle'
                loading={loading}
                dataSource={list}
                columns={getColumns()}
                className={styles.table}
                pagination={{
                  showTotal: () => `Total ${total} items`,
                  defaultPageSize: 10,
                  size: 'Pagination',
                  ...pagination,
                }}
                onChange={newPagination => actions.notifications.get({ pagination: newPagination })}
              />
            </TabPane>
          ))}
        </Tabs>
      </Card>

      {showModal && <Add isBanner={isBanner} notification={notification} show={showModal} onClose={() => setShowModal(false)} />}
    </>
  );
};

export default connect(({ notifications }) => ({
  list: notifications.list,
  loading: notifications.loading,
  pagination: notifications.pagination,
}))(Notifications);
