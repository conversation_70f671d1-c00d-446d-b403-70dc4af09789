import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Table, Select, Row, Col, Card, Input, Icon } from 'antd';
import { Link } from 'react-router-dom';
import config from 'config';
import moment from 'moment';
import * as constants from '../../const';
import Breadcrumb from '../../components/Breadcrumb';
import actions from '../../actions';

@connect(({ logs }) => ({
  list: logs.list,
  loading: logs.loading,
  pagination: logs.pagination,
  filters: logs.filters,
  sorter: logs.sorter,
}))

export default class Logs extends PureComponent {
  componentDidMount() {
    this.load();
  }

  /**
   * Get columns for table
   * @returns {*[]}
   */
  getColumns = () => {
    const columns = [
      {
        title: 'Date',
        dataIndex: 'createdAt',
        sorter: true,
        sortOrder: this.props.sorter && this.props.sorter.columnKey === 'createdAt' && this.props.sorter.order,
        render: timestamp => moment(timestamp).getFullUTC(),
      },
      {
        title: 'User',
        dataIndex: 'name',
        sorter: true,
        sortOrder: this.props.sorter && this.props.sorter.columnKey === 'name' && this.props.sorter.order,
        render: (name, val) => <Link to={`${config.rootRoute}/users/item/${val.user}`}>{name}</Link>,
      },
      {
        title: 'Action',
        dataIndex: 'action',
      }
    ];

    return columns;
  };

  /**
   * Set filter
   * @param filters
   */
  setFilters = (filters = {}) => {
    this.load({ filters, pagination: { ...this.props.pagination, current: 1 } });
  };

  /**
   * Load list users
   * @param props
   */
  load = (props = {}) => {
    const { sorter, filters, pagination } = this.props;

    actions.log.load({ sorter, filters, pagination, ...props });
  };

  /**
   * Handle table change
   * @param pagination
   * @param filters
   * @param sorter
   */
  handleTableChange = (pagination, filters, sorter) => {
    this.load({ pagination, sorter });
  };

  render() {
    const {
      list = [],
      loading,
      pagination = {},
      filters = {},
    } = this.props;

    return (
      <div>
        <Breadcrumb routes={[{ title: 'Dashboard', href: `${config.rootRoute}/` }, { title: 'Logs' }]} />

        <Row gutter={24}>
          <Col xl={6} lg={6} md={24} sm={24} xs={24}>
            <Card
              title='Filters'
              style={{ marginBottom: 24 }}
              bodyStyle={{ display: 'flex', flexDirection: 'column' }}
              bordered={false}
              extra={Object.keys(filters).length !== 0 && (
                <a onClick={() => this.setFilters()}>Clean all</a>
              )}
            >
              <Input
                key='name'
                size='large'
                addonBefore={<Icon type='search' />}
                placeholder='By Name'
                value={filters.name || ''}
                onChange={e => {
                  const val = e.target.value;

                  if (!val) {
                    const { name, ...newFilters } = filters;

                    actions.log.change({ filters: { ...newFilters } });
                  } else {
                    actions.log.change({ filters: { ...filters, name: val } });
                  }

                  clearTimeout(this.timerName);
                  this.timerName = setTimeout(() => this.load(), 600);
                }}
              />

              <Select
                key='action'
                value={filters.type}
                size='large'
                style={{ marginTop: 24 }}
                placeholder='By action'
                onChange={type => this.setFilters({ ...filters, type })}
              >
                {constants.ACTIONS.map(({ key, name }) => (
                  <Select.Option key={key} value={key}>{name}</Select.Option>
                ))}
              </Select>
            </Card>
          </Col>

          <Col xl={18} lg={18} md={24} sm={24} xs={24}>
            <Card title='Logs' style={{ marginBottom: 24 }} bordered={false}>
              <Table
                loading={loading}
                rowKey='_id'
                dataSource={list}
                columns={this.getColumns()}
                pagination={{
                  showSizeChanger: true,
                  ...pagination,
                }}
                onChange={this.handleTableChange}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  }
}
