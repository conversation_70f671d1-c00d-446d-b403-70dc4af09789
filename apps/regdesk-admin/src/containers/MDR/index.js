import React from 'react';
import _ from 'lodash';
import moment from 'moment';
import { connect } from 'react-redux';
import { <PERSON>ton, Card, Col, Icon, Input, Row, Switch, Table, Popconfirm, Tag, Tooltip } from 'antd';
import { Link } from 'react-router-dom';
import config from 'config';
import Breadcrumb from '../../components/Breadcrumb';
import { allCountriesWithEU } from '../../utils/countries';
import actions from '../../actions';

@connect(({ mdr, account }) => ({
  loading: mdr.loading,
  data: mdr.data,
  released: mdr.released,
  permissions: account.adminPermissions,
  availableCountries: account.adminCountriesForRegulation,
  limitCountries: account.adminLimitCountriesForRegulation,
}))

class MDRList extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      search: '',
      countries: [],
    };
  }

  componentDidMount() {
    const { availableCountries, limitCountries } = this.props;
    let countries = allCountriesWithEU.filter(({ alpha3code }) => !['GEN', 'GMP', 'ICH'].includes(alpha3code));

    if (limitCountries) countries = countries.filter(({ alpha3code }) => availableCountries.includes(alpha3code));

    actions.mdr
      .get()
      .then(() => this.setState({ countries }));
  }

  handleSearch = e => this.setState({ search: e.target.value });

  render() {
    const { countries, search } = this.state;
    const { loading, data = [], released, permissions } = this.props;

    const statuses = {
      verified: { color: 'green', name: 'Verified' },
      under_reviewing: { color: 'gold', name: 'New Alert Update' },
      to_be_reviewed_soon: { color: 'volcano', name: 'Major Regulatory Change' },
    };

    const accessRelease = permissions.includes('accessRegReleaseCountry');

    const columns = [
      {
        title: 'Country',
        dataIndex: 'alpha3code',
        key: 'alpha3code',
        render: (code, { name = '' } = {}) => <Link to={`${config.rootRoute}/devicereg/${code}`}>{name}</Link>,
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        filters: [{ text: 'No set', value: 'no_set' }, ...Object.entries(statuses).map(([value, { name: text }]) => ({ text, value }))],
        onFilter: (value, { status }) => status.indexOf(value) === 0,
        render: (value) => {
          if (!statuses[value]) return <Tag>No set</Tag>;

          const { color, name } = statuses[value];

          return <Tag color={color}>{name}</Tag>;
        },
      },
      {
        title: 'Release Time',
        dataIndex: 'releasedAt',
        key: 'releasedAt',
        render: (text, item) => (item.releasedAt ? moment(item.releasedAt).getFullUTC() : ''),
      },
      {
        title: 'Actions',
        dataIndex: 'key',
        key: 'key',
        render: (code, item) => item.releasedAt && (
          <Tooltip title={!accessRelease && 'No permission'}>
            <Popconfirm
              title='Do you make sure ?'
              onConfirm={() => actions.mdr.unRelease(code)}
              onCancel={() => {}}
              okText='Yes'
              cancelText='No'
              disabled={!accessRelease}
            >
              <Button type='link' disabled={!accessRelease}>Recall release</Button>
            </Popconfirm>
          </Tooltip>
        ),
      },
    ];

    const releasedObj = {};

    data.forEach(({ country, updatedAt }) => {
      releasedObj[country.id] = { updatedAt, status: country.status };
    });

    const dataSource = _.map(countries.slice(), v =>
      Object.assign({}, v, {
        key: v.alpha3code,
        releasedAt: releasedObj[v.alpha3code] ? releasedObj[v.alpha3code].updatedAt : '',
        status: releasedObj[v.alpha3code] ? releasedObj[v.alpha3code].status : 'no_set',
      })
    ).filter(v => (
      (search.length === 0 || v.name.toLowerCase().includes(search.toLowerCase()))
      && (!released || v.releasedAt)
    ));

    return (
      <div>
        <Breadcrumb routes={[{ title: 'Dashboard', href: `${config.rootRoute}/` }, { title: 'Device Regs' }]} />

        <Row gutter={24}>
          <Col xl={6} lg={6} md={24} sm={24} xs={24}>
            <Card title='Document Types' style={{ marginBottom: 24 }}>
              <Link to={`${config.rootRoute}/devicereg/docTypes/`}>
                <Button size='large' icon='setting' type='primary' style={{ width: '100%' }}>
                  Add new type
                </Button>
              </Link>
            </Card>

            <Card title='Reports' style={{ marginBottom: 24 }}>
              <Link to={`${config.rootRoute}/devicereg/reports/`}>
                <Button size='large' icon='setting' type='secondary' style={{ width: '100%' }}>
                  Reports
                </Button>
              </Link>
            </Card>

            <Card
              title='Filters'
              style={{ marginBottom: 24 }}
              bodyStyle={{ display: 'flex', flexDirection: 'column' }}
            >
              <Input
                size='large'
                addonBefore={<Icon type='search' />}
                placeholder='Search by Country Name'
                onChange={e => this.handleSearch(e)}
                style={{ marginBottom: 24 }}
              />

              <div>
                <Switch checked={released} onChange={() => actions.mdr.change({ released: !released })} />
                <span style={{ marginLeft: 10 }}>Released</span>
              </div>
            </Card>
          </Col>

          <Col xl={18} lg={18} md={24} sm={24} xs={24}>
            <Card>
              <Table
                bordered
                rowKey='key'
                loading={loading}
                columns={columns}
                dataSource={dataSource}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  }
}

export default MDRList;
