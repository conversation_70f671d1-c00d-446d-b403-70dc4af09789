import React from 'react';
import { connect } from 'react-redux';
import { <PERSON>, Col, Card, Button, Modal, message, Alert, Empty, Select, Tag, Tooltip } from 'antd';
import config from 'config';
import Menu from './Menu';
import Form, { Versions } from '../../../components/Form';
import Breadcrumb from '../../../components/Breadcrumb';
import EmptyImage from '../../../assets/empty-image.png';
import actions from '../../../actions/index';
import styles from '../../../components/Form/index.less';

const { Option } = Select;

@connect(({ mdr, account }) => ({
  loading: mdr.loading,
  item: mdr.item,
  buffer: mdr.buffer,
  permissions: account.adminPermissions,
}))

class MDRItem extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      selected: {},
    };
  }

  componentDidMount() {
    const { id } = this.props.match.params;

    actions.mdr.getById(id);
  }

  componentWillUnmount() {
    actions.mdr.change({ item: {} });
  }

  /**
   * Update mdr
   * @param data
   * @returns {Promise<any>}
   */
  update = (data) => {
    const { _id: id, updatedAt } = this.props.item;

    return actions.mdr
      .update({ ...data, id, updatedAt })
      .then(() => message.success('Save success!'))
      .catch(() => actions.mdr.change({ loading: false }));
  }

  /**
   * on Select Buffer
   * @param newItem
   */
  onSelectBuffer = newItem => {
    const { selected } = this.state;

    this.setState({ selected: { ...selected, item: newItem } });
  }

  render() {
    const {
      loading,
      buffer = [],
      permissions,
      item: propsItem
    } = this.props;

    const {
      _id: idp,
      country = {},
      updatedAt = ''
    } = propsItem || {};

    const {
      selected = {},
    } = this.state;

    const {
      item,
      fields,
      onUpdate,
      onRemove,
      itemTitle,
      onAutoSave,
    } = selected;

    const { first = '', second = '', third = '' } = itemTitle || {};
    let title = '';

    if (first) title += first;
    if (second) title += ` / ${second}`;
    if (third) title += ` / ${third}`;

    const accessApprove = permissions.includes('accessRegApproveRejectSection');
    const accessRelease = permissions.includes('accessRegReleaseCountry');
    const accessChangeStatus = permissions.includes('accessRegChangeStatus');
    const accessRemoveData = permissions.includes('accessRegRemoveData');
    const accessEnableBausch = permissions.includes('accessBauschEnableClass') ? null : true;

    return (
      <div className={styles.container}>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Device Regs', href: `${config.rootRoute}/devicereg` },
            { title: country.name },
            { title: first },
            { title: second },
            { title: third },
          ]}
        />

        <Row gutter={24}>
          <Col xl={6} lg={6} md={24} sm={24} xs={24}>
            <Card title='' style={{ marginBottom: 24 }}>
              <Tooltip title={!accessRelease && 'No permission'}>
                <Button
                  block
                  loading={loading}
                  disabled={loading || !accessRelease}
                  size='large'
                  icon='check'
                  type='primary'
                  style={{ marginBottom: 20 }}
                  onClick={() => {
                    const notApproved = JSON.stringify(propsItem).includes('"checked":false');

                    if (!country.status) {
                      message.warning('Please select a status');
                    } else {
                      Modal.confirm({
                        title: 'Confirm',
                        content: notApproved ? (
                          <Alert
                            description='You have not approved fields. Are you sure you want to release?'
                            type='error'
                          />
                        ) : 'Are you sure want to release?',
                        onOk: () => {
                          actions.mdr
                            .release(idp)
                            .then(() => message.success('Success!'));
                        },
                      });
                    }
                  }}
                >
                  Release
                </Button>
              </Tooltip>

              <Tooltip title={!accessChangeStatus && 'No permission'}>
                <Select
                  showSearch
                  defaultValue='verified'
                  style={{ width: '100%' }}
                  placeholder='Select status'
                  loading={loading}
                  disabled={loading || !accessChangeStatus}
                  value={country.status || undefined}
                  onChange={(value) => this.update({ country: { ...country, status: value || '' } })}
                >
                  <Option value='verified'><Tag color='green'>Verified</Tag></Option>
                  <Option value='under_reviewing'><Tag color='gold'>New Alert Update</Tag></Option>
                  <Option value='to_be_reviewed_soon'><Tag color='volcano'>Major Regulatory Change</Tag></Option>
                </Select>
              </Tooltip>
            </Card>

            <Card
              title='Menu'
              style={{ marginBottom: 24 }}
              extra={(
                <Button
                  type='link'
                  loading={loading}
                  disabled={loading}
                  style={{ marginRight: -15 }}
                  onClick={() => this.modalVersions.show()}
                >
                  Versions
                </Button>
              )}
            >
              <Menu
                onCloseEditor={() => this.setState({ selected: {} })}
                onSelect={(data) => this.setState({ selected: { ...selected, ...data } })}
                onUpdate={this.update}
              />
            </Card>
          </Col>

          <Col xl={18} lg={18} md={24} sm={24} xs={24}>
            {fields
              ? (
                <Form
                  autoSave
                  data={item}
                  onUpdate={onUpdate}
                  onAutoSave={onAutoSave}
                  onRemove={onRemove}
                  fields={fields}
                  loading={loading}
                  country={country}
                  module='device-reg'
                  buttonTop
                  itemTitle={title}
                  buffer={buffer}
                  updatedAt={updatedAt}
                  accessApprove={accessApprove}
                  accessRemoveData={accessRemoveData}
                  accessEnableBausch={accessEnableBausch}
                  onSelectBuffer={this.onSelectBuffer}
                  ref={ref => { this.form = ref; }}
                />
              )
              : (
                <Card style={{ height: 400, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  <Empty
                    image={EmptyImage}
                    imageStyle={{ height: 60 }}
                    description={
                      <span style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 15 }}>
                        Please select menu item
                      </span>
                    }
                  />
                </Card>
              )}
          </Col>
        </Row>

        <Versions
          id={idp}
          countryId={country.id}
          collectionName='mdrContainer'
          ref={ref => { this.modalVersions = ref; }}
          onSelect={newItem => {
            this.update(newItem);
            this.setState({ selected: {} });
          }}
        />
      </div>
    );
  }
}

export default MDRItem;
