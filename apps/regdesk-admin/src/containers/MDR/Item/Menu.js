import React from 'react';
import { Tree, Icon } from 'antd';
import { connect } from 'react-redux';
import { generateId } from '../../../components/Form/ItemId';
import actions from '../../../actions';

const { TreeNode } = Tree;

@connect(({ mdr }) => ({
  loading: mdr.loading,
  item: mdr.item
}))

class Menu extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      expandedKeys: ['0-0', '0-1', '0-2', '0-3', '0-4'],
    };
  }

  /**
   * Update mdr
   * @param data
   * @returns {Promise<any>}
   */
  update = (data) => {
    const { onUpdate } = this.props;

    if (onUpdate) onUpdate(data);
  }

  /**
   * Add new Agency
   */
  addAgency = () => {
    const { expandedKeys } = this.state;
    const { agencies } = this.props.item;

    agencies.push({
      id: generateId(),
      name: 'New Agency',
      website: '',
      contact: '',
      email: '',
      address: '',
      zip: '',
      customFields: [],
    });

    actions.mdr.change({ item: { ...this.props.item, agencies } });

    if (!expandedKeys.includes('0-0')) this.setState({ expandedKeys: expandedKeys.concat('0-0') });

    this.forceUpdate();
  };

  /**
   * Select item menu
   * @param key
   * @param info
   */
  handleSelect = (key, info) => {
    const { type, item, itemTitle, fields, itemKey, onUpdate, onRemove, onAutoSave } = info.node.props;
    const { onSelect } = this.props;

    if (type || itemTitle) {
      onSelect({
        type,
        item,
        fields,
        itemTitle,
        itemKey,
        onUpdate,
        onRemove,
        onAutoSave,
      });
    }
  };

  /**
   * Get title for Tree
   * @param title
   * @param check
   * @param comment
   * @returns {JSX.Element}
   */
  renderTitle = (title, check, comment) => {
    let color = 'inherit';

    if (check === false) color = '#ff9800';
    if (check === false && comment) color = '#ff5722';
    if (check === true) color = '#4caf50';

    return (
      <span style={{ color }}>
        {title.length > 30 ? `${title.slice(0, 30)}...` : title}
      </span>
    );
  };

  render() {
    const { item, loading, onCloseEditor } = this.props;
    const {
      agencies = [],
      classRules = {},
      obligations = {},
      devices = {},
      commonRegulations = {},
    } = item || {};

    return (
      <div>
        <Tree
          showLine
          onSelect={this.handleSelect}
          expandedKeys={this.state.expandedKeys}
          onExpand={expandedKeys => this.setState({ expandedKeys })}
          draggable
        >
          {/* -------------- */}
          {/* ---Agencies--- */}
          {/* -------------- */}

          <TreeNode
            title={
              <span className='b-hover'>
                Agencies&nbsp;
                <Icon
                  type='plus-circle'
                  style={{ fontSize: 14 }}
                  onClick={() => this.addAgency()}
                />
              </span>
            }
            loading={loading}
            disabled={loading}
            item={agencies}
            itemTitle={{ first: 'Agencies' }}
          >
            {
              agencies.map((data, i) =>
                <TreeNode
                  key={data.id}
                  loading={loading}
                  disabled={loading}
                  title={this.renderTitle(data.name, data.checked, data.comment)}
                  fields={[
                    { key: 'id', type: 'id' },
                    { key: 'name', type: 'input', label: 'Name' },
                    { key: 'website', type: 'input', label: 'Website' },
                    { key: 'contact', type: 'input', label: 'Phone/Fax' },
                    { key: 'email', type: 'input', label: 'Email' },
                    { key: 'address', type: 'input', label: 'Address' },
                    { key: 'zip', type: 'input', label: 'Zip' },
                    { key: 'customFields', type: 'customFields' },
                  ]}
                  item={data}
                  itemTitle={{ first: 'Agencies', second: data.name }}
                  onUpdate={(newData) => {
                    this.props.onSelect({ item: newData });
                    agencies[i] = newData;
                    this.update({ agencies });
                  }}
                  onAutoSave={(newData) => {
                    agencies[i] = newData;
                    this.update({ agencies });
                  }}
                  onRemove={() => {
                    agencies.splice(i, 1);
                    this.update({ agencies });
                    onCloseEditor();
                  }}
                />
              )
            }
          </TreeNode>

          {/* --------------------------- */}
          {/* ----Classification Rules--- */}
          {/* --------------------------- */}

          <TreeNode
            loading={loading}
            disabled={loading}
            title={this.renderTitle('Classification Rules', classRules.checked, classRules.comment)}
            itemTitle={{ first: 'Classification Rules' }}
            item={classRules}
            fields={[
              { key: 'regulationStatus', type: 'input', label: 'Regulation Status' },
              { key: 'definition', type: 'markDown', label: 'Definition' },
              { key: 'classificationRules', type: 'markDown', label: 'Classification Rules' },
              { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
              { key: 'classifications', type: 'classifications', title: 'Classifications' },
            ]}
            onUpdate={(newData) => {
              this.props.onSelect({ item: newData });
              this.update({ classRules: newData });
            }}
            onAutoSave={(newData) => {
              this.update({ classRules: newData });
            }}
          />

          {/* ----------------- */}
          {/* ---Obligations--- */}
          {/* ----------------- */}

          <TreeNode
            loading={loading}
            disabled={loading}
            title='Obligations'
            itemTitle={{ first: 'Obligations' }}
            item={obligations}
          >
            {
              [
                { title: 'Manufacturer', key: 'manufacturer' },
                { title: 'Importer', key: 'importer' },
                { title: 'Distributor', key: 'distributor' },
                { title: 'Authorized Representative', key: 'authorizedRepresentative' },
                { title: 'Notified Body', key: 'notifiedBody' },
              ].map(({ key, title }) => {
                const data = obligations[key] || {};

                return (
                  <TreeNode
                    key={key}
                    loading={loading}
                    disabled={loading}
                    title={this.renderTitle(title, data.checked, data.comment)}
                    item={data}
                    itemTitle={{ first: 'Obligations', second: title }}
                    fields={[
                      { key: 'definition', type: 'markDown', label: 'Definition' },
                      { key: 'obligations', type: 'markDown', label: 'Obligations' },
                      { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                      { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                      { key: 'requirements', type: 'requirements', label: 'Requirements' },
                      { key: 'sample', type: 'sample', title: 'Sample', addTitle: 'Add Sample' },
                    ]}
                    onUpdate={(newData) => {
                      this.props.onSelect({ item: newData });
                      obligations[key] = newData;
                      this.update({ obligations });
                    }}
                    onAutoSave={(newData) => {
                      const { item: propsItem } = this.props;
                      const propsData = propsItem.obligations;

                      propsData[key] = newData;
                      this.update({ obligations: propsData });
                    }}
                  />
                );
              })
            }
          </TreeNode>

          {/* ------------- */}
          {/* ---Devices--- */}
          {/* ------------- */}

          <TreeNode
            loading={loading}
            disabled={loading}
            title='Devices'
            itemTitle={{ first: 'Devices' }}
            item={devices}
          >
            {
              [
                { title: 'Medical Device', key: 'medicalDevice' },
                { title: 'In Vitro Diagnostic', key: 'inVitroDiagnostic' },
                { title: 'Combination Device', key: 'combinationDevice' },
                { title: 'Radiation Emitting Device', key: 'radiationEmittingDevice' },
                { title: 'Electromagnetic Compatibility Device', key: 'electromagneticCompatibilityDevice' },
                { title: 'Wireless Device', key: 'wirelessDevice' },
                { title: 'Software Device', key: 'softwareDevice' },
                { title: 'Implant Device', key: 'implantDevice' },
                { title: 'AI Medical Device', key: 'aiMedicalDevice' },
              ].map(({ key, title }) => {
                const data = devices[key] || {};
                let deviceFields = [
                  { key: 'regulationStatus', type: 'input', label: 'Regulation Status' },
                  { key: 'definition', type: 'markDown', label: 'Definition' },
                  { key: 'marketSpecific', type: 'markDown', label: 'Market Specific' },
                  { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                  { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                  { key: 'classifications', type: 'classifications', title: 'Classifications' },
                ];

                if (['medicalDevice', 'inVitroDiagnostic'].includes(key)) {
                  deviceFields = [
                    { key: 'regulationStatus', type: 'input', label: 'Regulation Status' },
                    { key: 'definition', type: 'markDown', label: 'Definition' },
                    { key: 'marketSpecific', type: 'markDown', label: 'Market Specific' },
                    { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                    { key: 'transitionalProvision', type: 'markDown', label: 'Transitional Provision' },
                    { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                    { key: 'classifications', type: 'classifications', title: 'Classifications' },
                  ];
                }

                if (key === 'aiMedicalDevice') {
                  deviceFields = [
                    { key: 'regulationStatus', type: 'input', label: 'Regulation Status' },
                    { key: 'definition', type: 'markDown', label: 'Definition' },
                    { key: 'requiredInformation', type: 'markDown', label: 'Required Information' },
                    { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                    { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                    { key: 'sample', type: 'sample', title: 'Sample' },
                    { key: 'requirements', type: 'requirements', label: 'Requirements', extra: ['arRequired', 'dossierLanguage'] },
                  ];
                }

                return (
                  <TreeNode
                    key={key}
                    loading={loading}
                    disabled={loading}
                    title={this.renderTitle(title, data.checked, data.comment)}
                    item={data}
                    itemTitle={{ first: 'Devices', second: title }}
                    fields={deviceFields}
                    onUpdate={(newData) => {
                      this.props.onSelect({ item: newData });
                      devices[key] = { ...devices[key], ...newData };
                      this.update({ devices });
                    }}
                    onAutoSave={(newData) => {
                      const { item: propsItem } = this.props;
                      const propsData = propsItem.devices;

                      propsData[key] = { ...propsData[key], ...newData };
                      this.update({ devices: propsData });
                    }}
                  >
                    {
                      [
                        { title: 'Quality Management System', key: 'qualityManagementSystem' },
                        { title: 'Clinical Trials', key: 'clinicalTrials' },
                        { title: 'Importation', key: 'importation' },
                        { title: 'Unique Device Identification', key: 'uniqueDeviceIdentification' },
                        { title: 'Labeling', key: 'labeling' },
                        { title: 'Advertisement and Promotion', key: 'advertisementAndPromotion' },
                        { title: 'Packaging', key: 'packaging' },
                        { title: 'Post Market Surveillance', key: 'postMarketSurveillance' },
                        { title: 'Adverse Event Report', key: 'adverseEventReport' },
                        { title: 'Renewal', key: 'renewal' },
                        { title: 'Materials Derived from Animal Origin', key: 'materialsDerivedFromAnimalOrigin' },
                      ]
                        .filter((item) => !(key === 'aiMedicalDevice' && !['qualityManagementSystem', 'labeling', 'postMarketSurveillance'].includes(item.key)))
                        .map((item) => {
                          if (
                            [
                              'wirelessDevice',
                              'softwareDevice',
                              'radiationEmittingDevice',
                              'electromagneticCompatibilityDevice'
                            ].includes(key)
                            && item.key === 'materialsDerivedFromAnimalOrigin'
                            || !['medicalDevice', 'inVitroDiagnostic'].includes(key) && item.key === 'renewal'
                          ) {
                            return null;
                          }

                          const fields = [];
                          const subData = data[item.key] || {};

                          if (['renewal'].includes(item.key)) {
                            fields.push({ key: 'regulationStatus', type: 'input', label: 'Regulation Status' });
                          }

                          if ([
                            'renewal',
                            'packaging',
                            'importation',
                            'clinicalTrials',
                            'adverseEventReport',
                            'postMarketSurveillance',
                            'qualityManagementSystem',
                            'advertisementAndPromotion',
                            'uniqueDeviceIdentification',
                            'materialsDerivedFromAnimalOrigin',
                          ].includes(item.key)) {
                            fields.push({ key: 'definition', type: 'markDown', label: 'Definition' });
                          }

                          if ([
                            'adverseEventReport',
                            'postMarketSurveillance',
                            'uniqueDeviceIdentification'
                          ].includes(item.key)) {
                            fields.push({ key: 'marketSpecific', type: 'markDown', label: 'Market Specific' });
                          }

                          if (['advertisementAndPromotion'].includes(item.key)) {
                            fields.push({ key: 'generalPrinciples', type: 'markDown', label: 'General Principles' });
                          }

                          if (['postMarketSurveillance'].includes(item.key) && key !== 'aiMedicalDevice') {
                            fields.push({ key: 'complaintHandling', type: 'markDown', label: 'Complaint Handling' });
                          }

                          if ([
                            'renewal',
                            'importation',
                            'clinicalTrials',
                            'uniqueDeviceIdentification',
                          ].includes(item.key)) {
                            fields.push({ key: 'criteria', type: 'markDown', label: 'Criteria' });
                          }

                          if ([
                            'packaging',
                            'materialsDerivedFromAnimalOrigin',
                          ].includes(item.key)) {
                            fields.push(
                              { key: 'requirement', type: 'markDown', label: 'Requirements' },
                              { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                              { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                              { key: 'sample', type: 'sample', title: 'Sample' },
                            );
                          }

                          if (['labeling'].includes(item.key)) {
                            fields.push(
                              { key: 'generalRequirements', type: 'markDown', label: 'General requirements' },
                              { key: 'language', type: 'markDown', label: 'Language' },
                              { key: 'innerLabel', type: 'markDown', label: 'Primary Packaging Label' },
                              { key: 'outerLabel', type: 'markDown', label: 'Secondary Packaging Label' },
                              { key: 'eifu', type: 'markDown', label: 'e-IFU' },
                              { key: 'instructionsForUse', type: 'markDown', label: 'Instructions for Use' },
                              { key: 'implantCard', type: 'markDown', label: 'Implant card' },
                              { key: 'labelingFormat', type: 'markDown', label: 'Labeling Format' },
                              { key: 'symbolRequirements', type: 'markDown', label: 'Symbol Requirements' },
                              { key: 'deviceLabel', type: 'markDown', label: 'Device Label (Direct Marking)' },
                              { key: 'softwareLabeling', type: 'markDown', label: 'Software Labeling' },
                              { key: 'serviceManual', type: 'markDown', label: 'Service Manual' },
                              { key: 'sscp', type: 'markDown', label: 'SSCP' },
                              { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                              { key: 'sample', type: 'sample', title: 'Sample' },
                            );
                          }

                          if ([
                            'clinicalTrials',
                            'uniqueDeviceIdentification',
                          ].includes(item.key)) {
                            fields.push(
                              { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                              { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                              { key: 'requirements', type: 'requirements', label: 'Requirements', extra: ['generalPrinciples'] },
                            );
                          }

                          if ([
                            'adverseEventReport',
                            'postMarketSurveillance',
                            'qualityManagementSystem',
                          ].includes(item.key)) {
                            fields.push(
                              { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                              { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                              { key: 'requirements', type: 'requirements', label: 'Requirements' },
                            );
                          }

                          if (['renewal'].includes(item.key)) {
                            fields.push(
                              { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                              { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                              { key: 'requirements', type: 'requirements', label: 'Requirements', extra: ['timelineAndFees', 'generalPrinciples'] }
                            );
                          }

                          if (['importation'].includes(item.key)) {
                            fields.push(
                              { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                              { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                              { key: 'requirements', type: 'requirements', label: 'Requirements', extra: ['arRequired', 'generalPrinciples'] },
                            );
                          }

                          if (['advertisementAndPromotion'].includes(item.key)) {
                            fields.push(
                              { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                              { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                              { key: 'requirements', type: 'requirements', label: 'Requirements', extra: ['dossierLanguage'] },
                            );
                          }

                          return (
                            <TreeNode
                              key={key + item.key}
                              loading={loading}
                              disabled={loading}
                              title={this.renderTitle(item.title, subData.checked, subData.comment)}
                              item={subData}
                              itemTitle={{ first: 'Devices', second: title, third: item.title }}
                              fields={fields}
                              onUpdate={(newData) => {
                                this.props.onSelect({ item: newData });
                                if (!devices[key]) devices[key] = {};
                                devices[key][item.key] = newData;
                                this.update({ devices });
                              }}
                              onAutoSave={(newData) => {
                                const { item: propsItem } = this.props;
                                const propsData = propsItem.devices;

                                if (!propsData[key]) propsData[key] = {};
                                propsData[key][item.key] = newData;
                                this.update({ devices: propsData });
                              }}
                            />
                          );
                        })
                    }
                  </TreeNode>
                );
              })
            }
          </TreeNode>

          {/* ------------------------ */}
          {/* ---Common Regulations--- */}
          {/* ------------------------ */}

          <TreeNode
            loading={loading}
            disabled={loading}
            title='Common Regulations'
            itemTitle={{ first: 'Common Regulations' }}
            item={commonRegulations}
          >
            {
              [
                { title: 'Quality Management System', key: 'qualityManagementSystem' },
                { title: 'Clinical Trials', key: 'clinicalTrials' },
                { title: 'Importation', key: 'importation' },
                { title: 'Unique Device Identification', key: 'uniqueDeviceIdentification' },
                { title: 'Labeling', key: 'labeling' },
                { title: 'CE Marking', key: 'ceMarking' },
                { title: 'Advertisement and Promotion', key: 'advertisementAndPromotion' },
                { title: 'Packaging', key: 'packaging' },
                { title: 'Cybersecurity', key: 'cyberSecurity' },
                { title: 'Post Market Surveillance', key: 'postMarketSurveillance' },
                { title: 'Adverse Event Report', key: 'adverseEventReport' },
                { title: 'Recalls', key: 'recalls' },
                { title: 'Renewals', key: 'renewals' },
                { title: 'Establishment License', key: 'establishmentLicence' },
                { title: 'Timeline and Fees', key: 'timelineAndFees' },
                { title: 'e-Submission', key: 'eSubmission' },
                { title: 'Battery Requirements', key: 'batteryRequirements' },
                { title: 'Shelf-Life Requirements', key: 'shelfLifeRequirements' },
                { title: 'REACH', key: 'reach' },
                { title: 'ROHS', key: 'rohs' },
                { title: 'Change Control Requirements', key: 'changeControlRequirements' },
                { title: 'Over-The Counter (OTC) Requirements', key: 'otcRequirements' },
                { title: 'Export Certificates', key: 'exportCertificates' },
                { title: 'Transportation and Storage', key: 'transportationAndStorage' },
                { title: 'Waste Management', key: 'wasteManagement' },
              ].map(({ title, key }) => {
                const fields = [{ key: 'regulationStatus', type: 'input', label: 'Regulation Status' }];
                const data = commonRegulations[key] || {};

                if ([
                  'rohs',
                  'reach',
                  'recalls',
                  'renewals',
                  'packaging',
                  'ceMarking',
                  'importation',
                  'cyberSecurity',
                  'clinicalTrials',
                  'wasteManagement',
                  'otcRequirements',
                  'exportCertificates',
                  'adverseEventReport',
                  'batteryRequirements',
                  'establishmentLicence',
                  'shelfLifeRequirements',
                  'postMarketSurveillance',
                  'qualityManagementSystem',
                  'transportationAndStorage',
                  'changeControlRequirements',
                  'advertisementAndPromotion',
                  'uniqueDeviceIdentification',
                ].includes(key)) {
                  fields.push({ key: 'definition', type: 'markDown', label: 'Definition' });
                }

                if ([
                  'adverseEventReport',
                  'establishmentLicence',
                  'postMarketSurveillance',
                  'uniqueDeviceIdentification'
                ].includes(key)) {
                  fields.push({ key: 'marketSpecific', type: 'markDown', label: 'Market Specific' });
                }

                if (['advertisementAndPromotion'].includes(key)) {
                  fields.push({ key: 'generalPrinciples', type: 'markDown', label: 'General Principles' });
                }

                if (['postMarketSurveillance'].includes(key)) {
                  fields.push({ key: 'complaintHandling', type: 'markDown', label: 'Complaint Handling' });
                }

                if (['timelineAndFees'].includes(key)) {
                  fields.push(
                    { key: 'timelineAndFees', type: 'markDown', label: 'Timeline and Fees' },
                    { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                  );
                }

                if (['eSubmission'].includes(key)) {
                  fields.push(
                    { key: 'procedure', type: 'markDown', label: 'Procedure' },
                    { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                  );
                }

                if ([
                  'renewals',
                  'importation',
                  'clinicalTrials',
                  'uniqueDeviceIdentification'
                ].includes(key)) {
                  fields.push({ key: 'criteria', type: 'markDown', label: 'Criteria' });
                }

                if (['ceMarking'].includes(key)) {
                  fields.push(
                    { key: 'ceMarking', type: 'markDown', label: 'CE Marking' },
                    { key: 'conformityAssessment', type: 'markDown', label: 'Conformity Assessment' }
                  );
                }

                if ([
                  'recalls',
                  'cyberSecurity',
                ].includes(key)) {
                  fields.push({ key: 'requiredInformation', type: 'markDown', label: 'Required Information' });
                }

                if (['packaging'].includes(key)) {
                  fields.push(
                    { key: 'procedurePacks', type: 'markDown', label: 'Procedure Packs' },
                    { key: 'requirement', type: 'markDown', label: 'Requirements' },
                  );
                }

                if (['labeling'].includes(key)) {
                  fields.push(
                    { key: 'generalRequirements', type: 'markDown', label: 'General requirements' },
                    { key: 'language', type: 'markDown', label: 'Language' },
                    { key: 'innerLabel', type: 'markDown', label: 'Primary Packaging Label' },
                    { key: 'outerLabel', type: 'markDown', label: 'Secondary Packaging Label' },
                    { key: 'eifu', type: 'markDown', label: 'e-IFU' },
                    { key: 'instructionsForUse', type: 'markDown', label: 'Instructions for Use' },
                    { key: 'implantCard', type: 'markDown', label: 'Implant card' },
                    { key: 'labelingFormat', type: 'markDown', label: 'Labeling Format' },
                    { key: 'symbolRequirements', type: 'markDown', label: 'Symbol Requirements' },
                    { key: 'deviceLabel', type: 'markDown', label: 'Device Label (Direct Marking)' },
                    { key: 'softwareLabeling', type: 'markDown', label: 'Software Labeling' },
                    { key: 'serviceManual', type: 'markDown', label: 'Service Manual' },
                    { key: 'sscp', type: 'markDown', label: 'SSCP' },
                    { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                  );
                }

                if ([
                  'rohs',
                  'reach',
                  'recalls',
                  'renewals',
                  'packaging',
                  'ceMarking',
                  'importation',
                  'cyberSecurity',
                  'clinicalTrials',
                  'wasteManagement',
                  'otcRequirements',
                  'exportCertificates',
                  'adverseEventReport',
                  'batteryRequirements',
                  'establishmentLicence',
                  'shelfLifeRequirements',
                  'postMarketSurveillance',
                  'transportationAndStorage',
                  'changeControlRequirements',
                  'advertisementAndPromotion',
                  'uniqueDeviceIdentification',
                ].includes(key)) {
                  fields.push(
                    { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                    { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                  );
                }

                if (['qualityManagementSystem'].includes(key)) {
                  fields.push(
                    { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                    { key: 'mdsap', type: 'input', label: 'MDSAP' },
                    { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                  );
                }

                if ([
                  'labeling',
                  'packaging',
                  'ceMarking',
                  'timelineAndFees',
                ].includes(key)) {
                  fields.push(
                    { key: 'sample', type: 'sample', title: 'Sample' },
                  );
                }

                if ([
                  'rohs',
                  'reach',
                  'recalls',
                  'cyberSecurity',
                  'wasteManagement',
                  'otcRequirements',
                  'exportCertificates',
                  'adverseEventReport',
                  'batteryRequirements',
                  'shelfLifeRequirements',
                  'postMarketSurveillance',
                  'qualityManagementSystem',
                  'transportationAndStorage',
                  'changeControlRequirements',
                ].includes(key)) {
                  fields.push({ key: 'requirements', type: 'requirements', label: 'Requirements' });
                }

                if (['uniqueDeviceIdentification'].includes(key)) {
                  fields.push({ key: 'requirements', type: 'requirements', label: 'Requirements', extra: ['generalPrinciples'] });
                }

                if (['establishmentLicence'].includes(key)) {
                  fields.push({ key: 'requirements', type: 'requirements', label: 'Requirements', extra: ['timelineAndFees'] });
                }

                if (['renewals'].includes(key)) {
                  fields.push({ key: 'requirements', type: 'requirements', label: 'Requirements', extra: ['timelineAndFees', 'generalPrinciples'] });
                }

                if (['advertisementAndPromotion'].includes(key)) {
                  fields.push({ key: 'requirements', type: 'requirements', label: 'Requirements', extra: ['dossierLanguage'] });
                }

                if (['importation'].includes(key)) {
                  fields.push({ key: 'requirements', type: 'requirements', label: 'Requirements', extra: ['timelineAndFees', 'arRequired', 'generalPrinciples'] });
                }

                if (['clinicalTrials'].includes(key)) {
                  fields.push({ key: 'requirements', type: 'requirements', label: 'Requirements', extra: ['timelineAndFees', 'checklists', 'generalPrinciples'] });
                }

                return (
                  <TreeNode
                    key={key}
                    loading={loading}
                    disabled={loading}
                    title={this.renderTitle(title, data.checked, data.comment)}
                    item={data}
                    itemTitle={{ first: 'Common Regulations', second: title }}
                    fields={fields}
                    onUpdate={(newData) => {
                      this.props.onSelect({ item: newData });
                      commonRegulations[key] = newData;
                      this.update({ commonRegulations });
                    }}
                    onAutoSave={(newData) => {
                      const { item: propsItem } = this.props;
                      const propsData = propsItem.commonRegulations;

                      propsData[key] = newData;
                      this.update({ commonRegulations: propsData });
                    }}
                  />
                );
              })
            }
          </TreeNode>
        </Tree>
      </div>
    );
  }
}

export default Menu;
