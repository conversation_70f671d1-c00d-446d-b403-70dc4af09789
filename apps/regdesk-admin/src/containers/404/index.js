import React from 'react';
import { Button } from 'antd';
import { Link } from 'react-router-dom';
import config from 'config';
import styles from './index.less';

export default () => (
  <div className={styles.exception}>
    <div className={styles.imgBlock}>
      <div className={styles.imgEle} />
    </div>

    <div className={styles.content}>
      <h1>404</h1>

      <div className={styles.desc}>
        Sorry, the page you visited does not exist
      </div>

      <div className={styles.actions}>
        <Link to={`${config.rootRoute}/`}>
          <Button type='primary'>Back to home</Button>
        </Link>
      </div>
    </div>
  </div>
);
