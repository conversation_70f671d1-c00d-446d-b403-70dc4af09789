import React, { useState } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';
import { Button, Form, Input, Select, Switch, DatePicker } from 'antd';
import { FULL_DATE_FORMAT, LEGACY_DATE_FORMAT } from '../../../utils/date';
import { allCountriesWithEU } from '../../../utils/countries';
import actions from '../../../actions';
import styles from '../index.less';

const { Item } = Form;
const { Option } = Select;
const { TextArea } = Input;

const formItemLayout = {
  labelCol: { xs: { span: 6 }, },
  wrapperCol: { xs: { span: 14 }, },
};

const formTailLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14, offset: 6 },
};

/**
 * Add/Update Pharma Alert
 * @param {?Object} alert
 * @param {boolean} limitCountries
 * @param {string[]} availableCountries
 * @param {function} onClose
 * @returns {JSX.Element}
 */
const PharmaAlertAdd = ({ alert = {}, limitCountries, availableCountries, onClose }) => {
  const { _id: id } = alert || {};
  const [createdDate, setCreatedDate] = useState(alert.createdDate || new Date());
  const [countryId, setCountryId] = useState(alert.countryId);
  const [title, setTitle] = useState(alert.title);
  const [content, setContent] = useState(alert.content);
  const [url, setUrl] = useState(alert.url);
  const [publishedDate, setPublishedDate] = useState(alert.publishedDate);
  const [released, setReleased] = useState(alert.released || false);

  let allCountries = allCountriesWithEU;

  if (limitCountries) allCountries = allCountries.filter(({ alpha3code }) => availableCountries.includes(alpha3code));

  const onAdd = () => {
    const params = {
      countryId,
      createdDate,
      title,
      content,
      url: url || '',
      publishedDate,
      released,
    };

    (id ? () => actions.pharmaAlerts.update(id, params) : () => actions.pharmaAlerts.add(params))();

    onClose();
  };

  return (
    <>
      <Item required label='Create Date' {...formItemLayout}>
        <DatePicker
          allowClear={false}
          format={FULL_DATE_FORMAT}
          className={styles.select}
          value={moment(createdDate)}
          onChange={date => setCreatedDate(date)}
        />
      </Item>

      <Item required label='Country' {...formItemLayout}>
        <Select
          showSearch
          value={countryId}
          className={styles.select}
          placeholder='Select country'
          onChange={idc => setCountryId(idc)}
          filterOption={(input, option) => option.props.children.toLowerCase().includes(input.toLowerCase())}
        >
          {allCountries.map(({ alpha3code, name }) => (
            <Option key={alpha3code} value={alpha3code}>
              {name}
            </Option>
          ))}
        </Select>
      </Item>

      <Item required label='Title' {...formItemLayout}>
        <Input
          value={title}
          placeholder='Enter title'
          onChange={e => setTitle(e.target.value)}
        />
      </Item>

      <Item required label='Content' {...formItemLayout}>
        <TextArea
          rows={4}
          value={content}
          placeholder='Enter content'
          onChange={e => setContent(e.target.value)}
        />
      </Item>

      <Item label='Url' {...formItemLayout}>
        <Input
          value={url}
          placeholder='Enter url'
          onChange={e => setUrl(e.target.value)}
        />
      </Item>

      <Item label='Published Date' {...formItemLayout}>
        <DatePicker
          format={FULL_DATE_FORMAT}
          placeholder='Select date'
          className={styles.select}
          value={publishedDate ? moment(publishedDate, LEGACY_DATE_FORMAT) : null}
          onChange={date => setPublishedDate(date)}
        />
      </Item>

      <Item label='Release' {...formItemLayout}>
        <Switch checked={released} onChange={() => setReleased(!released)} />
      </Item>

      <Item className={styles.btns} {...formTailLayout}>
        <Button onClick={() => onClose()}>Cancel</Button>
        <Button type='primary' disabled={!countryId || !createdDate || !title || !content} onClick={() => onAdd()}>Save</Button>
      </Item>
    </>
  );
};

export default connect(({ account }) => ({
  availableCountries: account.adminCountriesForPharmaAlert,
  limitCountries: account.adminLimitCountriesForPharmaAlert,
}))(PharmaAlertAdd);
