import React from 'react';
import { Modal } from 'antd';
import Form from './Form';
import styles from '../index.less';

/**
 * Add/Update Pharma Alert Modal
 * @param {?Object} alert
 * @param {boolean} show
 * @param {function} onClose
 * @returns {JSX.Element}
 */
export default ({ alert, show, onClose, ...props }) => (
  <Modal
    title={`${alert ? 'Update' : 'Create New'} Alert`}
    className={styles.form}
    visible={show}
    footer={null}
    centered
    destroyOnClose
    onCancel={onClose}
    onClose={onClose}
    width={900}
    zIndex={999}
  >
    <Form alert={alert || {}} onClose={onClose} {...props} />
  </Modal>
);
