.alerts {
  .btnWrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .filters {
      display: flex;
      gap: 10px;

      .select {
        width: 250px;
      }

      .search {
        width: 300px;
      }

      .datePicker {
        width: 350px;
      }
    }
  }

  .table {
    .column {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 3px;
    }

    .iconSuccess {
      color: #87d068;
    }

    .iconError {
      color: #f50;
    }
  }
}

.form {
  .select {
    width: 100%;
  }

  .btns {
    margin-bottom: 0;

    :global {
      .ant-form-item-children {
        display: flex;
        gap: 8px;
      }
    }
  }
}
