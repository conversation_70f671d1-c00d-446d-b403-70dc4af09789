import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, Divider, Input, message, Modal, Row, Select, Table, Tooltip, Icon } from 'antd';
import moment from 'moment';
import AddModal from './AddModal';
import CompareStandardsModal from './CompareStandardsModal';
import styles from './index.less';
import api from '../../utils/api';

const { Option } = Select;

class Standards extends React.Component {
  constructor(props) {
    super(props);

    const { isGuidance } = this.props;

    this.defaultPagination = {
      current: 1,
      pageSize: 10,
      total: 0,
    };

    this.state = {
      showAddModal: false,
      showCompareModal: false,
      modalTitle: '',
      standard: {},
      addType: 'add',
      loading: false,
      filters: {
        isGuidance,
        searchType: 'name',
      },
      groups: [],
      organizations: [],
    };
  }

  componentDidMount() {
    this.getStandards({ initialFetch: true });
  }

  getStandards = ({ initialFetch, pagination = this.defaultPagination } = {}) => {
    const { filters } = this.state;

    this.setState({ loading: true });

    api.standards
      .get({ initialFetch, pagination, filters })
      .then(data => this.setState({ ...data, loading: false }))
      .catch(() => this.setState({ loading: false }));
  }

  getColumns = () => {
    const { isGuidance, accessUpdate, accessRemove } = this.props;

    return [
      {
        title: 'Title',
        dataIndex: 'title',
        render: (text, item) => <span>{item.title}</span>,
      },
      {
        title: isGuidance ? 'Docket Number' : 'Designation Number',
        dataIndex: isGuidance ? 'docketNumber' : 'designationNumber',
        width: 250,
        render: (text) => <span>{text}</span>,
      },
      {
        title: 'Recognition Date',
        dataIndex: 'recognitionDate',
        width: '120px',
        render: (recognitionDate, item) => (
          <span>
            {recognitionDate
              ? moment(recognitionDate).format(item.dateFormat)
              : ''}
          </span>
        ),
      },
      {
        title: 'Action',
        key: 'Action',
        width: '300px',
        render: (text, item) => (
          <div>
            <Tooltip title={!accessUpdate && 'No permission'}>
              <a className={!accessUpdate ? styles.disabled : ''} onClick={() => accessUpdate && this.edit(item)}>
                Edit
              </a>
            </Tooltip>

            <Divider type='vertical' />

            <Tooltip title={!accessRemove && 'No permission'}>
              <a className={!accessRemove ? styles.disabled : ''} onClick={() => accessRemove && this.showDeleteConfirm(item)}>
                Delete
              </a>
            </Tooltip>
          </div>
        ),
      },
    ];
  }

  setFilters = (newFilters = {}) => this.setState(({ filters }) => ({ filters: { ...filters, ...newFilters } }));

  showDeleteConfirm = (item) => {
    const { pagination } = this.state;
    const { _id: standardId } = item;

    Modal.confirm({
      title: 'Delete this standard?',
      content:
        'If released, users won\'t be able to select it, but will keep it where it\'s already selected',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk: () => {
        api.standards.remove({ id: standardId }).then(() => {
          this.getStandards({ pagination });
          message.success('Delete success!');
        });
      },
      onCancel: () => {},
    });
  };

  edit = (record) => this.setState({ addType: 'edit', showAddModal: true, standard: record });

  add = (modalTitle) => this.setState({ addType: 'add', showAddModal: true, modalTitle });

  handleOk = (newStandard) => {
    const { addType, pagination } = this.state;

    if (addType === 'edit') {
      api.standards.update(newStandard).then(() => {
        this.getStandards({ pagination });
        message.success('Edit success!');
        this.setState({ showAddModal: false, standard: {} });
      });
    } else {
      api.standards.add(newStandard).then(() => {
        this.getStandards({ pagination });
        message.success('Add success!');
        this.setState({ showAddModal: false, standard: {} });
      });
    }
  }

  render() {
    const {
      isGuidance,
      accessCompare,
      accessStandards,
      accessGuidances
    } = this.props;
    const {
      loading,
      pagination,
      filters,
      list,
      addType,
      modalTitle,
      showAddModal,
      showCompareModal,
      standard,
      groups,
      organizations,
    } = this.state;

    if (!isGuidance && !accessStandards || isGuidance && !accessGuidances) {
      return (
        <Card bordered={false} bodyStyle={{ height: 'calc(100vh - 239px)', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: 18 }}>
          <Icon type='lock' style={{ fontSize: 20, marginRight: 8 }} /> No Module Access!
        </Card>
      );
    }

    return (
      <div>
        <Card bordered={false}>
          <Row>
            <div className={styles.filtersContainer}>
              <Col span={5}>
                <Input.Search
                  allowClear
                  loading={loading}
                  className={styles.searchStandard}
                  value={filters.searchValue}
                  placeholder={`Search ${isGuidance ? 'Guidances' : 'Standards'}...`}
                  addonBefore={
                    <Select
                      style={{ minWidth: 100, width: '100%' }}
                      value={filters.searchType}
                      getPopupContainer={triggerNode => triggerNode.parentNode}
                      onChange={(type) => this.setFilters({ searchValue: '', searchType: type })}
                    >
                      <Option value='name'>By Name</Option>

                      {isGuidance
                        ? <Option value='docketNumber'>By Docket Number</Option>
                        : <Option value='designationNumber'>By Designation Number</Option>
                      }
                    </Select>
                  }
                  onSearch={() => this.getStandards({ pagination })}
                  onChange={(e) => this.setFilters({ searchValue: e.target.value })}
                />
              </Col>
            </div>

            {isGuidance
              ? (
                <Button
                  style={{ marginLeft: '20px' }}
                  type='primary'
                  onClick={() => this.add('Add Guidance')}
                >
                  Add Guidance
                </Button>
              )
              : (
                <Button
                  style={{ marginLeft: '20px' }}
                  type='primary'
                  onClick={() => this.add('Add Standard')}
                >
                  Add Standard
                </Button>
              )
            }

            <Tooltip title={!accessCompare && 'No permission'}>
              <Button
                type='primary'
                disabled={!accessCompare}
                style={{ marginLeft: 20 }}
                onClick={() => this.setState({ showCompareModal: true })}
              >
                Compare {isGuidance ? 'Guidances' : 'Standards'}
              </Button>
            </Tooltip>
          </Row>

          <div style={{ marginTop: 20 }} />

          <Table
            rowKey='_id'
            columns={this.getColumns()}
            dataSource={list}
            loading={loading}
            pagination={{
              defaultPageSize: 10,
              size: 'Pagination',
              ...pagination,
            }}
            onChange={(newPagination) => this.getStandards({ pagination: newPagination })}
          />
        </Card>

        {showAddModal && (
          <AddModal
            title={addType === 'edit' ? 'Edit Detail' : modalTitle}
            isGuidance={isGuidance}
            standard={standard}
            type={addType}
            groups={groups}
            organizations={organizations}
            onConfirm={(newStandard) => this.handleOk(newStandard)}
            onCancel={() => this.setState({ showAddModal: false })}
          />
        )}

        {showCompareModal && (
          <CompareStandardsModal
            isGuidance={isGuidance}
            onCancel={() => this.setState({ showCompareModal: false })}
          />
        )}
      </div>
    );
  }
}

export default Standards;
