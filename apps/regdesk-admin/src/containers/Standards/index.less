.filtersContainer {
  :global {
    .ant-select {
      width: 90%;
    }

    .ant-select-selection {
      border-color: #828282;
    }

    .ant-input-affix-wrapper .ant-input:not(:first-child) {
      padding-left: 40px;
    }

    .ant-input-group-addon {
      border-color: #828282;
    }

    .ant-input-suffix {
      font-size: 22px;
      color: #828282;
      padding: 0;
      margin-left: -2px;
    }
  }

  .searchInput input {
    border-color: #828282;
  }

  .searchStandard input {
    border-color: #828282;
  }

  .searchIcon {
    font-size: 22px;
    color: #828282;
    padding: 0;
    margin-left: -2px;
  }
}

.disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.25) !important;
}
