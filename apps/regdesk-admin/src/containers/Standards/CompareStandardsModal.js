import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Popconfirm } from 'antd';
import moment from 'moment/moment';
import { alpha3ToName } from '../../utils/countries';
import api from '../../utils/api';

class CompareStandardsModal extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      loading: false,
      total: 0,
      current: 1,
      standardsToCompare: [],
    };
  }

  componentDidMount() {
    this.fetchData();
  }

  getInfoPanel = (label, text) => (
    <div style={{ display: 'flex' }}>
      <h4 style={{ marginRight: 10 }}>{label}:</h4>
      <span>{text}</span>
    </div>
  );

  fetchData = () => {
    const { current } = this.state;
    const { isGuidance } = this.props;

    this.setState({ loading: true });

    api.standards
      .getCompare({ current, isGuidance })
      .then(({ list, total }) => {
        this.setState((prevState) => ({
          standardsToCompare: list,
          total,
          loading: false,
          current: prevState.current + 1,
        }));
      })
      .catch(() => this.setState({ loading: false }));
  }

  handleCompare = (isUpdate, id) => {
    const { current } = this.state;

    this.setState({ loading: true });

    api.standards
      .compare({ isUpdate, id })
      .then(() => this.setState({ current: current - 1 }, () => this.fetchData()))
      .catch(() => this.setState({ loading: false }));
  }

  render() {
    const { total, loading, standardsToCompare } = this.state;
    const { onCancel } = this.props;

    if (standardsToCompare.length === 0) {
      return <Modal visible onCancel={onCancel} width={500} footer={null}><span>Nothing to compare</span></Modal>;
    }

    const [oldVersion, updatedVersion] = standardsToCompare;

    return (
      <Modal
        visible
        title='Compare standards'
        onCancel={onCancel}
        width='95%'
        destroyOnClose
        footer={null}
      >
        <div style={{ display: 'flex' }}>
          <div style={{ width: '50%', padding: 10 }}>
            <h3>Old standard</h3>
            {this.getInfoPanel('Title', oldVersion.title)}
            {this.getInfoPanel('Standard Type', oldVersion.standardType)}
            {oldVersion.countryId && this.getInfoPanel('Country', alpha3ToName(oldVersion.countryId))}

            {oldVersion.isGuidance
              ? this.getInfoPanel('Docket Number', oldVersion.docketNumber)
              : this.getInfoPanel('Designation Number', oldVersion.designationNumber)}

            {this.getInfoPanel('Group', oldVersion.group)}
            {this.getInfoPanel('Organization', oldVersion.organization)}
            {this.getInfoPanel('Status', oldVersion.status)}
            {this.getInfoPanel('Category', oldVersion.category)}
            {this.getInfoPanel('Recognition Date', moment(oldVersion.recognitionDate).format('LL'))}

            <div style={{ display: 'flex' }}>
              <h4 style={{ marginRight: 10 }}>Link of standard organization: </h4>
              <a href={oldVersion.standardOrganizationUrl} target='_blank' rel='noreferrer'>Link</a>
            </div>

            <div style={{ display: 'flex' }}>
              <h4 style={{ marginRight: 10 }}>Link of standard: </h4>
              <a href={oldVersion.standardUrl} target='_blank' rel='noreferrer'>Link</a>
            </div>
          </div>

          <div style={{ width: '50%', padding: 10 }}>
            <h3>Updated Version</h3>
            {this.getInfoPanel('Title', updatedVersion.title)}
            {this.getInfoPanel('Standard Type', updatedVersion.standardType)}
            {updatedVersion.countryId && this.getInfoPanel('Country', alpha3ToName(updatedVersion.countryId))}

            {oldVersion.isGuidance
              ? this.getInfoPanel('Docket Number', updatedVersion.docketNumber)
              : this.getInfoPanel('Designation Number', updatedVersion.designationNumber)}

            {this.getInfoPanel('Group', updatedVersion.group)}
            {this.getInfoPanel('Organization', updatedVersion.organization)}
            {this.getInfoPanel('Status', updatedVersion.status)}
            {this.getInfoPanel('Category', updatedVersion.category)}
            {this.getInfoPanel('Recognition Date', moment(updatedVersion.recognitionDate).format('LL'))}

            <div style={{ display: 'flex' }}>
              <h4 style={{ marginRight: 10 }}>Link of standard organization: </h4>
              <a href={updatedVersion.standardOrganizationUrl} target='_blank' rel='noreferrer'>Link</a>
            </div>

            <div style={{ display: 'flex', marginBottom: 20 }}>
              <h4 style={{ marginRight: 10 }}>Link of standard: </h4>
              <a href={updatedVersion.standardUrl} target='_blank' rel='noreferrer'>Link</a>
            </div>

            <h3>Is this standard correct updated version</h3>

            <div style={{ display: 'flex' }}>
              <Popconfirm
                title='Are you sure?'
                okText='Yes'
                cancelText='No'
                onConfirm={() => this.handleCompare(true, updatedVersion._id)}
              >
                <Button
                  type='primary'
                  style={{ width: 70 }}
                  loading={loading}
                  disabled={loading}
                >
                  Yes
                </Button>
              </Popconfirm>

              <Popconfirm
                title='Are you sure?'
                okText='Yes'
                cancelText='No'
                onConfirm={() => this.handleCompare(false, updatedVersion._id)}
              >
                <Button
                  type='primary'
                  style={{ width: 70, marginLeft: '20px' }}
                  loading={loading}
                  disabled={loading}
                >
                  No
                </Button>
              </Popconfirm>
            </div>
          </div>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
          <span>Total: {total}</span>

          <Button
            type='primary'
            style={{ marginLeft: 20 }}
            onClick={() => this.fetchData()}
            loading={loading}
            disabled={loading}
          >
            Next
          </Button>
        </div>
      </Modal>
    );
  }
}

export default CompareStandardsModal;
