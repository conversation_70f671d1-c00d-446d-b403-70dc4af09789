import React from 'react';
import config from 'config';
import { connect } from 'react-redux';
import { Breadcrumb, Icon, Tabs } from 'antd';
import StandardsList from './List';
import LegislationList from './Legislations/List';

@connect(({ account }) => ({
  permissions: account.adminPermissions
}))

export default class Standards extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      activeTab: '1'
    };
  }

  render() {
    const { permissions } = this.props;
    const { activeTab } = this.state;

    const accessUpdate = permissions.includes('accessStandardUpdateData');
    const accessRemove = permissions.includes('accessStandardRemoveData');
    const accessCompare = permissions.includes('accessStandardCompareData');
    const accessStandards = permissions.includes('accessStandards');
    const accessGuidances = permissions.includes('accessGuidances');
    const accessLegislations = permissions.includes('accessLegislations');

    return (
      <div>
        <Breadcrumb separator='>'>
          <Breadcrumb.Item href={`${config.rootRoute}/`}>Dashboard</Breadcrumb.Item>
          <Breadcrumb.Item>Standards</Breadcrumb.Item>
        </Breadcrumb>

        <Tabs
          defaultActiveKey='1'
          activeKey={activeTab}
          onChange={tab => this.setState({ activeTab: tab })}
          style={{ width: '100%' }}
        >
          <Tabs.TabPane
            tab={
              <span>
                <Icon type='profile' />
                Standards
              </span>
            }
            key='1'
          >
            <StandardsList
              isGuidance={false}
              accessUpdate={accessUpdate}
              accessRemove={accessRemove}
              accessCompare={accessCompare}
              accessStandards={accessStandards}
            />
          </Tabs.TabPane>

          <Tabs.TabPane
            tab={
              <span>
                <Icon type='profile' />
                Guidance
              </span>
            }
            key='2'
          >
            <StandardsList
              isGuidance
              accessUpdate={accessUpdate}
              accessRemove={accessRemove}
              accessCompare={accessCompare}
              accessGuidances={accessGuidances}
            />
          </Tabs.TabPane>

          <Tabs.TabPane
            tab={
              <span>
                <Icon type='profile' />
                Legislations
              </span>
            }
            key='3'
          >
            <LegislationList accessUpdate={accessUpdate} accessLegislations={accessLegislations} />
          </Tabs.TabPane>
        </Tabs>
      </div>
    );
  }
}
