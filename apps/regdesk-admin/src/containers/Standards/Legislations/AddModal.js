import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';
import config from 'config';
import {
  Form,
  Button,
  Input,
  DatePicker,
  Select,
  Modal,
  Switch,
  Icon,
  Collapse,
  Tree,
  Tooltip,
  Popconfirm,
  message,
} from 'antd';
import { Link } from 'react-router-dom';
import { allCountriesWithEU } from '../../../utils/countries';
import CountryFlag from '../../../components/CountryFlag';
import ModalUpload from '../../../components/Upload/Modal';
import AddLegislationModal from '../../../components/Form/Links/Modal';
import MarkDownEditor from '../../../components/Form/MarkDownEditor';
import api from '../../../utils/api';
import { FULL_DATE_FORMAT } from '../../../utils/date';
import { generateId } from '../../../components/Form/ItemId';
import styles from './index.less';

const FormItem = Form.Item;
const { Option } = Select;
const { Panel } = Collapse;
const { TreeNode } = Tree;

const PATH_NAMES = {
  // ccp
  updateTechnicalFile: 'Update Technical File (Non Significant)',
  notificationRequired: 'Notification Required (Significant)',
  reRegistration: 'Re-Registration (Significant)',
  updateTechnicalFileDT: 'Update Technical File (Non Significant) DT',
  notificationRequiredDT: 'Notification Required (Significant) DT',
  reRegistrationDT: 'Re-Registration (Significant) DT',
  generalInfo: 'General Info',
  laws: 'Laws',
};

@connect(({ account }) => ({
  availableCountries: account.adminCountriesForLegislation,
  limitCountries: account.adminLimitCountriesForLegislation,
}))

@Form.create()
export default class AddLegislation extends PureComponent {
  constructor(props) {
    super(props);

    this.formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 3 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 21 },
      },
    };

    this.submitFormLayout = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 21, offset: 3 },
      },
    };

    this.statuses = [
      { name: 'Current', value: 'current' },
      { name: 'Draft', value: 'draft' },
      { name: 'Superseded', value: 'superseded' },
    ];

    this.types = [
      { name: 'Regulation', value: 'regulation' },
      { name: 'Notice', value: 'notice' },
      { name: 'Circular', value: 'circular' },
      { name: 'Directive', value: 'directive' },
    ];

    this.modules = [
      { name: 'Device Regs', value: 'device-reg', module: 'md' },
      { name: 'Pharma Regs', value: 'pharma-reg', module: 'pharma' },
      { name: 'CCP', value: 'ccp', module: 'ccp' },
    ];

    const { legislation = {} } = this.props;

    const { isPublishDateDisabled } = legislation;

    this.state = {
      isNotAnnounced: isPublishDateDisabled,
      loading: false,
      legislationUsage: {},
      showModal: false,
      references: !legislation._id ? [{ key: generateId(), text: '' }] : [],
    };
  }

  componentDidMount() {
    const { legislation = {} } = this.props;

    if (legislation._id) {
      this.setState({ loading: true, references: legislation.references || [] });

      api.legislations
        .getUsage({ legislationId: legislation._id, countryId: legislation.countryId })
        .then(data => this.setState({ legislationUsage: data, loading: false }))
        .catch(() => this.setState({ loading: false }));
    }
  }

  renderTreeNodes = (data) => {
    return data.map((item) => (
      <TreeNode icon={<Icon type='file' />} title={item.title}>
        {item.children && this.renderTreeNodes(item.children)}
      </TreeNode>
    ));
  };

  getLegislationUsage = (referenceId) => {
    const { legislation } = this.props;
    const { legislationUsage } = this.state;

    if (Object.keys(legislationUsage).length === 0) return null;

    let {
      mdrContainerPath = {},
      mdrReleasePath = {},
      phrContainerPath = {},
      phrReleasePath = {},
      regulationControlPath = {},
      releaseRegulationControlPath = {},
    } = legislationUsage;

    if (referenceId) {
      mdrContainerPath = mdrContainerPath.references.map(item => item[referenceId]).filter(i => i);
      mdrReleasePath = mdrReleasePath.references.map(item => item[referenceId]).filter(i => i);
      phrContainerPath = phrContainerPath.references.map(item => item[referenceId]).filter(i => i);
      phrReleasePath = phrReleasePath.references.map(item => item[referenceId]).filter(i => i);
      regulationControlPath = regulationControlPath.references.map(item => item[referenceId]).filter(i => i);
      releaseRegulationControlPath = releaseRegulationControlPath.references.map(item => item[referenceId]).filter(i => i);
    } else {
      mdrContainerPath = mdrContainerPath.legislation;
      mdrReleasePath = mdrReleasePath.legislation;
      phrContainerPath = phrContainerPath.legislation;
      phrReleasePath = phrReleasePath.legislation;
      regulationControlPath = regulationControlPath.legislation;
      releaseRegulationControlPath = releaseRegulationControlPath.legislation;
    }

    if (
      mdrContainerPath.length === 0
      && mdrReleasePath.length === 0
      && phrContainerPath.length === 0
      && phrReleasePath.length === 0
      && regulationControlPath.length === 0
      && releaseRegulationControlPath.length === 0
    ) {
      return null;
    }

    const renderPathTree = (paths) => {
      return paths.map(path => (
        <Tree showIcon defaultExpandAll selectable={false}>
          <TreeNode icon={<Icon type='folder-open' />} title='Path'>
            {path.map((title, i) => (
              <TreeNode style={{ paddingLeft: i * 10 }} icon={<Icon type='file' />} title={PATH_NAMES[title] || title} />
            ))}
          </TreeNode>
        </Tree>
      ));
    };

    const regulations = [
      {
        header: 'Device reg unreleased',
        route: 'devicereg',
        path: mdrContainerPath,
      },
      {
        header: 'Device reg released',
        route: 'devicereg',
        path: mdrReleasePath,
      },
      {
        header: 'Pharma reg unreleased',
        route: 'pharmareg',
        path: phrContainerPath,
      },
      {
        header: 'Pharma reg released',
        route: 'pharmareg',
        path: phrReleasePath,
      },
      {
        header: 'CCP reg unreleased',
        route: 'cca',
        path: regulationControlPath,
      },
      {
        header: 'CCP reg released',
        route: 'cca',
        path: releaseRegulationControlPath,
      },
    ];

    return (
      <Collapse style={{ minWidth: 400 }}>
        {regulations.map(({ header, route, path = [] }) => {
          if (path.length === 0) return null;

          return (
            <Panel
              header={header}
              key={header}
              extra={<Link to={`${config.rootRoute}/${route}/${legislation.countryId}`} target='_blank'>Open Regulation</Link>}
            >
              {renderPathTree(path)}
            </Panel>
          );
        })}
      </Collapse>
    );
  };

  checkEditCountryDisabled = () => {
    const { legislation } = this.props;
    const { legislationUsage } = this.state;
    const { mdrContainerPath = [], mdrReleasePath = [], phrContainerPath = [], phrReleasePath = [], } = legislationUsage;

    if (
      legislation._id
      && (mdrContainerPath.length > 0
        || mdrReleasePath.length > 0
        || phrContainerPath.length > 0
        || phrReleasePath.length > 0)
    ) {
      return true;
    }

    return false;
  };

  getModuleName = () => {
    const { form } = this.props;
    const formType = form.getFieldValue('type');

    if (!formType) return null;

    const index = this.modules.findIndex(({ value }) => value === formType[0]);

    if (index === -1) return null;

    return this.modules[index].module;
  };

  onOldVersionSelect = ({ selectedLegislation: legislationId }) => {
    const { form } = this.props;

    this.setState({ loading: true, showModal: false });

    api.legislations
      .getById(legislationId)
      .then(({ legislation }) => form.setFieldsValue({ oldVersion: legislation }))
      .finally(() => this.setState({ loading: false }));
  };

  addReference = () => {
    const { references } = this.state;
    const newReferences = [...references, { key: generateId(), text: '' }];

    this.setState({ references: newReferences });
  }

  onReferenceChange = (index, value) => {
    const { references } = this.state;

    const newReferences = references.map((item, i) => {
      if (index === i) return { ...item, text: value };

      return item;
    });

    this.setState({ references: newReferences });
  }

  onReferenceRemove = (index) => {
    const { references } = this.state;

    const newReferences = references.filter((_, i) => index !== i);

    this.setState({ references: newReferences });
  }

  validateReferences = () => {
    const { references } = this.state;
    let isValid = true;

    if (references.length === 0) message.warn('Please enter reference');

    references.forEach(({ text }) => {
      if (!text) {
        message.warn('Reference should not be empty');
        isValid = false;
      }
    });

    return isValid;
  }

  handleSubmit = () => {
    const { legislation = {}, form, onConfirm } = this.props;
    const { references } = this.state;

    form.validateFieldsAndScroll((err, values) => {
      if (!err && this.validateReferences()) {
        onConfirm({
          ...legislation,
          ...values,
          oldVersion: values.oldVersion?._id,
          references,
        });
      }
    });
  };

  render() {
    const { loading, showModal, references = [], isNotAnnounced } = this.state;
    const {
      form,
      title,
      legislation = {},
      type,
      onCancel,
      limitCountries,
      availableCountries
    } = this.props;
    const { getFieldDecorator, getFieldValue, setFieldsValue } = form;
    const formIsCustomLaw = getFieldValue('isCustomLaw');
    const formCountryId = getFieldValue('countryId');
    const formModule = getFieldValue('modules');
    const formUrl = getFieldValue('url');
    const formOldVersion = getFieldValue('oldVersion');
    const isCountryDisabled = this.checkEditCountryDisabled();
    const module = this.getModuleName();
    let countries = allCountriesWithEU;

    if (limitCountries) countries = countries.filter(({ alpha3code }) => availableCountries.includes(alpha3code));

    return (
      <Modal
        visible
        title={title}
        onCancel={onCancel}
        width={1000}
        zIndex={100}
        destroyOnClose
        maskClosable={false}
        footer={null}
      >
        <Form onSubmit={this.handleSubmit}>
          {type === 'edit'
            ? (
              <FormItem label='Custom' {...this.formItemLayout}>
                {getFieldDecorator('isCustomLaw', {
                  initialValue: legislation.isCustomLaw || false,
                })(
                  <Popconfirm
                    title='Are you sure you want to proceed? This might affect visibility of the Legislation on the client side'
                    okText='Yes'
                    cancelText='No'
                    onConfirm={() => setFieldsValue({ isCustomLaw: !formIsCustomLaw })}
                  >
                    <Switch checked={formIsCustomLaw} />
                  </Popconfirm>
                )}
              </FormItem>
            )
            : (
              <FormItem label='Custom' {...this.formItemLayout}>
                {getFieldDecorator('isCustomLaw', {
                  initialValue: legislation.isCustomLaw || false,
                  valuePropName: 'checked',
                })(
                  <Switch />
                )}
              </FormItem>
            )
          }

          <FormItem label='Title' {...this.formItemLayout}>
            {getFieldDecorator('title', {
              rules: [{ required: true, message: 'Please enter title' }],
              initialValue: legislation.title || '',
            })(
              <Input placeholder='Title...' />
            )}
          </FormItem>

          <FormItem label='Description' {...this.formItemLayout}>
            {getFieldDecorator('description', {
              rules: [{ required: !formIsCustomLaw, message: 'Please enter Description' }],
              initialValue: legislation.description || '',
            })(
              <MarkDownEditor />
            )}
          </FormItem>

          {references.map(({ id, text, key }, index) => {
            const referenceUsage = this.getLegislationUsage(id || key);
            const isDeletedDisabled = referenceUsage || references.length === 1;

            return (
              <Form.Item
                {...this.formItemLayout}
                label={index === 0 ? 'References' : ''}
                required
                key={id || key}
                className={index === 0 ? '' : styles.referenceWithoutLabel}
              >
                <MarkDownEditor
                  value={text}
                  onChange={(value) => this.onReferenceChange(index, value)}
                />

                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <div>
                    {referenceUsage}

                    {index === references.length - 1 && (
                      <Button
                        style={{ marginTop: 10 }}
                        type='primary'
                        onClick={this.addReference}
                      >
                        Add
                      </Button>
                    )}
                  </div>

                  <Tooltip title={isDeletedDisabled ? 'Please remove the legislation with the reference from the source first!' : ''}>
                    <Popconfirm
                      disabled={isDeletedDisabled}
                      title='Are you sure you want to delete?'
                      onConfirm={() => this.onReferenceRemove(index)}
                      okText='Yes'
                      cancelText='No'
                    >
                      <Button disabled={loading || isDeletedDisabled}>
                        Delete
                      </Button>
                    </Popconfirm>
                  </Tooltip>
                </div>
              </Form.Item>
            );
          })}

          <FormItem label='Country' {...this.formItemLayout}>
            {getFieldDecorator('countryId', {
              rules: [{ required: true, message: 'Please select Country' }],
              initialValue: legislation.countryId || '',
            })(
              <Select
                showSearch
                allowClear
                disabled={isCountryDisabled}
                className={styles.selectCountry}
                placeholder='Select country'
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
              >
                {countries.map(({ alpha3code }) => (
                  <Option key={alpha3code} value={alpha3code}>
                    <Tooltip
                      placement='top'
                      title={isCountryDisabled ? 'Please remove associations from “Source To” before modifying this field!' : ''}
                    >
                      <div style={{ width: '100%' }}>
                        <CountryFlag countryId={alpha3code} fullName={false} />
                      </div>
                    </Tooltip>
                  </Option>
                ))}
              </Select>
            )}
          </FormItem>

          <FormItem label='Agency' {...this.formItemLayout}>
            {getFieldDecorator('agency', {
              rules: [{ required: !formIsCustomLaw, message: 'Please enter Agency' }],
              initialValue: legislation.agency || '',
            })(
              <Input placeholder='Agency...' />
            )}
          </FormItem>

          <FormItem label='Not Announced' {...this.formItemLayout}>
            {getFieldDecorator('isPublishDateDisabled', {
              initialValue: isNotAnnounced || false
            })(
              <Switch
                checked={isNotAnnounced}
                onChange={value => this.setState({ isNotAnnounced: value })}
              />
            )}
          </FormItem>

          <FormItem label='Publish Date' {...this.formItemLayout}>
            {getFieldDecorator('publishDate', {
              rules: [{ required: !isNotAnnounced, message: 'Please select Publish Date' }],
              initialValue: legislation.publishDate ? moment(legislation.publishDate) : null,
            })(
              <DatePicker
                format={FULL_DATE_FORMAT}
                disabled={isNotAnnounced}
                placeholder='Publish Date...'
              />
            )}
          </FormItem>

          <FormItem label='Type' {...this.formItemLayout}>
            {getFieldDecorator('type', {
              rules: [{ required: !formIsCustomLaw, message: 'Please select Type' }],
              initialValue: legislation.type || undefined,
            })(
              <Select
                showSearch
                allowClear
                placeholder='Select type'
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
              >
                {this.types.map(({ name, value }) => (
                  <Option key={value} value={value}>
                    {name}
                  </Option>
                ))}
              </Select>
            )}
          </FormItem>

          <FormItem label='Status' {...this.formItemLayout}>
            {getFieldDecorator('status', {
              rules: [{ required: !formIsCustomLaw, message: 'Please select Status' }],
              initialValue: legislation.status || undefined,
            })(
              <Select
                showSearch
                allowClear
                placeholder='Select status'
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
              >
                {this.statuses.map(({ name, value }) => (
                  <Option key={value} value={value}>
                    {name}
                  </Option>
                ))}
              </Select>
            )}
          </FormItem>

          <FormItem label='Modules' {...this.formItemLayout}>
            {getFieldDecorator('modules', {
              rules: [{ required: true, message: 'Please select Module' }],
              initialValue: legislation.modules || [],
            })(
              <Select
                showSearch
                allowClear
                mode='multiple'
                placeholder='Select module'
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
              >
                {this.modules.map(({ name, value }) => (
                  <Option key={value} value={value}>
                    {name}
                  </Option>
                ))}
              </Select>
            )}
          </FormItem>

          <FormItem label='Version number' {...this.formItemLayout}>
            {getFieldDecorator('version', {
              rules: [{ required: !formIsCustomLaw, message: 'Please enter Version Number' }],
              initialValue: legislation.version || '',
            })(
              <Input placeholder='Version number...' />
            )}
          </FormItem>

          <FormItem label='URL' {...this.formItemLayout}>
            {getFieldDecorator('url', {
              rules: [{ required: true, message: 'Please input URL' }],
              initialValue: legislation.url || '',
            })(
              <Tooltip placement='top' title={!module ? 'Please select Module' : ''}>
                <div style={{ width: '100%' }}>
                  <Input
                    value={formUrl}
                    onChange={(e) => form.setFieldsValue({ url: e.target.value })}
                    disabled={!module}
                    placeholder='Url...'
                    autoComplete='off'
                    addonAfter={<Icon type='upload' onClick={()=> { this.uploadModal && this.uploadModal.showModal(true); }} />}
                  />
                </div>
              </Tooltip>
            )}
          </FormItem>

          <FormItem label='Summarize' {...this.formItemLayout}>
            {getFieldDecorator('summarize', {
              rules: [{ required: false }],
              initialValue: legislation.summarize || '',
            })(
              <MarkDownEditor />
            )}
          </FormItem>

          <FormItem label='Old Version' {...this.formItemLayout}>
            {getFieldDecorator('oldVersion', {
              initialValue: legislation.oldVersion || null,
            })(
              <Tooltip
                placement='topLeft'
                title={(!formModule?.length || !formCountryId) ? 'Please select Module And Country' : ''}
              >
                <div style={{ width: '100%' }}>
                  <Button
                    size='small'
                    icon='plus'
                    type='primary'
                    disabled={!formModule?.length || !formCountryId}
                    style={{ marginBottom: 10 }}
                    onClick={() => this.setState({ showModal: true })}
                  >
                    Select
                  </Button>

                  <Button
                    size='small'
                    type='danger'
                    disabled={!formOldVersion}
                    style={{ marginLeft: 5 }}
                    onClick={() => form.setFieldsValue({ oldVersion: undefined })}
                  >
                    Remove
                  </Button>

                  {formOldVersion && (
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                      <span style={{ lineHeight: 1.6 }}>Title: {formOldVersion.title}</span>
                      <span style={{ lineHeight: 1.6 }}>Version: {formOldVersion.version}</span>
                    </div>
                  )}
                </div>
              </Tooltip>
            )}
          </FormItem>

          {legislation._id && (
            <FormItem label='Source To' {...this.formItemLayout}>
              {this.getLegislationUsage()}
            </FormItem>
          )}

          <FormItem style={{ marginBottom: 0 }} {...this.submitFormLayout}>
            <Button onClick={onCancel}>Cancel</Button>

            <Button
              style={{ marginLeft: 8 }}
              type='primary'
              onClick={this.handleSubmit}
              disabled={loading}
              loading={loading}
            >
              {type === 'edit' ? 'Update' : 'Add'}
            </Button>
          </FormItem>
        </Form>

        <ModalUpload
          ref={ref => { this.uploadModal = ref; }}
          onUpload={url => form.setFieldsValue({ url })}
          info={{ module, countryId: formCountryId }}
        />

        <AddLegislationModal
          params={{ countryId: formCountryId, module: formModule }}
          showModal={showModal}
          onSubmit={this.onOldVersionSelect}
          onCloseModal={() => this.setState({ showModal: false })}
          onCancel={() => this.setState({ showModal: false })}
          skipLegislations={[legislation._id]}
          legislationOnly
        />
      </Modal>
    );
  }
}
