import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, Input, message, Row, Select, Table, Tooltip, Icon, Divider } from 'antd';
import { connect } from 'react-redux';
import moment from 'moment';
import AddLegislation from './AddModal';
import CountryFlag from '../../../components/CountryFlag';
import api from '../../../utils/api';
import styles from '../index.less';

const { Option } = Select;
const { Search } = Input;

@connect(({ account }) => ({
  su: account.su,
}))

export default class Legislations extends React.Component {
  constructor(props) {
    super(props);

    this.defaultPagination = {
      current: 1,
      pageSize: 10,
      total: 0,
    };

    this.missingFields = [
      { name: 'Country', value: 'countryId' },
      { name: 'Agency', value: 'agency' },
      { name: 'Published Date', value: 'publishDate' },
      { name: 'Version', value: 'version' },
      { name: 'Summarize', value: 'summarize' },
      { name: 'References', value: 'references' },
      { name: 'Document', value: 'url' },
      { name: 'Status', value: 'status' },
      { name: 'Category', value: 'category' },
      { name: 'Module', value: 'type' },
    ];

    this.state = {
      showAddModal: false,
      legislation: {},
      addType: 'add',
      list: [],
      loading: false,
      filters: {
        searchValue: '',
        missingFields: [],
      },
    };
  }

  componentDidMount() {
    this.getLegislations();
  }

  getLegislations = ({ pagination = this.defaultPagination } = {}) => {
    const { filters } = this.state;

    this.setState({ loading: true });

    api.legislations
      .get({ pagination, filters })
      .then(data => this.setState({ ...data, loading: false }))
      .catch(() => this.setState({ loading: false }));
  }

  onSearch = (newFilters) => {
    this.setState(
      (prevState) => ({ filters: { ...prevState.filters, ...newFilters } }),
      this.getLegislations,
    );
  }

  getColumns = () => {
    const { accessUpdate, su } = this.props;

    return [
      {
        title: 'Title',
        dataIndex: 'title',
        render: (title) => <span>{title}</span>,
      },
      {
        title: 'Country',
        dataIndex: 'countryId',
        render: (countryId) => <CountryFlag countryId={countryId} fullName={false} />,
      },
      {
        title: 'Agency',
        dataIndex: 'agency',
        render: (agency) => <span>{agency}</span>,
      },
      {
        title: 'Published Date',
        dataIndex: 'publishDate',
        width: '120px',
        render: (publishDate, { isPublishDateDisabled }) => (
          <span>{isPublishDateDisabled ? 'Not Announced' : moment(publishDate).getUTC()}</span>
        ),
      },
      {
        title: 'Custom',
        dataIndex: 'isCustomLaw',
        render: (isCustomLaw) => <span>{isCustomLaw ? 'Yes' : 'No'}</span>,
      },
      {
        title: 'Action',
        key: 'Action',
        width: '300px',
        render: (text, item) => (
          <div>
            <Tooltip title={!accessUpdate && 'No permission'}>
              <a className={!accessUpdate ? styles.disabled : ''} onClick={() => accessUpdate && this.edit(item)}>
                Edit
              </a>
            </Tooltip>

            {su && <Divider type='vertical' /> }

            {su && (
              <Tooltip title='Only for SU! Update law in all tasks ССP'>
                <a
                  onClick={() => {
                    const hide = message.loading('Action in progress..', 0);

                    api.ccpTask.updateLow({ id: item._id }).then(() => { hide(); message.success('Done'); });
                  }}
                >
                  Update CCP
                </a>
              </Tooltip>
            )}
          </div>
        ),
      },
    ];
  }

  edit = (record) => this.setState({ addType: 'edit', showAddModal: true, legislation: record });

  add = () => this.setState({ addType: 'add', showAddModal: true });

  handleOk(newLegislation) {
    const { addType, pagination } = this.state;

    if (addType === 'edit') {
      api.legislations.update(newLegislation).then(() => {
        this.getLegislations({ pagination });
        message.success('Edit success!');
        this.setState({ showAddModal: false, legislation: {} });
      });
    } else {
      api.legislations.add(newLegislation).then(() => {
        this.getLegislations({ pagination });
        message.success('Add success!');
        this.setState({ showAddModal: false, legislation: {} });
      });
    }
  }

  render() {
    const { accessLegislations } = this.props;
    const {
      loading,
      pagination,
      filters,
      list,
      addType,
      showAddModal,
      legislation,
    } = this.state;

    if (!accessLegislations) {
      return (
        <Card bordered={false} bodyStyle={{ height: 'calc(100vh - 239px)', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: 18 }}>
          <Icon type='lock' style={{ fontSize: 20, marginRight: 8 }} /> No Module Access!
        </Card>
      );
    }

    return (
      <div>
        <Card bordered={false}>
          <Row>
            <div className={styles.filtersContainer}>
              <Col span={5} style={{ marginRight: '20px' }}>
                <Search
                  allowClear
                  loading={loading}
                  className={styles.searchStandard}
                  value={filters.searchValue}
                  placeholder='Search By Name...'
                  onChange={(e) => this.onSearch({ searchValue: e.target.value })}
                />
              </Col>

              <Col span={5}>
                <Select
                  showSearch
                  allowClear
                  loading={loading}
                  value={filters.missingFields}
                  mode='multiple'
                  style={{ width: '100%' }}
                  placeholder='Select missing fields'
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  onChange={(value) => this.onSearch({ missingFields: value })}
                >
                  {this.missingFields.map(({ name, value }) => (
                    <Option key={value} value={value}>
                      {name}
                    </Option>
                  ))}
                </Select>
              </Col>
            </div>

            <Button
              style={{ marginLeft: '20px' }}
              type='primary'
              onClick={() => this.add()}
            >
              Add Legislation
            </Button>
          </Row>

          <div style={{ marginTop: 20 }} />

          <Table
            rowKey='_id'
            columns={this.getColumns()}
            dataSource={list}
            loading={loading}
            pagination={{
              defaultPageSize: 10,
              size: 'Pagination',
              ...pagination,
            }}
            onChange={(newPagination) => this.getLegislations({ pagination: newPagination })}
          />
        </Card>

        {showAddModal && (
          <AddLegislation
            title={addType === 'edit' ? 'Edit Legislation' : 'Add Legislation'}
            legislation={legislation}
            type={addType}
            onConfirm={(newLegislation) => this.handleOk(newLegislation)}
            onCancel={() => this.setState({ showAddModal: false, legislation: {} })}
          />
        )}
      </div>
    );
  }
}
