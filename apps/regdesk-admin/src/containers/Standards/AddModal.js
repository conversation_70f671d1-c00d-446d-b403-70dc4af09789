import React, { PureComponent } from 'react';
import moment from 'moment';
import { Form, Button, Input, DatePicker, Select, Modal } from 'antd';
import { allCountriesWithEU } from '../../utils/countries';
import CountryFlag from '../../components/CountryFlag';
import { FULL_DATE_FORMAT } from '../../utils/date';

const FormItem = Form.Item;
const { Option } = Select;

@Form.create()
export default class AddStandard extends PureComponent {
  constructor(props) {
    super(props);

    this.formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 },
      },
    };

    this.submitFormLayout = {
      wrapperCol: {
        xs: { span: 24, offset: 0 },
        sm: { span: 14, offset: 6 },
      },
    };

    this.types = ['Country Specific', 'International', 'Guidance document'];
    this.statuses = ['Active', 'Under Development', 'Canceled'];

    this.categories = [
      { name: 'Product Specific', value: 'Product' },
      { name: 'Quality and Process Management', value: 'QPM' },
    ];
  }

  handleSubmit = () => {
    const { standard, form, onConfirm } = this.props;

    form.validateFieldsAndScroll((err, values) => {
      if (!err) onConfirm({ ...standard, ...values });
    });
  };

  render() {
    const {
      form,
      title,
      standard = {},
      isGuidance,
      type,
      groups = [],
      organizations = [],
      onCancel,
    } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    const formStandardType = getFieldValue('standardType');
    const countryRequired = ['Country Specific', 'Guidance document'].includes(formStandardType);

    return (
      <Modal
        visible
        title={title}
        onCancel={onCancel}
        width='95%'
        destroyOnClose
        footer={null}
      >
        <Form onSubmit={this.handleSubmit}>
          <FormItem label='Title' {...this.formItemLayout}>
            {getFieldDecorator('title', {
              rules: [{ required: true, message: 'Please enter title' }],
              initialValue: standard.title || '',
            })(
              <Input placeholder='Title...' />
            )}
          </FormItem>

          {type === 'add' && (
            <FormItem label='Standard Type' {...this.formItemLayout}>
              {getFieldDecorator('standardType', {
                rules: [{ required: true, message: 'Please select Standard Type' }],
                initialValue: standard.standardType || '',
              })(
                <Select
                  showSearch
                  allowClear
                  style={{ width: '100%', marginRight: '20px' }}
                  placeholder='Select type of standard'
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                >
                  {this.types.map((item) => (
                    <Option key={item} value={item}>
                      {item}
                    </Option>
                  ))}
                </Select>
              )}
            </FormItem>
          )}

          {type === 'add' && (
            <FormItem label='Country' {...this.formItemLayout}>
              {getFieldDecorator('countryId', {
                rules: [{ required: countryRequired, message: 'Please select Country' }],
                initialValue: standard.countryId || '',
              })(
                <Select
                  showSearch
                  allowClear
                  style={{ width: '100%', marginRight: '20px' }}
                  disabled={formStandardType === 'International'}
                  placeholder='Select country'
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                >
                  {allCountriesWithEU.map(({ alpha3code }) => (
                    <Option key={alpha3code} value={alpha3code}>
                      <CountryFlag countryId={alpha3code} fullName={false} />
                    </Option>
                  ))}
                </Select>
              )}
            </FormItem>
          )}

          {isGuidance
            ? (
              <FormItem label='Docket Number' {...this.formItemLayout}>
                {getFieldDecorator('docketNumber', {
                  rules: [{ required: true, message: 'Please enter Docket Number' }],
                  initialValue: standard.docketNumber || '',
                })(
                  <Input placeholder='Docket Number' />
                )}
              </FormItem>
            )
            : (
              <FormItem label='Designation Number' {...this.formItemLayout}>
                {getFieldDecorator('designationNumber', {
                  rules: [{ required: true, message: 'Please enter Designation Number' }],
                  initialValue: standard.designationNumber || '',
                })(
                  <Input placeholder='Designation Number' />
                )}
              </FormItem>
            )}

          <FormItem label='Group' {...this.formItemLayout}>
            {getFieldDecorator('group', {
              rules: [{ required: true, message: 'Please select Group' }],
              initialValue: standard.group || '',
            })(
              <Select
                showSearch
                allowClear
                style={{ width: '100%', marginRight: '20px' }}
                placeholder='Select group'
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
              >
                {groups.map((group) => (
                  <Option key={group} value={group}>
                    {group}
                  </Option>
                ))}
              </Select>
            )}
          </FormItem>

          <FormItem label='Organization' {...this.formItemLayout}>
            {getFieldDecorator('organization', {
              rules: [{ required: true, message: 'Please select Organization' }],
              initialValue: standard.organization || '',
            })(
              <Select
                showSearch
                allowClear
                style={{ width: '100%', marginRight: '20px' }}
                placeholder='Select organization'
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
              >
                {organizations.map((organization) => (
                  <Option key={organization} value={organization}>
                    {organization}
                  </Option>
                ))}
              </Select>
            )}
          </FormItem>

          <FormItem label='Link of Standard' {...this.formItemLayout}>
            {getFieldDecorator('standardUrl', {
              rules: [{ required: true, message: 'Please enter Standard URL' }],
              initialValue: standard.standardUrl || '',
            })(
              <Input placeholder='Link of Standard' />
            )}
          </FormItem>

          <FormItem label='Link of Standard Organization' {...this.formItemLayout}>
            {getFieldDecorator('standardOrganizationUrl', {
              rules: [{ required: true, message: 'Please enter Standard Organization URL' }],
              initialValue: standard.standardOrganizationUrl || '',
            })(
              <Input placeholder='Link of Standard Organization' />
            )}
          </FormItem>

          <FormItem label='Status' {...this.formItemLayout}>
            {getFieldDecorator('status', {
              rules: [{ required: true, message: 'Please select Status' }],
              initialValue: standard.status || '',
            })(
              <Select
                showSearch
                allowClear
                style={{ width: '100%', marginRight: '20px' }}
                placeholder='Select status'
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
              >
                {this.statuses.map((status) => (
                  <Option key={status} value={status}>
                    {status}
                  </Option>
                ))}
              </Select>
            )}
          </FormItem>

          {!isGuidance && (
            <FormItem label='Category' {...this.formItemLayout}>
              {getFieldDecorator('category', {
                rules: [{ required: true, message: 'Please select Category' }],
                initialValue: standard.category || '',
              })(
                <Select
                  showSearch
                  allowClear
                  style={{ width: '100%', marginRight: '20px' }}
                  placeholder='Select category'
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                >
                  {this.categories.map((category) => (
                    <Option key={category} value={category.value}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              )}
            </FormItem>
          )}

          <FormItem label='Recognition Date' {...this.formItemLayout}>
            {getFieldDecorator('recognitionDate', {
              rules: [{ required: true, message: 'Please select Recognition Date' }],
              initialValue: standard.recognitionDate ? moment(standard.recognitionDate): null,
            })(
              <DatePicker format={FULL_DATE_FORMAT} placeholder='Date...' />
            )}
          </FormItem>

          <FormItem style={{ marginBottom: 0 }} {...this.submitFormLayout}>
            <Button onClick={onCancel}>Cancel</Button>

            <Button
              style={{ marginLeft: 8 }}
              type='primary'
              onClick={this.handleSubmit}
            >
              {type === 'edit' ? 'Update' : 'Add'}
            </Button>
          </FormItem>
        </Form>
      </Modal>
    );
  }
}
