import React from 'react';
import { Tree, Icon } from 'antd';
import { connect } from 'react-redux';
import actions from '../../../actions';
import { generateId } from '../../../components/Form/ItemId';

const { TreeNode } = Tree;

@connect(({ phr }) => ({
  loading: phr.loading,
  item: phr.item,
}))

class Menu extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      expandedKeys: ['agencies', 'drugs', 'biologics', 'combinationProducts', 'api', 'excipients', 'additionalInformation'],
    };
  }

  /**
   * Update phr
   * @param data
   * @returns {Promise<any>}
   */
  update = (data) => {
    const { onUpdate } = this.props;

    if (onUpdate) onUpdate(data);
  }

  /**
   * Add new Agency
   */
  addAgency = () => {
    const { expandedKeys } = this.state;
    const { agencies } = this.props.item;

    agencies.push({
      id: generateId(),
      name: 'New Agency',
      website: '',
      contact: '',
      email: '',
      address: '',
      zip: '',
      customFields: [],
    });

    actions.phr.change({ item: { ...this.props.item, agencies } });

    if (!expandedKeys.includes('0-0')) this.setState({ expandedKeys: expandedKeys.concat('0-0') });

    this.forceUpdate();
  };

  /**
   * Select item menu
   * @param key
   * @param info
   */
  handleSelect = (key, info) => {
    const { type, item, itemTitle, fields, itemKey, onUpdate, onRemove, onAutoSave } = info.node.props;
    const { onSelect } = this.props;

    if (type || itemTitle) {
      onSelect({
        type,
        item,
        fields,
        itemTitle,
        itemKey,
        onUpdate,
        onRemove,
        onAutoSave,
      });
    }
  };

  /**
   * Get title for Tree
   * @param title
   * @param check
   * @param comment
   * @returns {JSX.Element}
   */
  renderTitle = (title, check, comment) => {
    let color = 'inherit';

    if (check === false) color = '#ff9800';
    if (check === false && comment) color = '#ff5722';
    if (check === true) color = '#4caf50';

    return (
      <span style={{ color }}>
        {title.length > 30 ? `${title.slice(0, 30)}...` : title}
      </span>
    );
  };

  render() {
    const { item, loading, onCloseEditor } = this.props;
    const {
      agencies = [],
      excipients = {},
      additionalInformation = {},
    } = item || {};

    return (
      <div>
        <Tree
          showLine
          onSelect={this.handleSelect}
          expandedKeys={this.state.expandedKeys}
          defaultExpandAll={false}
          onExpand={expandedKeys => this.setState({ expandedKeys })}
          draggable
        >
          {/* -------------- */}
          {/* ---Agencies--- */}
          {/* -------------- */}

          <TreeNode
            key='agencies'
            title={
              <span className='b-hover'>
                Agencies&nbsp;
                <Icon
                  type='plus-circle'
                  style={{ fontSize: 14 }}
                  onClick={() => this.addAgency()}
                />
              </span>
            }
            loading={loading}
            disabled={loading}
            item={agencies}
            itemTitle={{ first: 'Agencies' }}
          >
            {
              agencies.map((data, i) =>
                <TreeNode
                  key={data.id}
                  loading={loading}
                  disabled={loading}
                  title={this.renderTitle(data.name, data.checked, data.comment)}
                  fields={[
                    { key: 'id', type: 'id' },
                    { key: 'name', type: 'input', label: 'Name' },
                    { key: 'website', type: 'input', label: 'Website' },
                    { key: 'contact', type: 'input', label: 'Phone/Fax' },
                    { key: 'email', type: 'input', label: 'Email' },
                    { key: 'address', type: 'input', label: 'Address' },
                    { key: 'zip', type: 'input', label: 'Zip' },
                    { key: 'customFields', type: 'customFields' },
                  ]}
                  item={data}
                  itemTitle={{ first: 'Agencies', second: data.name }}
                  onUpdate={(newData) => {
                    this.props.onSelect({ item: newData });
                    agencies[i] = newData;
                    this.update({ agencies });
                  }}
                  onAutoSave={(newData) => {
                    agencies[i] = newData;
                    this.update({ agencies });
                  }}
                  onRemove={() => {
                    agencies.splice(i, 1);
                    this.update({ agencies });
                    onCloseEditor();
                  }}
                />
              )
            }
          </TreeNode>

          {/* ------------------------------------------------------- */}
          {/* ----- Drugs, Biologics, Combination Products, API ----- */}
          {/* ------------------------------------------------------- */}

          {
            [
              { title: 'Drugs', key: 'drugs' },
              { title: 'Biologics', key: 'biologics' },
              { title: 'Combination Products', key: 'combinationProducts' },
              { title: 'API', key: 'api' },
            ].map(({ key, title }) => {
              const data = item[key] || {};

              return (
                <TreeNode
                  key={key}
                  loading={loading}
                  disabled={loading}
                  title={this.renderTitle(title, data.checked, data.comment)}
                  itemTitle={{ first: title }}
                  item={data}
                  fields={[
                    { key: 'regulationStatus', type: 'input', label: 'Regulation Status' },
                    { key: 'definition', type: 'markDown', label: 'Definition' },
                    { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                  ]}
                  onAutoSave={(newData) => {
                    this.update({ [key]: { ...data, ...newData } });
                  }}
                  onUpdate={(newData) => {
                    this.props.onSelect({ item: newData });
                    this.update({ [key]: { ...data, ...newData } });
                  }}
                >
                  {
                    [
                      { title: 'Drugs Approval Routes', key: 'drugApproval' },
                      { title: 'PMS', key: 'pms' },
                      { title: 'Clinical Trial', key: 'clinicalTrial' },
                      { title: 'Summary', key: 'summary' },
                    ].map((i) => {
                      const fields = [];
                      const itemData = data[i.key] || {};

                      if (key === 'api' && ['clinicalTrial', 'summary'].includes(i.key)) {
                        return null;
                      }

                      if (['drugApproval'].includes(i.key)) {
                        fields.push(
                          { key: 'requirements', type: 'markDown', label: 'Requirements' },
                          { key: 'approvals', type: 'approvalRoutes' },
                        );
                      }

                      if (['pms'].includes(i.key)) {
                        fields.push(
                          { key: 'definition', type: 'markDown', label: 'Definition' },
                          { key: 'procedure', type: 'markDown', label: 'Procedure' },
                          { key: 'requirements', type: 'markDown', label: 'Requirements' },
                          { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                          { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                          { key: 'sample', type: 'sample', title: 'Sample' },
                          { key: 'approvals', type: 'approvalRoutes' },
                        );
                      }

                      if (['clinicalTrial'].includes(i.key)) {
                        fields.push(
                          { key: 'definition', type: 'markDown', label: 'Definition' },
                          { key: 'procedure', type: 'markDown', label: 'Procedure' },
                          { key: 'requirements', type: 'markDown', label: 'Requirements' },
                          { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
                          { key: 'checklist', type: 'procedure', label: 'Checklist' },
                          { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                          { key: 'sample', type: 'sample', title: 'Sample' },
                        );
                      }

                      if (['summary'].includes(i.key)) {
                        fields.push({ key: 'flowChart', type: 'markDown', label: 'Flow Chart' });
                      }

                      return (
                        <TreeNode
                          key={key + i.key}
                          loading={loading}
                          disabled={loading}
                          title={this.renderTitle(i.title, itemData.checked, itemData.comment)}
                          item={itemData}
                          itemTitle={{ first: title, second: i.title }}
                          fields={fields}
                          onUpdate={(newData) => {
                            this.props.onSelect({ item: newData });
                            data[i.key] = newData;
                            this.update({ [key]: data });
                          }}
                          onAutoSave={(newData) => {
                            const { item: propsItem } = this.props;
                            const propsData = propsItem[key];

                            propsData[i.key] = newData;
                            this.update({ [key]: propsData });
                          }}
                        />
                      );
                    })
                  }
                </TreeNode>
              );
            })
          }

          {/* ----------------- */}
          {/* ----Excipients--- */}
          {/* ----------------- */}

          <TreeNode
            key='excipients'
            loading={loading}
            disabled={loading}
            title={this.renderTitle('Excipients', excipients.checked, excipients.comment)}
            itemTitle={{ first: 'Excipients' }}
            item={excipients}
            fields={[
              { key: 'regulationStatus', type: 'input', label: 'Regulation Status' },
              { key: 'definition', type: 'markDown', label: 'Definition' },
              { key: 'procedure', type: 'markDown', label: 'Procedure' },
              { key: 'requirements', type: 'markDown', label: 'Requirements' },
              { key: 'additionalInformation', type: 'markDown', label: 'Additional Information' },
              { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
              { key: 'sample', type: 'sample', title: 'Sample', addTitle: 'Add Sample' },
            ]}
            onUpdate={(newData) => {
              this.props.onSelect({ item: newData });
              this.update({ excipients: newData });
            }}
            onAutoSave={(newData) => {
              this.update({ excipients: newData });
            }}
          />

          {/* ------------------------ */}
          {/* -Additional Information- */}
          {/* ------------------------ */}

          <TreeNode
            key='additionalInformation'
            loading={loading}
            disabled={loading}
            title='Additional Information'
            itemTitle={{ first: 'Additional Information' }}
            item={additionalInformation}
          >
            {
              [
                { title: 'Labeling', key: 'labeling' },
                { title: 'PIC/S Certification', key: 'picsCertification' },
                { title: 'Manufacturing License', key: 'manufacturingLicense' },
                { title: 'Licenses and Certificates', key: 'generalLicenses' },
                { title: 'GXPs', key: 'gxps' },
                { title: 'Post-Approval Variations', key: 'postApprovalVariations' },
                { title: 'Advertisement and Promotion', key: 'advertisementAndPromotion' },
                { title: 'Renewal', key: 'renewal' },
                { title: 'Recall', key: 'recall' },
                { title: 'Importation', key: 'importation' },
                { title: 'Timeline and Fees', key: 'timelineAndFees' },
                { title: 'e-Submission', key: 'eSubmission' },
                { title: 'Plant Master File', key: 'plantMasterFile' },
              ].map(({ key, title }) => {
                const fields = [{ key: 'regulationStatus', type: 'input', label: 'Regulation Status' }];
                const data = additionalInformation[key] || {};

                if (['labeling'].includes(key)) {
                  fields.push(
                    { key: 'language', type: 'markDown', label: 'Language' },
                    { key: 'outerLabel', type: 'markDown', label: 'Outer Label' },
                    { key: 'innerLabel', type: 'markDown', label: 'Inner Label' },
                    { key: 'ifu', type: 'markDown', label: 'IFU' },
                  );
                }

                if ([
                  'picsCertification',
                  'manufacturingLicense',
                  'gxps',
                  'postApprovalVariations',
                  'advertisementAndPromotion',
                  'renewal',
                ].includes(key)) {
                  fields.push({ key: 'definition', type: 'markDown', label: 'Definition' });
                }

                if ([
                  'picsCertification',
                  'manufacturingLicense',
                  'gxps',
                  'eSubmission',
                  'plantMasterFile',
                ].includes(key)) {
                  fields.push({ key: 'procedure', type: 'markDown', label: 'Procedure' });
                }

                if ([
                  'picsCertification',
                  'manufacturingLicense',
                  'advertisementAndPromotion',
                  'renewal',
                  'recall',
                  'importation',
                  'plantMasterFile',
                ].includes(key)) {
                  fields.push({ key: 'generalRequirements', type: 'markDown', label: 'General requirements' });
                }

                if (['gxps'].includes(key)) {
                  fields.push({ key: 'requirements', type: 'markDown', label: 'Requirements' });
                }

                if (['postApprovalVariations'].includes(key)) {
                  fields.push({ key: 'procedure', type: 'markDown', label: 'Procedure of Change' });
                }

                if ([
                  'picsCertification',
                  'manufacturingLicense',
                  'postApprovalVariations',
                  'advertisementAndPromotion',
                  'renewal',
                  'recall',
                  'importation',
                ].includes(key)) {
                  fields.push({ key: 'additionalInformation', type: 'markDown', label: 'Additional Information' });
                }

                if (['timelineAndFees'].includes(key)) {
                  fields.push({ key: 'feesAndTimeline', type: 'markDown', label: 'Fees & Timeline' });
                }

                if ([
                  'labeling',
                  'picsCertification',
                  'manufacturingLicense',
                  'gxps',
                  'postApprovalVariations',
                  'advertisementAndPromotion',
                  'renewal',
                  'recall',
                  'importation',
                  'timelineAndFees',
                  'eSubmission',
                  'plantMasterFile'
                ].includes(key)) {
                  fields.push(
                    { key: 'laws', type: 'links', title: 'Laws', addTitle: 'Add law' },
                    { key: 'sample', type: 'sample', title: 'Sample' },
                  );
                }

                if (['generalLicenses'].includes(key)) {
                  fields.push({ key: 'licenses', type: 'licenses' });
                }

                return (
                  <TreeNode
                    key={key}
                    loading={loading}
                    disabled={loading}
                    title={this.renderTitle(title, data.checked, data.comment)}
                    item={data}
                    itemTitle={{ first: 'Additional Information', second: title }}
                    fields={fields}
                    onUpdate={(newData) => {
                      this.props.onSelect({ item: newData });
                      additionalInformation[key] = newData;
                      this.update({ additionalInformation });
                    }}
                    onAutoSave={(newData) => {
                      const { item: propsItem } = this.props;
                      const propsData = propsItem.additionalInformation;

                      propsData[key] = newData;
                      this.update({ additionalInformation: propsData });
                    }}
                  />
                );
              })
            }
          </TreeNode>
        </Tree>
      </div>
    );
  }
}

export default Menu;
