import React, { useReducer, useEffect } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';
import config from 'config';
import { Button, message, Divider, DatePicker, Popconfirm, Card, Input, Table, Tooltip } from 'antd';
import Breadcrumb from '../../../components/Breadcrumb';
import Modal from './Modal';
import { LEGACY_DATE_FORMAT } from '../../../utils/date';
import api from '../../../utils/api';
import styles from '../../../components/Form/index.less';

let timerSearch;
const { RangePicker } = DatePicker;
const { Search } = Input;

const initialState = {
  list: [],
  filters: {},
  pagination: {},
  loading: false,
  report: null,
  showModal: false,
};

const reducer = (state, action) => {
  const { type, payload } = action || {};
  const { list, pagination, filters, report } = payload || {};

  switch (type) {
    case 'getData':
      return { ...state, loading: false, list, filters, pagination };
    case 'startLoading':
      return { ...state, loading: true };
    case 'endLoading':
      return { ...state, loading: false };
    case 'addReport':
      return { ...state, showModal: true };
    case 'editReport':
      return { ...state, showModal: true, report };
    case 'closeModal':
      return { ...state, showModal: false, report: null };
    case 'reset':
      return { ...initialState };
    default:
      return state;
  }
};

const PHRReports = ({ permissions }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const { list = [], filters = {}, pagination = {}, loading, showModal, report } = state;
  const accessUpdate = permissions.includes('accessPharmaRegUpdateReports');

  /**
   * Get list
   * @param {Object} newFilters
   * @param {Object} newPagination
   */
  const get = (newFilters, newPagination) => {
    const params = {
      filters: newFilters || {},
      pagination: newPagination || {},
    };

    dispatch({ type: 'startLoading' });

    api.phrReport
      .get(params)
      .then((data = {}) => {
        dispatch({
          type: 'getData',
          payload: {
            list: data.list || {},
            filters: data.filters || {},
            pagination: data.pagination || {},
          }
        });
      })
      .catch(() => {
        dispatch({ type: 'endLoading' });
      });
  };

  /**
   * Update Pagination
   * @param {Object} newPagination
   */
  const onUpdatePagination = newPagination => {
    get(filters, { ...pagination, ...newPagination });
  };

  /**
   * Update filters
   * @param {Object} newFilters
   */
  const onUpdateFilter = newFilters => {
    get({ ...filters, ...newFilters }, { current: 1 });
  };

  /**
   * Clean filter
   * @param {string} name
   */
  const onCleanFilter = name => {
    const { ...newFilters } = filters;

    delete newFilters[name];
    get(newFilters, { current: 1 });
  };

  /**
   * Search
   * @param {?string} val
   */
  const onSearch = val => {
    if (!val) onCleanFilter('searchValue');
    else onUpdateFilter({ ...filters, searchValue: val });
  };

  /**
   * Close Modal
   */
  const onCloseModal = () => dispatch({ type: 'closeModal' });

  /**
   * Remove
   * @param {string} id
   */
  const onRemove = (id, title) => {
    dispatch({ type: 'startLoading' });

    api.phrReport
      .remove(id)
      .then(() => {
        const { current = 1, total = 0, pageSize = 10 } = pagination;

        if (total % pageSize === 1) onUpdatePagination({ current: current - 1 });
        else get();

        message.success(`Report "${title}" has been removed`);
      });
  };

  /**
   * Handle Submit
   * @param {Object} data
   */
  const handleSubmit = (data = {}) => {
    const { _id, title } = data;

    if (_id) {
      api.phrReport
        .update(_id, { data })
        .then(() => {
          onCloseModal();
          get();
          message.success(`Report "${title}" has been updated`);
        });
    } else {
      api.phrReport
        .add(data)
        .then(() => {
          onCloseModal();
          get();
          message.success(`Report "${title}" has been uploaded`);
        });
    }
  };

  /**
   * Cmp mount
   */
  useEffect(() => {
    get();
  }, []);

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: '_id',
    },
    {
      title: 'Update Time',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 300,
      render: updatedAt => moment(updatedAt).getFullUTC(),
    },
    {
      title: 'Created Time',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 300,
      render: createdAt => moment(createdAt).getFullUTC(),
    },
    {
      title: 'Actions',
      dataIndex: '_id',
      key: '_id',
      width: 200,
      render: (id, item) => {
        return (
          <div>
            <Tooltip title={!accessUpdate && 'No permission'}>
              <a
                className={!accessUpdate ? styles.disabled : ''}
                onClick={() => accessUpdate && dispatch({ type: 'editReport', payload: { report: item } })}
              >
                Update
              </a>
            </Tooltip>

            <Divider type='vertical' />

            <Tooltip title={!accessUpdate && 'No permission'}>
              <Popconfirm
                title='Are you sure delete this Regulation Report?'
                onConfirm={() => onRemove(id, item.title)}
                disabled={!accessUpdate}
              >
                <a className={!accessUpdate ? styles.disabled : ''} style={{ color: accessUpdate && 'red' }}>
                  Delete
                </a>
              </Popconfirm>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  return (
    <div className={styles.container}>
      <Breadcrumb
        routes={[
          { title: 'Dashboard', href: `${config.rootRoute}/` },
          { title: 'Pharma Regs', href: `${config.rootRoute}/pharmareg` },
          { title: 'Reports' },
        ]}
      />

      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>
            <Search
              style={{ width: 200, marginRight: 10 }}
              placeholder='Search...'
              onChange={e => {
                const val = e.target.value;

                clearTimeout(timerSearch);
                timerSearch = setTimeout(() => onSearch(val), 600);
              }}
            />

            <RangePicker
              format={LEGACY_DATE_FORMAT}
              onChange={dateRange => {
                if (dateRange?.length) onUpdateFilter({ dateRange });
                else onCleanFilter('dateRange');
              }}
            />
          </div>

          <Tooltip title={!accessUpdate && 'No permission'}>
            <Button disabled={!accessUpdate} onClick={() => dispatch({ type: 'addReport' })}>
              Add Report
            </Button>
          </Tooltip>
        </div>

        <Table
          bordered
          style={{ marginTop: 20 }}
          loading={loading}
          columns={columns}
          dataSource={list}
          rowKey='_id'
          pagination={{
            showTotal: total => `Total ${total} items`,
            defaultPageSize: 10,
            size: 'Pagination',
            ...pagination,
          }}
          onChange={onUpdatePagination}
        />
      </Card>

      {showModal && (
        <Modal
          show={showModal}
          data={report}
          onSubmit={handleSubmit}
          onClose={onCloseModal}
        />
      )}
    </div>
  );
};

export default connect(({ account }) => ({
  permissions: account.adminPermissions,
}))(PHRReports);
