import React, { useState } from 'react';
import { Input, Form, Modal } from 'antd';
import { FormWithButton } from '../../../components/Form';
import MarkDownEditor from '../../../components/MarkDownEditor';
import Uploader from '../../../components/Upload';

export default ({ data = {}, show, onClose, onSubmit }) => {
  const [report, setReport] = useState(data);

  const handleSubmit = () => onSubmit(report);

  return (
    <Modal
      visible={show}
      footer={null}
      width='80%'
      onCancel={() => onClose()}
      onClose={() => onClose()}
      centered
      destroyOnClose
    >
      <FormWithButton
        disable={!(report?.content && report?.files && report?.title)}
        onSubmit={handleSubmit}
      >
        <Form.Item required label='Title'>
          <Input
            onChange={e => setReport({ ...report, title: e.target.value })}
            value={report?.title || ''}
            placeholder='Title...'
          />
        </Form.Item>

        <MarkDownEditor
          value={report?.content}
          label='New regulation report'
          setValue={content => setReport({ ...report, content })}
          required
        />

        <Uploader
          maxFiles={10}
          multiple
          data={{ common: true }}
          defaultDocs={report?.files}
          onChange={files => setReport({ ...report, files })}
          required
        />
      </FormWithButton>
    </Modal>
  );
};
