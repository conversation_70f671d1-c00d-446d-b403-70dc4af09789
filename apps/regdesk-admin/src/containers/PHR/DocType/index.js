import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import { Card, Table, Button, Input, Divider, Select, message, Tag, Switch, Popconfirm, Tooltip } from 'antd';
import Breadcrumb from '../../../components/Breadcrumb';
import actions from '../../../actions';
import styles from '../../../components/Form/index.less';

const { Search } = Input;

@connect(({ phrDocTypes, account }) => ({
  list: phrDocTypes.list,
  permissions: account.adminPermissions,
}))

export default class DocTypes extends PureComponent {
  constructor(props) {
    super(props);

    const { permissions } = props;

    this.accessUpdate = permissions.includes('accessPharmaRegUpdateDocTypes');

    this.columns = [
      {
        title: 'Name',
        dataIndex: 'dicName',
        render: (name, record) => {
          if (record.editable) {
            return (
              <Input
                autoFocus
                value={name}
                placeholder='Name ...'
                onChange={e => {
                  const newData = [...this.state.data];
                  const target = this.getRowByKey(record.key);

                  if (target) {
                    target.dicName = e.target.value;
                    this.setState({ data: newData });
                  }
                }}
                onKeyPress={e => {
                  if (e.key === 'Enter') this.saveRow(e, record.key);
                }}
              />
            );
          }

          return name;
        },
      },
      {
        title: 'Comparable',
        dataIndex: 'compare',
        render: (compare, record) => {
          if (record.editable) {
            return (
              <Switch
                defaultChecked={compare}
                onChange={checked => {
                  const newData = [...this.state.data];
                  const target = this.getRowByKey(record.key);

                  if (target) {
                    target.compare = checked;
                    this.setState({ data: newData });
                  }
                }}
              />
            );
          }

          if (compare) return <Tag color='green'>Yes</Tag>;

          return <Tag color='red'>No</Tag>;
        },
      },
      {
        title: 'Type',
        dataIndex: 'type',
        render: (type, record) => {
          if (record.editable) {
            return (
              <Select
                value={type}
                onChange={value => {
                  const newData = [...this.state.data];
                  const target = this.getRowByKey(record.key);

                  if (target) {
                    target.type = value;
                    this.setState({ data: newData });
                  }
                }}
                style={{ width: 200 }}
              >
                <Select.Option value='company'>Company</Select.Option>
                <Select.Option value='product'>Product</Select.Option>
              </Select>
            );
          }

          if (type === 'company') return <Tag color='#87d068'>Company</Tag>;
          if (type === 'product') return <Tag color='#108ee9'>Product</Tag>;

          return <Tag color='#f50'>No type</Tag>;
        },
      },
      {
        title: 'Actions',
        dataIndex: 'operation',
        render: (text, record) => {
          if (record.editable) {
            if (record.isNew) {
              return (
                <span>
                  <a onClick={e => this.saveRow(e, record.key)}>Add</a>
                  <Divider type='vertical' />
                  <a onClick={() => this.remove(record.key)}>Cancel</a>
                </span>
              );
            }

            return (
              <span>
                <a onClick={e => this.saveRow(e, record.key)}>Save</a>
                <Divider type='vertical' />
                <a onClick={e => this.cancel(e, record.key)}>Cancel</a>
              </span>
            );
          }

          return (
            <div>
              <Tooltip title={!this.accessUpdate && 'No permission'}>
                <a className={!this.accessUpdate ? styles.disabled : ''} onClick={e => this.accessUpdate && this.toggleEditable(e, record.key)}>
                  Update
                </a>
              </Tooltip>

              <Divider type='vertical' />

              <Tooltip title={!this.accessUpdate && 'No permission'}>
                <Popconfirm
                  title='Remove this document type ?'
                  onConfirm={() => this.deleteType(record.key)}
                  onCancel={() => {}}
                  okText='Yes'
                  cancelText='No'
                  disabled={!this.accessUpdate}
                >
                  <a className={!this.accessUpdate ? styles.disabled : ''} style={{ color: this.accessUpdate && 'red' }}>
                    Delete
                  </a>
                </Popconfirm>
              </Tooltip>
            </div>
          );
        },
      },
    ];

    this.cacheOriginData = {};

    this.state = {
      loading: true,
      data: this.formatData(props.list),
      search: '',
    };
  }

  componentDidMount() {
    this.load();
  }

  componentWillReceiveProps(nextProps) {
    const newList = this.formatData(nextProps.list);

    this.state.data.forEach(item => {
      if (item.editable) {
        if (item.isNew) newList.push(item);
        else newList[this.findOne(newList, 'key', item.key)] = item;
      }
    });

    this.setState({ data: newList, loading: false });
  }

  deleteType = id => {
    actions.phrDocTypes
      .remove(id)
      .then(() => {
        message.success('Remove success');
        this.load();
      });
  };

  /**
   * Load list
   */
  load = () => actions.phrDocTypes.get();

  /**
   * Get Row by Key
   * @param key
   * @returns {*}
   */
  getRowByKey = key => this.state.data.find(item => item.key === key);

  /**
   * Toggle editable
   * @param e
   * @param key
   */
  toggleEditable = (e, key) => {
    e.preventDefault();

    const target = this.getRowByKey(key);

    if (target) {
      if (!target.editable) this.cacheOriginData[key] = { ...target };

      target.editable = !target.editable;
      this.setState({ data: [...this.state.data] });
    }
  };

  formatData = (list = []) => {
    return list.map(item => {
      const { _id: key } = item;

      return { key, ...item };
    });
  };

  findOne = (list = [], keyField, key) => {
    for (let i = 0; i < list.length; i += 1) {
      if (list[i][keyField] === key) return i;
    }

    return -1;
  };

  remove = key => {
    const newData = this.state.data.filter(item => item.key !== key);

    this.setState({ data: newData });
  };

  /**
   * Add item
   */
  newItem = () => {
    const newData = [...this.state.data];

    newData.push({
      key: `${Date.now()}`,
      dicName: '',
      compare: true,
      type: 'company',
      editable: true,
      isNew: true,
    });

    this.setState({ data: newData });
  };

  /**
   * Update table
   */
  updateTable = () => this.load().then(() => message.success('Data updated'));

  /**
   * Save new type
   * @param e
   * @param key
   */
  saveRow(e, key) {
    e.persist();

    if (e) e.preventDefault();

    if (this.clickedCancel) {
      this.clickedCancel = false;

      return;
    }

    const target = this.getRowByKey(key) || {};

    if (!target.dicName) {
      message.error('Please indicate the name');

      return;
    }

    delete target.editable;

    if (target.isNew) {
      delete target.isNew;

      actions.phrDocTypes.add({ ...target }).then(this.updateTable);
    } else {
      const { _id: id, ...props } = target;

      actions.phrDocTypes.update({ id, ...props }).then(this.updateTable);
    }

    this.setState({ loading: true, data: [...this.state.data] });
  }

  cancel(e, key) {
    this.clickedCancel = true;
    e.preventDefault();

    const target = this.getRowByKey(key);

    if (this.cacheOriginData[key]) {
      Object.assign(target, this.cacheOriginData[key]);
      target.editable = false;
      delete this.cacheOriginData[key];
    }

    this.setState({ data: [...this.state.data] });
  }

  render() {
    const { data, search, loading } = this.state;
    const newData = data.filter(({ dicName }) => search.length === 0 || dicName.toLowerCase().includes(search.toLowerCase()));

    return (
      <div className={styles.container}>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Pharma Regs', href: `${config.rootRoute}/pharmareg` },
            { title: 'Doc Types' },
          ]}
        />

        <Card bordered={false}>
          <div style={{ marginBottom: 20, display: 'flex' }}>
            <Search
              autoFocus
              disabled={loading}
              style={{ width: 280 }}
              placeholder='Search by name...'
              value={search}
              onChange={e => this.setState({ search: e.target.value })}
            />
          </div>

          <Table
            loading={loading}
            rowKey='key'
            dataSource={newData}
            style={{ marginBottom: 16 }}
            columns={this.columns}
            pagination={false}
          />

          <Tooltip title={!this.accessUpdate && 'No permission'}>
            <Button
              block
              icon='plus'
              size='large'
              type='dashed'
              disabled={!this.accessUpdate}
              style={{ marginBottom: 8 }}
              onClick={this.newItem}
            >
              Add new type
            </Button>
          </Tooltip>
        </Card>
      </div>
    );
  }
}
