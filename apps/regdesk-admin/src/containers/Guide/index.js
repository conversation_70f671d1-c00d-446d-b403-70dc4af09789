import React, { PureComponent } from 'react';
import { Button, Input, Icon, Card, Form, Col, Row, message } from 'antd';
import config from 'config';
import Breadcrumb from '../../components/Breadcrumb';
import ModalUpload from '../../components/Upload/Modal';
import Uploader from '../../components/Upload';
import api from '../../utils/api';
import styles from '../Profile/index.less';

const FormItem = Form.Item;

export default class Guide extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      url: '',
      loading: true,
    };
  }

  componentDidMount() {
    api.guide
      .get()
      .then(({ url }) => this.setState({ url, loading: false }))
      .catch(() => this.setState({ loading: false }));
  }

  onUpdate = (link) => {
    const url = link || this.state.url;

    if (!url) return message.error('Please enter the url');

    this.setState({ loading: true });

    api.guide
      .update({ url })
      .then(() => message.success('Updated successfully'))
      .then(() => this.setState({ loading: false }))
      .catch(() => this.setState({ loading: false }));
  }

  render() {
    return (
      <div>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Guide' }
          ]}
        />

        <Card>
          <div className={styles.container}>
            <div className={styles.title}>Update Guide</div>

            <Row gutter={24} type='flex'>
              <Col xl={15} lg={12} md={12} sm={24} xs={24}>
                <FormItem label='Guide URL'>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Input
                      value={this.state.url}
                      disabled={this.state.loading}
                      placeholder='Url...'
                      autoComplete='off'
                      onChange={(e) => this.setState({ url: e.target.value })}
                      addonAfter={<Icon type='upload' onClick={()=> { this.uploadModal && this.uploadModal.showModal(true); }} />}
                    />

                    <Uploader listType='text' showUploadList={false} onChange={(files) => this.setState({ url: `/api/doc/${files[0]._id}` })}>
                      <Button loading={this.state.loading} disabled={this.state.loading} style={{ marginLeft: 15 }} type='link'>
                        Upload in local db
                      </Button>
                    </Uploader>
                  </div>
                </FormItem>

                <Button
                  loading={this.state.loading}
                  disabled={this.state.loading}
                  type='primary'
                  onClick={() => this.onUpdate()}
                >
                  Update
                </Button>
              </Col>
            </Row>
          </div>
        </Card>

        <ModalUpload
          ref={ref => { this.uploadModal = ref; }}
          onUpload={link => { this.setState({ url: link }); this.onUpdate(link); }}
          info={{ module: 'guide' }}
        />
      </div>
    );
  }
}
