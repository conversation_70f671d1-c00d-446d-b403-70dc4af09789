import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import config from 'config';
import moment from 'moment';
import { Row, Col, Spin, Typography, Card, Collapse, Descriptions } from 'antd';

import Breadcrumb from '../../../components/Breadcrumb';
import actions from '../../../actions';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

@connect(({ autofillHistory }) => ({
  item: autofillHistory.item,
  loading: autofillHistory.loading,
}))

export default class AutofillHistoryDetails extends PureComponent {
  componentDidMount() {
    const { match } = this.props;
    const { params } = match;

    if (params.id) actions.autofillHistory.getById(params.id);
  }

  render() {
    const { item = {}, loading } = this.props;

    return (
      <Spin spinning={loading}>
        <Breadcrumb
          routes={[
            { title: 'Dashboard', href: `${config.rootRoute}/` },
            { title: 'Autofill History', href: `${config.rootRoute}/autofillhistory` },
            { title: 'Details' },
          ]}
        />

        <Row gutter={24} loading='true'>
          <Col xl={6} lg={6} md={24} sm={24} xs={24}>
            {item.application && (
              <Card title='Overview'>
                <p>
                  <Text>
                    Application: {item.application.name ? item.application.name : item.application.id}
                  </Text>
                </p>

                <p>
                  <Text>
                    Country: {item.application.country ? item.application.country : ''}
                  </Text>
                </p>

                <p>
                  <Text>
                    {item.application.productFamily ? 'Product Family' : 'Product'}: {item.application.productFamily ? item.application.productFamily.name : item.application.product ? item.application.product.name : ''}
                  </Text>
                </p>

                <p>
                  <Text>
                    Owner: {item.owner ? `${item.owner.name} (${item.owner.email})` : 'deleted'}
                  </Text>
                </p>

                <p>
                  <Text>
                    Creator: {item.creator ? `${item.creator.name} (${item.creator.email})` : 'deleted'}
                  </Text>
                </p>

                <p>
                  <Text>
                    Date: {item.createdAt ? moment(item.createdAt).getFullUTC() : ''}
                  </Text>
                </p>
              </Card>
            )}
          </Col>

          {item.history && (
            <Col xl={18} lg={18} md={24} sm={24} xs={24}>
              <Card title='History'>
                <Collapse>
                  {item.history.map(({ questionId, questionType, questionScope, questionName, logs }, key) => (
                    <Panel header={questionName} key={key}>
                      <Descriptions title='Question Parameters'>
                        <Descriptions.Item label='ID'>{questionId}</Descriptions.Item>
                        <Descriptions.Item label='Type'>{questionType}</Descriptions.Item>
                        <Descriptions.Item label='Scope'>{questionScope}</Descriptions.Item>
                      </Descriptions>

                      <Card size='small' title='Log' style={{ background: 'rgba(0, 0, 0, 0.06)' }}>
                        <Paragraph style={{ width: '100%', whiteSpace: 'pre-line' }}>
                          {logs.map(value => `${value }\n`)}
                        </Paragraph>
                      </Card>
                    </Panel>
                  ))}
                </Collapse>
              </Card>
            </Col>
          )}
        </Row>
      </Spin>
    );
  }
}
