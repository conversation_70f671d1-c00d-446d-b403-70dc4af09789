import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Table, Row, Col, Card } from 'antd';
import { Link } from 'react-router-dom';
import config from 'config';
import moment from 'moment';
import Breadcrumb from '../../components/Breadcrumb';
import actions from '../../actions';

@connect(({ autofillHistory }) => ({
  list: autofillHistory.list,
  loading: autofillHistory.loading,
  pagination: autofillHistory.pagination,
}))

export default class AutofillHistory extends PureComponent {
  componentDidMount() {
    this.load();
  }

  /**
   * Get columns for table
   * @returns {*[]}
   */
  getColumns = () => {
    const columns = [
      {
        title: 'Application',
        dataIndex: 'application',
        key: 'application',
        render: (application) => application?.name || application?.id || 'deleted',
      },
      {
        title: 'Owner',
        dataIndex: 'owner',
        key: 'owner',
        render: (user) => user ? `${user.name} (${user.email})` : 'deleted',
      },
      {
        title: 'Creator',
        dataIndex: 'creator',
        key: 'creator',
        render: (user) => user ? `${user.name} (${user.email})` : 'deleted',
      },
      {
        title: 'Date',
        dataIndex: 'createdAt',
        key: 'date',
        render: timestamp => moment(timestamp).getFullUTC(),
      },
      {
        title: 'Actions',
        dataIndex: '_id',
        key: 'actions',
        render: (id) => (
          <div>
            <Link to={{ pathname: `${config.rootRoute}/autofillhistory/${id}` }}>
              Details
            </Link>
          </div>
        ),
      }
    ];

    return columns;
  };

  /**
   * Load history list
   * @param props
   */
  load = (props = {}) => {
    const { pagination } = this.props;

    actions.autofillHistory.load({ pagination, ...props });
  };

  /**
   * Handle table change
   * @param pagination
   */
  handleTableChange = (pagination) => this.load({ pagination });

  render() {
    const { list = [], loading, pagination = {} } = this.props;

    return (
      <div>
        <Breadcrumb routes={[{ title: 'Dashboard', href: `${config.rootRoute}/` }, { title: 'Autofill History' }]} />

        <Row gutter={24}>
          <Col xl={24} lg={24} md={24} sm={24} xs={24}>
            <Card title='Autofill History' style={{ marginBottom: 24 }} bordered={false}>
              <Table
                loading={loading}
                rowKey='_id'
                dataSource={list}
                columns={this.getColumns()}
                pagination={{
                  showSizeChanger: true,
                  ...pagination,
                }}
                onChange={this.handleTableChange}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  }
}
