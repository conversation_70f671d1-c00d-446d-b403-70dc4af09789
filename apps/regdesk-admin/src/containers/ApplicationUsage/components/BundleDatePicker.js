import React from 'react';
import moment from 'moment';
import { Checkbox, DatePicker } from 'antd';
import styles from '../Edit/index.less';
import { FULL_DATE_FORMAT } from '../../../utils/date';

const BundleDatePicker = ({
  oneYear = false,
  sameExpiryDate = false,
  expiryDate = null,
  currentPackageIsUsed = false,
  currentExpiryDate = new Date(),
  showSameExpiryDate = false,
  onSetYear,
  onChangeDate,
  onSetSameExpiryDate,
}) => {
  const disabledDate = moment(currentExpiryDate).isAfter(new Date()) && !currentPackageIsUsed ? currentExpiryDate : new Date();

  return (
    <div style={{ display: 'flex' }}>
      <h4 style={{ width: 150 }}>Expiry Date:</h4>

      <div className={styles.container}>
        <DatePicker
          disabled={oneYear}
          placeholder='Expiry Date...'
          autoComplete='off'
          format={FULL_DATE_FORMAT}
          value={expiryDate && !oneYear && moment(expiryDate, FULL_DATE_FORMAT)}
          disabledDate={(current) => current && current < moment(disabledDate).endOf('day')}
          onChange={onChangeDate}
        />

        <Checkbox
          checked={oneYear && !sameExpiryDate}
          disabled={!currentPackageIsUsed && new Date() < moment(currentExpiryDate)}
          onChange={onSetYear}
        >
          One year from Start
        </Checkbox>

        {showSameExpiryDate && (
          <div>
            <Checkbox
              checked={sameExpiryDate}
              disabled={currentPackageIsUsed && currentExpiryDate}
              onChange={onSetSameExpiryDate}
            >
              Same expiry date
            </Checkbox>
          </div>
        )}

      </div>
    </div>
  );
};

export default BundleDatePicker;
