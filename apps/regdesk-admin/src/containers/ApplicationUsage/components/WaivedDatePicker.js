import React, { useState } from 'react';
import moment from 'moment';
import { DatePicker, Select, Radio } from 'antd';
import styles from '../Edit/index.less';
import { FULL_DATE_FORMAT } from '../../../utils/date';

const { Option } = Select;

const CustomDatePicker = ({ currentExpiryDate = new Date(), expiryDate, timeline, onChangeDate, onSetTimeline }) => {
  const [timelineOff, setTimelineOff] = useState(false);

  const onSwitchExpiryDateType = () => {
    if (!timelineOff) onSetTimeline(undefined);
    else onSetTimeline('12');

    setTimelineOff(prev => !prev);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      <h4 style={{ width: 150 }}>Expiry Date Type:</h4>

      <div className={styles.container}>
        <Radio.Group
          value={String(timelineOff)}
          style={{ marginBottom: 10 }}
          onChange={onSwitchExpiryDateType}
        >
          <Radio value='true'>Custom</Radio>
          <Radio value='false'>Timelines</Radio>
        </Radio.Group>

        {timelineOff ? (
          <DatePicker
            style={{ width: '250px' }}
            placeholder='Expiry Date...'
            autoComplete='off'
            format={FULL_DATE_FORMAT}
            value={expiryDate && moment(expiryDate, FULL_DATE_FORMAT)}
            disabledDate={(current) => current && current < moment(currentExpiryDate).endOf('day')}
            onChange={onChangeDate}
          />
        ) : (
          <Select
            value={String(timeline)}
            onChange={onSetTimeline}
            style={{ width: '250px' }}
          >
            <Option value='3'>3 months</Option>
            <Option value='6'>6 months</Option>
            <Option value='12'>1 year</Option>
          </Select>
        )}
      </div>
    </div>
  );
};

export default CustomDatePicker;
