import React, { useReducer } from 'react';
import { Button, InputNumber, Modal, Switch } from 'antd';
import moment from 'moment';
import actions from '../../../actions';
import BundleDatePicker from '../components/BundleDatePicker';
import WaivedDatePicker from '../components/WaivedDatePicker';
import styles from './index.less';

const initialState = {
  packageType: 'unlimited',
  bundleSettings: {},
};

const reducer = (state, action) => {
  const { type, payload } = action || {};
  const { packageType, bundleSettings } = state || {};
  const { packageType: newType, bundleSettings: newBundleSettings } = payload || {};
  const timelineObj = type === 'changeSettings' && (payload.sameExpiryDate || payload.expiryDate || payload.count || (Object.prototype.hasOwnProperty.call(payload, 'timeline') && !payload.timeline)) ? { timeline: undefined } : { timeline: 12 };

  switch (type) {
    case 'setType':
      return { packageType: newType, bundleSettings: newBundleSettings };
    case 'changeSettings':
      return { packageType, bundleSettings: { ...bundleSettings, ...payload, ...timelineObj } };
    default:
      return state;
  }
};

const EditPackageModal = ({ user, showModal, onCloseModal, getUsersData }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const { packageType, bundleSettings } = state || {};
  const { _id: userId, applicationUsageControl: userUsageControl } = user;
  const { currentPackage } = userUsageControl || {};
  const currentPackageIsUsed = currentPackage && currentPackage.bundleSize <= currentPackage.applicationIds.length;

  let disabled = false;

  if (packageType !== 'unlimited' && (!bundleSettings?.expiryDate && !bundleSettings?.timeline && !bundleSettings.sameExpiryDate)) disabled = true;
  if (packageType === 'bundle' && !bundleSettings?.count) disabled = true;

  const onSwitch = (newType) => {
    dispatch({
      type: 'setType',
      payload: { packageType: newType, bundleSettings: { timeline: newType !== 'unlimited' ? 12 : undefined } },
    });
  };

  const onChangeBundleSettings = (key, val) => {
    dispatch({
      type: 'changeSettings',
      payload: { [key]: val },
    });
  };

  const onUpdateData = () => {
    onCloseModal();
    getUsersData();
  };

  const onSave = () => {
    actions.user
      .applicationUsageUpdate(userId, { applicationUsageControl: { type: packageType, bundleSettings } })
      .then(onUpdateData)
      .catch(onUpdateData);
  };

  return (
    <Modal
      title='Package'
      visible={showModal}
      footer={null}
      onCancel={onCloseModal}
      onClose={onCloseModal}
      width={500}
      zIndex={1001}
    >
      <div>
        <div className={styles.wrapper}>
          <Switch
            size='small'
            checked={packageType === 'bundle'}
            style={{ marginRight: 5 }}
            onChange={() => onSwitch('bundle')}
          />
          Bundle
        </div>

        {packageType === 'bundle' && (
          <div className={styles.container} style={{ marginTop: 10 }}>
            <div className={styles.wrapper}>
              <h4 style={{ width: 150 }}>Application Bundle:</h4>

              <InputNumber
                min={0}
                max={5000}
                formatter={value => {
                  if (+value === 0) return '';
                  if (+value > 5000) return bundleSettings?.count;

                  return typeof value === 'string' ? value.replace(/[.,]/g, '') : value;
                }}
                value={bundleSettings?.count || ''}
                onChange={(val) => +val !== 0 && +val < 5000 && onChangeBundleSettings('count', String(val))}
              />
            </div>

            <BundleDatePicker
              expiryDate={bundleSettings?.expiryDate || null}
              oneYear={bundleSettings?.timeline === 12}
              sameExpiryDate={bundleSettings?.sameExpiryDate || false}
              currentExpiryDate={currentPackage?.expiryDate}
              currentPackageIsUsed={currentPackageIsUsed}
              showSameExpiryDate={currentPackage?.expiryDate && moment(currentPackage.expiryDate).isAfter(new Date()) && currentPackage.type === 'bundle'}
              onChangeDate={(e) => onChangeBundleSettings('expiryDate', e)}
              onSetYear={(e) => onChangeBundleSettings('timeline', e.target.checked ? 12 : undefined)}
              onSetSameExpiryDate={(e) => onChangeBundleSettings('sameExpiryDate', e.target.checked)}
            />
          </div>
        )}

        <div className={styles.wrapper}>
          <Switch
            size='small'
            checked={packageType === 'unlimited'}
            style={{ marginRight: 5 }}
            onChange={() => onSwitch('unlimited')}
          />
          Per Application
        </div>

        <div className={styles.container}>
          <div className={styles.wrapper}>
            <Switch
              size='small'
              checked={packageType === 'waived'}
              style={{ marginRight: 5 }}
              onChange={() => onSwitch('waived')}
            />
            Waived
          </div>

          {packageType === 'waived' && (
            <div className={styles.container} style={{ marginTop: 10 }}>
              <WaivedDatePicker
                expiryDate={bundleSettings?.expiryDate || null}
                timeline={bundleSettings?.timeline}
                currentExpiryDate={currentPackage?.expiryDate}
                onChangeDate={(e) => onChangeBundleSettings('expiryDate', e)}
                onSetTimeline={(option) => onChangeBundleSettings('timeline', typeof option === 'string' ? +option : undefined)}
              />
            </div>
          )}
        </div>
      </div>

      <div className={styles.buttons}>
        <Button onClick={onCloseModal}>Cancel</Button>

        <Button type='primary' style={{ marginRight: 20 }} disabled={disabled} onClick={onSave}>
          Save
        </Button>
      </div>
    </Modal>
  );
};

export default EditPackageModal;
