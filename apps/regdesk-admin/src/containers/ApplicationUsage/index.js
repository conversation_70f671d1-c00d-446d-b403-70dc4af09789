import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { Table, Row, Col, Card, Tag, Divider, Avatar, Tooltip } from 'antd';
import { Link } from 'react-router-dom';
import config from 'config';
import moment from 'moment';
import Breadcrumb from '../../components/Breadcrumb';
import EditPackageModal from './Edit';
import actions from '../../actions';

const UsersList = ({ list, sorter, filters, pagination, loading }) => {
  const [editUser, setEditUser] = useState(undefined);

  const load = (props = {}) => {
    actions.user.load({
      select: { avatar: true, email: true, applicationUsageControl: true },
      sorter,
      filters,
      pagination,
      ...props
    });
  };

  useEffect(() => {
    load({
      view: {},
      select: { avatar: true, email: true, applicationUsageControl: true },
      pagination: { pageSize: 20 },
      filters: { mainAccountsOnly: true },
    });
  }, []);

  const handleTableChange = (newPagination) => load({ pagination: newPagination });

  const getColumns = () => {
    const columns = [
      {
        title: '',
        dataIndex: 'avatar',
        width: 32,
        render: (avatarId) => (
          <Avatar
            icon='user'
            src={avatarId && `${config.apiServer}/api/doc/${avatarId}`}
            alt='avatar'
            style={{ margin: '-5px 0' }}
          />
        ),
      },
      {
        title: 'Name',
        dataIndex: 'name',
        render: (name, val) => {
          const { _id: userId } = val;

          return (
            <Link to={`${config.rootRoute}/users/edit/${userId}`}>{name}</Link>
          );
        },
      },
      {
        title: 'Email',
        dataIndex: 'email',
      },
      {
        title: 'Type',
        dataIndex: 'applicationUsageControl.currentPackage.type',
        render: (type) => {
          if (!type) return null;
          if (type === 'unlimited') return <Tag color='#87d068'>Per Application</Tag>;
          if (type === 'waived' || type === 'wave') return <Tag color='#e5ce01'>Waived</Tag>;

          return <Tag color='#108ee9'>Bundle</Tag>;
        }
      },
      {
        title: 'Applications Created',
        dataIndex: 'applicationUsageControl.currentPackage',
        render: (currentPackage) => {
          const { type, applicationIds = [] } = currentPackage || {};

          if (type) return (applicationIds?.length);

          return <Tag color='#cac8c8'>N/A</Tag>;
        },
      },
      {
        title: 'Applications Left',
        render: (user) => {
          const { currentPackage } = user.applicationUsageControl || {};
          const { type, bundleSize = 0, applicationIds = [] } = currentPackage || {};

          if (type === 'bundle') return bundleSize - applicationIds.length;

          return <Tag color='#cac8c8'>N/A</Tag>;
        },
      },
      {
        title: 'Expiry Date',
        dataIndex: 'applicationUsageControl.currentPackage.expiryDate',
        render: (expiryDate) => {
          if (expiryDate) return moment(expiryDate).getUTC();

          return <Tag color='#cac8c8'>N/A</Tag>;
        },
      },
      {
        title: 'Actions',
        render: (user) => {
          const { _id: userId, applicationUsageControl } = user;
          const { availablePackages = [], unlimitedPackageId } = applicationUsageControl;
          const addBtnDisabled = !!availablePackages.length || !!unlimitedPackageId;

          return (
            <span>
              {addBtnDisabled ? (
                <Tooltip placement='bottomRight' title='Future packages limit exceeded!'>
                  <a style={{ opacity: '0.4' }}>Add</a>
                </Tooltip>
              ) : (
                <a onClick={() => setEditUser(userId)}>Add</a>
              )}

              <Divider type='vertical' />

              <Link to={`${config.rootRoute}/appUsage/logs/${userId}`}>
                Logs
              </Link>
            </span>
          );
        },
      },
    ];

    return columns;
  };

  return (
    <div>
      {editUser && (
        <EditPackageModal
          showModal={!!editUser}
          user={list.find(({ _id }) => _id === editUser)}
          onCloseModal={() => setEditUser(false)}
          getUsersData={load}
        />
      )}

      <Breadcrumb
        routes={[
          { title: 'Dashboard', href: `${config.rootRoute}/` },
          { title: 'Application Usage' },
        ]}
      />

      <Row gutter={24}>
        <Col xs={24}>
          <Card title='Users' style={{ marginBottom: 24 }} bordered={false}>
            <Table
              loading={loading}
              rowKey='_id'
              dataSource={list}
              columns={getColumns()}
              pagination={{
                showSizeChanger: true,
                ...pagination,
              }}
              onChange={handleTableChange}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default connect(({ users }) => ({
  list: users.list,
  loading: users.loading,
  pagination: users.pagination,
  filters: users.filters,
  sorter: users.sorter,
}))(UsersList);
