import React, { useReducer, useEffect } from 'react';
import config from 'config';
import { Empty, List } from 'antd';
import Breadcrumb from '../../../components/Breadcrumb';
import LogCard from './LogCard';
import api from '../../../utils/api';
import styles from './index.less';
import EmptyImage from '../../../assets/empty-image.png';

const reducer = (state, action) => {
  const { type, payload } = action || {};
  const { userName, logData, pagination } = payload || {};

  switch (type) {
    case 'setData':
      return { ...state, userName, logData, pagination };
    default:
      return state;
  }
};

const initialState = {
  logData: [],
  pagination: {
    current: 1,
    pageSize: 6,
    total: 12,
  },
};

const ApplicationUsageLogs = ({ match }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const { id } = match.params;
  const { userName, pagination, logData } = state;
  const { current, pageSize, total } = pagination;

  const getUserLogs = async(userId, newPagination) => {
    const result = await api.user.getPackageLogs({ userId, params: { pagination: newPagination }});

    const { userName: name, logs, pagination: logPagination } = result;

    dispatch({
      type: 'setData',
      payload: { userName: name, logData: logs, pagination: logPagination },
    });
  };

  useEffect(() => {
    if (id) getUserLogs(id);
  }, [id]);

  const onChangePage = (page) => getUserLogs(id, { ...pagination, current: page });

  const routes = [
    { title: 'Dashboard', href: `${config.rootRoute}/` },
    { title: 'Application Usage', href: `${config.rootRoute}/appUsage` },
    { title: `${userName || ''}` },
    { title: 'Logs' },
  ];

  if (!logData.length) {
    return (
      <div className={styles.container}>
        <Breadcrumb routes={routes} />

        <Empty
          image={EmptyImage}
          imageStyle={{ height: 60, marginTop: 100 }}
          description={<span>No Data</span>}
        />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Breadcrumb routes={routes} />

      <List
        grid={{ gutter: 16, column: 3 }}
        dataSource={logData}
        renderItem={item => <List.Item><LogCard key={item._id} log={item} /></List.Item>}
        pagination={{
          size: 'Pagination',
          current,
          pageSize,
          total,
          onChange: (nextPage) => onChangePage(nextPage)
        }}
      />
    </div>
  );
};

export default ApplicationUsageLogs;
