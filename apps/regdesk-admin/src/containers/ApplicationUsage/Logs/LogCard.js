import React from 'react';
import { Card } from 'antd';
import moment from 'moment';

const LogCard = ({ log }) => {
  const {
    user,
    createdAt,
    additionalInfo,
    newValues = [],
    applicationsCreated = 0,
    isCurrentPackage = false,
    packageName,
    startDate,
    expiryDate
  } = log;

  const isBundle = additionalInfo.type === 'Bundle';
  const isUnlimited = additionalInfo.type === 'Per Application';
  const bundleSize = newValues.find((val) => val?.name === 'bundle')?.value;
  const applicationsLeft = bundleSize - applicationsCreated;

  return (
    <div>
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <h4>{moment(createdAt).getUTC()}</h4>
              <h4>{user}</h4>
            </div>

            <div>{packageName}</div>
          </div>
        }
        style={{ borderRadius: '2px', borderColor: `${isCurrentPackage ? '#1890ff' : ''}` }}
      >
        <p>PACKAGE: {additionalInfo.type}</p>
        <p>Bundle: {isBundle ? bundleSize : 'N/A'}</p>
        <p>Start Date: {startDate ? moment(startDate).getUTC() : ''}</p>
        <p>Expiry Date: {isUnlimited ? 'N/A' : expiryDate ? moment(expiryDate).getUTC() : ''}</p>
        <p>Applications Created: {log.applicationsCreated}</p>
        <p>Applications Left: {isBundle ? applicationsLeft : 'N/A'}</p>
      </Card>
    </div>
  );
};

export default LogCard;
