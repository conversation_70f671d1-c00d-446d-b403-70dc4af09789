// / <reference types="cypress" />
// ***********************************************************
// This example plugins/index.js can be used to load plugins
//
// You can change the location of this file or turn off loading
// the plugins file with the 'pluginsFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/plugins-guide
// ***********************************************************

// This function is called when a project is opened or re-opened (e.g. due to
// the project's config changing)

const db = require('../../../server/src/db');

/**
 * Function for create fake mongo ObjectId
 **/
// const fakeMongodbID = () => {
//   // eslint-disable-next-line no-bitwise
//   const time = (new Date().getTime() / 1000 | 0).toString(16);
//   return time + 'xxxxxxxxxxxxxxxx'.replace(/[x]/g, () =>
//     // eslint-disable-next-line no-bitwise
//      (Math.random() * 16 | 0).toString(16)
//   ).toLowerCase();
// };


/**
 * @type {Cypress.PluginConfig}
 */
module.exports = (on, config) => {
  // require('@cypress/code-coverage/task')(on, config)
  // on('file:preprocessor', require('@cypress/code-coverage/use-babelrc'))

  on('task', {
    addUser(userObj) {
      return new Promise(resolve => {
        // eslint-disable-next-line no-underscore-dangle
        db.toObjectId(userObj._id);
        // eslint-disable-next-line new-cap
        const user = new db.user(userObj)
        return resolve([user.save(), user])
      });
    }
  });

  on('task', {
    removeUser(userId, name) {
      return new Promise((resolve, reject) => {
        db.log.deleteMany({ name })
        return resolve(db.user.findOneAndDelete({ _id: db.toObjectId(userId) })
        ).catch((err) => reject(console.log('[Error] No document found for query', err)))
      });
    }
  });

  /**
   * Function for remove all products by id
   * @param userId
   * @returns {Promise}
   **/
  on('task', {
    deleteProducts(userId) {
      return Promise.all([
        db.product.find({ owner: db.toObjectId(userId) })
          .then((allProducts) => {
            allProducts.forEach((prod) => {
              // eslint-disable-next-line no-param-reassign
              prod.deleted = true;
              prod.save();
              db.productChecklistItem.updateMany({ product: db.toObjectId(prod._id) }, { $set: { deleted: true } })
              db.productFamily.updateMany(
                { products: db.toObjectId(prod._id) },
                { $pull: { products: db.toObjectId(prod._id) } },
              );
              console.log('[OK] Product deleted');
            });
          }),
      ]);
    }
  });

  /**
   * Function for add product by id
   * @param {ObjectID} data
   * @param {(Object|string)} userId
   * @returns {ObjectID} newProduct._id
   **/
  on('task', {
    addProduct({ data, userId, limitProducts }) {
      const {
        name,
        version,
        description,
        image,
        type,
        category,
        classification,
        euLabeling,
        fdaBooklet,
        countries,
        gmdn,
        legalManufacturer,
      } = data;

      return new Promise(resolve => {
        db.product
          .countDocuments({ owner: db.toObjectId(userId) })
          .then((total) => {
            if (total >= limitProducts) {
              throw new Error('limitProducts');
            }
            // eslint-disable-next-line new-cap
            const newProduct = new db.product({
              name,
              version,
              type,
              description,
              image,
              category,
              classification,
              euLabeling,
              fdaBooklet,
              countries,
              owner: db.toObjectId(userId),
              gmdn,
              legalManufacturer,
            });

            // eslint-disable-next-line no-underscore-dangle
            newProduct.save().then(() => resolve({ id: newProduct._id }));
          }).catch((err) => console.log(err))
      });
    }
  });

  /**
   * Remove all SKUs
   * @param {(Object|string)} userId
   * @returns Promise
   **/
  on('task', {
    removeAllSku(userId) {
      return Promise.all([
        db.sku.find({ owner: db.toObjectId(userId) }).then((skuList) => {

          skuList.forEach((sku) => {
            return db.product.findById(db.toObjectId(sku.productId)).then((product) => {

              if (!product || !sku.productId) {
                sku.deleted = true;
                return Promise.all([
                  sku.save(), console.log(`[OK] SKU ${sku._id} WITHOUT PRODUCT WAS REMOVED`)
                ])
              }

              product.sku.remove(sku._id);
              sku.deleted = true;

              return Promise.all([sku.save(), product.save()]).then(() => {
                return console.log(`[OK] SKU ${sku._id} REMOVED`);
              });
            });
          });
        })
      ]);
    }
  });

  return config;
}
