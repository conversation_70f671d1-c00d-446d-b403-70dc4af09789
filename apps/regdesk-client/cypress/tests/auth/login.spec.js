// / <reference types="cypress" />

const ALLOWED_NUMBER_OF_LOGIN_ATTEMPTS = 5;

const testData = {
  user: {
    _id: '507f1f45bcf86cd590439909',
    email: '<EMAIL>',
    password: 'test_ui.password',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    role: 'admin'
  },
  wrongLoginMessage: 'Incorrect email or password',
  wrongAttemptMessage: 'You have too many incorrect password attempts, please reset your password by email',
  passwordEmptyMessage: 'Please fill field password',
  emailEmptyMessage: 'Please fill field email',
  invalidEmailMessage: 'Not a valid email address.',
  invalidPasswordMessage: 'Password must have more than 6 characters',
  singUpBodyCard: 'Please contact our <NAME_EMAIL> to create an account for you'
};

context('Login', () => {

  before(() => {
    cy.task('removeUser', testData.user._id, testData.user.name);
    cy.task('addUser', testData.user).then((id) => {
      testData.user._id = id[1]._id
    });
  });

  after(() => {
    cy.task('removeUser', testData.user._id, testData.user.name);
  })

  beforeEach(() => {
    cy.visit('/')
  });

  it('admin can login', () => {
    cy.get('#email').type(testData.user.email)
    cy.get('#password').type(testData.user.password)
    cy.server()
    cy.intercept('/api/dashboard').as('loginMethod')
    cy.contains('Login').click()
    cy.wait('@loginMethod')
    cy.url()
      .should('include', '/dashboard')
  });

  it('should not login (Incorrect password)', () => {
    cy.get('#email').type(testData.user.email)
    cy.get('#password').type('4hbtkjw4o53')
    cy.server()
    cy.intercept('POST', 'api/auth').as('loginMethodWrong')
    cy.contains('Login').click();
    cy.wait('@loginMethodWrong')
    cy.get('.ant-alert-message')
      .should('be.visible')
      .should('contain.text', testData.wrongLoginMessage)
  });

  it('should not login (Incorrect email)', () => {
    cy.get('#email').type('<EMAIL>')
    cy.get('#password').type(testData.user.password)
    cy.server()
    cy.intercept('POST', 'api/auth').as('loginMethodWrong')
    cy.contains('Login').click();
    cy.wait('@loginMethodWrong')
    cy.get('.ant-alert-message')
      .should('be.visible')
      .should('contain.text', testData.wrongLoginMessage)
  });

  it('should not login (without password)', () => {
    cy.get('#email').type(testData.user.email)
    cy.contains('Login').click();
    cy.get('.ant-form-explain')
      .should('be.visible')
      .should('contain.text', testData.passwordEmptyMessage)
  });

  it('should not login (without email)', () => {
    cy.get('#password').type(testData.user.password)
    cy.contains('Login').click();
    cy.get('.ant-form-explain')
      .should('be.visible')
      .should('contain.text', testData.emailEmptyMessage)
  });

  it('should not login (without email and password)', () => {
    cy.contains('Login').click();
    cy.contains(testData.emailEmptyMessage)
      .should('be.visible')
    cy.contains(testData.passwordEmptyMessage)
      .should('be.visible')
  });

  it('should not login (invalid email)', () => {
    cy.get('#email').type(testData.user.name)
    cy.contains('Login').click();
    cy.get('.ant-form-explain')
      .should('be.visible')
      .should('contain.text', testData.invalidEmailMessage)
  });

  it('should not login (invalid password)', () => {
    cy.get('#email').type(testData.user.email)
    cy.get('#password').type('454')
    cy.contains('Login').click();
    cy.get('.ant-form-explain')
      .should('be.visible')
      .should('contain.text', testData.invalidPasswordMessage)
  });

  it('should not login (5 attempts with wrong password)', () => {
    for (let i = 0; i < ALLOWED_NUMBER_OF_LOGIN_ATTEMPTS; i++) {
      cy.reload()
      cy.get('#email').type(testData.user.email)
      cy.get('#password').type('4hbtkjw4o53')
      cy.server()
      cy.intercept('POST', 'api/auth').as(`loginMethodWrong${i}`)
      cy.contains('Login').click();
      cy.wait(`@loginMethodWrong${i}`)
    }
    cy.get('.ant-alert-message')
      .should('be.visible')
      .should('contain.text', testData.wrongAttemptMessage)
  });

  it('should open sign bookmark', () => {
    cy.contains('SIGN UP').click()
    cy.get('.ant-card-body')
      .should('be.visible')
      .should('contain.text', testData.singUpBodyCard)
  });
})
