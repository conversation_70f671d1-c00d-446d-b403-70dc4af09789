// / <reference types="cypress" />

const testData = {
  user: {
    _id: '507f1f45bcf86cd590439909',
    email: '<EMAIL>',
    password: 'test_ui.password',
    name: 'TestUser<PERSON>',
    role: 'admin'
  },
};

context('Logout', () => {

  before(() => {
    cy.visit('/')
    cy.task('removeUser', testData.user._id, testData.user.name);
    cy.task('addUser', testData.user).then((id) => {
      testData.user._id = id[1]._id
    });
    cy.login(testData.user.email, testData.user.password).then(() => {
      cy.visit('/dashboard');
      cy.location().should((loc) => {
        expect(loc.pathname).to.eq('/dashboard')
      });
    })
  });

  it('admin can logout', () => {
    cy.get('.nav___1J5O2 > :nth-child(5)').click()
    cy.contains('Logout').click()
    cy.url()
      .should('not.include', '/dashboard')
  });
})
