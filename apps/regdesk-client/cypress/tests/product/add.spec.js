// / <reference types="cypress" />


const testData = {

  user: {
    _id: '60117fbc84eb2b4e26e8d42c',
    email: '<EMAIL>',
    password: 'test_ui_t.password',
    name: 'TestUserUIT',
    role: 'client',
    permission: ['products'],
    limitProducts: 1000
  },
  product: {
    name: 'Sc<PERSON>pel, single-use',
    version: '0.01',
    manufacturer: 'SCHOTT minifab', // recommended 100 symbols
    description: 'A sterile, hand-held, manual surgical instrument constructed as a one-piece handle' +
      'and scalpel blade (not an exchangeable component) used by the operator to' +
      'manually cut or dissect tissue. The blade is typically made of high-grade stainless' +
      'steel alloy or carbon steel and the handle is often made of plastic. This is a singleuse device', // recommended 500 symbols
    euLabeling: '90/385/EEC',
    classification: 1,
    gmdn: 47569 // 5-digit GMDN digital code
  },
  file: {
    png: 'scalpel.jpg'
  },
  imageLink: 'https://drive.google.com/file/d/1FKC6i_PFoTZlfnA-UPY8zFZU6JUNNevm/view?usp=sharing',
  // skuName: Helpers.RandomString(10),
  messages: {
    emptyNameMessage: 'Please, enter product name',
    invalidGmdnMessage: 'Invalid GMDN code',
    invalidLinkMessage: 'Not a valid url',
    emptyLinkMessage: 'Please fill field Link'
  }
};

context('Add product', () => {

  before(() => {
    cy.task('removeUser', testData.user._id, testData.user.name);
    cy.task('addUser', testData.user);
  });

  afterEach(() => {
    cy.task('deleteProducts', testData.user._id);
  })

  beforeEach(() => {
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(500)
    cy.intercept({ method: 'GET', url: '/api/session/check' }).as('session')
    cy.login(testData.user.email, testData.user.password).then((response) => {
      expect(response.body).to.not.have.property('error')
      cy.getCookie('__rd_api').should('exist')
      cy.intercept({ method: 'GET', url: '/api/v2/products' }).as('productsPage')
      cy.visit('/products');
      cy.wait('@session')
      cy.wait('@productsPage')
      cy.location().should((loc) => {
        expect(loc.pathname).to.eq('/products')
      });
    })
  })

  it('add product (with name)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (with Manufacturer)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get(':nth-child(4) > .ant-col-16 > .ant-form-item-control > .ant-form-item-children > .ant-input-group-wrapper > .ant-input-wrapper > .ant-input')
      .as('legalManufacturer') // TODO add id to legalManufacturer field
      .type(testData.product.manufacturer)
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (with description)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get('#description').type(testData.product.description)
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (with EU Labeling)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get('#euLabeling').type(testData.product.euLabeling)
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (with GMDN code)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get(':nth-child(3) > .ant-col-16 > .ant-form-item-control > .ant-form-item-children > .ant-input-group-wrapper > .ant-input-wrapper > .ant-input')
      .as('GMDN').type(testData.product.gmdn)
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (with version)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get('#version').type(testData.product.version)
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (with correct gmnd)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get(':nth-child(3) > .ant-col-16 > .ant-form-item-control > .ant-form-item-children > .ant-input-group-wrapper > .ant-input-wrapper > .ant-input')
      .as('GMDN').type(testData.product.gmdn)
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (classification N/A by default)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get('#version').type(testData.product.version)
    cy.get(':nth-child(3) > .ant-col-16 > .ant-form-item-control > .ant-form-item-children > .ant-input-group-wrapper > .ant-input-wrapper > .ant-input')
      .as('GMDN').type(testData.product.gmdn)
    cy.get(':nth-child(4) > .ant-col-16 > .ant-form-item-control > .ant-form-item-children > .ant-input-group-wrapper > .ant-input-wrapper > .ant-input')
      .as('legalManufacturer')
      .type(testData.product.manufacturer)
    cy.get('#description').type(testData.product.description)
    cy.get('#euLabeling').type(testData.product.euLabeling)
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (classification Class I)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get('#classification > :nth-child(2)').as('Class I')
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (classification Class II)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get('#classification > :nth-child(3)').as('Class II')
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (classification Class III)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get('#classification > :nth-child(4)').as('Class III')
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (with image by link)', () => {
    // TODO BUG image can not be added by link
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get('.image___3tJLb > .ant-btn').click()
    cy.contains('Set Link').click()
    cy.get('#link').type(testData.imageLink)
    cy.get('[style="margin-bottom: 0px;"] > .ant-col > .ant-form-item-control > .ant-form-item-children > .ant-btn').click()
    cy.get('.ant-col-md-15 > :nth-child(1) > [type="submit"]').click()
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('add product (with image by AWS)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get('.image___3tJLb > .ant-btn').click()
    cy.intercept({ method: 'POST', url: '/api/doc' }).as('fileUpload')
    cy.get('input[type=file]').attachFile(testData.file.png)
    cy.wait('@fileUpload')
    cy.get('.ant-col-md-15 > :nth-child(1) > [type="submit"]').click()
    cy.intercept({ method: 'POST', url: '/api/v2/products' }).as('products')
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
    cy.wait('@products')
    cy.get('.ant-card-body').contains(testData.product.name)
  });

  it('not add product (without name)', () => {
    cy.contains('Add Product').click()
    cy.get('.ant-form-item-children > .ant-btn-primary').as('Add').click()
    cy.get('.ant-form-explain').should('contain.text', testData.messages.emptyNameMessage)
  });

  it('not add product (name is contain only spaces)', () => {
    cy.contains('Add Product').click()
    // TODO BUG: user can add product with name, that contain only spaces
    cy.get('#name').type('    ')
    cy.get('.ant-form-item-children > .ant-btn-primary').as('Add').click()
    cy.get('.ant-form-explain').should('contain.text', testData.messages.emptyNameMessage)
  });

  it('not add product (GMDN code not a 5-digit GMDN digital code)', () => {
    cy.contains('Add Product').click()
    // TODO BUG missing field GMDN validation. GMDN should be 5-digit code
    cy.get('#name').type(testData.product.name)
    cy.get(':nth-child(3) > .ant-col-16 > .ant-form-item-control > .ant-form-item-children > .ant-input-group-wrapper > .ant-input-wrapper > .ant-input')
      .as('GMDN').type('56khbyolhdpgebgoertbgireerejnvpejrtbveputbpeiorugpuiobtub5iutb4y5ofhpho4b45kj6bl3k5b34u5p6igbpse978o')
    cy.get('.ant-form-explain').should('contain.text', testData.messages.invalidGmdnMessage)
    cy.get('.ant-form-item-children > .ant-btn-primary').click()
  });

  it('not add product (with invalid image link)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get('.image___3tJLb > .ant-btn').click()
    cy.contains('Set Link').click()
    cy.get('#link').type('file/d/1KNYNw7dOCGEEksSYMtZbZXatDWJgMwp')
    cy.contains('Set Link').click()
    cy.get('.ant-form-explain').should('contain.text', testData.messages.invalidLinkMessage)
  });

  it('not add product (with empty image link)', () => {
    cy.contains('Add Product').click()
    cy.get('#name').type(testData.product.name)
    cy.get('.image___3tJLb > .ant-btn').click()
    cy.contains('Set Link').click()
    cy.get('[style="margin-bottom: 0px;"] > .ant-col > .ant-form-item-control > .ant-form-item-children > .ant-btn').click()
    cy.get('.ant-form-explain').should('contain.text', testData.messages.emptyLinkMessage)
  });

  it('close modal window "add product"', () => {
    cy.contains('Add Product').click()
    cy.get('.ant-modal-close-x').click()
    cy.get('.ant-modal-body').should('not.be.visible')
  });

  it('navigation (Home <-> Products)', () => {
    cy.get('.btn-group-vertical > :nth-child(1)').as('dashboard').click()
    cy.contains('Home').click()
    cy.location('pathname').should('not.include', 'products')

    cy.get('.btn-group-vertical > :nth-child(2)').click()
    cy.contains('Products').click()
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(400)
    cy.location().should((loc) => {
      expect(loc.pathname).to.eq('/products')
    });
  });
});
