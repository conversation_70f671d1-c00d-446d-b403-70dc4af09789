const randomString = require('../../support/randomString')

const testData = {
  user: {
    _id: '6012bda8caf26b395a619b4e',
    email: '<EMAIL>',
    password: 'test_ui_sku.password',
    name: 'TestUserUISKU',
    role: 'client',
    permission: ['products'],
    limitProducts: 10
  },
  product: {
    name: '<PERSON><PERSON><PERSON>',
    version: '0.01',
    manufacturer: 'SCHOTT minifab', // recommended 100 symbols
    description: 'A sterile, hand-held, manual surgical instrument', // recommended 500 symbols
    euLabeling: '90/385/EEC',
    'fdaBooklet': '',
    classification: 1,
    gmdn: [47569] // 5-digit GMDN digital code
  },
  sku: {
    name: 'GR2120',
    UDI: '4055724ABC123AR', // up to 25 characters long (https://www.gs1.org/industries/healthcare/udi)
    description: 'Fake description',
    country: 'Germany',
    countrySet: 'EU'
  },
  messages: {
    emptySkyMessage: 'Please! Enter SKU'
  },
  productID: null
};

context('Add SKU', () => {

  before(() => {
    cy.task('removeAllSku', testData.user._id);
    cy.task('removeUser', testData.user._id, testData.user.name);
    cy.task('addUser', testData.user);
    cy.task('addProduct', {
      data: testData.product,
      userId: testData.user._id,
      limitProducts: testData.user.limitProducts
    }).then((r) => {
     testData.productID = r.id
    });
  });

  after(() => {
    cy.task('deleteProducts', testData.user._id);
  })

  beforeEach(() => {
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(500)
    cy.intercept({ method: 'GET', url: '/api/session/check' }).as('session')
    cy.login(testData.user.email, testData.user.password).then((response) => {
      expect(response.body).to.not.have.property('error')
      cy.getCookie('__rd_api').should('exist')
      cy.intercept({ method: 'GET', url: '/api/v2/products' }).as('productsPage')
      cy.visit('/products');
      cy.wait('@session')
      cy.wait('@productsPage')
      cy.location().should((loc) => {
        expect(loc.pathname).to.eq('/products')
      });
      cy.get('.ant-table-tbody').contains(testData.product.name)
      cy.get(`[data-row-key=${testData.productID}] > :nth-child(7) > [style="display: flex; align-items: center;"] > :nth-child(1) > .btn-text___1KjVg`)
        .as('skuButtonInProductList')
        .click()
      cy.contains('Add SKU').click()
    })
  })

  afterEach(() => {
    cy.task('removeAllSku', testData.user._id);
  })

  it('add current sku (from product list)', () => {
    cy.get('#name').type(testData.sku.name)
    cy.intercept({ method: 'POST', url: '/api/products/sku' }).as('newSku')
    cy.get('[style="display: flex; justify-content: flex-end; margin-top: 10px;"] > .ant-btn-primary')
      .as('Add SKU').click()
    cy.wait('@newSku')
    cy.get('.ant-modal-body > :nth-child(1) > .ant-table-wrapper > .ant-spin-nested-loading > .ant-spin-container > .ant-table > .ant-table-content')
      .should('contain.text', testData.sku.name)

    // empty description if countries were not selected
    cy.contains(testData.sku.name).click()
    cy.get('.ant-empty-description').should('be.visible')
  });

  it('not add sku without name (from product list)', () => {
    cy.get('[style="display: flex; justify-content: flex-end; margin-top: 10px;"] > .ant-btn-primary')
      .as('Add SKU').click()
    cy.get('.ant-form-explain').should('contain.text', testData.messages.emptySkyMessage)
  });

  it('add sku with Basic UDI-DI', () => {
    cy.get('#name').type(testData.sku.name)
    cy.get('#udi').type(testData.sku.UDI)
    cy.intercept({ method: 'POST', url: '/api/products/sku' }).as('newSku')
    cy.get('[style="display: flex; justify-content: flex-end; margin-top: 10px;"] > .ant-btn-primary')
      .as('Add SKU').click()
    cy.wait('@newSku')
    cy.get('.ant-modal-body > :nth-child(1) > .ant-table-wrapper > .ant-spin-nested-loading > .ant-spin-container > .ant-table > .ant-table-content')
      .should('contain.text', testData.sku.name)

    // empty description if countries were not selected
    cy.contains(testData.sku.name).click()
    cy.get('.ant-empty-description').should('be.visible')
  });

  it('add sku with description', () => {
    cy.get('#name').type(testData.sku.name)
    cy.get('#description').type(testData.sku.description)

    cy.intercept({ method: 'POST', url: '/api/products/sku' }).as('newSku')
    cy.get('[style="display: flex; justify-content: flex-end; margin-top: 10px;"] > .ant-btn-primary')
      .as('Add SKU').click()
    cy.wait('@newSku')
    cy.get('.ant-modal-body > :nth-child(1) > .ant-table-wrapper > .ant-spin-nested-loading > .ant-spin-container > .ant-table > .ant-table-content')
      .should('contain.text', testData.sku.name)

    // saved description should be visible
    cy.contains(testData.sku.name).click()
    cy.get('#rcDialogTitle2').should('contain.text', `Description: ${testData.sku.description}`)
  });

  it('add sku with country from list', () => {
    cy.get('#name').type(testData.sku.name)

  });

  it.only('add sku with country from map', () => {

  });

  it('add sku with country set', () => {

  });

  //cy.get('.searchContainer___1DC_N > .ant-dropdown-trigger') -> cy.get('.ant-dropdown > .ant-dropdown-menu > .ant-dropdown-menu-item').contains(testData.sku.countrySet)


  //cy.get('.search___bceY4 > .ant-select-selection') choose country


  //on map cy.get('.DEU')
  //cy.get('.selectedCountriesList___13U-n > .ant-tag').contains(testData.sku.country) is visible
});

