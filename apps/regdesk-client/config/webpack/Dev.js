const WebpackBaseConfig = require('./Base');
const Dotenv = require('dotenv-webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');

class WebpackDevConfig extends WebpackBaseConfig {

  constructor() {
    super();
    this.config = {
      devtool: 'cheap-module-source-map',
      devServer: {
        contentBase: './src/',
        publicPath: '/',
        historyApiFallback: true,
        hot: true,
        inline: true,
        host: 'localhost',
        // stats: {
        //   modules: true,
        //   maxModules: 10000,
        // },
        port: 8000,
      },
      plugins:  [
        new HtmlWebpackPlugin({
          template: 'index.html',
        }),
        new Dotenv()
      ],
    };
  }
}

module.exports = WebpackDevConfig;
