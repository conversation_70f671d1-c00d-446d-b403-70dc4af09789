'use strict';

/**
 * Dist configuration. Used to build the
 * final output when running npm run dist.
 */
const path = require('path');
const webpack = require('webpack');
const WebpackBaseConfig = require('./Base');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const Dotenv = require('dotenv-webpack');

class WebpackDistConfig extends WebpackBaseConfig {
  constructor() {
    super();
    this.config = {
      cache: false,
      devtool: 'none',
      plugins: [
        new CleanWebpackPlugin(),
        new webpack.DefinePlugin({
          'process.env.NODE_ENV': '"production"',
        }),
        new webpack.optimize.AggressiveMergingPlugin(),
        new CopyPlugin([
          {
            from: path.resolve(__dirname, '../../src/static'),
            to: path.resolve(__dirname, '../../../regdesk-server/public/portal/static/'),
          },
          {
            from: path.resolve(__dirname, '../../src/firebase-messaging-sw.js'),
            to: path.resolve(__dirname, '../../../regdesk-server/public/portal/'),
          },
        ]),
        new HtmlWebpackPlugin({
          template: 'index.html',
        }),
        new Dotenv(),
        // new Dotenv({
        //   path: path.resolve(__dirname, '../../.env'),
        // }),
      ],
    };

    // Deactivate hot-reloading if we run dist build on the dev server
    this.config.devServer.hot = false;
    this.config.devServer.port = 80;
  }

  /**
   * Get the environment name
   * @return {String} The current environment
   */
  get env() {
    return 'dist';
  }
}

module.exports = WebpackDistConfig;
