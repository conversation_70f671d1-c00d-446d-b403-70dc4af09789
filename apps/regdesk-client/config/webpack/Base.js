'use strict';
const fs = require('fs');
const path = require('path');
const Dotenv = require('dotenv-webpack');

const npmBase = path.join(__dirname, '../../node_modules');
class WebpackBaseConfig {
  constructor() {
    this._config = {};
  }
  get includedPackages() {
    return [].map(pkg => fs.realpathSync(path.join(npmBase, pkg)));
  }
  set config(data) {
    this._config = Object.assign({}, this.defaultSettings, data);
    return this._config;
  }
  get config() {
    return this._config;
  }
  get env() {
    return 'dev';
  }
  get srcPathAbsolute() {
    return path.resolve('./src');
  }
  get nodeModulesPathAbsolute() {
    return path.resolve('./node_modules');
  }
  get defaultSettings() {
    const cssModulesQuery = {
      modules: true,
      importLoaders: 1,
      localIdentName: '[name]-[local]-[hash:base64:5]',
    };
    const postCssOptions = {
      plugins: [
        require('postcss-import')({
          // Import all the css files...
          onImport: function(files) {
            files.forEach(this.addDependency); // ...and add dependecies from the main.css files to the other css files...
          }.bind(this), // ...so they get hot–reloaded when something changes...
        }),
        require('postcss-simple-vars')(), // ...then replace the variables...
        require('postcss-focus')(), // ...add a :focus to ever :hover...
        require('autoprefixer')({
          // ...and add vendor prefixes...
          browsers: ['last 2 versions', 'IE > 8'], // ...supporting the last 2 major browser versions and IE 8 and up...
        }),
        require('postcss-reporter')({
          // This plugin makes sure we get warnings in the console
          clearMessages: true,
        }),
      ],
    };
    const sassLoaderOptions = {
      data: '@import "variables";',
      includePaths: [path.resolve(__dirname, '../../src/styles/utils')],
    };
    return {
      context: this.srcPathAbsolute,
      devtool: 'eval',
      devServer: {
        contentBase: './src/',
        publicPath: '/assets/',
        historyApiFallback: true,
        hot: true,
        inline: true,
        host: 'localhost',
        port: 8000,
      },
      entry: './index.js',
      module: {
        rules: [
          {
            test: /\.(js|jsx)$/,
            exclude: /node_modules/,
            use: ['babel-loader'],
          },
          {
            test: /^.((?!cssmodule).)*\.css$/,
            loaders: [
              { loader: 'style-loader' },
              { loader: 'css-loader' },
              { loader: 'postcss-loader', options: postCssOptions },
            ],
          },
          {
            test: /\.(png|jpg|gif|svg|mp4|ogg|woff|woff2|pdf)$/,
            loader: 'file-loader',
            options: {
              outputPath: 'assets',
            },
          },
          {
            test: /^.((?!cssmodule).)*\.(sass|scss)$/,
            loaders: [
              { loader: 'style-loader' },
              { loader: 'css-loader' },
              { loader: 'postcss-loader', options: postCssOptions },
              { loader: 'sass-loader', options: sassLoaderOptions },
            ],
          },
          {
            test: /^.((?!cssmodule).)*\.less$/,
            use: [
              {
                loader: 'style-loader',
              },
              {
                loader: 'css-loader',
                options: {
                  sourceMap: true,
                  modules: true,
                  localIdentName: '[local]___[hash:base64:5]',
                },
              },
              {
                loader: 'less-loader',
              },
            ],
          },
          {
            test: /^.((?!cssmodule).)*\.styl$/,
            loaders: [
              { loader: 'style-loader' },
              { loader: 'css-loader' },
              { loader: 'postcss-loader', options: postCssOptions },
              { loader: 'stylus-loader' },
            ],
          },
          {
            test: /\.(js|jsx)$/,
            include: [].concat(this.includedPackages, [this.srcPathAbsolute]),
            loaders: [{ loader: 'babel-loader' }],
          },
          {
            test: /\.cssmodule\.(sass|scss)$/,
            loaders: [
              { loader: 'style-loader' },
              {
                loader: 'css-loader',
                query: cssModulesQuery,
              },
              { loader: 'postcss-loader', options: postCssOptions },
              { loader: 'sass-loader', options: sassLoaderOptions },
            ],
          },
          {
            test: /\.cssmodule\.css$/,
            loaders: [
              { loader: 'style-loader' },
              {
                loader: 'css-loader',
                query: cssModulesQuery,
              },
              { loader: 'postcss-loader', options: postCssOptions },
            ],
          },
          {
            test: /\.cssmodule\.less$/,
            loaders: [
              { loader: 'style-loader' },
              {
                loader: 'css-loader',
                query: cssModulesQuery,
              },
              { loader: 'postcss-loader', options: postCssOptions },
              { loader: 'less-loader' },
            ],
          },
          {
            test: /\.cssmodule\.styl$/,
            loaders: [
              { loader: 'style-loader' },
              {
                loader: 'css-loader',
                query: cssModulesQuery,
              },
              { loader: 'postcss-loader', options: postCssOptions },
              { loader: 'stylus-loader' },
            ],
          },
        ],
      },
      output: {
        path: path.resolve('../regdesk-server/public/portal/'),
        filename: 'app.js',
        publicPath: '/',
      },
      plugins: [new Dotenv()],
      externals: {
        jquery: 'window.jQuery',
        TweenMax: 'TweenMax',
        TimelineMax: 'TimelineMax',
        Datamaps: 'Datamap',
        readXlsxFile: 'readXlsxFile',
      },
      resolve: {
        alias: {
          config: `${this.srcPathAbsolute}/config/${this.env}.js`,
        },
        extensions: ['.js', '.jsx', '.json'],
        modules: [this.srcPathAbsolute, 'node_modules'],
      },
    };
  }
}
module.exports = WebpackBaseConfig;
