import config from 'config';
import { message } from 'antd';
import api from '../utils/api';
import * as constants from '../const';
import store from '../stores';
import Session from './Session';
import Profile from './Profile';
import events from '../utils/events';

const AuthActions = {
  /**
   * Logout
   * @returns {Function}
   */
  logout() {
    api.auth
      .logout()
      .then(() => {
        localStorage.removeItem('token');
        Session.stop();
        store.dispatch({ type: constants.RESET_STORE });
        if (window.zE) window.zE('webWidget', 'hide');
        events.emit('logout');
      });
  },

  /**
   * Login
   * @param data
   * @returns {*}
   */
  login(data) {
    AuthActions.change({ loading: true });

    return api.auth
      .login(data)
      .then(payload => {
        Session.init(payload);
        AuthActions.change({ ...payload, loading: false });
        Profile.loadCustomTitles();

        // Show Zendesk Widget and prefill user data
        if (window.zE) {
          window.zE('webWidget', 'show');

          window.zE('webWidget', 'identify', {
            email: payload?.email,
            name: payload?.name,
          });
        }

        return payload;
      })
      .catch(err => AuthActions.change({ loading: false, message: (err && err.message) || '' }));
  },

  restorePassword(data, textMessage) {
    AuthActions.change({ loading: true });

    return api.auth
      .restorePassword(data)
      .then(() => {
        AuthActions.change({ loading: false, message: '' });
        message.success(textMessage);
      })
      .catch(err => AuthActions.change({ loading: false, message: (err && err.message) || '' }));
  },

  loginAzure({ esign }) {
    return api.auth.loginAzure({ esign })
      .then(({ redirect }) => {
        if (redirect) {
          if (esign) return redirect;

          window.location.href = redirect;
        }

        return null;
      });
  },

  loginOkta({ esign }) {
    return api.auth.loginOkta({ esign })
      .then(({ redirect }) => {
        if (redirect) {
          if (esign) return redirect;

          window.location.href = redirect;
        }

        return null;
      });
  },

  /**
   * Change auth reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.ACCOUNT_CHANGE, payload });
  },

  /**
   * Open site
   */
  openAdmin() {
    window.location.href = config.admin;
  },

  /**
   * Two-Step
   * */
  verify(data) {
    AuthActions.change({ loading: true });

    api.auth
      .verify(data)
      .then(payload => {
        Session.init(payload);
        AuthActions.change({ ...payload, loading: false });
      })
      .catch(err => AuthActions.change({ loading: false, message: (err && err.message) || '' }));
  },

  /**
   * Reset password
   * */
  resetPassword(data) {
    AuthActions.change({ loading: true });

    return api.auth
      .resetPassword(data)
      .then(() => {
        AuthActions.change({ loading: false, message: '' });
      })
      .catch(err => {
        AuthActions.change({ loading: false, message: (err && err.message) || '' });
        throw err;
      });
  },
};

export default AuthActions;
