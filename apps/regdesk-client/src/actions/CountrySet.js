import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const CountrySet = {
  /**
   * Get data
   * @param props
   */
  get(props = {}) {
    const { filters, pagination } = store.getState().countrySet;

    CountrySet.change({ loading: true });

    return api.countrySet
      .get({ filters, pagination, ...props })
      .then(data => {
        CountrySet.change({ ...data, loading: false });

        return data;
      })
      .catch(() => CountrySet.change({ loading: false }));
  },

  /**
   * Search
   * @param value
   * @param table
   */
  search(value, table = false) {
    const { pagination, filters } = store.getState().countrySet;
    const newFilters = { ...filters, name: value };

    CountrySet.change({ filters: newFilters });

    clearTimeout(this.timerId);

    this.timerId = setTimeout(() => {
      const props = { filters: newFilters, pagination };

      if (table) {
        props.pagination = { ...pagination, current: 1 };
      }

      CountrySet.get(props);
    }, 300);
  },

  /**
   * Remove by ID
   * @param id
   */
  remove(id) {
    const { pagination } = store.getState().countrySet;

    CountrySet.change({ loading: true });

    if (pagination.total !== 1 && pagination.total % pagination.pageSize === 1) {
      pagination.current -= 1;
    }

    return api.countrySet
      .remove({ countrySetId: id })
      .then(() => CountrySet.change({ pagination: { current: pagination.current } }))
      .catch(() => CountrySet.change({ loading: false }));
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.countrySet.add(data).then(() => message.success('Country Set added'));
  },

  /**
   * Update
   * @param body
   * @returns {*}
   */
  update(body) {
    CountrySet.change({ loading: true });

    return api.countrySet
      .update(body)
      .then(data => {
        message.success('Country Set updated');
        CountrySet.change({ ...data, loading: false });
      })
      .catch(() => CountrySet.change({ loading: false }));
  },

  /**
   * Close all modals
   */
  closeModal() {
    CountrySet.change({ showModal: '', item: {} });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.COUNTRY_SET_CHANGE, payload });
  },
};

export default CountrySet;
