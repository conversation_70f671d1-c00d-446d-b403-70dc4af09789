import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get() {
    Actions.change({ loading: true });

    api.access
      .get()
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get users
   * @returns {Function}
   */
  getUsers() {
    return api.access.getUsers().then(({ list }) => Actions.change({ list }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}){
    store.dispatch({ type: constants.ACL_CHANGE, payload });
  },
};

export default Actions;
