import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import Map from './Map';

const Actions = {
  /**
   * Get list task fpr ccp
   * @param projectId
   * @param params
   * @returns {Promise<void | *>}
   */
  get(projectId, params) {
    return api.ccpProjects.getTask(projectId, params);
  },

  /**
   * Get data
   * @returns {Function}
   */
  getById(id) {
    Actions.change({ loading: true });

    return api.ccpTasks
      .getById(id)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.ccpTasks.add(data);
  },

  /**
   * Update
   * @param data
   */
  update(data) {
    return api.ccpTasks.update(data);
  },

  /**
   * Share
   * @param data
   */
  share(data) {
    return api.ccpTasks.share(data);
  },

  /**
   * Modify Strategy
   */

  modifyStrategy(data) {
    return api.ccpTasks.modifyStrategy(data);
  },

  /**
   * Update Task Procedures
   */

  updateProcedures(data) {
    return api.ccpTasks.updateProcedures(data).then((response) => {
      const { item } = store.getState().ccpTasks;

      Actions.change({ item: { ...item, regulationCopiedData: response.task.regulationCopiedData } });
    });
  },

  /**
   * Update Documents
   */
  updateDocuments(data) {
    return api.ccpTasks.updateDocuments(data).then(response => {
      const { item } = store.getState().ccpTasks;

      Actions.change({ item: { ...item, regulationCopiedData: response.task.regulationCopiedData } });
    });
  },

  /**
   * Update DMS Documents
   */
  updateDMSDocuments(data) {
    return api.ccpTasks.updateDMSDocuments(data).then(response => {
      const { item } = store.getState().ccpTasks;

      Actions.change({ item: { ...item, regulationCopiedData: response.task.regulationCopiedData } });
    });
  },

  /**
   * Remove
   * @param id
   */
  remove(id) {
    return api.ccpTasks.remove(id);
  },

  /**
   * Get map
   */
  getMap() {
    return api.ccpRelease
      .getMap()
      .then(data => {
        Map.change({ data: data.list }, 'controlTask');

        return data.list;
      });
  },

  /**
   * Get map
   * @param map
   */
  getExistMap({ projectId, map }) {
    Map.change({ loading: true }, map);

    return api.ccpProjects
      .getTask(projectId, { map: true })
      .then(({ list }) => Map.change({ data: list, loading: false }, map))
      .catch(() => Map.change({ loading: false }, map));
  },

  /**
   * Get Changes
   * @param params
   * @returns {Promise<void | *>}
   */
  getChanges(params = {}) {
    return api.ccpChanges.get(params);
  },

  /**
   * Get Changes
   * @param params
   * @returns {Promise<void | *>}
   */
  getChangesByCountries(params = {}) {
    return api.ccpChanges.getByCountries(params);
  },

  /**
   * Get Actions Required
   * @returns {Promise<void | *>}
   */
  getActionRequired() {
    return api.ccpReport.getActionRequired();
  },

  /**
   * Get workflow
   * @param params
   * @returns {Promise<void | *>}
   */
  getWorkflow(params) {
    return api.ccpRelease.getWorkflow(params);
  },

  /**
   * Approve
   * @param params
   * @returns {Promise<void | *>}
   */
  approve(params) {
    Actions.change({ loading: true });

    return api.ccpTasks
      .approve(params)
      .finally(() => Actions.change({ loading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.CCP_TASKS_CHANGE, payload });
  },
};

export default Actions;

