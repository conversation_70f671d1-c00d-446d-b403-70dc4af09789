import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import i18n from '../utils/i18n';

const { t } = i18n;

const Actions = {
  /**
   * Get data
   * @param props
   */
  get(props) {
    const { filters, pagination } = store.getState().sku;
    const { projectId: ccpProjectId } = store.getState().ccpProjects;
    const { item: taskItem } = store.getState().ccpTasks;
    const { item: projectItem } = store.getState().projects;
    const { _id: withoutTask } = taskItem || {};
    const { _id: projectId } = projectItem || {};

    const params = { ...props };

    if (projectId) params.projectId = projectId;
    if (ccpProjectId) params.projectId = ccpProjectId;
    if (withoutTask) params.withoutTask = withoutTask;

    Actions.change({ loading: true });

    return api.sku
      .get({ filters, pagination, ...params })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Search
   * @param value
   * @param table
   */
  search(value, table = false, module) {
    const { pagination, filters } = store.getState().sku;
    const newFilters = { ...filters, name: value };

    Actions.change({ filters: newFilters });

    clearTimeout(this.timerId);

    this.timerId = setTimeout(() => {
      const props = { filters: newFilters, pagination, module };

      if (table) {
        props.table = true;
        props.pagination = { ...pagination, current: 1 };
      }

      Actions.get(props);
    }, 300);
  },

  /**
   * Upload SKU from file
   * @param text
   */
  getListFromFile(text) {
    const { list } = store.getState().sku;

    const newList = [];

    if (list?.length > 0 && !!text) {
      for (let i = 0; i < list.length; i++) {
        const { name } = list[i];
        const matchFoundIndex = text.search(name);

        if (matchFoundIndex !== -1) {
          newList.push(list[i]);
        }
      }

      Actions.change({ selectedList: newList });
    }
  },

  /**
   * Get tracking for SKU
   * @param props
   */
  getTracking(props) {
    Actions.change({ tracking: { loading: true } });

    api.tracking
      .get(props)
      .then(data => Actions.change({ tracking: { ...data, loading: false } }))
      .catch(() => Actions.change({ tracking: { loading: false } }));
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: '', menuItem: '', item: {} });
  },

  /**
   * Remove by ID
   * @param id
   * @param module
   */
  remove(id, module) {
    Actions.change({ loading: true });

    return api.sku.remove(id, module);
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.sku.add(data);
  },

  /**
   * Update
   * @param body
   * @returns {*}
   */
  update(body) {
    Actions.change({ modalLoading: true });

    return api.sku
      .update(body)
      .then(data => {
        message.success(t('Glossary.SKUUpdated_one'));
        Actions.change({ ...data, modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  updateMultiple(body) {
    Actions.change({ modalLoading: true });

    return api.sku
      .updateMultiple(body)
      .then(data => {
        message.success(t('Glossary.SKUUpdated_one'));
        Actions.change({ ...data, modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * UpdateMany
   * @param body
   * @returns {*}
   */
  updateMany(body) {
    Actions.change({ modalLoading: true });

    return api.sku
      .updateMany(body)
      .then(data => {
        message.success("SKU'S updated");
        Actions.change({ modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Get by id
   * @param body
   * @returns {*}
   */
  getById() {
    const { ids, menuItem } = store.getState().sku;

    Actions.change({ modalLoading: true });

    return api.sku
      .getById(ids)
      .then(({ sku }) => {
        Actions.change({ modalLoading: false, item: sku, menuItem: menuItem || sku.countries[0] || '' });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  addPartNumber({ skuId, countryId, partNumber }) {
    const { item } = store.getState().sku;

    Actions.change({ modalLoading: true });

    return api.sku
      .partNumberAdd({ skuId, countryId, partNumber })
      .then((data) => {
        const newItem = { ...item };
        const index = newItem.countriesInfo.findIndex(
          ({ country }) => country === countryId
        );

        newItem.countriesInfo[index].partNumbers = data.partNumbers;
        Actions.change({ item: newItem, modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  updatePartNumber({ skuId, countryId, oldPartNumber, partNumber }) {
    const { item } = store.getState().sku;

    Actions.change({ modalLoading: true });

    return api.sku
      .partNumberUpdate({ skuId, countryId, oldPartNumber, partNumber })
      .then((data) => {
        const newItem = { ...item };
        const index = newItem.countriesInfo.findIndex(
          ({ country }) => country === countryId
        );

        newItem.countriesInfo[index].partNumbers = data.partNumbers;
        Actions.change({ item: newItem, modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  removePartNumber({ skuId, countryId, partNumber }) {
    const { item } = store.getState().sku;

    Actions.change({ modalLoading: true });

    return api.sku
      .partNumberRemove({ skuId, countryId, partNumber })
      .then((data) => {
        const newItem = { ...item };
        const index = newItem.countriesInfo.findIndex(
          ({ country }) => country === countryId
        );

        newItem.countriesInfo[index].partNumbers = data.partNumbers;
        message.success('Part Number is deleted');
        Actions.change({ item: newItem, modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.SKU_CHANGE, payload });
  },
};

export default Actions;
