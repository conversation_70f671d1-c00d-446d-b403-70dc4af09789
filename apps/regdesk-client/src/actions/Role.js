import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

let timerId = null;

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    const { filters } = store.getState().role;

    Actions.change({ loading: true });

    api.role
      .get({ filters, ...props })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Search
   * @param value
   */
  search(value) {
    const { filters } = store.getState().role;
    const newFilters = { ...filters, title: value };

    if (!value) delete newFilters.title;

    Actions.change({ filters: newFilters });
    clearTimeout(timerId);

    timerId = setTimeout(() => Actions.get(), 500);
  },

  /**
   * Get role
   * @returns {Function}
   */
  getById(idr) {
    Actions.change({ modalLoading: true });

    return api.role
      .getById(idr)
      .then(data => {
        Actions.change({ ...data, modalLoading: false });

        return data;
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Remove by ID
   * @param id
   * @param assignee
   */
  remove(id) {
    return api.role.remove(id);
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.role.add(data);
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  update(data) {
    return api.role.update(data);
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: '', modalLoading: false, idr: null, role: {} });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}){
    store.dispatch({ type: constants.ROLE_CHANGE, payload });
  },

  /**
   * Filter
   * @param newFilter
   */
  filter(newFilter = {}) {
    const { filters = {} } = store.getState().role;
    const nextFilters = { ...filters, ...newFilter };

    Actions.get({ filters: nextFilters });
  },

  /**
   * Clean filter
   * @param key
   */
  cleanFilter(key) {
    const { filters = {} } = store.getState().role;

    delete filters[key];

    Actions.get({ filters });
  },

  /**
   * Clean all filters
   */
  cleanAllFilters() {
    Actions.get({ filters: {} });
  },
};

export default Actions;

