import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import { getDefaultDashboard, getDefaultLayouts, strings } from '../containers/Dashboard/utils';

const { NOTIFICATIONS_REGULATORY, TODO_LIST } = strings;

const Actions = {
  getList() {
    Actions.change({ loading: true });

    api.dashboard
      .getList()
      .then(({ list = [], isBausch }) => {
        if (!list.length) {
          const { permission } = store.getState().account;

          Actions.change({
            isBausch,
            list: [{ ...getDefaultDashboard(isBausch, permission) }],
            loading: false
          });

          return;
        }

        Actions.change({ isBausch, list, loading: false });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  getWidgetData(widgetId, params) {
    Actions.change({ loading: true });

    const { dashboardSettings } = store.getState().account;
    const requestParams = { ...params };

    if (widgetId === NOTIFICATIONS_REGULATORY) requestParams.dashboardSettings = { ...dashboardSettings };

    api.dashboard
      .get({ widgetId, params: requestParams })
      .then((widgetsData = {}) => {
        const { sorter = 'ascend' } = requestParams;
        const oldData = store.getState().dashboard.widgetsData;
        const customData = {};

        if (widgetId === TODO_LIST) {
          customData[`${TODO_LIST}_${sorter}`] = { ...widgetsData[TODO_LIST] };
        }

        Actions.change({
          widgetsData: { ...oldData, ...widgetsData, ...customData },
          loading: false
        });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  getPage(widgetId, { label, page = 1, sorter, params }) {
    Actions.change({ loading: true });

    const requestParams = { ...params };

    const { dashboardSettings = {} } = store.getState().account;
    const { recentUpdateDate: storedDate, recentUpdateIds: storedIds } = dashboardSettings;

    const { filters, subWidgetsData = {}, dashboardSettings: settings = {} } = store.getState().dashboard;
    const { recentUpdateDate } = settings;
    const { recentUpdateIds = [] } = subWidgetsData;

    if (widgetId === NOTIFICATIONS_REGULATORY) {
      if (recentUpdateDate) {
        requestParams.dashboardSettings = { recentUpdateDate, recentUpdateIds };
      } else if (storedDate) {
        requestParams.dashboardSettings = { recentUpdateDate: storedDate, recentUpdateIds: storedIds };
      }
    }

    api.dashboard
      .getPage({ widgetId, label, page, filters, sorter, params: requestParams })
      .then(data => Actions.change({ filters, sorter, subWidgetsData: data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  getExistingData(props) {
    const { widgetId } = props || {};

    const { dashboardSettings = {} } = store.getState().account;
    const { subWidgetsData = {}, dashboardSettings: settings = {} } = store.getState().dashboard;
    const { recentUpdateDate: storedDate, recentUpdateIds: storedIds } = dashboardSettings;

    const { recentUpdateDate } = settings;
    const { recentUpdateIds = [] } = subWidgetsData;
    const params = {};

    if (widgetId === NOTIFICATIONS_REGULATORY) {
      if (recentUpdateDate) params.dashboardSettings = { recentUpdateDate, recentUpdateIds };
      else if (storedDate) params.dashboardSettings = { recentUpdateDate: storedDate, recentUpdateIds: storedIds };
    }

    api.dashboard.getExistingData({ ...props, params }).then(data => Actions.change({ ...data }));
  },

  filter(newFilter = {}) {
    const { filters = {} } = store.getState().dashboard;
    const nextFilters = { ...filters, ...newFilter };

    if ('countryId' in newFilter && newFilter.countryId === filters.countryId) {
      delete nextFilters.countryId;
    }

    Actions.change({ filters: nextFilters });
  },

  cleanFilter(key) {
    const { filters = {} } = store.getState().dashboard;

    delete filters[key];
    Actions.change({ filters });
  },

  cleanAllFilters() {
    Actions.change({ filters: {} });
  },

  addDashboard(title, tempId) {
    if (!title.trim()) return;

    const { list: oldData, isBausch } = store.getState().dashboard;
    const { permission } = store.getState().account;

    const newDashboard = {
      title,
      widgetIds: [],
      layouts: getDefaultLayouts(),
      defaultDashboard: { ...getDefaultDashboard(isBausch, permission) }
    };

    const list = [...oldData, { _id: tempId, ...newDashboard }];

    Actions.change({ list, loading: true });

    api.dashboard
      .add(newDashboard)
      .then(({ _id, defaultDashboard = {} }) => {
        const { _id: defaultId } = defaultDashboard;

        let updatedList = [...oldData, { _id, ...newDashboard }];

        if (defaultId) {
          updatedList = [
            { ...oldData[0], _id: defaultId },
            { _id, ...newDashboard }
          ];
        }

        Actions.change({ list: updatedList, loading: false });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  renameDashboard(title, dashboardId) {
    const oldData = store.getState().dashboard.list;
    const index = oldData.findIndex(({ _id }) => _id === dashboardId);

    if (!title.trim() || !dashboardId || index < 0) {
      return;
    }

    const list = [
      ...oldData.slice(0, index),
      { ...oldData[index], title },
      ...oldData.slice(index + 1),
    ];

    Actions.change({ list });
  },

  deleteDashboard(dashboardId) {
    const oldData = store.getState().dashboard.list;
    const index = oldData.findIndex(({ _id }) => _id === dashboardId);

    if (!dashboardId || index < 0) {
      return;
    }

    const list = [
      ...oldData.slice(0, index),
      ...oldData.slice(index + 1),
    ];

    Actions.change({ list, loading: true });

    if (dashboardId === strings.DEFAULT_DASHBOARD) {
      Actions.change({ loading: false });

      return;
    }

    api.dashboard
      .remove(dashboardId)
      .then(() => Actions.change({ loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  removeWidget(widgetId, dashboardId) {
    const oldData = store.getState().dashboard.list;
    const index = oldData.findIndex(({ _id }) => _id === dashboardId);

    if (!widgetId || !dashboardId || index < 0) {
      return;
    }

    const widgetIds = [...oldData[index].widgetIds.filter(id => id !== widgetId)];

    const oldLayouts = oldData[index].layouts;
    const layouts = {};

    Object
      .entries(oldLayouts)
      .forEach(([breakpoint, value]) => {
        layouts[breakpoint] = value.filter(({ i }) => i !== widgetId);
      });

    const list = [
      ...oldData.slice(0, index),
      { ...oldData[index], widgetIds, layouts },
      ...oldData.slice(index + 1),
    ];

    Actions.change({ list });
  },

  addDefaultDashboard({ title, widgetIds, layouts }) {
    if (!title.trim()) return;

    const newDashboard = { title, widgetIds, layouts };
    const list = [{ _id: strings.DEFAULT_DASHBOARD, ...newDashboard }];

    Actions.change({ list, loading: true });

    api.dashboard
      .add(newDashboard)
      .then(({ _id }) => {
        const updatedList = [{ _id, ...newDashboard }];

        Actions.change({ list: updatedList, loading: false });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update
   * @param props
   */
  async setUserDashboardSettings({ recentUpdateDate }) {
    Actions.change({ loading: true });

    const { _id } = store.getState().account;
    const { dashboardSettings = {}, subWidgetsData = {} } = store.getState().dashboard;
    const { recentUpdateIds = [] } = subWidgetsData;

    api.user
      .updateDashboardSettings(_id, { recentUpdateDate, recentUpdateIds })
      .then(() => Actions.change({
        loading: false,
        dashboardSettings: { ...dashboardSettings, recentUpdateDate, recentUpdateIds }
      }));

  },

  /**
   * Update
   * @param dashboardId
   * @param props
   */
  update({ dashboardId, ...props }) {
    const { list = [], ...restProps } = props;

    Actions.change({ list, loading: true });

    if (dashboardId === strings.DEFAULT_DASHBOARD) {
      const { title, widgetIds, layouts } = restProps;

      Actions.addDefaultDashboard({ title, widgetIds, layouts });

      return;
    }

    api.dashboard
      .update(dashboardId, restProps)
      .then(() => Actions.change({ loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.DASHBOARD_CHANGE, payload });
  },
};

export default Actions;
