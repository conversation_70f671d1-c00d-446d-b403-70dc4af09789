import FileSaver from 'file-saver';
import { message } from 'antd';

import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import Map from './Map';

const makePayload = (filters = {}) => ({
  filters,
  map: 'checklists'
});

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    const {
      sorter,
      filters,
      pagination,
      expandedRowKeys,
      searchValue,
      advancedSearchList,
      advancedSearchValue,
    } = store.getState().checklists;

    Actions.change({ loading: true });

    return api.checklists
      .get({ sorter, filters, pagination, searchValue, advancedSearchList, advancedSearchValue, ...props })
      .then(data => {
        if (expandedRowKeys.length === 0 && data.list.length > 0) {
          expandedRowKeys.push(data.list[0]._id);
        }

        Actions.change({ ...data, expandedRowKeys, loading: false });

        const { filters: dataFilters } = data;
        let { list } = data;

        list = list.map(({ countryId }) => ({ countryId }));

        return { list, filters: dataFilters };
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Search
   * @param value
   * @param sortByProduct
   */
  search(value, sortByProduct = true) {
    const { pagination } = store.getState().checklists;

    Actions.change({ searchValue: value });

    clearTimeout(this.timerId);

    this.timerId = setTimeout(() => {
      const { searchValue } = store.getState().checklists;

      Actions.change({
        pagination: { ...pagination, current: 1 }
      });

      Actions.get({ searchValue, sortByProduct });
    }, 2500);
  },

  /**
   * Advanced Search
   */
  advancedSearch() {
    const { advancedSearchList, advancedSearchValue } = store.getState().checklists;

    Actions.change({ loading: true, advancedSearchEnabled: true });

    api.checklists
      .get({ advancedSearchList, advancedSearchValue, pagination: {} })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get Report
   */
  getReport() {
    Actions.change({ loading: true });

    api.checklists
      .getDocNames()
      .then(data => {
        Actions.changeReport(data);
        Map.change({ data: data.list }, 'checklistsReport');
        Actions.change({ loading: false });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get Summary Logs
   */
  getSummaryLogs() {
    Actions.change({ loading: true });

    const { item } = store.getState().checklists;

    api.checklists
      .getSummaryLogs(item._id)
      .then(({ summaryLogs }) => {
        Actions.change({ summaryLogs, loading: false });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Check if user has Type and SubType with name Not For Registration
   */
  checkTypesSubTypes() {
    return api.checklists.checkTypesSubTypes();
  },

  /**
   * Check if checklist exist with productId / productFamilyId and country
   */
  async checkProductAndCountry(data = {}) {
    return await api.checklists.checkProductAndCountry(data);
  },

  /**
   * Export
   * @param data
   */
  export(data) {
    return api.checklists.export(data);
  },

  exportReport(data) {
    api.checklists
      .exportReport(data)
      .then((response) => {

        const filename = response.headers['content-disposition'].replace(/.+filename="(.+)"/, '$1');

        const s2ab = s => {
          const buf = new ArrayBuffer(s.length);
          const view = new Uint8Array(buf);

          for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;

          return buf;
        };

        const blob = new Blob([s2ab(response.data)], {
          type: 'application/vnd.ms-excel;',
        });

        FileSaver.saveAs(blob, filename);

        Actions.change({ loading: false });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Make all DCT documents reusable/unreusable
   */
  changeStateReusableAllDocs(state) {
    const { item } = store.getState().checklists;

    return api.checklists.changeStateReusableAllDocs({ checklistId: item._id, state });
  },

  /**
   * Change Report
   * @param data
   */
  changeReport(data = {}) {
    const { report } = store.getState().tracking;

    Actions.change({ report: { ...report, ...data } });
  },

  /**
   * Get data by document name
   * @param name
   * @returns {Function}
   */
  getByDocName(name = '') {
    Actions.change({ loading: true, docName: name });

    api.checklists
      .get({ docName: name })
      .then(({ list }) => Actions.change({ docList: list, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  getTypes(params) {
    Actions.change({ loading: true });

    api.checklists.getTypes(params)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  addType(data) {
    return api.checklists.addType(data);
  },

  addSubType(data) {
    return api.checklists.addSubType(data);
  },

  hideType(body) {
    Actions.change({ hidding: body._id });

    return api.checklists
      .hideType(body)
      .then(({ type }) => {
        const updatedTypes = [...store.getState().checklists.types];
        const index = updatedTypes.findIndex(i => i._id === type._id);

        updatedTypes.splice(index, 1, type);
        Actions.change({ hidding: null, types: updatedTypes });
      })
      .catch(() => Actions.change({ hidding: null }));
  },

  hideSubType(body) {
    Actions.change({ hidding: body._id });

    return api.checklists
      .hideSubType(body)
      .then(({ subtype }) => {
        const updatedSubTypes = [...store.getState().checklists.subtypes];
        const index = updatedSubTypes.findIndex(i => i._id === subtype._id);

        updatedSubTypes.splice(index, 1, subtype);
        Actions.change({ hidding: null, subtypes: updatedSubTypes });
      })
      .catch(() => Actions.change({ hidding: null }));
  },
  /**
   * Expanded or collapse a table row
   * @param {*} expanded
   * @param {*} id
   */
  expand(expanded, id) {
    const { expandedRowKeys } = store.getState().checklists;

    const newExpandedRowKeys = [...expandedRowKeys];

    if (expanded) {
      const index = newExpandedRowKeys.indexOf(id);

      if (index === -1) {
        newExpandedRowKeys.push(id);
      }
    } else {
      const index = newExpandedRowKeys.indexOf(id);

      if (index > -1) {
        newExpandedRowKeys.splice(index, 1);
      }
    }

    Actions.change({ expandedRowKeys: newExpandedRowKeys });
  },

  /**
   * Remove by ID
   * @param id
   */
  remove(id) {
    Actions.change({ loading: true });

    return api.checklists.remove(id);
  },

  /**
   * Update ACL by ID
   * @param id
   * @param acl
   */
  updateAcl({ id, acl }) {
    Actions.change({ loading: true });

    return api.checklists
      .updateAcl({ id, acl })
      .then(() => {
        Actions.get({ sortByProduct: true });
        message.success('Checklist ACL updated');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update ACL
   * @param acl
   * @param revoke
   * @param props
   */
  updateAclForMany({ acl, revoke = false, ...props }) {
    Actions.change({ sharingAll: 0 });

    return api.checklists.updateAclForMany({ acl, revoke, ...props });
  },

  /**
   * Clone
   * @param data
   */
  clone(data) {
    return api.checklists.clone(data);
  },

  /**
   * Clone History
   * @param id
   */
  getCloneHistory(id) {
    return api.checklists.getCloneHistory(id);
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.checklists.add(data);
  },

  /**
   * Update
   * @param data
   */
  update(data) {
    return api.checklists.update(data);
  },

  /**
   * Get archive
   * @param data
   * @returns {*|never}
   */
  archive(data) {
    return api.checklists.archive(data);
  },

  /**
   * Get Checklist Subtypes
   * @param data
   */
  getChecklistSubtypes(params) {
    Actions.change({ loading: true });

    return api.checklists.getSubtypes(params)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Filter
   * @param newFilter
   */
  filter(newFilter = {}, sortByProduct = false) {
    const { filters = {}, pagination } = store.getState().checklists;
    const nextFilters = { ...filters, ...newFilter };

    if ('countryId' in newFilter && newFilter.countryId === filters.countryId) {
      delete nextFilters.countryId;
    }

    Actions.get({ sortByProduct, filters: nextFilters, pagination: { ...pagination, current: 1 } })
      .then(data => Actions.getMap(makePayload(nextFilters), data));
  },

  /**
   * Set filters
   * @param newFilter
   */
  setFilter(newFilter = {}) {
    const { filters = {} } = store.getState().checklists;
    const nextFilters = { ...filters, ...newFilter };

    Actions.change({ filters: nextFilters });
    Map.change({ filters: nextFilters }, 'checklists');
  },

  /**
   * Clean filter
   * @param key
   */
  cleanFilter(key, sortByProduct = false) {
    const { filters = {} } = store.getState().checklists;

    delete filters[key];

    Actions.get({ filters, sortByProduct })
      .then(data => Actions.getMap(makePayload(filters), data));
  },

  /**
   * Clean all filters
   */
  cleanAllFilters(sortByProduct = false) {
    Actions.change({ advancedSearchList: [], advancedSearchValue: '', isAdvancedSearch: false });

    Actions.get({ filters: {}, sortByProduct })
      .then(data => Actions.getMap(makePayload(), data));
  },

  /**
   * Get bi Id
   * @param checklistId
   */
  getById(checklistId) {
    return api.checklists.getById(checklistId).then(data => Actions.change({ ...data }));
  },

  /**
   * Add doc
   * @param data
   * @returns {*}
   */
  addDoc(data) {
    return api.checklists.addDoc(data);
  },

  /**
   * Get doc by id
   * @param docId
   */
  getDoc(docId) {
    return api.checklists.getDoc(docId).then(data => {
      Actions.change(data);
    });
  },

  /**
   * Remove Doc
   * @param docId
   * @returns {*}
   */
  removeDoc(docId) {
    return api.checklists.removeDoc({ docId });
  },

  /**
   * Update Doc
   * @param data
   */
  updateDoc(data) {
    return api.checklists.updateDoc(data);
  },

  /**
   * Attach file to docs
   * @param data
   * @returns {*}
   */
  attach(data) {
    return api.checklists.attach(data);
  },

  /**
   * Get data form map
   * @param filters
   * @param map
   * @param selected
   * @param {Object} isDataAlreadyExists
   */
  getMap({ map, selected, ...props }, isDataAlreadyExists = {}) {
    const { filters, advancedSearchList, advancedSearchValue } = store.getState().checklists;

    Map.change({ loading: true }, map);

    if (isDataAlreadyExists.list) {
      const { list: data, filters: nextFilters } = isDataAlreadyExists;

      return Map.change({ data, filters: nextFilters, loading: false }, map);
    }

    api.checklists
      .get({ filters, advancedSearchList, advancedSearchValue, ...props, map: true })
      .then(({ list, ...payload }) => Map.change({ data: list, ...payload, selected, loading: false }, map))
      .catch(() => Map.change({ loading: false }, map));
  },

  getDocNames() {
    const { filters, searchValue, advancedSearchList, advancedSearchValue } = store.getState().checklists;

    Actions.change({ loading: true });

    api.checklists
      .getDocNames({ distinct: true, filters, searchValue, advancedSearchList, advancedSearchValue })
      .then(({ list }) => Actions.change({ docNames: list, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Search for doc names
   * @param value
   */
  searchDocNames(value) {
    Actions.change({ searchDocName: value, searchAssigneeValue: '' });

    clearTimeout(this.timerId);

    this.timerId = setTimeout(() => {
      const { searchDocName, filters, searchValue, advancedSearchList, advancedSearchValue } = store.getState().checklists;

      Actions.change({ loading: true });

      api.checklists
        .getDocNames({ name: searchDocName, distinct: true, filters, searchValue, advancedSearchList, advancedSearchValue })
        .then(({ list }) => Actions.change({ docNames: list, loading: false, docNamePaginationCurrent: 1 }))
        .catch(() => Actions.change({ loading: false }));
    }, 300);
  },

  /**
   * Search for checklist names
   * @param value
   */
  searchChecklistNames(value) {
    Actions.change({ searchByName: value });

    const { docName, docNames, searchByName } = store.getState().checklists;
    const target = searchByName.trim();
    const filter = { docName };

    if (!docName) return;

    if (target && docNames?.includes(docName)) filter.searchByName = target;

    Actions.change({ loading: true });

    api.checklists
      .get(filter)
      .then(({ list }) => Actions.change({ docList: list, loading: false, docNamePaginationCurrent: 1 }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add comment
   * @param filters
   * @param map
   */
  addDocComment({ message, mentionedUsers }) {
    const { doc } = store.getState().checklists;

    if (!doc.comments) {
      doc.comments = [];
    }

    Actions.change({ loading: true });

    return api.checklists
      .addDocComment({ docId: doc._id, body: { message, mentionedUsers } })
      .then(({ comment }) => Actions.change({ loading: false, doc: { ...doc, comments: [...doc.comments, comment] } }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Resolve comment
   * @param filters
   * @param map
   */
  resolveComment(commentId) {
    const { doc, item } = store.getState().checklists;

    return api.comments.resolve(commentId, { module: 'dct', checklistId: item._id, doc }).then(({ comment }) => {
      const commentIndex = doc.comments.findIndex(c => c._id === commentId);

      doc.comments.splice(commentIndex, 1, comment);
      Actions.change({ doc: { ...doc, comments: doc.comments } });
    });
  },

  setAssignees(assignees) {
    const { item } = store.getState().checklists;

    Actions.change({ loading: true });

    return api.checklists.setAssignees({ checklistId: item._id, assignees }).then(() =>
      Actions.getById(item._id)
        .then(() => Actions.change({ loading: false }))
        .catch(() => Actions.change({ loading: false }))
    );
  },

  /**
   * Approve Checklist
   * @param props
   */
  approve(props) {
    Actions.change({ loading: true });

    return api.checklists.approve(props).finally(() => Actions.change({ loading: false }));
  },

  /**
   * Approve Doc
   * @param props
   */
  approveDoc(props) {
    Actions.change({ loading: true });

    return api.checklists.approveDoc(props).finally(() => Actions.change({ loading: false }));
  },

  activateDocsSubmission(props) {
    Actions.change({ loading: true });

    return api.checklists.activateDocsSubmission(props).finally(() => Actions.change({ loading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.CHECKLISTS_CHANGE, payload });
  },
};

export default Actions;
