import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';


const Actions = {
  /**
   * Get data
   * @param props
   */
  get(props) {
    Actions.change({ loading: true });

    return api.skuContact
      .get(props)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }))
  },

  /**
   * Get
   * @param props
   */
  getByType(props) {
    Actions.change({ form: { loading: true }});

    api.skuContact
      .get(props)
      .then(data => Actions.change({ form: { ...data, loading: false }}))
      .catch(() => Actions.change({ form: { loading: false }}))
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.skuContact.add(data)
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  update(data) {
    return api.skuContact.update(data)
  },

  /**
   * Remove from product
   */
  remove(data) {
    return api.skuContact.remove(data)
  },

  /**
   * Add To SKU Contact
   */
  addToSkuExistingContact(data) {
    return api.skuContact.addToSkuExistingContact(data)
  },

  /**
   * Add to All Countries Log
   */
  addToAllCountries(data) {
    return api.skuContact.addToAllCountries(data)
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}){
    store.dispatch({ type: constants.SKU_CONTACT_CHANGE, payload });
  },
};

export default Actions;
