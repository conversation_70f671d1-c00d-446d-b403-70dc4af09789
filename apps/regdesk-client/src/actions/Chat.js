import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    const { limit } = store.getState().chat;

    if (!props.timer) {
      Actions.change({ loading: true });
    }

    return api.chat
      .get({ limit, ...props })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get by id
   * @returns {Function}
   */
  getById(id, props = {}) {
    const { item = {} } = store.getState().chat;
    const newProps = { ...props };

    if (!props.timer && !props.hide) {
      Actions.change({ loadingItem: true });
    }

    if (!('skip' in newProps) && 'skip' in item) {
      newProps.skip = item.skip;
    }

    return api.chat
      .getById(id, newProps)
      .then(data => Actions.change({ ...data, loadingItem: false }))
      .catch(() => Actions.change({ loadingItem: false }));
  },

  /**
   * Search Members
   */
  searchMembers(value, addNewMembers = false) {
    Actions.change({ searchValue: value });

    clearTimeout(this.searchMembersTimeout);

    this.searchMembersTimeout = setTimeout(() => {
      const { searchType, searchValue } = store.getState().chat;

      const data = { searchType, searchValue, addNewMembers };

      Actions.getMembers(data);
    }, 300);
  },

  /**
   * Get members
   * @param props
   */
  getMembers(props = {}) {
    const { item = null, showModal, actionMembers = null } = store.getState().chat;
    const pagination = props.pagination || props.addNewMembers ? store.getState().chat.notAddedPagination : store.getState().chat.pagination;

    Actions.change({ loadingMembers: true });

    props.pagination = pagination;

    if (item && showModal === 'modifyMembers') {
      props.membersIds = actionMembers ? actionMembers.map(({ _id }) => _id) : item.access.map(({ _id: id }) => id);
    }

    api.chat
      .getMembers(props)
      .then(data => Actions.change({ ...data, loadingMembers: false }))
      .catch(() => Actions.change({ loadingMembers: false }));
  },

  /**
   * Add chat
   * @param body
   */
  add(body) {
    return api.chat.add(body);
  },

  /**
   * Add message
   * @param body
   * @returns {*}
   */
  addMessage(body) {
    return api.chat.addMessage(body);
  },

  /**
   * Search
   */

  search({ searchValue, searchType, ...props }) {

    return api.chat.search({ searchValue, searchType, ...props })
      .then((data) => Actions.change({ ...data }));
  },

  /**
   * Edit Message
   * @param body
   * @returns {*}
   */
  editMessage(body) {
    return api.chat.editMessage(body);
  },

  /**
   * Remove Message
   * @param params
   * @returns {*}
   */
  removeMessage(params) {
    return api.chat.removeMessage(params);
  },

  /**
   * Edit Link
   * @param body
   * @return {*}
   */
  editLink(body) {
    return api.chat.editLink(body);
  },

  /**
   * Delete chat
   * @param chatId
   * @return {*}
   */
  delete(chatId) {
    Actions.change({ loading: true });

    return api.chat
      .delete(chatId)
      .then(() => {
        Actions.change({ loading: false });
        Actions.get();
      })
      .catch(() => {
        Actions.change({ loading: false });
      });
  },

  /**
   * Rename Chat
   * @param body
   * @return {*}
   */

  update(body) {
    return api.chat.update(body);
  },

  /**
   * Leave chat
   */

  leave(chatId, body) {
    return api.chat.leave(chatId, body);
  },

  /**
   * Reply to Message
   */
  replyToMessage(body) {
    return api.chat.replyToMessage(body);
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.CHAT_CHANGE, payload });
  },
};

export default Actions;
