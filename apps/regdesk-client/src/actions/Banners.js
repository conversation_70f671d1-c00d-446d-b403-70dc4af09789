import api from '../utils/api';
import * as constants from '../const';
import store from '../stores';

const BannersActions = {

  /**
   * Get banners list
   * @returns {Function}
   */
  get(page, update) {
    return api.banners.get(page || 1).then(({list}) => {
      BannersActions.change({list: list});
    });
  },

  change(payload) {
    store.dispatch({type: constants.BANNERS_CHANGE, payload});
  },

  /**
   * Update banner notice
   * @param id
   * @param body
   */
  update(id, body) {
    return api.banners.update(id, body);
  },
};

export default BannersActions;
