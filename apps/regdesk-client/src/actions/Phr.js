import api from '../utils/api';
import store from '../stores';
import * as constants from '../const';
import MapActions from './Map';
import { EUCountriesCodes } from '../utils/countries';

const cache = {};
let timerId = null;

/**
 * Actions for Regulations
 */
const Actions = {
  getMap(key = 'phr') {
    Actions.change({ loading: true });

    api.phrRelease.get().then(data => {
      const { list } = data;

      Actions.change({ loading: false, list });
      MapActions.change({ data: list }, key);
    });
  },

  /**
   * Get by country
   * @param idc
   */
  async getByCountry(idc) {
    if (!cache[idc]) {
      Actions.change({ loading: true });
      cache[idc] = await api.phrRelease.getByCountry(idc);
    }

    Actions.change({ loading: false, ...cache[idc] });

    // cache EU for switch
    if (EUCountriesCodes.includes(idc) && !cache.EU) {
      cache.EU = await api.phrRelease.getByCountry('EU');
    }
  },

  /**
   * Get classification by country id
   * @param idc
   * @returns {Promise<void | *>}
   */
  getClassifications(idc) {
    return api.phrRelease.getClassifications(idc);
  },

  /**
   * Get report
   * @param filters
   * @returns {*}
   */
  getReports(filters = {}) {
    const { pharmaRegulationReports } = store.getState();
    const change = (payload = {}) => store.dispatch({ type: constants.PHARMA_REGULATION_REPORTS_CHANGE, payload });

    if (Object.keys(filters).length) {
      clearTimeout(timerId);

      timerId = setTimeout(() => {
        change({ loading: true });

        api.phrReports
          .get({ page: 1 }, { filters: { ...pharmaRegulationReports.filters, ...filters } })
          .then(data => change({ ...data, loading: false }));
      }, 300);
    } else if (pharmaRegulationReports.nextPage && !pharmaRegulationReports.loading) {
      change({ loading: true });

      api.phrReports
        .get({ page: pharmaRegulationReports.nextPage }, { filters: pharmaRegulationReports.filters })
        .then(data => change({ ...data, docs: [...pharmaRegulationReports.docs, ...data.docs], loading: false }));
    }
  },

  generateReport(state) {
    const { reportType } = state;

    return api.phrReports.generate({ reportType, body: { state } });
  },

  /**
   * Change
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.PHR_CHANGE, payload });
  },
};

export default Actions;
