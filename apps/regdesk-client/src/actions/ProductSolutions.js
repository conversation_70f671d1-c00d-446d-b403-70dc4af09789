import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    const { module, filters, pagination, advancedSearchValue, advancedSearchList } = store.getState().productSolutions;
    const params = { ...props };

    Actions.change({ loading: true });

    if (module) {
      params.module = module;
    }

    api.productSolutions
      .get({ filters, pagination, advancedSearchValue, advancedSearchList, ...params })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Search
   * @param value
   * @param table
   */
  search(value, table = false) {
    const { pagination, filters } = store.getState().productSolutions;
    const newFilters = { ...filters, name: value };

    Actions.change({ filters: newFilters, searchValue: value, module: 'products' });

    clearTimeout(this.timerId);

    this.timerId = setTimeout(() => {
      const props = { filters: newFilters, pagination };

      if (table) {
        props.pagination = { ...pagination, current: 1 };
      }

      Actions.get(props);
    }, 300);
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.productSolutions.add(data);
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  update(data) {
    return api.productSolutions.update(data);
  },

  /**
   * Remove
   * @param id
   */
  remove(id) {
    return api.productSolutions.remove(id);
  },

  /**
   * Filter
   * @param newFilter
   */
  filter(newFilter = {}) {
    const { filters = {} } = store.getState().productSolutions;
    const nextFilters = { ...filters, ...newFilter };

    Actions.change({ filters: nextFilters });
    Actions.get({ filters: nextFilters });
  },

  /**
   * Clean filter
   * @param key
   */
  cleanFilter(key) {
    const { filters = {} } = store.getState().productSolutions;

    delete filters[key];

    Actions.get({ filters });
  },

  /**
   * Clean all filters
   */
  cleanAllFilters() {
    Actions.get({ filters: {} });
  },

  /**
   * Update ACL by ID
   * @param id
   * @param acl
   */
  updateAcl({ id, acl }) {
    Actions.change({ loading: true });

    return api.productSolutions
      .updateAcl({ id, acl })
      .then(() => {
        Actions.get();
        message.success('Product Solution ACL updated');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update ACL
   * @param acl
   * @param filters
   * @param advancedSearch
   * @param revoke
   */
  updateAclForMany({ acl, filters, advancedSearch, revoke = false }) {
    Actions.change({ sharingAll: 0 });

    return api.productSolutions.updateAclForMany({ acl, filters, revoke, advancedSearch });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.PRODUCT_SOLUTIONS_CHANGE, payload });
  },
};

export default Actions;
