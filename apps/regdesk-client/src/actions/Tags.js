import api from '../utils/api';
import store from '../stores';
import * as constants from '../const';

const Tags = {
  /**
   * Get all tags
   * @returns {*}
   */
  getAll: (params) => {
    store.dispatch({ type: constants.TAGS_CHANGE, payload: { loading: true } });

    return api.tags.getAll(params).then(({ list }) => {
      store.dispatch({ type: constants.TAGS_CHANGE, payload: { list, loading: false } });
    });
  },
};

export default Tags;
