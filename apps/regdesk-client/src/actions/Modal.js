import * as constants from '../const';
import store from '../stores';

/**
 * Actions for Modal
 */
const Actions = {
  /**
   * Show modal window
   * @param payload
   */
  show(payload = {}) {
    Actions.change({ ...payload, show: true });
  },

  /**
   * Hide modal
   */
  hide() {
    Actions.change({ show: false });
  },

  /**
   * Change
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.MODAL_CHANGE, payload });
  },
};

export default Actions;
