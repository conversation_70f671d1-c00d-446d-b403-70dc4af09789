import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    const { sorter, filters, pagination } = store.getState().regPlan;

    Actions.change({ loading: true });

    api.regPlan
      .get({ sorter, filters, pagination, ...props })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Remove
   * @param id
   */
  remove(id) {
    Actions.change({ loading: true });

    return api.regPlan.remove(id)
      .then(Actions.get);
  },

  /**
   * Create new application
   * @param data
   * @returns {*|{url, body}}
   */
  add(data) {
    return api.regPlan.add(data);
  },

  /**
   * Update
   * @param props
   */
  update(props) {
    return api.regPlan.update(props);
  },

  updateInfo({ id, body }) {
    return api.regPlan.updateInfo({ id, body });
  },

  /**
   * Get by Id
   * @param id
   */
  getById(id) {
    Actions.change({ loading: true });

    return api.regPlan.getById(id).then(data => Actions.change({ loading: false, ...data }));
  },

  /**
   * Freeze
   * @param status
   */
  freeze(status) {
    Actions.change({ loading: status });
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: '', item: {}, modalLoading: false });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.REG_PLAN_CHANGE, payload });
  },
};

export default Actions;
