import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import FileSaver from 'file-saver';

const Actions = {
  /**
   * Get data
   * @param id
   * @param props
   */
  get({id = null, module = "", format='csv'}) {
    Actions.change({ loading: true });
    if(!id) id=123

    return api.logs
      .get(id,{ module, format})
      .then((data) => {
        let filename = `logs-${Date.now()}.${format}`;
        let blob = null;

        if (format === 'xls') {
          const s2ab = s => {
            const buf = new ArrayBuffer(s.length);
            const view = new Uint8Array(buf);
            for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
          };

          filename = `logs-${Date.now()}.xlsx`;
          blob = new Blob([s2ab(data)], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;',
          });
        }

        if (format === "csv") {
          blob = new Blob([data], { type: 'text/csv' });
        }

        FileSaver.saveAs(blob, filename);
        Actions.change({ loading: false })
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.LOGS_CHANGE, payload });
  },
};

export default Actions;
