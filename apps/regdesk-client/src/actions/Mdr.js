import PrintJS from 'print-js';
import api from '../utils/api';
import store from '../stores';
import * as constants from '../const';
import MapActions from './Map';
import { EUCountriesCodes } from '../utils/countries';

const cache = {};
let timerId = null;

/**
 * Actions for Regulations
 */
const Actions = {
  /**
   * Get list
   */
  getMap(key = 'mdr') {
    Actions.change({ loadingList: true });

    api.mdrRelease.get().then(data => {
      const { list } = data;

      Actions.change({ loadingList: false, list });
      MapActions.change({ data: list }, key);
    });
  },

  /**
   * Get list
   * @param params
   * @returns {*|Promise<*>}
   */
  get(params) {
    return api.mdrRelease.get(params);
  },

  /**
   * Get by country
   * @param idc
   */
  async getByCountry(idc) {
    if (!cache[idc]) {
      Actions.change({ loading: true });
      cache[idc] = await api.mdrRelease.getByCountry(idc);
    }

    Actions.change({ loading: false, ...cache[idc] });

    // cache EU for switch
    if (EUCountriesCodes.includes(idc) && !cache.EU) {
      cache.EU = await api.mdrRelease.getByCountry('EU');
    }
  },

  /**
   * Get classification by country id
   * @param idc
   * @returns {Promise<void | *>}
   */
  getClassifications(idc) {
    return api.mdrRelease.getClassifications(idc);
  },

  /**
   * Get access level
   * @returns {Promise<void | *>}
   */
  getAccessLevels() {
    return api.mdrRelease.getAccessLevels();
  },

  /**
   * Get report
   * @param filters
   * @returns {*}
   */
  getReports(filters = {}) {
    const { regulationReports } = store.getState();
    const change = (payload = {}) => store.dispatch({ type: constants.REGULATION_REPORTS_CHANGE, payload });

    if (Object.keys(filters).length) {
      clearTimeout(timerId);

      timerId = setTimeout(() => {
        change({ loading: true });

        api.mdrReports
          .get({ page: 1 }, { filters: { ...regulationReports.filters, ...filters } })
          .then(data => change({ ...data, loading: false }));
      }, 300);
    } else if (regulationReports.nextPage && !regulationReports.loading) {
      change({ loading: true });

      api.mdrReports
        .get({ page: regulationReports.nextPage }, { filters: regulationReports.filters })
        .then(data => change({ ...data, docs: [...regulationReports.docs, ...data.docs], loading: false }));
    }
  },

  generateReport(state) {
    const { reportType } = state;

    return api.mdrReports.generate({ reportType, body: { state } });
  },

  print(body) {
    Actions.change({ loadingPrint: true });

    api.mdrRelease
      .printClassification(body)
      .then(data => {
        PrintJS({ printable: data.base64, type: 'pdf', base64: true });
        Actions.change({ loadingPrint: false });
      })
      .catch(() => Actions.change({ loadingPrint: false }));
  },

  /**
   * Change
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.MDR_CHANGE, payload });
  },
};

export default Actions;
