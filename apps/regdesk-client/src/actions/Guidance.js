import { GUI<PERSON>NCE_CHANGE } from '../const';
import api from '../utils/api';
import store from '../stores';
import { SECOND_ROW_TABS } from '../containers/Standards/helpers';

const { DATABASE, SUMMARIZATION, LINKED_PRODUCTS, WATCHLIST, AFFECTED_PRODUCTS, ASSESSMENTS } = SECOND_ROW_TABS;

const Actions = {
  getTotal() {
    const guidanceStore = store.getState().guidance;

    api.standards
      .getTotal({
        isGuidance: true,
        [`${DATABASE}Filters`]: guidanceStore[DATABASE].filters,
        [`${SUMMARIZATION}Filters`]: guidanceStore[SUMMARIZATION].filters,
        [`${LINKED_PRODUCTS}Filters`]: guidanceStore[LINKED_PRODUCTS].filters,
        [`${AFFECTED_PRODUCTS}Filters`]: guidanceStore[AFFECTED_PRODUCTS].filters,
        [`${ASSESSMENTS}Filters`]: guidanceStore[ASSESSMENTS].filters,
      })
      .then(data => Actions.change(data));
  },

  getFiltersData() {
    api.standards
      .getFiltersData({ isGuidance: true })
      .then((data) => Actions.change(data));
  },

  setLoading(tabName, isLoading) {
    const tabValues = store.getState().guidance[tabName];

    Actions.change({ [tabName]: { ...tabValues, loading: isLoading } });
  },

  setFilter(tabName, newFilters = {}) {
    const tabValues = store.getState().guidance[tabName];
    const nextFilters = { ...tabValues.filters, ...newFilters };

    Actions.change({ [tabName]: { ...tabValues, filters: nextFilters } });
  },

  fetchData(tabName, { newPagination = {}, newFilters = {}, newSorter } = {}) {
    const { filters, pagination, sorter } = store.getState().guidance[tabName];
    const params = {
      filters: { ...filters, ...newFilters },
      pagination: { ...pagination, ...newPagination },
      sorter: { ...sorter, ...newSorter },
    };

    Actions.setLoading(tabName, true);

    const apiMapper = {
      [DATABASE]: api.standards.get,
      [SUMMARIZATION]: api.standards.getSummarized,
      [LINKED_PRODUCTS]: api.standards.getByProduct,
      [WATCHLIST]: api.watchlists.get,
      rules: api.standardRules.get,
      [AFFECTED_PRODUCTS]: api.standards.getAffectedProducts,
      [ASSESSMENTS]: api.standardsWorkflow.getWorkflowList,
    };
    const request = apiMapper[tabName];

    return request(params)
      .then(({ list = [], filters = {}, pagination = {}, sorter = {} }) => {
        const payload = {
          [tabName]: {
            list,
            filters,
            pagination,
            sorter,
          },
          [`${tabName}Total`]: pagination.total || 0,
        };

        Actions.change(payload);
        Actions.setLoading(tabName, false);

        return list;
      })
      .catch(() => Actions.setLoading(tabName, false));
  },

  // Database
  getDatabase({ newPagination = {} } = {}) {
    Actions.fetchData(DATABASE, { newPagination });
  },

  filterDatabase(newFilters = {}) {
    Actions.setFilter(DATABASE, newFilters);
  },

  setDatabaseLoading(isLoading) {
    Actions.setLoading(DATABASE, isLoading);
  },

  // Summarization
  getSummarization({ newPagination = {} } = {}) {
    Actions.fetchData(SUMMARIZATION, { newPagination });
  },

  filterSummarization(newFilters = {}) {
    Actions.setFilter(SUMMARIZATION, newFilters);
  },

  // Linked Products
  filterLinkedProducts(newFilters = {}) {
    Actions.setFilter(LINKED_PRODUCTS, newFilters);
  },

  getLinkedProducts({ newPagination = {} } = {}) {
    return Actions.fetchData(LINKED_PRODUCTS, { newPagination });
  },

  // Watchlist
  setWatchlistLoading(isLoading) {
    Actions.setLoading(WATCHLIST, isLoading);
  },

  filterWatchlist(newFilters = {}) {
    Actions.setFilter(WATCHLIST, newFilters);
  },

  getWatchlist({ newPagination = {} } = {}) {
    Actions.fetchData(WATCHLIST, { newPagination });
  },

  // Rules
  setRulesLoading(isLoading) {
    Actions.setLoading('rules', isLoading);
  },

  filterRules(newFilters = {}) {
    Actions.setFilter('rules', newFilters);
  },

  getRules({ newPagination = {} } = {}) {
    Actions.fetchData('rules', { newPagination });
  },

  // Affected Products
  setAffectedProductsLoading(isLoading) {
    Actions.setLoading(AFFECTED_PRODUCTS, isLoading);
  },

  filterAffectedProducts(newFilters = {}) {
    Actions.setFilter(AFFECTED_PRODUCTS, newFilters);
  },

  getAffectedProducts({ newPagination = {}, newSorter } = {}) {
    Actions.fetchData(AFFECTED_PRODUCTS, { newPagination, newSorter });
  },

  // Assessments
  filterAssessments(newFilters = {}) {
    Actions.setFilter(ASSESSMENTS, newFilters);
  },

  getAssessments({ newPagination = {}, newSorter } = {}) {
    Actions.fetchData(ASSESSMENTS, { newPagination, newSorter });
  },

  change(payload = {}) {
    store.dispatch({ type: GUIDANCE_CHANGE, payload });
  },
};

export default Actions;
