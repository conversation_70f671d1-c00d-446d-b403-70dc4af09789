import store from '../stores';
import * as constants from '../const';
import api from '../utils/api';

const Actions = {
  getUsers() {
    Actions.change({ loading: true });

    return api.standards.getUsers().then(({ users }) => {
      Actions.change({ loading: false, users });
    })
      .catch(() => Actions.change({ loading: false }));
  },

  getList(props = {}) {
    const { filtersWorkflow } = store.getState().standardsWorkflow;

    Actions.change({ loading: true });

    api.standardsWorkflow
      .getWorkflowList({ filtersWorkflow, ...props })
      .then((data) => {
        Actions.change({
          workflowList: data.list,
          loading: false,
        });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  getById(id) {
    Actions.change({ loading: true });

    return api.standardsWorkflow
      .getWorkflowById(id)
      .then((data) => {
        Actions.change({
          item: data.item,
          loading: false,
        });

        return data;
      })
      .catch(() => Actions.change({ loading: false }));
  },

  add(body) {
    return api.standardsWorkflow.addWorkflow(body);
  },

  update(id, body) {
    Actions.change({ loading: true });

    return api.standardsWorkflow.updateWorkflow(id, body)
      .then(() => Actions.change({ loading: false, }))
      .catch(() => Actions.change({ loading: false }));
  },

  changeData(id, body) {
    Actions.change({ loading: true });

    return api.standardsWorkflow.changeData(id, body)
      .then(() => Actions.change({ loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  change(payload = {}) {
    store.dispatch({ type: constants.STANDARDS_WORKFLOW_CHANGE, payload });
  },
};

export default Actions;
