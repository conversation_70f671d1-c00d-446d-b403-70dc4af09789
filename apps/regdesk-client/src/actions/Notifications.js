import api from '../utils/api';
import * as constants from '../const';
import store from '../stores';

const NotificationActions = {
  /**
   * Get list notifications
   * @returns {Function}
   */
  get(page, update) {
    return api.notifications.get(page || 1).then(({list, unreadCount}) => {
      if(update) {
        NotificationActions.change({list, unreadCount});
      } else {
        const {list: oldData} = store.getState().notifications;
        const newList = oldData.docs ? {...list, docs: [...oldData.docs, ...list.docs]} : list;
        NotificationActions.change({list: newList, unreadCount});
      }
    });
  },

  /**
   * Clear all notifications
   */
  clear() {
    api.notifications.clear()
      .then(() => NotificationActions.get(undefined, true));
  },

  /**
   * Update notification
   * @param id
   * @param body
   */
  update(id, body, params) {
    api.notifications.update(id, body)
      .then(() => {
        if(params.update) {
          NotificationActions.get(1, params.update)
        } else {
          NotificationActions.get(1, )
        }
      });
  },

  /**
   * Subscribe on notifications
   * @param body
   */
  subscribe(body) {
    api.notifications.subscribe(body);
  },

  /**
   * Change reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({type: constants.NOTIFICATIONS_CHANGE, payload});
  },
};

export default NotificationActions;
