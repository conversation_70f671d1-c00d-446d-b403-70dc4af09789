import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

let timerSearch = null;

const Actions = {
  /**
   * Get data
   * @param {?Object} data
   * @returns {Function}
   */
  get(props = {}) {
    const { sorter, filters, pagination } = store.getState().projects;

    Actions.change({ loading: true });

    return api.projects
      .get({ sorter, filters, pagination, ...props })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get project by id
   * @param {string} id
   * @returns {Function}
   */
  getById(id) {
    Actions.change({ loading: true });

    return api.projects
      .getById(id)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add
   * @param {Object} data
   */
  add(data) {
    return api.projects.add(data);
  },

  /**
   * Update
   * @param {Object} data
   */
  update(data) {
    return api.projects.update(data);
  },

  /**
   * update Acl
   * @param data
   */
  updateAcl(data) {
    return api.projects.updateAcl(data);
  },

  /**
   * Update Acl for many
   * @param {Object} acl
   * @param {?Boolean} revoke
   * @param {Object} props
   */
  updateAclForMany({ acl, revoke = false, ...props }) {
    return api.projects.updateAclForMany({ acl, revoke, ...props });
  },

  /**
   * Remove
   * @param {string} id
   */
  remove(id) {
    Actions.change({ loading: true });
    api.projects.remove(id).then(() => Actions.get());
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: '', modalLoading: false });
  },

  /**
   * Change store
   * @param {?Object} payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.SLICE_CHANGE, payload });
  },
};

export default Actions;
