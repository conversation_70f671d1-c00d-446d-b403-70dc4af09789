import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import MapActions from './Map';

const Actions = {
  /**
   * Get data
   * @param props
   */
  get(props = {}) {
    const { filters, pagination } = store.getState().alerts;

    Actions.change({ loading: true });

    return api.alerts
      .get({ filters, pagination, ...props })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get data for map
   * @param props
   */
  getMap(props = {}) {
    const { filters } = store.getState().alerts;

    Actions.change({ loadingList: true });

    return api.alerts
      .getMap({ filters, ...props })
      .then(({ allCountries, todayCountries, ...data }) => {
        Actions.change({ ...data, allCountries, todayCountries, filters, loadingList: false });
        MapActions.change({ data: [allCountries, todayCountries] }, 'alerts');
      })
      .catch(() => Actions.change({ loadingList: false }));
  },

  /**
   * Get by country
   * @param id
   * @param props
   */
  getByCountry(id, props = {}) {
    const { filters } = store.getState().alerts;

    Actions.change({ loading: true });

    return api.alerts
      .getByCountry(id, { filters, ...props })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Filter
   * @param newFilter
   */
  filter(newFilter = {}) {
    const { filters = {}, pagination } = store.getState().alerts;
    const newFilters = { ...filters, ...newFilter };
    const params = { pagination: { ...pagination, current: 1 } };

    Actions.change({ filters: newFilters });
    Actions.get(params);
  },

  /**
   * Filter
   * @param newFilter
   */
  filterMap(newFilter = {}) {
    const { filters = {}, pagination } = store.getState().alerts;
    const newFilters = { ...filters, ...newFilter };
    const params = { pagination: { ...pagination, current: 1 } };

    Actions.change({ filters: newFilters });
    Actions.getMap(params);
  },

  /**
   * Clean
   */
  clean() {
    Actions.change({
      list: [],
      countries: [],
      filters: {},
      paginations: {},
      existsCountries: [],
    });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.ALERTS_CHANGE, payload });
  },
};

export default Actions;
