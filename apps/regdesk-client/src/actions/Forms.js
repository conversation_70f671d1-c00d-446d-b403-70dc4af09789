import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import Map from './Map';

const makePayload = (filters = {}, type) => ({
  filters,
  map: 'forms',
  type,
});

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    const { type = constants.GSPR_TYPE } = props;

    const { sorter = {}, filters = {}, pagination = {} } = store.getState().forms[type];

    Actions.loadingOn();

    const newState = { sorter, filters, pagination, ...props };

    return api.forms
      .get(newState)
      .then(data => {
        const state = { ...newState, ...data };

        Actions.change({ [type]: state, loading: false });

        return state;
      })
      .catch(() => Actions.loadingOff());
  },

  /**
   * Advanced Search
   * @param props
   * @param pagination
   */
  advancedSearch(props, pagination = {}) {
    const { type = constants.GSPR_TYPE } = props;
    const { gspr, doc } = store.getState().forms;
    const obj = type === 'gspr' ? gspr : doc;
    const { advancedSearchList, advancedSearchValue, advancedSearchEnabled } = obj;

    if (!advancedSearchEnabled) {
      pagination = {};
    }

    Actions.change({ loading: true });
    Actions.change({ [type]: { ...obj, advancedSearchEnabled: true, searchValue: '' } });

    api.forms
      .advancedSearch({ advancedSearchList, advancedSearchValue, pagination, type })
      .then(data => {
        Actions.change({ loading: false });
        Actions.change({ [type]: { ...obj, ...data, advancedSearchEnabled: true } });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get Forms by Id and type
   * @param id
   * @param type (gspr, doc)
   */
  getById(id, type) {
    const state = store.getState().forms[type] || {};

    Actions.loadingOn();

    return api.forms.getById({ id, type }).then(data => {
      const item = { ...state, activeItem: data };

      Actions.change({ loading: false, [type]: item });
    });
  },

  /**
   * Create new form
   * @param data
   * @returns {*|{url, body}}
   */
  addForm(data) {
    return api.forms.add(data);
  },

  /**
   * Update GSPR || DoC form
   * @param id
   * @param data
   */
  updateForm(id, type, data) {
    Actions.loadingOn();

    if (type === constants.GSPR_TYPE) {
      return Actions.updateGSPRForm(id, data);
    }

    return api.forms
      .updateDOCForm(id, data)
      .then(result => {
        Actions.change({ loading: false, exportedDocAlreadyExist: false });

        return result;
      })
      .catch(() => {
        Actions.loadingOff();
      });

  },
  /**
   * Update GSPR form
   * @param id
   * @param data
   */
  updateGSPRForm(id, data) {
    Actions.loadingOn();

    return api.forms
      .updateGSPRForm(id, data)
      .then(() => {
        Actions.change({ loading: false, exportedDocAlreadyExist: false });
      })
      .catch(() => {
        Actions.loadingOff();
      });
  },

  /**
   * Get New Linked standards
   * @param id
   * @param type
   */
  getNewLinkedStandards(id, type) {
    Actions.loadingOn();

    return api.forms
      .getNewLinkedStandards({ id, type })
      .then(({ newLinkedStandards }) => {
        Actions.loadingOff();

        return newLinkedStandards;
      })
      .catch(() => {
        Actions.loadingOff();
      });
  },

  /**
   * Clear New Linked Standards in GSPR form
   * @param id
   * @param data
   */
  clearNewLinkedStandards(id, data) {
    Actions.loadingOn();

    return api.forms.clearNewLinkedStandards(id, data).catch(() => {
      Actions.loadingOff();
    });
  },

  /**
   * Get Updated standards
   * @param id
   * @param type
   */
  getUpdatedStandards(id, type) {
    Actions.loadingOn();

    return api.forms
      .getUpdatedStandards({ id, type })
      .then(({ updatedStandards }) => {
        Actions.loadingOff();

        return updatedStandards;
      })
      .catch(() => {
        Actions.loadingOff();
      });
  },

  /**
   * Update standards in Forms
   * @param id
   * @param data
   */
  updateStandards(id, data) {
    Actions.loadingOn();

    return api.forms
      .updateStandards(id, data)
      .then(() => {
        Actions.change({ loading: false, exportedDocAlreadyExist: false });
      })
      .catch(() => {
        Actions.loadingOff();
      });
  },

  /**
   * Get AI suggestions
   * @param type
   * @param data
   */
  getSuggestions(type, data) {
    return api.forms.getSuggestions(type, data).then(result => result);
  },

  /**
   * Remove
   * @param id
   */
  remove(id, type) {
    Actions.loadingOn();

    return api.forms.remove({ id, type });
  },

  /**
   * Get classifications
   */
  getClassifications({ type }) {
    return api.forms.getClassifications({ type });
  },

  /**
   * Update responder
   * @param props
   */
  updateResponder(props) {
    return api.forms.updateResponder(props);
  },

  /**
   * Update reviewer
   * @param props
   */
  updateReviewer(props) {
    return api.forms.updateReviewer(props);
  },

  /**
   * Update Info
   */
  updateInfo(props) {
    return api.forms.updateInfo(props);
  },

  /**
   * Update ACL by ID
   * @param id
   * @param acl
   * @param type //gspr || doc
   */
  updateAcl({ id, acl, type }) {
    Actions.change({ loading: true });

    return api.forms
      .updateAcl({ id, acl, type })
      .then(() => {
        Actions.get({ type });
        message.success(`${type.toUpperCase()} Form ACL updated`);
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update ACL for many
   * @param acl
   * @param revoke
   * @param props
   */
  updateAclForMany({ acl, revoke = false, ...props }) {
    Actions.change({ sharingAll: 0 });

    return api.forms.updateAclForMany({ acl, revoke, ...props });
  },

  /**
   * Clone
   * @param id
   */
  clone(id, type) {
    Actions.loadingOn();

    return api.forms.clone({ id, type }).catch(() => loadingOff());
  },

  /**
   * CloneList
   * @param id
   */
  getCloneHistory(id, type) {
    return api.forms.getCloneHistory({ id, type });
  },

  /**
   * Filter
   * @param newFilter
   */
  filter(newFilter = {}, type) {
    const { filters = {}, pagination } = store.getState().forms[type];
    const nextFilters = { ...filters, ...newFilter };

    if ('countryId' in newFilter && newFilter.countryId === filters.countryId) {
      delete nextFilters.countryId;
    }

    Actions.get({ type, filters: nextFilters, pagination: { ...pagination, current: 1 } });
    // .then(data =>
    //   Actions.getMap(makePayload(nextFilters, type), data)
    // );
  },

  /**
   * Set filters
   * @param newFilter
   */
  setFilter(newFilter = {}, type) {
    const { filters = {} } = store.getState().forms[type];
    const nextFilters = { ...filters, ...newFilter };

    Actions.change({ [type]: { filters: nextFilters } });
    Map.change({ filters: nextFilters }, 'forms');
  },

  /**
   * Clean filter
   * @param key
   * @param type
   */
  cleanFilter(key, type) {
    const { filters = {} } = store.getState().forms[type];

    delete filters[key];

    Actions.get({ filters, type }).then(data => Actions.getMap(makePayload(filters, type), data));
  },

  cleanAllFilters(type) {
    Actions.get({ filters: {}, type });
  },

  getMap({ map, filters, type }) {
    Map.change({ loading: true }, map);

    api.forms
      .get({ type, filters, map: true })
      .then(({ list }) => Map.change({ data: list, loading: false }, map))
      .catch(() => Map.change({ loading: false }, map));
  },

  getTemplatesMap({ map, type }) {
    Map.change({ loading: true }, map);

    api.forms
      .get({ type, templates: true })
      .then(({ templateCountries }) => Map.change({ data: templateCountries, loading: false }, map))
      .catch(() => Map.change({ loading: false }, map));
  },

  /**
   * Get template data to create a form
   * @returns {Function}
   */
  getData({ filters, fieldName }) {
    Actions.loadingOn();

    const { templateData } = store.getState().forms;

    return api.forms
      .getData({ filters, fieldName })
      .then(({ data }) => {
        Actions.change({ templateData: { ...templateData, [fieldName]: data }, loading: false });

        return data;
      })
      .catch(() => Actions.loadingOff());
  },

  /**
   * Export preview
   * @param params
   */
  exportPreview(params = {}) {
    Actions.loadingOn();

    return api.forms
      .exportPreview(params)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.loadingOff());
  },

  /**
   * Approve
   * @param params
   */
  approve(params = {}) {
    const { id, type } = params;

    Actions.loadingOn();

    return api.forms
      .approve(params)
      .then(() => Actions.getById(id, type))
      .catch(() => Actions.loadingOff());
  },

  /**
   * Open modal
   */
  openModal(modalName, type) {
    Actions.change({ showModal: modalName, type });
  },

  /**
   * Close modal
   */
  closeModal() {
    Actions.change({ showModal: '' });
  },

  loadingOn() {
    Actions.change({ loading: true });
  },

  loadingOff() {
    Actions.change({ loading: false });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.FORMS_CHANGE, payload });
  },
};

export default Actions;
