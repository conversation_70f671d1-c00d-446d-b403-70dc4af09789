import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import Map from './Map';

const WizardActions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    WizardActions.change({ loading: true });

    api.wizards
      .get(props)
      .then(data => WizardActions.change({ ...data, loading: false }))
      .catch(() => WizardActions.change({ loading: false }));
  },

  /**
   * Get data form map
   * @param filters
   * @param map
   */
  getMap({ map, selected, ...props }) {
    Map.change({ loading: true }, map);

    return api.wizards
      .get({ ...props, map: true })
      .then(({ list, ...payload }) => {
        Map.change({ data: list, ...payload, selected, loading: false }, map);
      })
      .catch(() => Map.change({ loading: false }, map));
  },

  /**
   * Get data for create application
   * @returns {Function}
   */
  getData({ filters, fieldName }) {
    WizardActions.change({ loading: true });

    const { applicationAddData } = store.getState().wizards;

    return api.wizards
      .getData({ filters, fieldName })
      .then(({ data }) => {
        WizardActions.change({ applicationAddData: { ...applicationAddData, [fieldName]: data }, loading: false });

        return data;
      })
      .catch(() => WizardActions.change({ loading: false }));
  },

  /**
  * Get Suggestions
  */
  getSuggestions() {
    return api.wizards.getSuggestions().then(data => WizardActions.change(data));
  },

  /**
  * Get classifications by country
  */
  getClassificationsByCountry(countryId) {
    return api.wizards.getClassificationsByCountry(countryId).then((data) => WizardActions.change(data));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.WIZARDS_CHANGE, payload });
  },

  /**
   * Clean
   */
  clean() {
    store.dispatch({ type: constants.WIZARDS_CLEAN_UP });
  },
};

export default WizardActions;
