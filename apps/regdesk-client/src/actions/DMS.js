import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import Map from './Map';

let timerId = null;

const Actions = {
  /**
   * Get tags
   * @param {*} props
   */
  getTags(props = {}) {
    const state = store.getState().dms;
    const {
      filters = state.filters,
      pagination = state.pagination,
      sorter = state.sorter,
      advancedSearchList = state.advancedSearchList,
      advancedSearchValue = state.advancedSearchValue,
    } = props;

    Actions.change({ loading: true, containerFilters: {} });

    api.dms
      .getTags({ filters, pagination, sorter, advancedSearchList, advancedSearchValue })
      .then((data) => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Search
   * @param value
   */
  searchTags(value) {
    const state = store.getState().dms;
    const { pagination } = state;

    const newFilters = { ...state.filters };

    if (value) {
      newFilters.name = value;
    } else {
      delete newFilters.name;
    }

    Actions.change({ filters: newFilters });

    clearTimeout(timerId);

    timerId = setTimeout(() => {
      Actions.getTags({ filters: newFilters, pagination: { ...pagination, current: 1 } });
    }, 500);
  },

  /**
   * Add tag
   */
  addTag(data) {
    Actions.change({ modalLoading: true });

    api.dms
      .addTag(data)
      .then((res) => {
        message.success('Sent successfully');
        Actions.closeModal();
        Actions.getTags();
        Actions.change({ addedTag: res.id });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Update tag
   */
  updateTag(data) {
    Actions.change({ modalLoading: true });

    return api.dms
      .updateTag(data)
      .then(({ tag }) => {
        message.success('Sent successfully');
        Actions.closeModal();
        Actions.getTags();

        return tag;
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Archive tag
   */
  archiveTag(id) {
    Actions.change({ loading: true });

    api.dms
      .archiveTag(id)
      .then(() => {
        message.success('Archived successfully');
        Actions.change({ loading: false });
        Actions.getTags();
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: '', item: {}, modalLoading: false });
  },

  /**
   * Get containers
   * @param {*} props
   */
  getContainers(props = {}) {
    const state = store.getState().dms;

    const {
      filters = state.containerFilters,
      sorter = state.containersSorter,
      pagination = state.containerPagination,
      id = state.selectedTag?._id
    } = props;

    Actions.change({ selectedContainer: {}, loading: true });

    api.dms
      .getContainers({ filters, sorter, pagination, id })
      .then((data) => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get usage
   * @param {*} props
   */
  getUsage(props) {
    return api.dms.getUsage(props);
  },

  /**
   * Search containers
   * @param value
   */
  searchContainers(data) {
    const state = store.getState().dms;
    const newFilters = {
      ...state.containerFilters,
      name: data?.value,
    };

    if (!data?.value) {
      delete newFilters.name;
    }

    Actions.change({ containerFilters: newFilters });
    clearTimeout(timerId);

    timerId = setTimeout(() => {
      Actions.getContainers({
        filters: newFilters,
        pagination: { ...state.containerPagination, current: 1 },
        id: data?.id,
      });
    }, 500);
  },

  /**
   * Add container
   */
  addContainer(data) {
    return api.dms.addContainer(data);
  },

  /**
   * Update container
   */
  updateContainer(data) {
    return api.dms.updateContainer(data);
  },

  /**
   * Relocate container
   */
  relocateContainer({ item, tagId }) {
    const { _id } = item;

    Actions.change({ loading: true });

    return api.dms
      .relocateContainer({ id: _id, tagId })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get containers for system tag
   */
  getSystemContainers(props) {
    const { containerFilters: filters, containersSorter: sorter, containerPagination: pagination } = store.getState().dms;

    Actions.change({ loading: true });

    return api.dms
      .getContainersSystem({ filters, sorter, pagination, ...props })
      .then((data) => {
        Actions.change({ ...data, loading: false });

        return data;
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add container for system tag
   */
  addContainerSystem(data) {
    return api.dms.addContainerSystem(data);
  },

  /**
   * Archive container
   */
  archiveContainer(id, tagId) {
    Actions.change({ loading: true });

    api.dms
      .archiveContainer(id, { tagId })
      .then((newData) => {
        message.success('Archived successfully');
        Actions.change({ loading: false });
        Actions.getContainers({ id: newData.tagId });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get document
   * @param {*} id
   */
  getDocument(id) {
    Actions.change({ loading: true });

    api.dms
      .getDocument({ id })
      .then((data) => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get document By Id
   * @param {*} id
   */
  getDocumentById(id) {
    return api.dms.getDocumentById(id);
  },

  /**
   * Get document versions
   * @param {*} id
   */
  getDocumentVersions(id) {
    Actions.change({ loading: true });

    api.dms
      .getDocumentVersions({ id })
      .then((data) => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Upload
   */
  upload(data) {
    return api.dms.upload(data);
  },

  /**
   * Add Document
   */
  addDocument(data) {
    return api.dms.addDocument(data);
  },

  /**
   * Get latest versions of a container
   */
  getLatestVersions(data) {
    return api.dms.getDocumentLatestVersion(data);
  },

  /**
   * Get data form map
   * @param filters
   * @param map
   */
  getMap({ map, tagId, ...props }, isDataAlreadyExists = {}) {
    const { containerFilters: filters } = store.getState().dms;

    Map.change({ loading: true }, map);

    if (isDataAlreadyExists.list) {
      const { list, filters: nextFilters } = isDataAlreadyExists;

      return Map.change({ data: list, filters: nextFilters, loading: false }, map);
    }

    return api.dms
      .getContainers({ filters, id: tagId, map: true, ...props })
      .then(({ list, ...payload }) => Map.change({ data: list, ...payload, loading: false }, map))
      .catch(() => Map.change({ loading: false }, map));
  },

  /**
   * Filter
   * @param newFilter
   */
  filter(newFilter = {}, tagId, parentModuleData) {
    const { containerFilters: filters = {}, containerPagination: pagination } = store.getState().dms;
    const nextFilters = { ...filters, ...newFilter };

    if ('countryId' in newFilter && newFilter.countryId === filters.countryId) {
      delete nextFilters.countryId;
    }

    if (!parentModuleData || parentModuleData.type !== 'application') {
      Actions.getContainers({ filters: nextFilters, id: tagId, pagination: { ...pagination, current: 1 } });
    } else {
      Actions.getSystemContainers({ filters: nextFilters, id: tagId, pagination: { ...pagination, current: 1 }, ...parentModuleData });
    }
  },

  /**
   * Clean filter
   * @param key
   */
  cleanFilter(key, tagId) {
    const { containerFilters: filters = {}, containerPagination: pagination } = store.getState().dms;

    delete filters[key];
    Actions.getContainers({ filters, id: tagId, pagination: { ...pagination, current: 1 } });
  },

  /**
   * Update ACL for tag by ID
   * @param props
   */
  updateAclTag(props) {
    Actions.change({ loading: true });

    return api.dms
      .updateAclTag(props)
      .then(() => {
        Actions.getTags();
        message.success('Tag ACL updated');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update ACL for many tags
   * @param acl
   * @param revoke
   * @param props
   */
  updateAclForManyTags({ acl, revoke = false, ...props }) {
    Actions.change({ sharingAllTags: 0 });

    return api.dms.updateAclForManyTags({ acl, revoke, ...props });
  },

  /**
   * Update ACL for container by ID
   * @param id
   * @param acl
   */
  updateAclContainer({ id, acl, tagId }) {
    Actions.change({ loading: true });

    return api.dms
      .updateAclContainer({ id, acl })
      .then(() => {
        Actions.getContainers({ id: tagId });
        message.success('ACL updated');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update ACL for many containers
   * @param acl
   * @param revoke
   * @param props
   */
  updateAclForManyContainers({ acl, revoke = false, ...props }) {
    Actions.change({ sharingAllContainers: 0 });

    return api.dms.updateAclForManyContainers({ acl, revoke, ...props });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.DMS_CHANGE, payload });
  },
};

export default Actions;
