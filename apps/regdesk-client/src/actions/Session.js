import { Modal } from 'antd';
import api from '../utils/api';
import * as constants from '../const';
import store from '../stores';
import Auth from './Auth';
import Task from './Task';
import Profile from './Profile';
import Zendesk from '../utils/zendesk';

window.session = {
  timer: null,
  timeEnd: 0,
  sessionLifetime: 0,
  timestamp: Date.now(),
};

const SessionActions = {
  /**
   * Start timer
   * @param sessionTime
   */
  init({ sessionTime }) {
    if (window.session.sessionLifetime || !sessionTime) {
      console.log('STOP DOUBLE INIT SESSION');

      return;
    }

    window.session.sessionLifetime = sessionTime;
    SessionActions.updateTimer();
    clearInterval(window.session.timer);

    window.session.timer = setInterval(() => {
      const timeLeft = Math.trunc((window.session.timeEnd - Date.now()) / 1000);

      if (timeLeft <= 300) {
        SessionActions.change({ sessionExpires: timeLeft });
      }

      if (timeLeft <= 0) {
        SessionActions.timeout();
        SessionActions.stop();
      }
    }, 1000);

    document.addEventListener('keyup', SessionActions.checkEvent);
    document.addEventListener('mouseup', SessionActions.checkEvent);
  },

  /**
   * Check Event
   */
  checkEvent() {
    if (window.session.sessionLifetime && window.session.timestamp + (60 * 1000) < Date.now()) {
      SessionActions.update();
    }
  },

  /**
   * Update Timer
   */
  updateTimer() {
    if (window.session.sessionLifetime) {
      window.session.timestamp = Date.now();
      window.session.timeEnd = Date.now() + window.session.sessionLifetime * 1000;
      SessionActions.change({ sessionExpires: window.session.sessionLifetime });
    }
  },

  /**
   * Stop timer
   */
  stop() {
    clearInterval(window.session.timer);
    window.session.timer = null;
    window.session.timeEnd = 0;
    window.session.sessionLifetime = 0;
    document.removeEventListener('keyup', SessionActions.checkEvent);
    document.removeEventListener('mouseup', SessionActions.checkEvent);
    Task.stop();
  },

  /**
   * Timeout
   */
  timeout() {
    if (window.session.timer) {
      Modal.error({
        title: 'Session timeout',
        content: 'Please login again to continue working',
        onOk: () => {
          Auth.logout();
        },
      });
    }
  },

  /**
   * Check session
   */
  check() {
    api.session
      .check()
      .then((user = {}) => {
        SessionActions.change({ ...user, loaded: true });
        Zendesk.init(user);

        if (user._id) {
          SessionActions.init(user);
          Profile.loadCustomTitles();
        }
      })
      .catch(err => SessionActions.change({ loaded: true, loading: false, sessionExpires: 0, message: (err && err.message) || '' }));
  },

  /**
   * Update session
   */
  update() {
    api.session
      .update()
      .then(() => SessionActions.updateTimer())
      .catch(err => SessionActions.change({ sessionExpires: 0, message: (err && err.message) || '' }));
  },

  /**
   * Change auth reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.ACCOUNT_CHANGE, payload });
  },
};

export default SessionActions;
