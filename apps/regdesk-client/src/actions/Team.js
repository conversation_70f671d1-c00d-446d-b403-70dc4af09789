import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

let timerId = null;

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    const { filters } = store.getState().team;

    Actions.change({ loading: true });

    api.team
      .get({ filters, ...props })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Search
   * @param value
   */
  search(value) {
    const { filters } = store.getState().team;
    const newFilters = { ...filters, name: value };

    if (!value) delete newFilters.name;

    Actions.change({ filters: newFilters });
    clearTimeout(timerId);

    timerId = setTimeout(() => Actions.get(), 500);
  },

  /**
   * Get role for sub user
   * @returns {Function}
   */
  getTeamRole() {
    Actions.change({ loading: true });

    api.team
      .getTeamRole()
      .then(({ filters, ...data }) => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get user
   * @returns {Function}
   */
  getById(idu) {
    Actions.change({ modalLoading: true });

    return api.team
      .getById(idu)
      .then(data => {
        Actions.change({ ...data, modalLoading: false });

        return data;
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Remove by ID
   * @param id
   * @param assignee
   */
  remove(id, assignee) {
    return api.team.remove(id, assignee);
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.team.add(data);
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  update(data) {
    return api.team.update(data);
  },

  /**
   * Change password
   * @param data
   * @returns {*}
   */
  changePassword(data) {
    return api.team.changePassword(data);
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: '', modalLoading: false, idu: null, user: {} });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}){
    store.dispatch({ type: constants.TEAM_CHANGE, payload });
  },

  /**
   * Filter
   * @param newFilter
   */
  filter(newFilter = {}) {
    const { filters = {} } = store.getState().team;
    const nextFilters = { ...filters, ...newFilter };

    Actions.get({ filters: nextFilters });
  },

  /**
   * Clean filter
   * @param key
   */
  cleanFilter(key) {
    const { filters = {} } = store.getState().team;

    delete filters[key];

    Actions.get({ filters });
  },

  /**
   * Clean all filters
   */
  cleanAllFilters() {
    Actions.get({ filters: {} });
  },

  /**
   * Add Organization
   */
  addOrganization(data) {
    return api.team.addOrganization(data);
  },

  /**
   * Get Organizations
   */
  getOrganizations(params) {
    const pagination = params.pagination || store.getState().team[params.productId || params.productFamilyId ? 'dataPagination' : 'organizationsPagination'];

    Actions.change({ modalLoading: true });

    return api.team
      .getOrganizations({ pagination, ...params })
      .then(data => Actions.change({ ...data }))
      .finally(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Delete Organization
   */
  removeOrganization(id, type) {
    Actions.change({ modalLoading: true });

    return api.team
      .removeOrganization(id, type)
      .then(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Update Organization
   */
  updateOrganization(id, body) {
    Actions.change({ modalLoading: true });

    return api.team
      .updateOrganization(id, body)
      .then(() => {
        Actions.change({ modalLoading: false });
        Actions.getOrganizations({ type: body.type });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },
};

export default Actions;

