import { message } from 'antd';
import * as constants from '../const';
import { BAUSCH_EU } from '../containers/Products/BauschVersion/helpers';
import store from '../stores';
import api from '../utils/api';
import { objectFromArray } from '../utils/helpers';

const Actions = {
  /**
   * Get data
   */
  getPage({ page = 1, filters = {} }) {
    Actions.change({ modalLoading: true });

    return api.productsBausch
      .getPage({ page, filters })
      .then(({ drafts }) => Actions.change({ drafts, currentFilters: filters, modalLoading: false }))
      .catch(() => Actions.change({ modalLoading: false, currentFilters: filters }));
  },

  removeBauschSku(skuId) {
    Actions.change({ modalLoading: true });

    return api.productsBausch.removeBauschSku(skuId);
  },

  /**
   * Get by id
   */
  getById(id) {
    Actions.change({ modalLoading: true });

    return api.productsBausch
      .getById(id)
      .then(({ item }) => {
        const data = {
          ...item,
          prevCountries: item.countries?.map(country => ({ ...country, classification: { ...country.classification } })),
          sku: objectFromArray(item.sku, 'name'),
          prevApplicableEU: item.applicableEU,
          prevApplicableUS: item.applicableUS,
          // prevApplicableGlobal: item.applicableGlobal,
        };

        Actions.change({
          newProductState: {
            stage: 1,
            step: 1,
            data,
          },
          modalLoading: false,
        });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Create Add Product Bausch Log
   */
  createBauschLog(body) {
    // eslint-disable-next-line no-param-reassign
    body.itemId = store.getState().products.idp || store.getState().products.draftId;

    return api.productsBausch.createBauschLog(body);
  },

  createBauschLogUpdateDraft(body) {
    body.productId = store.getState().products.idp || store.getState().products.draftId;

    const { isUpdateDraft } = store.getState().products;

    if (isUpdateDraft) {
      body.oldPValue = store.getState().products.oldPValue;
    }

    return api.productsBausch.createBauschLogUpdateDraft(body);
  },

  /**
   * Add draft
   * @param data
   */
  addDraft(data) {
    Actions.change({ modalLoading: true });

    return api.productsBausch
      .addDraft(data)
      .then(() => {
        message.success('Product draft created successfully');

        Actions.change({
          showModal: '',
          modalLoading: false,
          draftId: null,
          globalEdit: false,
          newProductState: {
            stage: 1,
            step: 1,
            data: {},
          },
        });

        const { drafts, currentFilters } = store.getState().products;
        const { docs, page } = drafts;

        Actions.getPage({
          page: docs.length === 1 && page !== 1 ? page - 1 : page,
          filters: currentFilters,
        });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Add draft
   * @param data
   */
  addProduct(data) {
    const { idp } = store.getState().products;

    Actions.change({ modalLoading: true });

    return api.productsBausch
      .updateProductSkuGlobal(data, idp)
      .then(() => {
        message.success('Product added');

        Actions.change({
          showModal: '',
          modalLoading: false,
          draftId: null,
          idp,
          globalEdit: false,
          newProductState: {
            stage: 1,
            step: 1,
            data: {},
          },
        });

        const { drafts, currentFilters } = store.getState().products;
        const { docs, page } = drafts;

        Actions.getPage({
          page: docs.length === 1 && page !== 1 ? page - 1 : page,
          filters: currentFilters,
        });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  checkSku(sku) {
    return api.productsBausch.checkSku(sku);
  },

  addProductImmediatly(data) {
    Actions.change({ modalLoading: true });

    return api.productsBausch
      .addProductImmediatly(data)
      .then(response => {
        message.success(`${response.message} created`);

        Actions.change({
          showModal: '',
          modalLoading: false,
          draftId: response.id,
          idp: response.id,
          globalEdit: false,
          isDraftCreating: false,
          newProductState: {
            stage: 1,
            step: 1,
            data: {},
          },
        });

        const { drafts, currentFilters } = store.getState().products;
        const { docs, page } = drafts;

        Actions.getPage({
          page: docs.length === 1 && page !== 1 ? page - 1 : page,
          filters: currentFilters,
        });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Update draft
   * @param data
   * @returns {*}
   */
  updateDraft({ _id, ...rest }) {
    Actions.change({ modalLoading: true });

    return api.productsBausch
      .updateDraft(rest, _id)
      .then((response) => {
        message.success('Product draft updated successfully');

        Actions.change({
          modalLoading: false,
          draftId: _id,
          showModal: '',
          isUpdateDraft: true,
          oldPValue: response.oldValue
        });

        const { drafts, currentFilters } = store.getState().products;
        const { docs, page } = drafts;

        Actions.getPage({
          page: docs.length === 1 && page !== 1 ? page - 1 : page,
          filters: currentFilters,
        });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Update product
   * @param data
   * @returns {*}
   */
  updateProduct(data, { disableLoading = false } = {}) {
    const { idp } = store.getState().products;
    const list = [...store.getState().products.list];

    !disableLoading && Actions.change({ modalLoading: true });

    return api.productsBausch
      .updateProduct(data, idp)
      .then(({ product }) => {
        message.success('Product updated successfully');

        const index = list.findIndex(i => i._id === product._id);

        list.splice(index, 1, product);
        Actions.change({ modalLoading: false, list, item: product });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Update product Sku EU
   * @param data
   * @returns {*}
   */
  updateProductSkuEU(data) {
    const idp = store.getState().products.idp || store.getState().products.draftId;

    Actions.change({ modalLoading: true, idp });

    return api.productsBausch
      .updateProductSkuEU(data, idp)
      .then((response) => {
        const { oldPValue } = store.getState().products;

        if (response.oldValue) {
          oldPValue.EU = response.oldValue;
        }
        message.success('Product SKU updated successfully');
        Actions.change({ modalLoading: false, idp, oldPValue });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Update product Sku US
   * @param data
   * @returns {*}
   */
  updateProductSkuUS(data) {
    const idp = store.getState().products.idp || store.getState().products.draftId;

    Actions.change({ modalLoading: true, idp });

    return api.productsBausch
      .updateProductSkuUS(data, idp)
      .then((response) => {
        const { oldPValue } = store.getState().products;

        if (response.oldValue) {
          oldPValue.US = response.oldValue.oldValue;
        }
        message.success('Product SKU updated successfully');
        Actions.change({ modalLoading: false, idp, draftId: idp, oldPValue });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Update product Sku Global
   * @param data
   * @returns {*}
   */
  updateProductSkuGlobal(data) {
    const idp = store.getState().products.idp || store.getState().products.draftId;

    Actions.change({ modalLoading: true });

    return api.productsBausch
      .updateProductSkuGlobal(data, idp)
      .then(() => {
        message.success('Product SKU updated successfully');
        Actions.change({ modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Remove
   * @param id
   */
  remove(id) {
    Actions.change({ modalLoading: true });

    return api.productsBausch
      .remove(id)
      .then(() => {
        const { drafts, currentFilters } = store.getState().products;
        const { docs, page } = drafts;

        Actions.getPage({
          page: docs.length === 1 && page !== 1 ? page - 1 : page,
          filters: currentFilters,
        });
        message.success('Deleted successfully');
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Get Assignees
   */
  getAssignees() {
    Actions.change({ modalLoading: true });

    return api.productsBausch
      .getAssignees()
      .then(({ assignees }) => Actions.change({ assignees, modalLoading: false }))
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Get Draft SKUs
   */
  getSkuList(params) {
    const {
      idp,
      newProductState: { data },
      draftId,
    } = store.getState().products;

    if (!params.productId && !idp && !draftId && data?.sku) {
      let skuList = Object.values(data.sku);

      if (params.eu) {
        skuList = skuList.map(s => ({
          ...s,
          countries: s.countries.filter(c => BAUSCH_EU.includes(c)),
        }));
      } else if (params.us) {
        skuList = skuList.map(s => ({
          ...s,
          countries: s.countries.filter(c => c === 'USA'),
        }));
      }

      return Actions.change({ skuList });
    }
    params.productId = idp || draftId;

    return api.productsBausch.getSkuList(params).then(({ sku }) => Actions.change({ skuList: sku }));
  },

  /**
   * Update SKU Registration data
   */
  updateSkuRegData(data) {
    Actions.change({ modalLoading: true });

    const { item } = store.getState().sku;

    return api.productsBausch
      .updateSkuRegData(data, item._id)
      .then(({ sku }) => {
        Actions.change({ modalLoading: false });
        message.success('SKU updated successfully');

        return sku;
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Get classifications
   */
  getClassificationData(filters = {}) {
    Actions.change({ modalLoading: true });

    return api.productsBausch
      .getClassifications(filters)
      .then(({ classifications }) => {
        Actions.change({ classifications, modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Get SKU data
   */
  getSkuData(params) {
    Actions.change({ modalLoading: true });

    if (!params.productId) {
      params.productId = store.getState().products.idp;
    }

    return api.productsBausch
      .getSkuData(params)
      .then(({ data }) => Actions.change({ modalLoading: false, skuData: data }))
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Get license holders
   */
  getSuggestions(params = {}) {
    const { suggestions } = store.getState().products;

    Actions.change({ modalLoading: true });

    return api.productsBausch
      .getSuggestions(params)
      .then(newData => {
        Actions.change({ suggestions: { ...suggestions, ...newData }, modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.PRODUCTS_CHANGE, payload });
  },
};

export default Actions;
