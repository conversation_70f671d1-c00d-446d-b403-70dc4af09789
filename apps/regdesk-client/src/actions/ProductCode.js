import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import i18n from '../utils/i18n';

const { t } = i18n;

const Actions = {
  /**
   * Get data
   * @param props
   */
  get(props) {
    const { filters, pagination } = store.getState().productCode;

    Actions.change({ loading: true });

    api.productCode
      .get({ filters, pagination, ...props })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Search
   * @param value
   * @param table
   */
  search(value, table = false) {
    const { pagination, filters } = store.getState().productCode;
    const newFilters = { ...filters, name: value };

    Actions.change({ filters: newFilters });

    clearTimeout(this.timerId);

    this.timerId = setTimeout(() => {
      const props = { filters: newFilters, pagination };

      if (table) {
        props.table = true;
        props.pagination = { ...pagination, current: 1 };
      }

      Actions.get(props);
    }, 300);
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: '', menuItem: '', item: {} });
  },

  /**
   * Remove by ID
   * @param id
   */
  remove(id) {
    Actions.change({ loading: true });

    return api.productCode.remove(id);
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.productCode.add(data);
  },

  /**
   * Update
   * @param body
   * @returns {*}
   */
  update(body) {
    Actions.change({ modalLoading: true });

    return api.productCode
      .update(body)
      .then(data => {
        message.success(t('Glossary.SKUUpdated_one'));
        Actions.change({ ...data, modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  updateMultiple(body) {
    Actions.change({ modalLoading: true });

    return api.productCode
      .updateMultiple(body)
      .then(data => {
        message.success(t('Glossary.SKUUpdated_one'));
        Actions.change({ ...data, modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * UpdateMany
   * @param body
   * @returns {*}
   */
  updateMany(body) {
    Actions.change({ modalLoading: true });
    return api.productCode
      .updateMany(body)
      .then(data => {
        message.success("SKU'S updated");
        Actions.change({ modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Get by id
   * @param body
   * @returns {*}
   */
  getById() {
    const { ids, menuItem } = store.getState().productCode;
    Actions.change({ modalLoading: true });
    return api.productCode
      .getById(ids)
      .then(({ productCode }) => {
        Actions.change({ modalLoading: false, item: productCode, menuItem: menuItem || productCode.countries[0] || '' });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.PORTAL_CODE_CHANGE, payload });
  },
};

export default Actions;
