import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get to do data
   * @returns {Function}
   */
  getToDoPage({ page = 1, filters = {} }) {
    Actions.change({ loadingToDo: true, currentToDoFilters: filters });

    api.checklists.getTodoPage({ page, filters })
      .then(({ data }) => Actions.change({ loadingToDo: false, toDoData: data }))
      .catch(() => Actions.change({ loadingToDo: false }));
  },

  /**
   * Get sentOut data
   * @returns {Function}
   */
  getSentOutPage({ page = 1, filters = {} }) {
    Actions.change({ loadingSentOut: true, currentSentOutFilters: filters });

    api.checklists.getSentOutPage({ page, filters })
      .then(({ data }) => Actions.change({ loadingSentOut: false, sentOutData: data }))
      .catch(() => Actions.change({ loadingSentOut: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}){
    store.dispatch({ type: constants.TODO_CHANGE, payload });
  },
};

export default Actions;

