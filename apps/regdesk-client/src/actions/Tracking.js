import FileSaver from 'file-saver';
import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import Map from './Map';
import history from '../utils/browserHistory';

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    const { sorter, filters, pagination, searchValue, advancedSearchList, advancedSearchValue } = store.getState().tracking;

    Actions.change({ loading: true });

    const params = { sorter, filters, pagination, ...props };

    if (advancedSearchValue?.length && advancedSearchList?.length) {
      params.advancedSearchList = advancedSearchList;
      params.advancedSearchValue = advancedSearchValue;
    }

    if (searchValue) params.searchValue = searchValue;

    return api.tracking
      .get(params)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Search
   * @param value
   * @param sortByProduct
   */
  search(value) {
    Actions.change({
      searchValue: value,
    });

    clearTimeout(this.timerId);

    this.timerId = setTimeout(() => {
      const { searchValue } = store.getState().tracking;

      Actions.get({ searchValue });
    }, 1500);
  },

  /**
   * Remove
   * @param id
   */
  remove(id) {
    Actions.change({ loading: true });

    return api.tracking
      .remove(id)
      .then(() => {
        const { pagination } = store.getState().tracking;
        const { current, pageSize = 10, total = 0 } = pagination;
        let newPagination = pagination;

        if (total % pageSize === 1) newPagination = { current: current > 1 ? current - 1 : 1, pageSize };

        Actions.get({ pagination: newPagination });
        message.success('Tracking removed');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Add Project Type (TypeSybmission)
   * @param data
   * @returns {*|{url, body}}
   */
  addProjectType(data) {
    return api.tracking.addProjectType(data);
  },

  /**
   * Get Project Types (TypeSybmissions)
   * @param params
   * @returns {*|{url, body}}
   */
  getProjectTypes(params) {
    Actions.change({ popupLoading: true });

    return api.tracking
      .getProjectTypes(params)
      .then(data => Actions.change({ ...data,  popupLoading: false  }))
      .catch(() => Actions.change({ popupLoading: false }));
  },

  /**
   * Hide Project Type
   * @param body
   * @returns {*}
   */
  hideProjectType(body) {
    Actions.change({ hidding: body._id });

    return api.tracking
      .hideProjectType(body)
      .then(({ projectType }) => {
        const updatedProjectTypes = [...store.getState().tracking.projectTypes];
        const index = updatedProjectTypes.findIndex(i => i._id === projectType._id);

        updatedProjectTypes.splice(index, 1, projectType);
        Actions.change({ hidding: null, projectTypes: updatedProjectTypes });
      })
      .catch(() => Actions.change({ hidding: null }));
  },

  /**
   * Add
   * @param data
   * @returns {*|{url, body}}
   */
  add(data) {
    return api.tracking.add(data);
  },

  /**
   * Update ACL by ID
   * @param id
   * @param acl
   */
  updateAcl({ id, acl }) {
    Actions.change({ loading: true });

    return api.tracking
      .updateAcl({ id, acl })
      .then(() => {
        Actions.get();
        message.success('Tracking ACL updated');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  updateAclForMany({ acl, revoke = false, ...props }) {
    Actions.change({ sharingAll: 0 });

    return api.tracking.updateAclForMany({ acl, revoke, ...props });
  },

  /**
   * Clone
   * @param data
   */
  clone(data) {
    return api.tracking.clone(data);
  },

  /**
   * Clone History
   * @param id
   */
  getCloneHistory(id) {
    return api.tracking.getCloneHistory(id);
  },

  /**
   * Export
   * @param data
   */
  export(data) {
    return api.tracking.export(data);
  },

  /**
   * Get by Id
   * @param id
   */
  getById(id, opt) {
    Actions.change({ loading: true });

    return api.tracking
      .getById(id)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(e => {
        const { code } = e;

        if (opt?.redirectToTrackingWithError && code === '151') {
          history.replace('/tracking');
        }
        Actions.change({ loading: false });
      });
  },

  exportReport(props) {
    const opt = {};

    if (['15-2'].includes(props.type)) {
      opt.responseType = 'blob';
    }

    Actions.change({ loading: true });

    return api.tracking
      .exportReport(props, opt)
      .then(response => {
        const filename = response.headers['content-disposition'].replace(/.+filename="(.+)"/, '$1');
        const { type } = response.headers;

        if (['15-2'].includes(type)) {
          FileSaver.saveAs(response.data, filename);
        } else {
          const s2ab = s => {
            const buf = new ArrayBuffer(s.length);
            const view = new Uint8Array(buf);

            for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;

            return buf;
          };

          const blob = new Blob([s2ab(response.data)], {
            type: 'application/vnd.ms-excel;',
          });

          FileSaver.saveAs(blob, filename);
        }
        Actions.change({ loading: false });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Change Report
   * @param data
   */
  changeReport(data = {}) {
    const { report } = store.getState().tracking;

    Actions.change({ report: { ...report, ...data } });
  },

  /**
   * Update
   * @param trackId
   * @param props
   */
  update({ trackId, ...props }) {
    Actions.change({ popupLoading: true });

    return api.tracking
      .update(trackId, props)
      .then(() => Actions.change({ popupLoading: false }))
      .catch(() => Actions.change({ popupLoading: false }));
  },

  /**
   * Get certificates
   * @param params
   */
  getCertificates(params) {
    Actions.change({ popupLoading: true });

    return api.tracking
      .getCertificates(params)
      .then(data => Actions.change({ ...data, popupLoading: false }))
      .catch(() => Actions.change({ popupLoading: false }));
  },

  /**
   * Add Certificate
   * @param body
   * @returns {*}
   */
  addCertificate(body) {
    return api.tracking.addCertificate(body);
  },

  /**
   * Hide Certificate
   * @param body
   * @returns {*}
   */
  hideCertificate(body) {
    Actions.change({ hidding: body._id });

    return api.tracking
      .hideCertificate(body)
      .then(({ certificate }) => {
        const updatedCertificates = [...store.getState().tracking.certificates];
        const index = updatedCertificates.findIndex(i => i._id === certificate._id);

        updatedCertificates.splice(index, 1, certificate);
        Actions.change({ hidding: null, certificates: updatedCertificates });
      })
      .catch(() => Actions.change({ hidding: null }));
  },

  /**
   * Get responders
   * @param props
   */
  getResponders(props = {}) {
    return api.tracking.getResponders(props).then(data => Actions.change({ ...data }));
  },

  /**
   * Filter
   * @param newFilter
   */
  filter(newFilter = {}) {
    const { filters = {}, pagination } = store.getState().tracking;
    const nextFilters = { ...filters, ...newFilter };

    if ('countryId' in newFilter && newFilter.countryId === filters.countryId) {
      delete nextFilters.countryId;
    }

    Actions.get({ filters: nextFilters, pagination: { ...pagination, current: 1 } });
  },

  /**
   * Set filters
   * @param newFilter
   */
  setFilter(newFilter = {}) {
    const { filters = {} } = store.getState().tracking;
    const nextFilters = { ...filters, ...newFilter };

    Actions.change({ filters: nextFilters });
    Map.change({ filters: nextFilters }, 'tracking');
  },

  /**
   * Clean filter
   * @param key
   */
  cleanFilter(key) {
    const { filters = {} } = store.getState().tracking;

    delete filters[key];

    Actions.get({ filters });
  },

  /**
   * Clean all filters
   */
  cleanAllFilters() {
    Actions.get({ filters: {} });
  },

  /**
   * Get data form map
   * @param filters
   * @param map
   */
  getMap({ map, ...props }, isDataAlreadyExists = {}) {
    const { filters } = store.getState().tracking;

    Map.change({ loading: true }, map);

    if (isDataAlreadyExists.list) {
      const { list: data, filters: nextFilters } = isDataAlreadyExists;

      return Map.change({ data, filters: nextFilters, loading: false }, map);
    }

    api.tracking
      .get({ filters, ...props, map: true })
      .then(({ list, ...payload }) => Map.change({ data: list, ...payload, loading: false }, map))
      .catch(() => Map.change({ loading: false }, map));
  },

  /**
   * Get Checklist
   * @param filters
   */
  getChecklist(filters) {
    Actions.change({ formLoading: true });

    api.checklists
      .get({ filters, tracking: true, sortByProduct: false })
      .then(({ list }) => Actions.change({ checklists: list, formLoading: false }))
      .catch(() => Actions.change({ formLoading: false }));
  },

  /**
   * Add comment
   * @param logId
   * @param status
   * @param map
   */
  addComment(logId, status, { message, mentionedUsers }) {
    const { item } = store.getState().tracking;

    if (!item.comments) {
      item.comments = [];
    }

    Actions.change({ popupLoading: true });

    return api.tracking
      .addComment({ id: item._id, body: { logId, message, status, mentionedUsers } })
      .then(({ comment }) =>
        Actions.change({ popupLoading: false, item: { ...item, comments: [...item.comments, comment] } }),
      )
      .catch(() => Actions.change({ popupLoading: false }));
  },

  /**
   * Resolve comment
   * @param commentId
   */
  resolveComment(commentId) {
    const { item } = store.getState().tracking;

    return api.comments.resolve(commentId, { module: 'tracking', trackingId: item._id }).then(({ comment }) => {
      const commentIndex = item.comments.findIndex(c => c._id === commentId);

      item.comments.splice(commentIndex, 1, comment);
      Actions.change({ item: { ...item, comments: item.comments } });
    });
  },

  /**
   * Approve
   * @param props
   */
  approve(props) {
    Actions.change({ loading: true });

    return api.tracking.approve(props).catch(() => Actions.change({ loading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.TRACKING_CHANGE, payload });
  },
};

export default Actions;
