import { message } from 'antd';
import * as constants from '../const';
import store from '../stores';
import api from '../utils/api';

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    Actions.change({ loading: true });

    const { module } = store.getState().products;
    const { selectedTag } = store.getState().dms;
    const { projectId: ccpProjectId } = store.getState().ccpProjects;
    const { item: taskItem } = store.getState().ccpTasks;
    const { item: projectItem } = store.getState().projects;
    const { _id: tagId } = selectedTag || {};
    const { _id: withoutTask } = taskItem || {};
    const { _id: projectId } = projectItem || {};

    const params = { ...props };

    if (props.module) params.module = props.module;
    else if (module) params.module = module;

    if (tagId) params.tagId = tagId;
    if (projectId) params.projectId = projectId;
    if (ccpProjectId) params.projectId = ccpProjectId;
    if (withoutTask) params.withoutTask = withoutTask;

    return api.products
      .getList(params)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get products for table
   * @param props
   */
  getTable(props = {}) {
    const {
      sorter,
      filters,
      pagination,
      searchType,
      searchValue,
      advancedSearchValue,
      advancedSearchList,
    } = store.getState().products;

    return Actions.get({
      sorter,
      pagination,
      filters,
      searchValue,
      searchType,
      advancedSearchValue,
      advancedSearchList,
      ...props,
      table: true,
    });
  },

  /**
   * Get matched products and productFamilies
   * @param props
   */
  getMatchedProducts(props = {}) {
    const params = { ...props };

    Actions.change({ productsFoundInFile: { loading: true } });

    return api.products
      .getMatchedList(params)
      .then(data => Actions.change({ productsFoundInFile: { ...data, loading: false } }))
      .catch(() => Actions.change({ productsFoundInFile: { loading: false } }));
  },

  /**
   * Get by Id
   * @param id
   */
  getById(id) {
    Actions.change({ modalLoading: true });

    return api.products
      .getById(id)
      .then(data => Actions.change({ ...data, modalLoading: false }))
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   *
   * Get by Id
   * @param id
   */
  getByIdWithTopCountryData(id) {
    Actions.change({ modalLoading: true });

    return api.products
      .getById(id)
      .then(data => {
        api.regulations
          .getMap({ map: false })
          .then(topData => {
            Actions.change({ item: data.item, topCountries: topData.data, modalLoading: false });
          })
          .catch(() => Actions.change({ modalLoading: false }));
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Search
   * @param value
   * @param table
   */
  search(value, table = false) {
    Actions.change({
      searchValue: value,
      advancedSearchList: [],
      advancedSearchValue: '',
      advancedSearchEnabled: false,
    });

    clearTimeout(this.timerId);

    this.timerId = setTimeout(() => {
      const { module, searchType, searchValue, pagination, filters } = store.getState().products;

      const data = { searchType, searchValue, filters, module: module || 'products' };

      if (table) {
        data.table = true;
        data.pagination = { ...pagination, current: 1 };
      }

      Actions.get(data);
    }, 300);
  },

  /**
   * Get detail data
   * @param table
   * @param props
   */
  getDetail(table, props = {}) {
    const { idp, ...product } = store.getState().products;

    if (!idp || !['applications', 'tracking', 'checklists', 'productFamilies'].includes(table)) {
      return;
    }

    const { pagination } = product[table];

    Actions.change({ [table]: { ...product[table], loading: true } });

    api[table]
      .get({ pagination, ...props, filters: { productId: idp } })
      .then(data => Actions.change({ [table]: { ...product[table], ...data, loading: false } }))
      .catch(() => Actions.change({ [table]: { ...product[table], loading: false } }));
  },

  /**
   * Get Selected List
   */
  getSelectedList(props) {
    const { idp, ...product } = store.getState().products;

    const { pagination } = product.selectedProducts;

    Actions.change({ selectedProducts: { ...product.selectedProducts, loading: true } });

    return api.products
      .getSelectedList({ pagination, ...props })
      .then(data => Actions.change({ selectedProducts: { ...product.selectedProducts, ...data, loading: false } }))
      .catch(() => Actions.change({ selectedProducts: { ...product.selectedProducts, loading: false } }));
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.products.add(data);
  },

  /**
   * Export
   * @param data
   */
  export(data) {
    return api.products.export(data);
  },
  /**
   * importProductRow
   * @param data
   */

  importProductRow(data) {
    return api.products.importProductRow(data);
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  update(data) {
    return api.products.update(data);
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  updateMany(data) {
    return api.products.updateMany(data);
  },

  /**
   * Remove
   * @param id
   */
  remove(id) {
    Actions.change({ loading: true });

    return api.products
      .remove(id)
      .then(() => Actions.change({ loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  getCategories() {
    Actions.change({ loading: true });

    return api.products.getCategories().then(data => {
      Actions.change({ ...data, loading: false });
    });
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: '', modalLoading: false, idp: null, item: {}, globalEdit: false });
  },

  getAdditionalAttributes({ productId, pagination = {} }) {
    Actions.change({ loading: true });

    api.products
      .getAdditionalAttributes({ productId, params: { pagination } })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  addAdditionalAttribute({ productId, name, text }) {
    Actions.change({ addLoading: true });

    return api.products
      .addAdditionalAttributes({ productId, data: { name, text } })
      .then(data => Actions.change({ ...data, addLoading: false }))
      .catch(err => {
        Actions.change({ addLoading: false });
        throw err;
      });
  },

  updateAdditionalAttribute({ productId, oldText, name, text }) {
    Actions.change({ loading: true });

    return api.products
      .updateAdditionalAttributes({ productId, data: { oldText, name, text } })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(err => {
        Actions.change({ loading: false });
        throw err;
      });
  },

  deleteAdditionalAttribute({ productId, text }) {
    Actions.change({ loading: true });

    return api.products
      .deleteAdditionalAttributes({ productId, text })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(err => {
        Actions.change({ loading: false });
        throw err;
      });
  },

  addGmdn({ productId, gmdn }) {
    const { list, item } = store.getState().products;

    Actions.change({ addLoading: true });

    return api.products
      .gmdnAdd({ productId, body: { gmdn } })
      .then(() => {
        const index = list.findIndex(p => p._id === productId);

        list.splice(index, 1, { ...list[index], gmdn });
        Actions.change({ list: [...list], item: { ...item, gmdn }, loading: false, addLoading: false });
      })
      .catch(() => Actions.change({ loading: false, addLoading: false }));
  },

  updateGmdn({ productId, gmdn, oldGmdn, newGmdn, add = false }) {
    const { list, item } = store.getState().products;

    if (add) {
      Actions.change({ addLoading: true });
    } else {
      Actions.change({ loading: true });
    }

    return api.products
      .gmdnUpdate({ productId, body: { gmdn, oldGmdn, newGmdn } })
      .then(() => {
        const index = list.findIndex(p => p._id === productId);

        list.splice(index, 1, { ...list[index], gmdn });
        Actions.change({ list: [...list], item: { ...item, gmdn }, loading: false, addLoading: false });
      })
      .catch(() => Actions.change({ loading: false, addLoading: false }));
  },

  removeGmdn({ productId, gmdn }) {
    const { list, item } = store.getState().products;

    Actions.change({ loading: true });

    return api.products
      .gmdnRemove({ productId, body: { gmdn } })
      .then(() => {
        const index = list.findIndex(p => p._id === productId);

        list.splice(index, 1, { ...list[index], gmdn });
        Actions.change({ list: [...list], item: { ...item, gmdn }, loading: false, addLoading: false });
      })
      .catch(() => Actions.change({ loading: false, addLoading: false }));
  },

  updateLegacy({ idp, legacy }) {
    Actions.change({ loading: true });

    return api.products.legacyUpdate({ idp, body: { legacy } }).catch(() => Actions.change({ loading: false }));
  },

  updateDiscontinued({ idp, discontinued }) {
    Actions.change({ loading: true });

    return api.products
      .updateDiscontinued({ idp, body: { discontinued } })
      .catch(() => Actions.change({ loading: false }));
  },

  updateTopProduct({ idp, topProduct }) {
    Actions.change({ loading: true });

    return api.products.updateTopProduct({ idp, body: { topProduct } }).catch(() => Actions.change({ loading: false }));
  },

  topCountriesUpdate({ idp, topCountries }) {
    Actions.change({ loading: true });

    return api.products
      .topCountriesUpdate({ idp, body: { topCountries } })
      .catch(() => Actions.change({ loading: false }));
  },

  addLegalManufacturer({ productId, legalManufacturer }) {
    const { list, item } = store.getState().products;

    Actions.change({ addLoading: true });

    return api.products
      .legalManufacturerAdd({ productId, body: { legalManufacturer } })
      .then(() => {
        const index = list.findIndex(p => p._id === productId);

        list.splice(index, 1, { ...list[index], legalManufacturer });
        Actions.change({ list: [...list], item: { ...item, legalManufacturer }, loading: false, addLoading: false });
      })
      .catch(() => Actions.change({ loading: false, addLoading: false }));
  },

  updateLegalManufacturer({ productId, legalManufacturer }) {
    const { list, item } = store.getState().products;

    Actions.change({ loading: true });

    return api.products
      .legalManufacturerUpdate({ productId, body: { legalManufacturer } })
      .then(() => {
        const index = list.findIndex(p => p._id === productId);

        list.splice(index, 1, { ...list[index], legalManufacturer });
        Actions.change({ list: [...list], item: { ...item, legalManufacturer }, loading: false });
      })
      .catch(() => Actions.change({ loading: false, addLoading: false }));
  },

  legalManufacturerRemove({ productId, legalManufacturer }) {
    const { list, item } = store.getState().products;

    Actions.change({ loading: true });

    return api.products
      .legalManufacturerRemove({ productId, body: { legalManufacturer } })
      .then(() => {
        const index = list.findIndex(p => p._id === productId);

        list.splice(index, 1, { ...list[index], legalManufacturer });
        Actions.change({ list: [...list], item: { ...item, legalManufacturer }, loading: false });
      })
      .catch(() => Actions.change({ loading: false, addLoading: false }));
  },

  /**
   * Update ACL by ID
   * @param id
   * @param acl
   */
  updateAcl({ id, acl }) {
    Actions.change({ loading: true });

    return api.products
      .updateAcl({ id, acl })
      .then(() => {
        Actions.getTable();
        message.success('Product ACL updated');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update Product Classifications
   */
  updateClassifications({ idp, countries, country }) {
    Actions.change({ loading: true });

    return api.products
      .updateClassifications({ idp, body: { countries, country } })
      .then(() => Actions.change({ loading: false }))
      .catch(() => Actions.change({ loading: false, addLoading: false }));
  },

  /**
   * Update ACL
   * @param acl
   * @param filters
   * @param advancedSearch
   * @param revoke
   */
  updateAclForMany({ acl, filters, advancedSearch, revoke = false }) {
    Actions.change({ sharingAll: 0 });

    return api.products.updateAclForMany({ acl, filters, revoke, advancedSearch });
  },

  /**
   * Associate Organization
   */
  associateOrganization(data) {
    return api.products.associateOrganization(data);
  },

  /**
   * Remove organization
   */
  removeOrganization(data) {
    return api.products.removeOrganization(data);
  },

  /** EUDAMED UPDATE
   *
   */
  updateEuDamed(data) {
    Actions.change({ modalLoading: true });

    return api.products.updateEuDamed(data);
  },

  /** EUDAMED EXPORT
   *
   */
  exportEudamed(idsList) {
    return api.products.eudamedExport({ ids: idsList });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.PRODUCTS_CHANGE, payload });
  },
};

export default Actions;
