import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import i18n from '../utils/i18n';

const { t } = i18n;

const Actions = {
  getCountriesByProductId(productId, props) {
    const { filters, pagination } = store.getState().documents;

    Actions.change({ loading: true, list: [] });

    api.documents
      .getCountriesByProductId(productId, { filters, pagination, ...props })
      .then((data) => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  getByCountryProductId(productId, countryId, props) {
    const { filters, pagination } = store.getState().documents;

    Actions.change({ modalLoading: true });

    api.documents
      .getByCountryProductId(productId, countryId, { filters, pagination, ...props })
      .then(({ pagination, filters, docs, sorter }) =>
        Actions.change({
          modalPagination: pagination,
          modalFilters: filters,
          selectedItemDocs: docs,
          modalLoading: false,
          sorter
        })
      )
      .catch(() => Actions.change({ modalLoading: false }));
  },

  getGroupByTags(productId, props, clearList) {
    const { filters, pagination } = store.getState().documents;

    if(clearList) {
      Actions.change({ loading: true, list: [] });
    } else {
      Actions.change({ loading: true });
    }

    api.documents
      .getGroupByTags(productId, { filters, pagination, ...props })
      .then((data) => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  getByTag(productId, tagId, props) {
    const { filters } = store.getState().documents;

    Actions.change({ modalLoading: true });

    api.documents
      .getByTag(productId, tagId, { filters, ...props })
      .then(({ pagination, filters, docs, sorter }) =>
        Actions.change({
          modalPagination: pagination,
          modalFilters: filters,
          selectedItemDocs: docs,
          modalLoading: false,
          sorter
        })
      )
      .catch(() => Actions.change({ modalLoading: false }));
  },

  getByProductId(productId, params) {
    Actions.change({ loading: true });

    api.documents
      .getByProductId(productId, params && params)
      .then(({ allByProduct }) => Actions.change({ allByProduct, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  getByFamilyId(familyId, params) {
    Actions.change({ loading: true });

    api.documents
      .getByFamilyId(familyId, params && params)
      .then(({ allByProduct }) => Actions.change({ allByProduct, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  getBySolutionId(solutionId, params) {
    Actions.change({ loading: true });

    api.documents
      .getBySolutionId(solutionId, params && params)
      .then(({ allByProduct }) => Actions.change({ allByProduct, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  searchByProductId(productId, searchValue) {
      clearTimeout(this.timerId);
      this.timerId = setTimeout(() => {
        if(searchValue) {
          Actions.getByProductId(productId, {searchValue});
        } else {
          Actions.getByProductId(productId);
        }
      },300);

  },

  searchByFamilyId(familyId, searchValue) {
    clearTimeout(this.timerId);
    this.timerId = setTimeout(() => {
      if(searchValue) {
        Actions.getByFamilyId(familyId, {searchValue});
      } else {
        Actions.getByFamilyId(familyId);
      }
    },300);

  },
  /**
   * Search
   * @param value
   * @param table
   */
  search(productId, value, byTag) {
    const { filters } = store.getState().documents;
    const newFilters = { ...filters, name: value };

    Actions.change({ filters: newFilters });

    clearTimeout(this.timerId);

    this.timerId = setTimeout(() => {
      const props = { filters: newFilters };

      if (byTag) {
        Actions.getGroupByTags(productId, props);
      } else {
        Actions.getCountriesByProductId(productId, props);
      }
    }, 300);
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: '', menuItem: '', item: {} });
  },

  /**
   * Remove by ID
   * @param id
   */
  remove(id) {
    Actions.change({ loading: true });

    return api.sku.remove(id);
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.sku.add(data);
  },

  /**
   * Search in doc list
   * @param productId
   * @param tagId
   * @param value
   * @param table
   */
  searchModal(productId, value) {
    const { modalFilters, selectedItem } = store.getState().documents;
    const newFilters = { ...modalFilters, name: value };

    Actions.change({ modalFilters: newFilters });

    clearTimeout(this.timerId);

    this.timerId = setTimeout(() => {
      const props = { filters: newFilters };
      // const props = { filters: newFilters, pagination: modalPagination };

      // if (table) {
      //   props.table = true;
      //   props.pagination = { ...modalPagination, current: 1 };
      // }

      if (selectedItem.key === 'tag') {
        Actions.getByTag(productId, selectedItem.value._id, props);
      } else {
        Actions.getByCountryProductId(productId, selectedItem.value, props);
      }
    }, 300);
  },

  /**
   * Update
   * @param body
   * @returns {*}
   */
  update(body) {
    Actions.change({ modalLoading: true });

    return api.sku
      .update(body)
      .then((data) => {
        message.success(t('Glossary.SKUUpdated_one'));
        Actions.change({ ...data, modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * UpdateMany
   * @param body
   * @returns {*}
   */
  updateMany(body) {
    Actions.change({ modalLoading: true });
    return api.sku
      .updateMany(body)
      .then((data) => {
        message.success(t('Glossary.SKUUpdated_other'));
        Actions.change({ modalLoading: false });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  addToProduct({ productId, docsIds }) {
    Actions.change({ modalLoading: true });
    const { filters, pagination, modalPagination, selectedItem } = store.getState().documents;

    api.products
      .addDocuments({ productId, docsIds })
      .then(() => {
        Actions.change({ modalLoading: false, loading: false });
        if (selectedItem) {
          Actions.getByTag(productId, selectedItem.value._id, { filters, pagination: modalPagination });
          Actions.getGroupByTags(productId, { filters, pagination });
        } else {
          Actions.getGroupByTags(productId, { filters: {} });
        }
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  deleteFromProduct({ productId, docId }) {
    Actions.change({ modalLoading: true });
    const { selectedItemDocs, filters, pagination } = store.getState().documents;

    api.products
      .deleteDocument({ productId, docId })
      .then(() => {
        Actions.change({ modalLoading: false, selectedItemDocs: selectedItemDocs.filter((d) => d._id !== docId) });
        Actions.getGroupByTags(productId, { filters, pagination });
      })
      .catch(() => Actions.change({ modalLoading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.DOCUMENTS_CHANGE, payload });
  },
};

export default Actions;
