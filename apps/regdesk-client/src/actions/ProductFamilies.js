import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    const { filters, pagination, advancedSearchValue, advancedSearchList } = store.getState().productFamilies;
    const { selectedTag } = store.getState().dms;
    const { projectId: ccpProjectId } = store.getState().ccpProjects;
    const { item: taskItem } = store.getState().ccpTasks;
    const { item: projectItem } = store.getState().projects;
    const { _id: tagId } = selectedTag || {};
    const { _id: withoutTask } = taskItem || {};
    const { _id: projectId } = projectItem || {};

    const params = { ...props };

    if (tagId) params.tagId = tagId;
    if (projectId) params.projectId = projectId;
    if (ccpProjectId) params.projectId = ccpProjectId;
    if (withoutTask) params.withoutTask = withoutTask;

    Actions.change({ loading: true });

    api.productFamilies
      .get({ filters, pagination, advancedSearchValue, advancedSearchList, module: 'products', ...params })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  getById(id) {
    Actions.change({ modalLoading: true });

    return api.productFamilies
      .getById(id)
      .then(data => Actions.change({ item: { ...data }, modalLoading: false }))
      .catch(() => Actions.change({ modalLoading: false }));
  },

  getDetail(table, props = {}) {
    const { idp, ...productFamilies } = store.getState().productFamilies;

    if (!idp || !['applications', 'tracking', 'checklists', 'products'].includes(table)) {
      return;
    }

    const { pagination } = productFamilies[table];

    Actions.change({ [table]: { ...productFamilies[table], loading: true } });

    if (table === 'products') {
      api[table]
        .getList({ pagination, ...props, filters: { productFamilyId: idp }, table: true })
        .then(data => Actions.change({ [table]: { ...productFamilies[table], ...data, loading: false } }))
        .catch(() => Actions.change({ [table]: { ...productFamilies[table], loading: false } }));
    } else {
      api[table]
        .get({ pagination, ...props, filters: { productFamilyId: idp } })
        .then(data => Actions.change({ [table]: { ...productFamilies[table], ...data, loading: false } }))
        .catch(() => Actions.change({ [table]: { ...productFamilies[table], loading: false } }));
    }
  },

  /**
   * Get matched products and productFamilies
   * @param props
   */
  getMatchedProductFamilies(props = {}) {
    const params = { ...props };

    Actions.change({ productFamiliesFoundInFile: { loading: true } });

    return api.products
      .getMatchedList(params)
      .then(data => Actions.change({ productFamiliesFoundInFile: { ...data, loading: false } }))
      .catch(() => Actions.change({ productFamiliesFoundInFile: { loading: false } }));
  },

  getAdditionalAttributes({ id, pagination = {} }) {
    Actions.change({ loading: true });

    api.productFamilies
      .getAdditionalAttributes({ id, params: { pagination } })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  addAdditionalAttribute({ id, name, text }) {
    Actions.change({ addLoading: true });

    return api.productFamilies
      .addAdditionalAttributes({ id, data: { name, text } })
      .then(data => Actions.change({ ...data, addLoading: false }))
      .catch(err => {
        Actions.change({ addLoading: false });
        throw err;
      });
  },

  updateAdditionalAttribute({ id, oldText, name, text }) {
    Actions.change({ loading: true });

    return api.productFamilies
      .updateAdditionalAttributes({ id, data: { oldText, name, text } })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(err => {
        Actions.change({ loading: false });
        throw err;
      });
  },

  deleteAdditionalAttribute({ id, text }) {
    Actions.change({ loading: true });

    return api.productFamilies
      .deleteAdditionalAttributes({ id, text })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(err => {
        Actions.change({ loading: false });
        throw err;
      });
  },

  closeModal() {
    Actions.change({ showModal: false });
  },

  /**
   * Search
   * @param value
   * @param table
   * @param module
   */
  search(value, table = false, module) {
    Actions.change({
      searchValue: value,
      advancedSearchList: [],
      advancedSearchValue: '',
      advancedSearchEnabled: false,
    });

    const { pagination, filters, searchType, searchValue } = store.getState().productFamilies;
    const newFilters = { ...filters };

    if (searchType !== 'sku') {
      newFilters.name = value;
    }

    Actions.change({ filters: newFilters, searchValue: value, module: 'products' });

    clearTimeout(this.timerId);

    this.timerId = setTimeout(() => {
      const props = { filters: newFilters, pagination, module, searchType, searchValue };

      if (table) props.pagination = { ...pagination, current: 1 };

      Actions.get(props);
    }, 300);
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.productFamilies.add(data);
  },

  /**
   * Update
   * @param data
   * @returns {*}
   */
  update(data) {
    return api.productFamilies.update(data);
  },

  /**
   * Remove
   * @param id
   */
  remove(id) {
    return api.productFamilies.remove(id);
  },

  /**
   * Filter
   * @param newFilter
   */
  filter(newFilter = {}) {
    const { filters = {} } = store.getState().productFamilies;
    const nextFilters = { ...filters, ...newFilter };

    Actions.change({ filters: nextFilters });
    Actions.get({ filters: nextFilters });
  },

  /**
   * Clean filter
   * @param key
   */
  cleanFilter(key) {
    const { filters = {} } = store.getState().productFamilies;

    delete filters[key];

    Actions.get({ filters });
  },

  /**
   * Clean all filters
   */
  cleanAllFilters() {
    Actions.get({ filters: {} });
  },

  /**
   * Update ACL by ID
   * @param id
   * @param acl
   */
  updateAcl({ id, acl }) {
    Actions.change({ loading: true });

    return api.productFamilies
      .updateAcl({ id, acl })
      .then(() => {
        Actions.get({ table: true });
        message.success('Product Family ACL updated');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update ACL
   * @param acl
   * @param filters
   * @param advancedSearch
   * @param revoke
   */
  updateAclForMany({ acl, filters, advancedSearch, revoke = false }) {
    Actions.change({ sharingAll: 0 });

    return api.productFamilies.updateAclForMany({ acl, filters, revoke, advancedSearch });
  },

  /**
   * Associate Organization
   */
  associateOrganization(data) {
    return api.productFamilies.associateOrganization(data);
  },

  /**
   * Remove organization
   */
  removeOrganization(data) {
    return api.productFamilies.removeOrganization(data);
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.PRODUCT_FAMILIES_CHANGE, payload });
  },
};

export default Actions;
