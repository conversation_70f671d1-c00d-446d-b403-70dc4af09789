import api from '../utils/api';
import events from '../utils/events';

let timerId = null
let timerId2 = null

const Actions = {
  /**
   * Check is progress
   */
  check(defaultCountdown = 15) {
    this.get()
      .then((inProgress) => {
        if (!inProgress.length && !timerId && !timerId2) {
          let countdown = defaultCountdown;

          timerId2 = setInterval(() => {
            if (!countdown) {
              clearInterval(timerId2);
            } else {
              countdown -= 1;
              Actions.get();
            }
          }, 2000);
        }
      });
  },

  /**
   * Get progressBar
   * @returns {Promise<string[]>}
   */
  get() {
    return api.task
      .get()
      .then(({ inProgress = [] }) => {
        if (inProgress.length && !timerId) {
          timerId = setInterval(this.get, 2000)
          clearInterval(timerId2)
          timerId2 = null
        }

        if (inProgress.length === 0 && timerId) {
          clearInterval(timerId)
          timerId = null
        }

        inProgress.forEach(({ id, ...data }) => {
          events.emit(id, data)
        })

        return inProgress
      })
  },

  stop() {
    clearInterval(timerId)
    clearInterval(timerId2)
  }
};

export default Actions;
