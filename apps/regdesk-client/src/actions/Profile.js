import { message } from 'antd';
import i18n from 'i18next';
import api from '../utils/api';
import * as constants from '../const';
import store from '../stores';
import Auth from './Auth';
import SessionActions from './Session';
import { prepareCustomBundle } from '../utils/prepareCustomTitlesBundle';

const ProfileActions = {
  /**
   * Change password
   * @param data
   * @returns {*}
   */
  changePassword(data) {
    ProfileActions.change({ loading: true });

    return api.profile
      .changePassword(data)
      .then(ProfileActions.getUserData)
      .then(() => {
        ProfileActions.change({ loading: false });
        message.success('Password changed successfully');
      })
      .catch((err) => {
        ProfileActions.change({ loading: false });

        if (err && err.message) {
          message.error(err.message);
        }
        throw err;
      });
  },

  /**
   * Get user data
   */
  getUserData(opt) {
    return api.session
      .check()
      .then(data => {
        if (opt?.cb) opt.cb(data);

        SessionActions.change({ ...data, loaded: true });
      })
      .catch(() => SessionActions.change({ loaded: true }));
  },

  /**
   * Update profile
   * @param data
   */
  update(data) {
    ProfileActions.change({ loading: true });

    return api.profile
      .update(data)
      .then(ProfileActions.getUserData)
      .then(() => {
        ProfileActions.change({ loading: false });
        message.success('Updated successfully');
      })
      .catch(() => ProfileActions.change({ loading: false }));
  },

  /**
   * Get default ACL settings
   */
  getDefaultAcl() {
    ProfileActions.change({ loading: true });

    return api.profile
      .getDefaultAcl()
      .then(data => ProfileActions.change({ loading: false, ...data }))
      .catch(() => ProfileActions.change({ loading: false }));
  },

  /**
   * Update default ACL settings
   * @param data
   */
  updateDefaultAcl(data) {
    ProfileActions.change({ loading: true });

    return api.profile
      .updateDefaultAcl(data)
      .then(ProfileActions.getUserData)
      .then(() => {
        ProfileActions.change({ loading: false });
        message.success('Updated successfully');
      })
      .catch(() => ProfileActions.change({ loading: false }));
  },

  /**
   * Update Notifications
   * @param data
   */
  updateNotifications(data) {
    ProfileActions.change({ loading: true });

    return api.profile
      .updateNotifications(data)
      .then(ProfileActions.getUserData)
      .then(() => {
        ProfileActions.change({ loading: false });
        message.success('Sent successfully');
      })
      .catch(() => ProfileActions.change({ loading: false }));
  },

  /**
   * Update Integration
   * @param data
   */
  updateIntegration(data) {
    ProfileActions.change({ loading: true });

    return api.profile
      .updateIntegration(data)
      .then(ProfileActions.getUserData)
      .then(() => {
        ProfileActions.change({ loading: false });
        message.success('Sent successfully');
      })
      .catch(() => ProfileActions.change({ loading: false }));
  },

  /**
   * Destroy account
   * @param data
   */
  destroy(data) {
    return api.profile.destroy(data).then(() => {
      message.success('Sent successfully');
      Auth.logout();
    });
  },

  /**
   * Change auth reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.ACCOUNT_CHANGE, payload });
  },

  /**
   * Change Language
   * @returns {Function}
   */
  changeLanguage(lng) {
    return i18n.changeLanguage(lng);
  },

  /**
   * Load custom titles
   */
  loadCustomTitles() {
    const { customTitles } = store.getState().account;

    if (!customTitles) return;

    // TODO: refactor this process after change dictionary delivery method
    // Remove resource bundle to correct rerender of pages and titles on page
    // Because of prepareCustomBundle function and undefined value in it
    i18n.removeResourceBundle(i18n.resolvedLanguage, 'override');

    i18n.addResourceBundle(i18n.resolvedLanguage, 'override', prepareCustomBundle(customTitles), true, true);
  },
};

export default ProfileActions;
