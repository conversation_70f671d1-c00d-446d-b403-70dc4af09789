import { LEGISLATION_CHANGE } from '../const';
import api from '../utils/api';
import store from '../stores';
import { SECOND_ROW_TABS } from '../containers/Standards/helpers';

const { DATABASE, SUMMARIZATION, LINKED_PRODUCTS, WATCHLIST, AFFECTED_PRODUCTS, ASSESSMENTS } = SECOND_ROW_TABS;

const Actions = {
  getTotal() {
    const legislationStore = store.getState().legislation;

    api.legislation
      .getTotal({
        [`${DATABASE}Filters`]: legislationStore[DATABASE].filters,
        [`${SUMMARIZATION}Filters`]: legislationStore[SUMMARIZATION].filters,
        [`${LINKED_PRODUCTS}Filters`]: legislationStore[LINKED_PRODUCTS].filters,
        [`${AFFECTED_PRODUCTS}Filters`]: legislationStore[AFFECTED_PRODUCTS].filters,
        [`${ASSESSMENTS}Filters`]: legislationStore[ASSESSMENTS].filters,
      })
      .then(data => Actions.change(data));
  },

  getFiltersData() {
    api.legislation
      .getFiltersData()
      .then((data) => Actions.change(data));
  },

  getById(id) {
    return api.legislationAssessment.getById(id).then(({ assessment }) => Actions.change({ item: assessment }));
  },

  setLoading(tabName, isLoading) {
    const tabValues = store.getState().legislation[tabName];

    Actions.change({ [tabName]: { ...tabValues, loading: isLoading } });
  },

  setFilter(tabName, newFilters = {}) {
    const tabValues = store.getState().legislation[tabName];
    const nextFilters = { ...tabValues.filters, ...newFilters };

    Actions.change({ [tabName]: { ...tabValues, filters: nextFilters } });
  },

  fetchData(tabName, { newPagination = {}, newFilters = {}, newSorter } = {}) {
    const { filters, pagination, sorter } = store.getState().legislation[tabName];
    const params = {
      filters: { ...filters, ...newFilters },
      pagination: { ...pagination, ...newPagination },
      sorter: { ...sorter, ...newSorter },
    };

    Actions.setLoading(tabName, true);

    const apiMapper = {
      [DATABASE]: api.legislation.get,
      [SUMMARIZATION]: api.legislation.getSummarized,
      [LINKED_PRODUCTS]: api.legislation.getLinkedProducts,
      [WATCHLIST]: api.legislationWatchlist.get,
      rules: api.legislationRules.get,
      [AFFECTED_PRODUCTS]: api.legislation.getAffectedProducts,
      [ASSESSMENTS]: api.legislationAssessment.getList,
    };
    const request = apiMapper[tabName];

    return request(params)
      .then(({ list = [], filters = {}, pagination = {}, sorter = {} }) => {
        const payload = {
          [tabName]: {
            list,
            filters,
            pagination,
            sorter,
          },
          [`${tabName}Total`]: pagination.total || 0,
        };

        Actions.change(payload);
        Actions.setLoading(tabName, false);

        return list;
      })
      .catch(() => Actions.setLoading(tabName, false));
  },

  // Database
  getDatabase({ newPagination = {} } = {}) {
    Actions.fetchData(DATABASE, { newPagination });
  },

  filterDatabase(newFilters = {}) {
    Actions.setFilter(DATABASE, newFilters);
  },

  setDatabaseLoading(isLoading) {
    Actions.setLoading(DATABASE, isLoading);
  },

  // Summarization
  getSummarization({ newPagination = {} } = {}) {
    Actions.fetchData(SUMMARIZATION, { newPagination });
  },

  filterSummarization(newFilters = {}) {
    Actions.setFilter(SUMMARIZATION, newFilters);
  },

  // Linked Products
  filterLinkedProducts(newFilters = {}) {
    Actions.setFilter(LINKED_PRODUCTS, newFilters);
  },

  getLinkedProducts({ newPagination = {} } = {}) {
    return Actions.fetchData(LINKED_PRODUCTS, { newPagination });
  },

  // Watchlist
  setWatchlistLoading(isLoading) {
    Actions.setLoading(WATCHLIST, isLoading);
  },

  filterWatchlist(newFilters = {}) {
    Actions.setFilter(WATCHLIST, newFilters);
  },

  getWatchlist({ newPagination = {} } = {}) {
    Actions.fetchData(WATCHLIST, { newPagination });
  },

  // Rules
  setRulesLoading(isLoading) {
    Actions.setLoading('rules', isLoading);
  },

  filterRules(newFilters = {}) {
    Actions.setFilter('rules', newFilters);
  },

  getRules({ newPagination = {} } = {}) {
    Actions.fetchData('rules', { newPagination });
  },

  // Affected Products
  setAffectedProductsLoading(isLoading) {
    Actions.setLoading(AFFECTED_PRODUCTS, isLoading);
  },

  filterAffectedProducts(newFilters = {}) {
    Actions.setFilter(AFFECTED_PRODUCTS, newFilters);
  },

  getAffectedProducts({ newPagination = {}, newSorter } = {}) {
    Actions.fetchData(AFFECTED_PRODUCTS, { newPagination, newSorter });
  },

  // Assessments
  filterAssessments(newFilters = {}) {
    Actions.setFilter(ASSESSMENTS, newFilters);
  },

  getAssessments({ newPagination = {}, newSorter } = {}) {
    Actions.fetchData(ASSESSMENTS, { newPagination, newSorter });
  },

  change(payload = {}) {
    store.dispatch({ type: LEGISLATION_CHANGE, payload });
  },
};

export default Actions;
