import { message } from 'antd';
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import Map from './Map';

const Actions = {
  /**
   * Get data
   * @param props
   */
  get(props = {}) {
    const { sorter, filters, pagination, advancedSearchList, advancedSearchValue } = store.getState().applications;

    Actions.change({ loading: true });

    const params = { sorter, filters, pagination, ...props };

    if (advancedSearchValue?.length && advancedSearchList?.length) {
      params.advancedSearchList = advancedSearchList;
      params.advancedSearchValue = advancedSearchValue;
    }

    return api.applications
      .get(params)
      .then(data => {
        Actions.change({ ...data, loading: false });

        const { list } = data;

        return { ...data, list: list.map(({ country }) => ({ wizard: { country } })) };
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get application list
   * @param filters
   */
  getApplicationList(filters) {
    Actions.change({ loading: true });

    api.applications
      .get({ filters, tracking: true })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update ACL by ID
   * @param id
   * @param acl
   */
  updateAcl({ id, acl }) {
    Actions.change({ loading: true });

    return api.applications
      .updateAcl({ id, acl })
      .then(() => {
        Actions.get();
        message.success('Application ACL updated');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update ACL for many
   * @param acl
   * @param revoke
   * @param props
   */
  updateAclForMany({ acl, revoke = false, ...props }) {
    Actions.change({ sharingAll: 0 });

    return api.applications.updateAclForMany({ acl, revoke, ...props });
  },

  /**
   * Remove application
   * @param id
   */
  remove(id) {
    Actions.change({ loading: true });

    return api.applications
      .remove(id)
      .then(() => {
        const { pagination } = store.getState().applications;
        const { current, pageSize = 10, total = 0 } = pagination;
        let newPagination = pagination;

        if (total % pageSize === 1) newPagination = { current: current > 1 ? current - 1 : 1, pageSize };

        Actions.get({ pagination: newPagination });
        message.success('Application removed');
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Clone application
   * @param id
   */
  clone(id) {
    Actions.change({ loading: true });

    return api.applications
      .clone(id)
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get clone list
   * @param id
   */
  getCloneHistory(id) {
    return api.applications.getCloneHistory(id);
  },

  /**
   * Create new application
   * @param data
   */
  add(data) {
    return api.applications.add(data);
  },

  /**
   * Get application by Id
   * @param id
   */
  getById(id) {
    Actions.change({ loading: true });

    return api.applications
      .getById(id)
      .then(({ item }) => {
        const { wizard, opened } = item;

        Actions.change({ item, wizard, opened: [true, false].includes(opened) ? opened : true, loading: false });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Get wizard by Id
   * @param id
   */
  getWizardById(id) {
    Actions.change({ loading: true });

    return api.applications
      .getWizardById(id, { isPreview: true })
      .then(({ wizard }) => Actions.change({ wizard, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update application
   * @param props
   */
  update(props = {}) {
    const { id } = props;

    return api.applications.update(props)
      .then(() => Actions.getById(id))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Update application menu
   * @param props
   */
  updateMenu(props) {
    return api.applications.update(props);
  },

  /**
   * Update application info
   * @param props
   */
  updateInfo(props) {
    return api.applications.updateInfo(props);
  },

  /**
   * Update responder
   * @param props
   */
  updateResponder(props) {
    return api.applications.updateResponder(props);
  },

  /**
   * Update reviewer
   * @param props
   */
  updateReviewer(props) {
    return api.applications.updateReviewer(props);
  },

  /**
   * Update section
   * @param props
   */
  updateSection(props) {
    return api.applications.updateSection(props).then(data => Actions.change({ ...data, saving: null }));
  },

  /**
   * Update question
   * @param props
   */
  updateQuestion(props) {
    return api.applications.updateQuestion(props).then(data => Actions.change({ ...data, saving: null }));
  },

  /**
   * Freeze
   * @param status
   */
  freeze(status) {
    Actions.change({ loading: status });
  },

  /**
   * Filter
   * @param newFilter
   */
  filter(newFilter = {}) {
    const { filters = {}, pagination } = store.getState().applications;
    const nextFilters = { ...filters, ...newFilter };

    if ('countryId' in newFilter && newFilter.countryId === filters.countryId) {
      delete nextFilters.countryId;
    }

    Actions.get({ filters: nextFilters, pagination: { ...pagination, current: 1 } });
  },

  /**
   * Clean all filters
   */
  cleanAllFilters() {
    Actions.get({ filters: {} });
  },

  /**
   * Set filters
   * @param newFilter
   */
  setFilter(newFilters = {}) {
    const { filters = {} } = store.getState().applications;
    const nextFilters = { ...filters, ...newFilters };

    Actions.change({ filters: nextFilters });
  },

  /**
   * Clean filter
   * @param key
   */
  cleanFilter(key) {
    const { filters = {} } = store.getState().applications;

    delete filters[key];

    Actions.get({ filters });
  },

  /**
   * Get data form map
   * @param map
   * @param isDataAlreadyExists
   */
  getMap({ map, ...props }, isDataAlreadyExists = {}) {
    const { filters } = store.getState().applications;

    Map.change({ loading: true }, map);

    if (isDataAlreadyExists.list) {
      const { list: data, filters: nextFilters } = isDataAlreadyExists;

      return Map.change({ data, filters: nextFilters, loading: false }, map);
    }

    return api.applications
      .get({ filters, ...props, map: true })
      .then(({ list, ...payload }) => Map.change({ data: list, ...payload, loading: false }, map))
      .catch(() => Map.change({ loading: false }, map));
  },

  /**
   * Add comment
   * @param message
   * @param section
   * @param mentionedUsers
   */
  addComment({ message: newMessage, section, mentionedUsers }) {
    const { item } = store.getState().applications;
    const { _id } = item;

    if (!item.comments) item.comments = [{ section, list: [] }];

    return api.applications
      .addComment({ id: _id, body: { section, message: newMessage, mentionedUsers } })
      .then(({ comment }) => {
        const index = item.comments.findIndex(({ section: sectionId }) => sectionId === section);

        if (index !== -1) item.comments[index].list.push(comment);
        else item.comments.push({ section, list: [comment] });

        Actions.change({ item: { ...item, comments: item.comments } });
      });
  },

  /**
   * Resolve comment
   * @param commentId
   * @param section
   */
  resolveComment({ commentId, section }) {
    const { item } = store.getState().applications;

    return api.comments.resolve(commentId).then(({ comment }) => {
      const sIndex = item.comments.findIndex(c => c.section === section);
      const cIndex = item.comments[sIndex].list.findIndex(({ _id }) => _id === commentId);

      item.comments[sIndex].list.splice(cIndex, 1, comment);
      Actions.change({ item: { ...item, comments: item.comments } });
    });
  },

  /**
   * Get all unique application wizards: applicationType, productType and classification
   */
  getWizards() {
    api.applications.getWizards().then(({ data }) => data.length && Actions.change({ wizards: data[0] }));
  },

  /**
   * Approve
   * @param props
   */
  approve(props) {
    Actions.change({ loading: true });

    return api.applications.approve(props).catch(() => Actions.change({ loading: false }));
  },

  /**
   * Clean
   */
  clean() {
    Actions.change({
      list: [],
      sorter: {},
      filters: {},
      paginations: {},
      advancedSearchList: [],
      advancedSearchValue: '',
    });
  },

  /**
   * Change autofill progress
   * @param payload
   */
  autofillProgress(payload = {}) {
    store.dispatch({ type: constants.APPLICATIONS_AUTOFILL_PROGRESS, payload });
  },

  /**
   * Change autofill status
   * @param payload
   */
  autofillComplete(payload = {}) {
    store.dispatch({ type: constants.APPLICATIONS_AUTOFILL_COMPLETE, payload });
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}) {
    store.dispatch({ type: constants.APPLICATIONS_CHANGE, payload });
  },
};

export default Actions;
