import auth from './Auth';
import session from './Session';
import profile from './Profile';
import applications from './Applications';
import checklists from './Checklists';
import map from './Map';
import modal from './Modal';
import team from './Team';
import role from './Role';
import notifications from './Notifications';
import banners from './Banners';
import tracking from './Tracking';
import wizards from './Wizards';
import productCode from './ProductCode';
import products from './Products';
import documents from './Documents';
import productFamilies from './ProductFamilies';
import productSolutions from './ProductSolutions';
import sku from './Sku';
import skuContact from './SkuContact';
import doc from './Doc';
import nav from './Nav';
import chat from './Chat';
import access from './Access';
import tags from './Tags';
import alerts from './Alerts';
import standards from './Standards';
import countrySet from './CountrySet';
import productsBausch from './ProductsBausch';
import logs from './Logs';
import exportFeature from './Export';
import tasks from './Tasks';
import dashboard from './Dashboard';
import ccpProjects from './CCPProjects';
import ccpTasks from './CCPTasks';
import regPlan from './RegPlan';
import pharmaAlerts from './PharmaAlerts';
import dms from './DMS';
import standardsWorkflow from './StandardsWorkflow';
import projects from './Projects';
import task from './Task';
import forms from './Forms';
import mdr from './Mdr';
import phr from './Phr';
import esign from './ESign';
import dropDown from './DropDown';
import legislation from './Legislation';
import guidance from './Guidance';
import twoStep from './TwoStep';

export default {
  auth,
  twoStep,
  session,
  profile,
  access,
  map,
  sku,
  nav,
  doc,
  chat,
  modal,
  applications,
  checklists,
  team,
  role,
  wizards,
  tracking,
  notifications,
  banners,
  productCode,
  products,
  documents,
  productFamilies,
  productSolutions,
  projects,
  skuContact,
  tags,
  alerts,
  standards,
  countrySet,
  productsBausch,
  logs,
  exportFeature,
  dashboard,
  tasks,
  ccpProjects,
  ccpTasks,
  regPlan,
  pharmaAlerts,
  dms,
  standardsWorkflow,
  task,
  forms,
  mdr,
  phr,
  esign,
  dropDown,
  legislation,
  guidance,
};
