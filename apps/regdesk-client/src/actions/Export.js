import { message } from 'antd'
import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

const Actions = {
   export(body) {
    Actions.change({ loading: true });
     return api.export
      .generateFile(body)
      .then(({ msg }) => {
        if (msg) {
          message.success(msg)
        }
        Actions.change({ loading: false })
      })
      .catch(() => Actions.change({ loading: false }))
  },

  exportEStar(body) {
    Actions.change({ loading: true });
     return api.applications
      .exportEStar(body)
      .then(({ msg }) => {
        if (msg) {
          message.success(msg)
        }
        Actions.change({ loading: false })
      })
      .catch(() => Actions.change({ loading: false }))
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}){
    store.dispatch({ type: constants.EXPORT_CHANGE, payload })
  },
};

export default Actions
