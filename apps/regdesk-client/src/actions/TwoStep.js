import api from '../utils/api';
import * as constants from '../const';
import store from '../stores';

const Actions = {
  /**
   * Get session2FA for current user
   * @returns {Function}
   */
  get() {
    Actions.change({ loading2FA: true });

    api.twoStep
      .get()
      .then((data) => Actions.change({ ...data, loading2FA: false }))
      .catch(() => Actions.change({ loading2FA: false }));
  },

  /**
   * Get session2FA by id
   * @returns {Function}
   */
  getById(id) {
    Actions.change({ loading2FA: true });

    api.twoStep
      .getById(id)
      .then((data) => Actions.change({ ...data, loading2FA: false }))
      .catch(() => Actions.change({ loading2FA: false }));
  },

  /**
   * Resend SMS code
   * @param value
   */
  resend(value) {
    Actions.change({ loading2FA: true });

    return api.twoStep
      .resend(value)
      .then((data = {}) => {
        Actions.change({ ...data, loading2FA: false });

        return data.session2FA || {};
      })
      .catch((error) => {
        Actions.change({ loading2FA: false });

        return error;
      });
  },

  /**
   * Verify code
   * @param value
   */
  verify(value) {
    Actions.change({ loading2FA: true });

    return api.twoStep
      .verify(value)
      .then((data) => {
        Actions.change({ ...data, loading2FA: false });

        return data;
      });
  },

  /**
   * Enable 2FA
   * @param value
   */
  enable(value) {
    Actions.change({ loading2FA: true });

    return api.twoStep
      .enable(value)
      .then((data) => Actions.change({ ...data, loading2FA: false }))
      .catch(() => Actions.change({ loading2FA: false }));
  },

  /**
   * Disable 2FA
   * @param value
   */
  disable(value) {
    Actions.change({ loading2FA: true });

    return api.twoStep
      .disable(value)
      .then((data) => Actions.change({ ...data, loading2FA: false }));
  },

  /**
   * Change account reducers
   * @param payload
   */
  change(payload) {
    store.dispatch({ type: constants.ACCOUNT_CHANGE, payload });
  },
};

export default Actions;
