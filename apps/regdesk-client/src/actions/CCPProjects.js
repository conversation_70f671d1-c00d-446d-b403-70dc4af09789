import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';

let timerId = null;

const Actions = {
  /**
   * Get data
   * @returns {Function}
   */
  get(props = {}) {
    const { sorter, filters, pagination } = store.getState().ccpProjects;

    Actions.change({ loading: true });

    return api.ccpProjects
      .get({ sorter, filters, pagination, ...props })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Advanced Search
   * @returns {Function}
   */
  advancedSearch(pagination = {}) {
    const { advancedSearchList, advancedSearchValue, advancedSearchEnabled } = store.getState().ccpProjects;

    if (!advancedSearchEnabled) {
      pagination = {};
    }

    Actions.change({ loading: true, advancedSearchEnabled: true, searchValue: '' });

    api.ccpProjects
      .advancedSearch({ advancedSearchList, advancedSearchValue, pagination })
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Search
   * @param value
   */
  search(value) {
    const { filters, pagination, advancedSearchTasks } = store.getState().ccpProjects;
    const newFilters = { ...filters, name: value };
    const params = { pagination: { ...pagination, current: 1 } };
    if (!value) {
      delete newFilters.name;
    }

    if (advancedSearchTasks) {
      params.advancedSearchTasks = advancedSearchTasks;
    }

    Actions.change({ filters: newFilters });
    clearTimeout(timerId);

    timerId = setTimeout(() => Actions.get(params), 500);
  },

  /**
   * Add
   * @param data
   */
  add(data) {
    return api.ccpProjects.add(data);
  },

  /**
   * Add tasks from report
   * @param data
   */
  addTasks(data) {
    return api.ccpProjects.addTasks(data);
  },

  /**
   * Update
   * @param data
   */
  update(data) {
    return api.ccpProjects.update(data);
  },

  /**
   * Approve
   * @param data
   */
  approve(data) {
    Actions.change({ loading: true });

    return api.ccpProjects
      .approve(data)
      .finally(() => Actions.change({ loading: false }));
  },

  /**
   * unApprove
   * @param data
   */
  unApprove(data) {
    return api.ccpProjects.unApprove(data);
  },

  /**
   * Remove
   * @param id
   */
  remove(id) {
    return api.ccpProjects.remove(id);
  },

  /**
   * Update Acl For Many
   * @param acl
   * @param revoke
   * @param props
   * @returns {*}
   */
  updateAclForMany({ acl, revoke = false, ...props }) {
    Actions.change({ sharingAll: 0 });

    return api.ccpProjects.updateAclForMany({ acl, revoke, ...props });
  },

  /**
   * Close all modals
   */
  closeModal() {
    Actions.change({ showModal: '', item: {}, modalLoading: false, projectId: '' });
  },

  /**
   * Analysis
   * @param id
   */
  analysis(id) {
    Actions.change({ loading: true });

    api.ccpReport
      .analysisSKU(id)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Product Name Change
   * @param product
   */
  productAnalysis(type, product) {
    Actions.change({ loading: true });

    api.ccpReport
      .analysisProduct(type, product)
      .then(data => Actions.change({ ...data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  /**
   * Change store
   * @param payload
   */
  change(payload = {}){
    store.dispatch({ type: constants.CCP_PROJECTS_CHANGE, payload });
  },
};

export default Actions;

