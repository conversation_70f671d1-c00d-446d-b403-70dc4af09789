import * as constants from '../const';
import api from '../utils/api';
import store from '../stores';
import Map from './Map';
import { SECOND_ROW_TABS } from '../containers/Standards/helpers';

const { DATABASE, SUMMARIZATION, LINKED_PRODUCTS, WATCHLIST, AFFECTED_PRODUCTS, ASSESSMENTS } = SECOND_ROW_TABS;

const Actions = {
  addApplyLog(body) {
    return api.standards.addApplyLog(body);
  },

  getMap({ map, isGuidance }) {
    Map.change({ loading: true }, map);

    api.standards
      .getAffectedProductsMap({ isGuidance })
      .then(({ list }) => Map.change({ data: list, loading: false }, map))
      .catch(() => Map.change({ loading: false }, map));
  },

  getById(id) {
    Actions.change({ loading: true });

    return api.standards
      .getById(id)
      .then(({ data }) => Actions.change({ item: data, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  getNotes(standardId) {
    Actions.change({ loading: true });

    return api.standards
      .getNotes(standardId)
      .then(({ notes }) => Actions.change({ notes, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  addNote(data) {
    return api.standards.addNote(data);
  },

  removeNote({ noteId, guidance }) {
    Actions.change({ loading: true });

    return api.standards
      .removeNote({ noteId, guidance })
      .finally(() => Actions.change({ loading: false }));
  },

  closeModal() {
    Actions.change({ showModal: '' });
  },

  updateNote(noteId, data) {
    Actions.change({ loading: true });

    return api.standards
      .updateNote(noteId, data)
      .finally(() => Actions.change({ loading: false }));
  },

  getUsers() {
    Actions.change({ loading: true });

    return api.standards
      .getUsers()
      .then(({ users }) => Actions.change({ loading: false, users }))
      .catch(() => Actions.change({ loading: false }));
  },

  addComment(id, body) {
    Actions.change({ loading: true });

    return api.standards
      .addComment(id, body)
      .finally(() => Actions.change({ loading: false }));
  },

  getLog(standardId) {
    return api.standards
      .getLogById(standardId)
      .then(({ standardLogList }) => Actions.change({ standardLogList }));
  },

  addOrUpdateLog(data) {
    return api.standards.addOrUpdateLog(data);
  },

  getTags(standardId) {
    Actions.change({ loading: true });

    return api.standards
      .getTags(standardId)
      .then(({ standardTagsList }) => Actions.change({ standardTagsList, loading: false }))
      .catch(() => Actions.change({ loading: false }));
  },

  addTags(data) {
    return api.standards.addTags(data);
  },

  updateTag(tagId, data) {
    Actions.change({ loading: true });

    return api.standards
      .updateTag(tagId, data)
      .finally(() => Actions.change({ loading: false }));
  },

  getStandardsNews(props = {}) {
    const { filtersNews } = store.getState().standards;

    Actions.change({ loading: true });

    api.standards
      .loadNews({ filtersNews, ...props })
      .then((data) => {
        Actions.change({
          standardsNewsList: data.news,
          standardsNewsOrganizationsList: data.organizations,
          totalNews: data.total,
          filtersNews,
          loading: false
        });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  linkProduct(data) {
    return api.standards.linkProduct(data);
  },

  removeLinkProduct({ id, isGuidance }) {
    return api.standards.removeLinkProduct({ id, isGuidance });
  },

  /**
   * Update DMS documents
   * @returns {Function}
   */
  updateDocuments(data) {
    return api.standards.updateDocuments(data);
  },

  addCompliance(data) {
    return api.standards.addCompliance(data);
  },

  getWorkflowById(id) {
    Actions.change({ loading: true });

    api.standards
      .getWorkflowById(id)
      .then((data) => {
        Actions.change({
          workflowItem: data.item,
          loading: false,
        });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  updateWorkflow(id, body) {
    Actions.change({ loading: true });

    return api.standards.updateWorkflow(id, body)
      .then((data) => {
        Actions.change({
          workflowItem: data.item,
          loading: false,
        });
      })
      .catch(() => Actions.change({ loading: false }));
  },

  ignore(id) {
    Actions.change({ loading: true });

    return api.standardsWorkflow.ignoreWorkflow(id)
      .then(() => Actions.change({ loading: false, }))
      .catch(() => Actions.change({ loading: false }));
  },

  initiate(data) {
    Actions.change({ loading: true });

    return api.standards.initiateStandard(data)
      .then(() => Actions.change({ loading: false, }))
      .catch(() => Actions.change({ loading: false }));
  },

  addType(data) {
    return api.standards.addType(data);
  },

  getTotal() {
    const standardsStore = store.getState().standards;

    api.standards
      .getTotal({
        isGuidance: false,
        [`${DATABASE}Filters`]: standardsStore[DATABASE].filters,
        [`${SUMMARIZATION}Filters`]: standardsStore[SUMMARIZATION].filters,
        [`${LINKED_PRODUCTS}Filters`]: standardsStore[LINKED_PRODUCTS].filters,
        [`${AFFECTED_PRODUCTS}Filters`]: standardsStore[AFFECTED_PRODUCTS].filters,
        [`${ASSESSMENTS}Filters`]: standardsStore[ASSESSMENTS].filters,
      })
      .then(data => Actions.change(data));
  },

  getFiltersData() {
    api.standards
      .getFiltersData({ isGuidance: false })
      .then((data) => Actions.change(data));
  },

  setLoading(tabName, isLoading) {
    const tabValues = store.getState().standards[tabName];

    Actions.change({ [tabName]: { ...tabValues, loading: isLoading } });
  },

  setFilter(tabName, newFilters = {}) {
    const tabValues = store.getState().standards[tabName];
    const nextFilters = { ...tabValues.filters, ...newFilters };

    Actions.change({ [tabName]: { ...tabValues, filters: nextFilters } });
  },

  fetchData(tabName, { newPagination = {}, newFilters = {}, newSorter } = {}) {
    const { filters, pagination, sorter } = store.getState().standards[tabName];
    const params = {
      filters: { ...filters, ...newFilters },
      pagination: { ...pagination, ...newPagination },
      sorter: { ...sorter, ...newSorter },
    };

    Actions.setLoading(tabName, true);

    const apiMapper = {
      [DATABASE]: api.standards.get,
      [SUMMARIZATION]: api.standards.getSummarized,
      [LINKED_PRODUCTS]: api.standards.getByProduct,
      [WATCHLIST]: api.watchlists.get,
      rules: api.standardRules.get,
      [AFFECTED_PRODUCTS]: api.standards.getAffectedProducts,
      [ASSESSMENTS]: api.standardsWorkflow.getWorkflowList,
    };
    const request = apiMapper[tabName];

    return request(params)
      .then(({ list = [], filters = {}, pagination = {}, sorter = {} }) => {
        const payload = {
          [tabName]: {
            list,
            filters,
            pagination,
            sorter,
          },
          [`${tabName}Total`]: pagination.total || 0,
        };

        Actions.change(payload);
        Actions.setLoading(tabName, false);

        return list;
      })
      .catch(() => Actions.setLoading(tabName, false));
  },

  // Database
  getDatabase({ newPagination = {} } = {}) {
    Actions.fetchData(DATABASE, { newPagination });
  },

  filterDatabase(newFilters = {}) {
    Actions.setFilter(DATABASE, newFilters);
  },

  setDatabaseLoading(isLoading) {
    Actions.setLoading(DATABASE, isLoading);
  },

  // Summarization
  getSummarization({ newPagination = {} } = {}) {
    Actions.fetchData(SUMMARIZATION, { newPagination });
  },

  filterSummarization(newFilters = {}) {
    Actions.setFilter(SUMMARIZATION, newFilters);
  },

  // Linked Products
  filterLinkedProducts(newFilters = {}) {
    Actions.setFilter(LINKED_PRODUCTS, newFilters);
  },

  getLinkedProducts({ newPagination = {} } = {}) {
    return Actions.fetchData(LINKED_PRODUCTS, { newPagination });
  },

  // Watchlist
  setWatchlistLoading(isLoading) {
    Actions.setLoading(WATCHLIST, isLoading);
  },

  filterWatchlist(newFilters = {}) {
    Actions.setFilter(WATCHLIST, newFilters);
  },

  getWatchlist({ newPagination = {} } = {}) {
    Actions.fetchData(WATCHLIST, { newPagination });
  },

  // Rules
  setRulesLoading(isLoading) {
    Actions.setLoading('rules', isLoading);
  },

  filterRules(newFilters = {}) {
    Actions.setFilter('rules', newFilters);
  },

  getRules({ newPagination = {} } = {}) {
    Actions.fetchData('rules', { newPagination });
  },

  // Affected Products
  setAffectedProductsLoading(isLoading) {
    Actions.setLoading(AFFECTED_PRODUCTS, isLoading);
  },

  filterAffectedProducts(newFilters = {}) {
    Actions.setFilter(AFFECTED_PRODUCTS, newFilters);
  },

  getAffectedProducts({ newPagination = {}, newSorter } = {}) {
    Actions.fetchData(AFFECTED_PRODUCTS, { newPagination, newSorter });
  },

  // Assessments
  filterAssessments(newFilters = {}) {
    Actions.setFilter(ASSESSMENTS, newFilters);
  },

  getAssessments({ newPagination = {}, newSorter } = {}) {
    Actions.fetchData(ASSESSMENTS, { newPagination, newSorter });
  },

  change(payload = {}) {
    store.dispatch({ type: constants.STANDARDS_CHANGE, payload });
  },
};

export default Actions;
