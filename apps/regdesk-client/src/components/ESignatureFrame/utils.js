const transformDocType = (docType) => {
    const words = docType.match(/[A-Z]?[a-z]+/g);
    let capitalizedWords = words.map(word => word.charAt(0).toUpperCase() + word.slice(1));
    capitalizedWords[0] = ['Gspr', 'Doc'].includes(capitalizedWords[0]) ? capitalizedWords[0].toUpperCase() : capitalizedWords[0];
    if ('GSPR' === capitalizedWords[0]) {
      capitalizedWords[0] = 'GSPR/ER';
    }
    if ('DOC' === capitalizedWords[0]) {
      capitalizedWords[0] = 'DoC';
    }
    const upperCaseDocType = capitalizedWords.join(' ');
    return upperCaseDocType;
  }

export default {
    transformDocType
}