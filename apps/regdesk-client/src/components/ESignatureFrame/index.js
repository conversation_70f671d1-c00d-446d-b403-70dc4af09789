import React from 'react';
import { Form, Select, Input, Button, Modal } from 'antd';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import signHere from '../../assets/sign_here.png';
import ESignatureConfirmPassword from '../ESignatureConfirmPassword';
import ESignatureExport from '../ESignatureExport';
import ESignature from '../ESignature';
import ESignUtil from './utils';
import utils from '../../utils';
import actions from '../../actions';
import styles from './index.less';

const FormItem = Form.Item;
const { Option } = Select;

@connect(({ account }) => ({
  fullName: account.name,
  email: account.email,
  avatar: account.avatar,
  accountId: account._id,
  isSSO: account.isSSO,
  ssoProvider: account.ssoProvider,
}))

@Form.create()
class ESignatureFrame extends React.Component {
  constructor(props) {
    super(props);

    const { t } = props;

    this.t = t;

    this.state = {
      signatureSample: signHere,
      signature: '',
      showCustomReason: false,
      showCustomReasonEditButton: false,
      reasons: ['I approve this document', 'I have reviewed this document', 'I am the author of this document'],
      customReasons: [],
      reason: '',
      customReason: '',
      oldCustomReason: '',
      showExportModal: false,
      showESignModal: false,
      customReasonAddDisable: true
    };
  }

  handleReasonChange = (value) => {
    const { reasons, customReasons } = this.state;

    if (value === 'Custom Reason') {
      this.setState({ showCustomReason: true, showCustomReasonEditButton: false });
    } else if (customReasons.length > 0 && customReasons.includes(value)) {
      // remove value of reasons
      this.setState({ oldCustomReason: value, reasons, customReason: value, showCustomReason: true, showCustomReasonEditButton: true });
    } else {
      this.setState({ reason: value, showCustomReason: false, showCustomReasonEditButton: false });
    }
  }

  handleAddReason = () => {
    const { form } = this.props;
    const customReason = form.getFieldValue('customReason');
    const { reasons, customReasons, oldCustomReason } = this.state;

    reasons.push(customReason);
    customReasons.push(customReason);
    form.setFieldsValue({ customReason: '', reason: customReason });

    // if oldCustomReason has value and included in reasons and customResons, remove it
    if (oldCustomReason && reasons.includes(oldCustomReason)) {
      const index = reasons.indexOf(oldCustomReason);

      reasons.splice(index, 1);
    }

    if (oldCustomReason && customReasons.includes(oldCustomReason)) {
      const index = customReasons.indexOf(oldCustomReason);

      customReasons.splice(index, 1);
    }

    this.setState({ reason: customReason, reasons, customReasons, showCustomReason: false, customReason: '', oldCustomReason: '', showCustomReasonEditButton: true });
  };

  handleEditReason = () => {
    const { form } = this.props;
    const { customReasons, reasons } = this.state;
    const reason = form.getFieldValue('reason');

    // remove reason from reasons and customReasons
    if (reasons.includes(reason)) {
      const index = reasons.indexOf(reason);

      reasons.splice(index, 1);
    }

    if (customReasons.includes(reason)) {
      const index = customReasons.indexOf(reason);

      customReasons.splice(index, 1);
    }

    this.setState({ reasons, customReasons, showCustomReason: true, customReason: reason, showCustomReasonEditButton: false });
  }

  onSignClick() {
    const { fullName } = this.state;

    this.formDocument.showModal(fullName);
  }

  signChanged = (item) => {
    const { form } = this.props;

    if (!item) {
      this.formDocument.closeModal();

      return;
    }

    this.setState({ signature: item });
    form.setFieldsValue({ signature: item });
    form.validateFields(['signature'], { force: true });

    actions.esign.change({ signature: item });
    this.formDocument.closeModal();
  };

  handleApplyAndSign = () => {
    const { form } = this.props;
    const { reason, signature } = this.state;

    this.setState({ loading: true });

    form.validateFields(async(err) => {
      if (!err) {
        const { id, fullName, email, accountId, avatar, isApproveChecklist, docType, docName, productName, productId, productFamilyName, productFamilyId, parentId, country, sku, onClose, onCloseModal } = this.props;
        const body = {
          documentId: id,
          docType,
          docName,
          productName,
          productId,
          productFamilyName,
          productFamilyId,
          parentId,
          sku,
          reason,
          fullName,
          email,
          signature,
          country,
          isApproveChecklist,
          accountId,
          avatar,
        };

        await utils.api.esign
          .agreeAndSign(body)
          .then(async(res) => {
            this.setState({
              ip: res.ip,
              signId: res.id,
              showExportModal: true,
              loading: false
            });
          })
          .catch(() => {
            this.setState({ loading: false });

            if (typeof onCloseModal === 'function') onCloseModal();
            else onClose();
          });
      }
    });
  }

  validateForm = () => {
    const { form } = this.props;

    form.validateFields((err) => {
      if (!err) return true;
    });
  }

  render() {
    const {
      isSSO,
      ssoProvider,
      form,
      fullName,
      email,
      accountId,
      avatar,
      showModal,
      docType,
      docName,
      productName,
      productId,
      productFamilyName,
      productFamilyId,
      sku,
      country,
      loading,
      onClose,
      onCloseModal,
      showPasswordModal = true,
      onPasswordModalClose
    } = this.props;
    const { getFieldDecorator } = form;

    const {
      customReasonAddDisable,
      signature,
      signatureSample,
      customReason,
      reasons,
      reason,
      signId,
      ip,
      showCustomReason,
      showExportModal,
      showCustomReasonEditButton,
      showESignModal,
      loading: stateLoading
    } = this.state;

    const onCloseEsignModal = () => {
      this.setState({
        showESignModal: false,
        reason: '',
        customReason: '',
        signature: '',
        customReasons: []
      });
    };

    const onCloseEsignExportModal = () => {
      this.setState({ showExportModal: false });
      onCloseEsignModal();

      if (typeof onCloseModal === 'function') onCloseModal();
      else onClose();
    };

    const handleOpenESign = () => this.setState({ showESignModal: true });

    const upperCaseDocType = ESignUtil.transformDocType(docType);

    return (
      <div>
        <Modal
          title=''
          visible={showESignModal}
          footer={null}
          centered
          destroyOnClose
          maskClosable={false}
          onCancel={onCloseEsignModal}
          onClose={onCloseEsignModal}
          width={850}
          height={665}
          zIndex={1001}
        >

          <div className={styles.container}>
            <div className={styles.header}>
              <div className={styles.logo} />
              RegDesk eSignature
            </div>

            <div className={styles.content}>
              <div className={styles.subheader}>RegDesk eSignature</div>
              By signing below you approve of any document changes provided
            </div>

            <div className={styles.liner} />

            <div className={styles.content}>
              Document Type: <span className={styles.bold}>{upperCaseDocType}</span>
            </div>

            <div className={styles.content}>
              Name/ID: <span className={styles.bold}>{docName}</span>
            </div>

            {productName && (
              <div className={styles.content}>
                {this.t('Glossary.Product_one')}: <span className={styles.bold}>{productName}</span>
              </div>
            )}

            {productFamilyName && (
              <div className={styles.content}>
                {this.t('Glossary.ProductFamily_one')}: <span className={styles.bold}>{productFamilyName}</span>
              </div>
            )}

            {sku && (
              <div className={styles.content}>
                {this.t('Glossary.SKU_one')}: <span className={styles.bold}>{sku}</span>
              </div>
            )}

            <div className={styles.grayline} />

            <div className={styles.content}>
              Full name: <span className={styles.bold}>{fullName}</span>
            </div>

            <div className={styles.content}>
              Email address: <span className={styles.bold}>{email}</span>
            </div>

            <Form className={styles.form}>
              <FormItem label='' className={styles.content}>
                <div>
                  <div>
                    {getFieldDecorator('reason', {
                      rules: [
                        {
                          required: true,
                          message: 'Please select reason!',
                        },
                      ],
                    })(
                      <Select
                        required
                        showSearch
                        placeholder='Reason'
                        style={{ width: 300 }}
                        optionFilterProp='children'
                        className={styles.select}
                        onChange={this.handleReasonChange}
                      >
                        {reasons.map(r => <Option key={r} value={r}>{r}</Option>)}
                        <Option value='Custom Reason'>Custom Reason</Option>
                      </Select>
                    )}

                    {showCustomReasonEditButton && <Button type='default' style={{ marginLeft: 10 }} onClick={this.handleEditReason}>Edit</Button>}
                  </div>
                </div>
              </FormItem>

              {showCustomReason && (
                <FormItem label='' className={styles.content}>
                  <div>
                    <div>
                      {getFieldDecorator('customReason', {
                        rules: [
                          {
                            validator: (rule, value, callback) => {
                              if (value && value.length > 50) {
                                this.setState({ customReasonAddDisable: true });
                                callback('Reason should not exceed 50 characters');
                              } else {
                                this.setState({ customReasonAddDisable: false });
                                callback();
                              }
                            },
                          },
                        ],
                        initialValue: customReason,
                      })(
                        <Input
                          required
                          disabled={loading}
                          autoComplete='off'
                          style={{ width: 300 }}
                          placeholder='Type your reason'
                        />
                      )}

                      <Button
                        type='default'
                        style={{ marginLeft: 10 }}
                        disabled={customReasonAddDisable}
                        onClick={this.handleAddReason}
                      >
                        Add
                      </Button>
                    </div>

                    <div>You can write up to 50 characters for a custom reason</div>
                  </div>
                </FormItem>
              )}

              <FormItem label='' className={styles.content}>
                {getFieldDecorator('signature', {
                  rules: [
                    {
                      required: true,
                      message: 'PLease sign here',
                    },
                  ],
                  initialValue: signature,
                })(
                  !signature && <img className={styles.sign} src={signatureSample} onClick={() => this.onSignClick({ activeKey: '0' })} alt='esignature' />
                  || signature && <img className={styles.sign} src={signature} onClick={() => this.onSignClick({ activeKey: '0' })} alt='esignature' />
                )}
              </FormItem>
            </Form>

            <div className={styles.liner} />

            <div className={styles.footer}>
              <div className={styles.left}>
                By signing below, I acknowledge my electronic user ID and password combination act as an electronic signature and my electronic signature is the legally binding equivalent of my handwritten signature.
              </div>

              <Button
                size='large'
                type='primary'
                loading={stateLoading}
                disabled={stateLoading || !signature || !reason || !fullName || !email}
                onClick={this.handleApplyAndSign}
              >
                Agree & Sign
              </Button>
            </div>
          </div>

          <ESignature fullName={fullName} ref={ref => { this.formDocument = ref; }} onSign={this.signChanged} />
        </Modal>

        <ESignatureExport
          showModal={showExportModal}
          docName={docName}
          docType={docType}
          productName={productName}
          sku={sku}
          reason={reason}
          signature={signature}
          signId={signId}
          ip={ip}
          productId={productId}
          productFamilyName={productFamilyName}
          productFamilyId={productFamilyId}
          country={country}
          onClose={onCloseEsignExportModal}
          name={fullName}
          email={email}
          accountId={accountId}
          avatar={avatar}
          isSSO={isSSO}
        />

        {showPasswordModal && (
          <ESignatureConfirmPassword
            isSSO={isSSO}
            ssoProvider={ssoProvider}
            showModal={showModal}
            onOpenESign={handleOpenESign}
            onClose={() => {
              if (typeof onPasswordModalClose === 'function') {
                onPasswordModalClose();
              }
            }}
          />
        )}
      </div>
    );
  }
}

export default withTranslation()(ESignatureFrame);
