import React, { Component } from 'react';
import moment from 'moment-timezone';
import { DatePicker } from 'antd';

class FiscalYearPicker extends Component {
  pickerRef = React.createRef();

  componentDidMount() {
    this.attachInputEvents();
  }

  componentWillUnmount() {
    this.detachInputEvents();
  }

  attachInputEvents = () => {
    const input = this.pickerRef.current?.input;

    if (input) {
      input.addEventListener('blur', this.handleInputBlur);
      input.addEventListener('keydown', this.handleInputKeyDown);
    }
  };

  detachInputEvents = () => {
    const input = this.pickerRef.current?.input;

    if (input) {
      input.removeEventListener('blur', this.handleInputBlur);
      input.removeEventListener('keydown', this.handleInputKeyDown);
    }
  };

  handleInputBlur = (e) => {
    this.validateAndSetYear(e.target.value, 'blur');
  };

  handleInputKeyDown = (e) => {
    if (e.key === 'Enter') {
      this.validateAndSetYear(e.target.value, 'enter');
    }
  };

  validateAndSetYear = (valueStr, eventType) => {
    const { onChange, value } = this.props;
    const isValid = /^\d{4}$/.test(valueStr);
    
    if (isValid) {
      const yearValue = moment.utc([parseInt(valueStr, 10), 0, 1]);

      onChange(yearValue);
    } else {
      const shouldSetCurrentYear = eventType === 'enter' || (eventType === 'blur' && value);

      if (shouldSetCurrentYear) {
        onChange(moment.utc().startOf('year'));
      } else if (eventType === 'blur') {
        onChange(null);
      }
    }
  };

  render() {
    const { value, placeholder, format, open, ...rest } = this.props;

    return (
      <DatePicker
        ref={this.pickerRef}
        dropdownClassName='manual-ant-calendar-picker-container'
        value={value ? moment.utc(value) : null}
        format={format || 'YYYY'}
        placeholder={open ? 'YYYY' : (placeholder || 'Select fiscal year')}
        mode='year'
        {...rest}
      />
    );
  }
}

export default FiscalYearPicker;