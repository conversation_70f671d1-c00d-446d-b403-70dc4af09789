import React, { Component } from 'react';
import moment from 'moment-timezone';
import { DatePicker as AntDatePicker } from 'antd';
import { FULL_DATE_FORMAT } from '../../utils/date';

const modifyDate = value => {
  if (!value) return '';

  return moment.utc([value.year(), value.month(), value.date()]);
};

class DatePicker extends Component {
  inputRef = React.createRef();

  state = { isOpen: false };

  componentDidMount() {
    this.attachInputEvents();
  }

  componentWillUnmount() {
    this.detachInputEvents();
  }

  handleInputBlur = (e) => {
    const valueStr = e.target.value;

    this.validateAndSetDate(valueStr, 'blur');
  };

  handleInputKeyDown = (e) => {
    if (e.key === 'Enter') {
      const valueStr = e.target.value;

      this.validateAndSetDate(valueStr, 'enter');
    }
  };

  validateAndSetDate = (valueStr, eventType) => {
    const { onChange, value } = this.props;

    if (!onChange) return;

    const parsedDate = moment.utc(valueStr, FULL_DATE_FORMAT, true);
    const isValid = parsedDate.isValid();

    if (isValid) {
      onChange(modifyDate(parsedDate));
    } else {
      const shouldSetToday = (eventType === 'enter') || (eventType === 'blur' && value);

      if (shouldSetToday) {
        onChange(moment.utc().startOf('day'));
      }
    }
  };

  changeHandler = date => {
    const { onChange } = this.props;

    return onChange && onChange(modifyDate(date));
  }
  
  handleOpenChange = open => {
    this.setState({ isOpen: open });
  };

  attachInputEvents() {
    const input = this.inputRef.current?.input;

    if (input) {
      input.addEventListener('blur', this.handleInputBlur);
      input.addEventListener('keydown', this.handleInputKeyDown);
    }
  }

  detachInputEvents() {
    const input = this.inputRef.current?.input;

    if (input) {
      input.removeEventListener('blur', this.handleInputBlur);
      input.removeEventListener('keydown', this.handleInputKeyDown);
    }
  }

  render() {
    const { disabled, value, style, placeholder, disabledDate, allowClear, ...rest } = this.props;
    const { isOpen } = this.state;

    return (
      <AntDatePicker
        ref={this.inputRef}
        dropdownClassName='manual-ant-calendar-picker-container'
        style={style || {}}
        format={FULL_DATE_FORMAT}
        value={value}
        onChange={this.changeHandler}
        disabled={disabled}
        disabledDate={disabledDate}
        allowClear={allowClear || true}
        placeholder={isOpen ? 'DD MMM YYYY' : (placeholder || 'Select date')}
        onOpenChange={this.handleOpenChange}
        {...rest}
      />
    );
  }
}

export default DatePicker;