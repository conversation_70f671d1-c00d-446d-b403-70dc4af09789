import React, { PureComponent } from 'react';
import {Select} from 'antd';
import { allCountriesWithEU } from '../../../utils/countries';

export default class MapSearch extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      countries: props.searchCountries || allCountriesWithEU.map(({ alpha3code }) => alpha3code)
    }
  }

  componentDidMount() {
    if (this.props.setCountries) {
      this.props.setCountries(this.setCountries.bind(this))
    }
  }

  setCountries = (countries) => this.setState({ countries })

  render() {
    const { countries } = this.state

    return (
      <div>
        <Select
          style={{ width: 300 }}
          showSearch
          placeholder="Search a country name"
          onSelect={value => {
            const fullCountry = allCountriesWithEU.find(f => f.alpha3code === value);

            if (fullCountry && this.props.onSelect) {
              this.props.onSelect(fullCountry);
            }
          }}
          optionFilterProp="children"
          filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
        >
          {allCountriesWithEU.map(({name, alpha3code}) => countries.includes(alpha3code) && (
            <Select.Option key={name} value={alpha3code}>
              {name}
            </Select.Option>
          ))}
        </Select>
      </div>
    );
  }
}
