.container {
  position: absolute;
  left: 1px;
  bottom: 24px;
  background-color: rgb(255,255,255,.93);
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
  padding: 10px 15px;

  .title {
    margin-bottom: 5px;
    font-size: 12px;
    text-transform: uppercase;
    font-weight: 500;
    line-height: normal;
  }

  .item {
    display: flex;
    flex-direction: row;
    align-items: center;
    line-height: normal;
  }

  .cube {
    display: inline-block;
    width: 26px;
    height: 14px;
    border-radius: 2px;
    margin-right: 5px;
  }
}
