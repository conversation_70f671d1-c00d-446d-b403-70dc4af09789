import React from 'react';
import styles from './index.less';

export default ({ data = [], ...props }) => {
  if (!data || data.length === 0) {
    return null;
  }

  return (
    <div className={styles.container} {...props}>
      <div className={styles.title}>Map legend:</div>

      {data.map(({ color = '#c7e0e8', title = '' }) => (
        <div key={title} className={styles.item}>
          <span className={styles.cube} style={{ backgroundColor: color }} />- {title}
        </div>
      ))}
    </div>
  );
};
