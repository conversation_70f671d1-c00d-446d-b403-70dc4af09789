import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Spin } from 'antd';


@connect(
  (state, ownProps) => ({
    loading: state.maps[ownProps.map] && state.maps[ownProps.map].loading,
  })
)

export default class Loader extends PureComponent {
  render() {
    if (!this.props.loading) {
      return null;
    }

    return (
      <Spin
        size="large"
        style={{ position: 'absolute', left: '50%', top: '50%', marginLeft: -20, marginTop: -20 }}
      />
    );
  }
}
