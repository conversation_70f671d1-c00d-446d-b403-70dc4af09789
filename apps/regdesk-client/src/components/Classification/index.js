import React, { useEffect, useState } from 'react';
import { List, Icon, Spin, Collapse, Empty } from 'antd';
import actions from '../../actions';
import styles from './index.less';

const { Panel } = Collapse;

export default ({
  countryId,
  classificationId,
  onSelect,
  showNA = true,
  hideRequirements = false,
  previewInDevReg = false,
  previewInProducts = false,
  withOutNa = false,
  module = 'mdr',
  IS_BAUSCH = false
}) => {
  const [loading, setLoading] = useState(true);
  const [classificationList, setClassificationList] = useState([]);

  useEffect(() => {
    if (countryId) {
      setLoading(true);

      actions[module]
        .getClassifications(countryId)
        .then(({ list }) => {
          const newList = hideRequirements
            ? list.filter(({ menu }) => menu === 'classifications')
            : list;

          setClassificationList(newList);
          setLoading(false);
        })
        .catch(() => setLoading(false));
    }
  }, [countryId]);

  const list = showNA
    ? [{ classifications: [{ id: 'N/A', name: 'N/A', className: 'N/A', previewInBauschProducts: IS_BAUSCH }], permission: null }, ...classificationList]
    : classificationList;

  const isEmptyList = list.length === 0 || list.every(({ classifications, sub }) => (
    classifications.length === 0 && (sub && sub.length === 0 || hideRequirements)
  ));

  if (list && isEmptyList) {
    return (
      loading ? (
        <div className={styles.spin}>
          <Spin />
        </div>
      ) : (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', paddingTop: 24 }}>
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      )
    );
  }

  const getClassificationsForProducts = classList => {
    let classifications = [];

    if (IS_BAUSCH) {
      classList.forEach(item => {
        if (item.previewInBauschProducts) classifications.push(item);
        if (item.previewInProducts) classifications.push(item);
      });

      if (classifications.find(({ previewInBauschProducts }) => previewInBauschProducts)) {
        classifications = classifications.filter(({ previewInProducts: productsShow }) => !productsShow);
      }

      return classifications;
    }

    return classList.filter((item) => item.previewInProducts !== false);
  };

  return (
    <div className={styles.container}>
      {!loading && list ? (
        list.map(({ title, classifications, permission, sub, alias }) => {
          let classificationsList = classifications;
          const itemTitle = title && module === 'mdr' ? title.slice(title.indexOf('-') + 1) : title;
          const activePanel = sub && sub.find(i => i && i.classifications && i.classifications.some(({ id }) => id === classificationId));

          if (withOutNa) classificationsList = classificationsList.filter((item) => item.id !== null);
          if (previewInDevReg) classificationsList = classificationsList.filter((item) => item.previewInDevReg !== false);
          if (previewInProducts) classificationsList = getClassificationsForProducts(classificationsList);

          return (
            <div className={styles.list} key={permission}>
              {itemTitle && (classificationsList.length > 0 || sub && sub.length > 0 && !hideRequirements)
                && <div className={styles.title}>{itemTitle}</div>}

              {classificationsList.length > 0 && (
                <List
                  bordered={false}
                  dataSource={classificationsList}
                  renderItem={({ id, name }) => {
                    const isActive = id === classificationId && classificationId !== null;
                    const sectionName = alias
                      ? `${alias} - ${name}`
                      : name;

                    return (
                      <List.Item
                        key={id}
                        onClick={() => onSelect({ id, name: sectionName })}
                        className={isActive ? styles.active : styles.item}
                      >
                        {name}
                        <Icon type='right' />
                      </List.Item>
                    );
                  }}
                />
              )}

              {sub && sub.length > 0 && !hideRequirements && (
                <Collapse
                  className={styles.sub}
                  bordered={false}
                  defaultActiveKey={activePanel && [activePanel.permission]}
                  expandIcon={({ isActive }) => (
                    isActive ? (
                      <Icon style={{ color: '#41ADDD' }} className={styles.icon} type='minus-square' />
                    ) : (
                      <Icon style={{ color: '#898989' }} className={styles.icon} type='plus-square' />
                    )
                  )}
                >
                  {sub.map(item => (
                    <Panel header={item.title} key={item.permission}>
                      <List
                        bordered={false}
                        dataSource={item.classifications}
                        renderItem={({ id, name }) => {
                          const isActive = id === classificationId && classificationId !== null;
                          const sectionName = alias
                            ? `${alias} - ${item.alias} - ${name}`
                            : `${item.alias} - ${name}`;

                          return (
                            <List.Item
                              key={id}
                              onClick={() => onSelect({ id, name: sectionName })}
                              className={isActive ? styles.active : styles.item}
                            >
                              {name}
                              <Icon type='right' />
                            </List.Item>
                          );
                        }}
                      />
                    </Panel>
                  ))}
                </Collapse>
              )}
            </div>
          );
        })
      ) : (
        <div className={styles.spin}>
          <Spin />
        </div>
      )}
    </div>
  );
};
