import React, { useEffect, useState } from 'react';
import { List, Icon, Spin } from 'antd';
import actions from '../../actions';
import styles from './index.less';

export default ({ onSelect, section, accessLevel }) => {
  const [loading, setLoading] = useState(true);
  const [accessLevelList, setAccessLevelList] = useState([]);

  useEffect(() => {
    actions.mdr
      .getAccessLevels()
      .then(({ list }) => {
        setAccessLevelList(list);
        setLoading(false);
      })
      .catch(() => setLoading(false));
  }, []);

  return (
    <div className={styles.container}>
      {!loading && accessLevelList ? (
        accessLevelList.map(({ label, value, sub }) => (
          <div className={styles.list} key={value}>
            <div className={styles.title}>{label}</div>

            {sub.length > 0 && (
              <List
                bordered={false}
                dataSource={sub}
                renderItem={subItem => (
                  <List.Item
                    key={subItem.value}
                    onClick={() => onSelect({ section: value, accessLevel: subItem.value })}
                    className={(section === value && accessLevel === subItem.value) ? styles.active : styles.item}
                  >
                    {subItem.label}
                    <Icon type='right' />
                  </List.Item>
                )}
              />
            )}
          </div>
        ))
      ) : (
        <div className={styles.spin}>
          <Spin />
        </div>
      )}
    </div>
  );
};
