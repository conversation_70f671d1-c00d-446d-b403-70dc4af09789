.container {
  max-height: 520px;
  overflow: auto;
  min-height: 200px;

  .list {
    .title {
      background-color: rgba(0, 0, 0, 0.02);
      color: rgba(0, 0, 0, 0.45);
      font-style: italic;
      padding-left: 8px;
      line-height: 22px;
    }

    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 8px 16px 6px;
      border-left: 4px solid transparent;
      cursor: pointer;

      &:hover {
        background-color: rgba(108, 192, 229, 0.2);
        border-left: 4px solid #6cc0e5;
      }
    }

    .active {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 8px 16px 6px;
      background-color: rgba(108, 192, 229, 0.2);
      border-left: 4px solid #6cc0e5;
    }
  }

  .sub {
    border: 0;
    background-color: #fff;

    :global {
      .ant-collapse-item {
        border: 0;
        padding: 0;
      }

      .ant-collapse-header {
        border: 0;
        padding: 0 0 0 40px;
        margin: 12px 0 12px -6px;
        background-color: #fafafa;
        color: #595959;
      }

      .ant-collapse-content-box {
        padding: 0 !important;
      }

      .ant-list-items {
        padding-left: 20px;
      }
    }

    .icon {
      font-size: 16px !important;
      line-height: 16px !important;
    }
  }
}

.spin {
  min-height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}
