import React from 'react';
import { Breadcrumb } from 'antd';
import { Link } from 'react-router-dom';
import Title from './Title';
import styles from './index.less';
import actions from '../../actions';

const { Item } = Breadcrumb;

export default function BreadcrumbHeader({ routes, ...props }) {
  return (
    <Breadcrumb separator='>' className={styles.breadcrumb} {...props}>
      {routes.map(({ href, title, onClick }, index) => (
        <Item key={title || index} onClick={onClick}>
          {
            href
              ? (
                <Link
                  onClick={() => {
                    if (props.type && props.type === 'reports') {
                      return actions.checklists.change({ searchValue: '' });
                    }
                  }}
                  to={href}
                >
                  {title}
                </Link>
              )
              : title
          }
        </Item>
      )
      )}
    </Breadcrumb>
  );
}

BreadcrumbHeader.Title = Title;
