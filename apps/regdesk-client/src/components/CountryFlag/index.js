import { Tooltip } from 'antd';
import classNames from 'classnames';
import React from 'react';
import styles from './index.less';
import { alpha3ToAlpha2, alpha3ToName } from '../../utils/countries';
import getIcon from '../../utils/getIcon';

const getCustomizeCountryFlag = (countyId, className) => {
  const flags = { ANT: getIcon('flag_ANT') };
  const flag = flags[countyId];

  return flag ? <div className={className}>{flags[countyId]}</div> : null;
};

export default ({ countyId, className, name, fullName, tooltip, ...props }) => {
  const flag = getCustomizeCountryFlag(countyId, classNames(styles.flag, styles['flag--customize'], className)) ?? (
    <span
      className={classNames(
        `flag-icon flag-icon-squared flag-icon-${alpha3ToAlpha2(countyId).toLowerCase()}`,
        styles.flag,
        className
      )}
    />
  );

  if (name) {
    return (
      <span {...props}>
        <Tooltip title={tooltip || ''}>
          {flag}
          {name}
        </Tooltip>
      </span>
    );
  }

  return (
    <span {...props}>
      <Tooltip placement='bottom' title={alpha3ToName(countyId)}>
        { flag }
        {fullName === false ? <span>{alpha3ToName(countyId)}</span> : <span>{countyId}</span>}
      </Tooltip>
    </span>
  );
};
