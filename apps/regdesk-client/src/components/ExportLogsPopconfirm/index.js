import React, {PureComponent} from 'react';
import {Popconfirm, Radio, message} from 'antd';
import actions from '../../actions';
import api from '../../utils/api';

export default class ExportLogsPopconfirm extends PureComponent {
  constructor(props) {
    super(props);
  }

  getLogs = () => {
    const {id, module} = this.props
    actions.logs.change({loading: true})
    api.auditTrail.generate({module, itemId: id})
      .then(({msg}) => {
        const defaultMessage = `File saved in Product documents with tag ${module} logs`;
        actions.logs.change({loading: false})
        message.success(msg || defaultMessage);
      })
      .catch(() => actions.logs.change({loading: false}))
  };

  render() {
    const {position = 'top'} = this.props
    return (
      <Popconfirm
        icon={null}
        okText='Export'
        onConfirm={this.getLogs}
        placement={position}
      >
        {this.props.children}
      </Popconfirm>)
  }
}

