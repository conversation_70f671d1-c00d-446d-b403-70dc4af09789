import React from 'react';
import classNames from 'classnames';
import { Col } from 'antd';
import styles from './index.less';

const responsive = {
  1: { xs: 24 },
  2: { xs: 24, sm: 12 },
  3: { xs: 24, sm: 12, md: 8 },
  4: { xs: 24, sm: 12, md: 6 },
};

const Description = ({ term, text, column, className, children, ...restProps }) => {
  const clsString = classNames(styles.description, className);

  return (
    <Col className={clsString} {...responsive[column]} {...restProps}>
      {text && <div className={styles.text}>{text}</div>}
      {term && <div className={styles.term}>{term}</div>}
      {children && <div className={styles.detail}>{children}</div>}
    </Col>
  );
};

export default Description;
