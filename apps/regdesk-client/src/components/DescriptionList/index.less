.descriptionList {
  // offset the padding-bottom of last row
  :global {
    .ant-row {
      margin-bottom: -16px;
      overflow: hidden;
    }
  }

  .title {
    font-size: 14px;
    color: fade(#000, 85%);;
    font-weight: 500;
    margin-bottom: 16px;
  }

  .term, .text {
    line-height: 22px;
    padding-bottom: 16px;
    margin-right: 8px;
    color: fade(#000, 85%);;
    white-space: nowrap;
    display: table-cell;
    vertical-align: top;

    &:after {
      content: ":";
      margin: 0 8px 0 2px;
      position: relative;
      top: -.5px;
    }
  }

  .text {
    padding-bottom: 0;

    &:after {
      content: "";
      margin: 0;
    }
  }

  .detail {
    line-height: 22px;
    width: 100%;
    padding-bottom: 16px;
    color: fade(#000, 65%);
    display: table-cell;
  }

  &.vertical {

    .term {
      padding-bottom: 8px;
      display: block;
    }

    .detail {
      display: block;
    }
  }
}

.descriptionListSmall {
  // offset the padding-bottom of last row
  :global {
    .ant-row {
      margin-bottom: -8px;
    }
  }
  .title {
    margin-bottom: 12px;
    color: fade(#000, 65%);
  }

  .description {
    padding-bottom: 8px;
  }

  .term, .detail {
    padding-bottom: 0;
  }
}
.descriptionListLarge {
  .title {
    font-size: 16px;
  }
}
