import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import { Icon, Alert } from 'antd';
import actions from '../../actions';
import CountryFlag from '../CountryFlag';

import styles from '../Nav/index.less';

@connect(state => ({
  list: state.banners.list,
}))
export default class List extends PureComponent {
  state = {
    loading: false,
  };

  componentDidMount() {
    actions.banners.get(1);
  }

  onBannerClose = (_id) => {
    actions.banners.update(_id, { readed: 1 });
  }

  render() {
    const { list } = this.props;
    
    const alertList = list?.map((banner, index) => {
      const { _id, title, country, content, url, color } = banner;
      const countryFlag = country && <div style={{ marginRight: 8, width: country !== 'EU' && 60, flexShrink: 0 }}><CountryFlag countyId={country} /></div>;
      const link = url && <Link to={{ pathname: `${url}/` }} style={{ color: '#1890ff' }} target="_blank">{url}</Link>;

      return (
        <Alert
          key={index}
          message={
            <div style={{ display: 'flex' }}>
              <div style={{ marginRight: 14, fontWeight: 700, flexShrink: 0 }}>{title}</div>
              {countryFlag}
              <div>
                {content}
                {link && <span style={{ marginLeft: 12, fontWeight: 600 }}>{link}</span>}
              </div>
            </div>
          }
          type={color}
          showIcon={false}
          style={{ marginTop: '5px' }}
          className={styles[color]}
          closable={false}
          closeText={<Icon type="close" />}
          afterClose={() => this.onBannerClose(_id)}
        />
      );
    });

    return (
      <div className={styles.notificationList}>
        {alertList}
      </div>
    );
  }
}
