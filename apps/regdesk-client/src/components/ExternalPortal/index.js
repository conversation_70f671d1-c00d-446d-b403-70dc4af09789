import React, { PureComponent } from 'react';
import ReactDOM from 'react-dom';
import config from 'config';
import md5 from 'md5';
import ConfirmPassword from '../Modal/ConfirmPassword';
import { Modal } from 'antd';

export default class ExternalPortal extends PureComponent {
  constructor(props) {
    super(props);
    this.containerEl = null;
    this.externalWindow = null;
  }

  onPasswordConfirmed = () => {
    this.viewWindow();
  };

  viewWindow = () => {
    const {
      dataId = '',
      dataDocType = '',
      dataUserId = '',
      dataIsApproveChecklist = null,
      dataParentId = null,
      dataDocName = '',
      dataProductName = '',
      dataSkuName = '',
      windowWidth = 1024,
      windowHeight = 1500,
      onClose = () => {
        this.externalWindow.close();
      },
    } = this.props;

    const subdomain = window.location.host.split('.')[0];
    const time = new Date().toISOString();

    const mapForm = document.createElement('form');
    mapForm.target = 'RegDesk Sign Approval';
    mapForm.method = 'POST'; // or "post" if appropriate
    mapForm.action = config.esignServer;

    // adding document id
    const idInput = document.createElement('input');
    idInput.type = 'hidden';
    idInput.name = 'id';
    idInput.value = dataId;
    mapForm.appendChild(idInput);

    if (dataParentId) {
      // adding document id
      const parentIdInput = document.createElement('input');
      parentIdInput.type = 'hidden';
      parentIdInput.name = 'parentId';
      parentIdInput.value = dataParentId;
      mapForm.appendChild(parentIdInput);
    }

    // adding document type
    const typeInput = document.createElement('input');
    typeInput.type = 'hidden';
    typeInput.name = 'doctype';
    typeInput.value = dataDocType;
    mapForm.appendChild(typeInput);

    // adding document name
    const nameInput = document.createElement('input');
    nameInput.type = 'hidden';
    nameInput.name = 'doc_name';
    nameInput.value = dataDocName;
    mapForm.appendChild(nameInput);

    // adding product name
    const productNameInput = document.createElement('input');
    productNameInput.type = 'hidden';
    productNameInput.name = 'product';
    productNameInput.value = dataProductName;
    mapForm.appendChild(productNameInput);

    // adding sku name
    const skuNameInput = document.createElement('input');
    skuNameInput.type = 'hidden';
    skuNameInput.name = 'sku';
    skuNameInput.value = dataSkuName;
    mapForm.appendChild(skuNameInput);

    // adding user id
    const userIdInput = document.createElement('input');
    userIdInput.type = 'hidden';
    userIdInput.name = 'user_id';
    userIdInput.value = dataIsApproveChecklist !== null ? `${dataUserId}/${JSON.stringify({dataIsApproveChecklist})}` : dataUserId;
    mapForm.appendChild(userIdInput);

    // adding time
    const timeInput = document.createElement('input');
    timeInput.type = 'hidden';
    timeInput.name = 'time';
    timeInput.value = time;
    mapForm.appendChild(timeInput);

    // adding hash
    const hashInput = document.createElement('input');
    hashInput.type = 'hidden';
    hashInput.name = 'hash';
    hashInput.value = md5(`bitnami${  time}`);
    mapForm.appendChild(hashInput);

    // adding res
    const resInput = document.createElement('input');
    resInput.type = 'hidden';
    resInput.name = 'res';
    resInput.value = md5(subdomain + time);
    mapForm.appendChild(resInput);

    document.body.appendChild(mapForm);

    this.externalWindow = window.open('', 'RegDesk Sign Approval', `width=${windowWidth},height=${windowHeight}`);
    this.containerEl = this.externalWindow.document.createElement('div');
    this.externalWindow.document.body.appendChild(this.containerEl);

    this.externalWindow.document.title = 'RegDesk Sign Approval';
    mapForm.submit();
    const timer = setInterval(() => {
      if (this.externalWindow.closed) {
        clearInterval(timer);
        onClose();
      }
    }, 1000);
  };

  render() {
    const { onCloseModal } = this.props;
    if (!this.containerEl) {
      return (
        <Modal
          title="Confirm password"
          visible={!this.containerEl}
          footer={null}
          centered
          destroyOnClose
          maskClosable
          onCancel={() => {
            if (onCloseModal && onCloseModal instanceof Function) {
              onCloseModal();
            }
          }}
        >
          <ConfirmPassword onPasswordConfirmed={() => this.onPasswordConfirmed()} />
        </Modal>
      );
    }

    return ReactDOM.createPortal(this.props.children, this.containerEl);
  }
}
