import React from 'react';
import { notification, Typography } from 'antd';
import getIcon from '../../utils/getIcon';

import styles from './style.less';

const { Text } = Typography;

const ErrorNotification = (message) => {
  notification.error({
    message: (
      <div className={styles.message}>
        <Text>{message}</Text>
      </div>
    ),
    icon: getIcon('bird'),
    className: styles.errorNotification,
    style: {
      padding: '10px 16px 10px 16px',
      borderStyle: 'solid',
      borderWidth: 1,
      borderColor: '#f5222d',
      minHeight: 74,
    },
  });

  return null;
};

export default ErrorNotification;