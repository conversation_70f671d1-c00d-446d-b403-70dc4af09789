import React from 'react';
import moment from 'moment';
import { Button, Icon, Avatar, Modal } from 'antd';
import config from 'config';
import { withTranslation } from 'react-i18next';
import utils from '../../utils';
import ESignUtil from '../ESignatureFrame/utils';
import styles from '../ESignatureFrame/index.less';

class ESignatureExport extends React.Component {
  constructor(props) {
    super(props);

    const { t } = props;

    this.t = t;

    this.state = {
      loading: false,
    };
  }

  saveAsPDF = async() => {
    const { signId } = this.props;
    const params = {
      signId
    };

    this.setState({ loading: true });

    return utils.api.esign.saveESignPDF(params).then(async(res) => {
      const blob = new Blob([res.data], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');

      a.href = url;
      a.download = 'ESIGN.pdf';
      a.click();
      URL.revokeObjectURL(url);
      this.setState({ loading: false });
    });
  }

  render() {
    const { name, avatar, accountId, email, showModal, onClose, docName, docType, productName, productFamilyName, sku, reason, signature, signId, ip, isSSO } = this.props;
    const { loading } = this.state;

    // get the time of now, formate the date to '18 MAY 2023 14:55 PM timezone'
    const dateString = `${moment().utc().format('DD MMM YYYY HH:mm A').toUpperCase()} GMT`;
    // make the docType first word uppercase
    // const upperCaseDocType = docType.charAt(0).toUpperCase() + docType.slice(1);
    const upperCaseDocType = ESignUtil.transformDocType(docType);

    return (
      <Modal
        title=''
        visible={showModal}
        footer={null}
        centered
        destroyOnClose
        maskClosable={false}
        onCancel={onClose}
        onClose={onClose}
        width='900px'
        zIndex={1020}
      >
        <div className={styles.container}>
          <div className={styles.header}>
            <div className={styles.logo} />
            RegDesk eSignature
          </div>

          <div className={styles.content}>
            <div className={styles.subheader}>
              <Icon style={{ color: '#87d068', marginRight: 10 }} type='check-circle' />
              Document Approved - {name}
            </div>
          </div>

          <div className={styles.content}>
            Document Name: <span className={styles.bold}>{docName}</span>
          </div>

          <div className={styles.content}>
            Document ID: <span className={styles.bold}>{signId}</span>
          </div>

          <div className={styles.content}>
            By signing below you approve of any document changes provided
          </div>

          <div className={styles.liner} />

          <div className={styles.content}>
            Document Type: <span className={styles.bold}>{upperCaseDocType}</span>
          </div>

          <div className={styles.content}>
            Name/ID: <span className={styles.bold}>{docName}</span>
          </div>

          {productName && (
            <div className={styles.content}>
              {this.t('Glossary.Product_one')}: <span className={styles.bold}>{productName}</span>
            </div>
          )}

          {productFamilyName && (
            <div className={styles.content}>
              {this.t('Glossary.ProductFamily_one')}: <span className={styles.bold}>{productFamilyName}</span>
            </div>
          )}

          {sku && (
            <div className={styles.content}>
              {this.t('Glossary.SKU_one')}: <span className={styles.bold}>{sku}</span>
            </div>
          )}

          <div className={styles.content}>
            Reason: <span className={styles.bold}>{reason}</span>
          </div>

          <div className={styles.content}>
            {signature && <img className={styles.sign} src={signature} alt='signature' />}
          </div>

          <div className={styles.content}>
            Signed by {name}
          </div>

          <div className={styles.content}>
            Signed on: {dateString}
          </div>

          <div className={styles.content}>
            SSO Credentials Used: {isSSO ? 'Yes' : 'No'}
          </div>

          <div className={styles.liner} />

          <div className={styles.content}>
            <div className={styles.subheader}>Signature Certificate</div>
          </div>

          <div className={styles.content}>
            Document Name: <span className={styles.bold}>{docName}</span>
          </div>

          <div className={styles.content}>
            Document ID: <span className={styles.bold}>{signId}</span>
          </div>

          <div className={styles.content}>
            <div className={styles.avatarContent}>
              <div className={styles.avatar}>
                {avatar && (
                  <Avatar
                    alt='avatar'
                    key='avatar'
                    icon='user'
                    size={92}
                    style={{ marginRight: 5 }}
                    src={avatar && `${config.apiServer}/api/doc/${avatar}`}
                  />
                )}
              </div>

              <div className={styles.userDetail}>
                <div>{name}</div>
                <div>User ID: {accountId}</div>
                <div>IP Address: {ip}</div>
              </div>
            </div>
          </div>

          <div className={styles.liner} />

          <div className={styles.content}>
            <div className={styles.grid}>
              <div className={styles.timestamp}>
                <div className={styles.bold}>Timestamp</div>
                <div>{dateString}</div>
              </div>

              <div className={styles.audit}>
                <div className={styles.bold}>Audit</div>
                <div>Document signed by {name} - {email} IP {ip}</div>
                <div>Reason: {reason}</div>
                <div>The document has been signed by all parties and is now closed. </div>
              </div>
            </div>
          </div>

          <div className={styles.liner} />

          <div className={styles.footer}>
            <div className={styles.right}>
              <Button
                type='primary'
                size='large'
                onClick={this.saveAsPDF}
                loading={loading}
              >
                Save As PDF
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    );
  }
}

export default withTranslation()(ESignatureExport);
