import React from 'react';

import { Icon, Tooltip } from 'antd';
import classNames from 'classnames';
import styles from './index.less';

export default function ButtonLink({ className, icon, text, onClick, color }) {
  const clickHandler = (e) => {
    e.preventDefault();
    onClick();
  };

  return (
    <Tooltip overlayClassName={icon ? styles['btn-tooltip'] : styles['btn-tooltip-hide']} title={text}>
      <a
        style={color ? { color } : null}
        href="#!"
        onClick={(e) => clickHandler(e)}
        className={`${styles['my-btn']} ${classNames(className)}`}
      >
        {icon && <Icon type={icon} />}
        <span className={icon ? styles['btn-text'] : null}>{text}</span>
      </a>
    </Tooltip>
  );
}
