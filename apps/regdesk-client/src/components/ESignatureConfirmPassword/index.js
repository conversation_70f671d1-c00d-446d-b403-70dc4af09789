import React from 'react';
import { withTranslation } from 'react-i18next';
import { Input, Button, Form, Modal } from 'antd';
import ErrorNotification from '../ErrorNotification';
import { totalPasswordRules } from '../../utils/helpers';
import { getCookie, removeCookie } from '../../utils/cookie';
import api from '../../utils/api';
import actions from '../../actions';
import styles from '../ESignatureFrame/index.less';

const FormItem = Form.Item;

@Form.create()
class ESignatureConfirmPassword extends React.Component {
  constructor(props) {
    super(props);

    const { t } = props;

    this.t = t;

    this.state = { loading: false };
  }

  componentWillUnmount() {
    if (this.cookieInterval) {
      clearInterval(this.cookieInterval);
    }
  }

  onPasswordConfirmed = () => {
    const { onClose, onOpenESign } = this.props;

    onOpenESign();
    onClose();
  };

  handleSubmit = e => {
    e.preventDefault();

    const { form } = this.props;

    form.validateFieldsAndScroll((err, { email, password }) => {
      if (!err) {
        this.setState({ loading: true });

        api.auth
          .checkEmailAndPassword({ email, password })
          .then(() => {
            this.setState({ loading: false });
            this.onPasswordConfirmed();
          })
          .catch(() => this.setState({ loading: false }));
      }
    });
  };

  onSsoConfirm = action => {
    this.setState({ loading: true });
    removeCookie('esignSSO');

    actions.auth[action]({ esign: true })
      .then(redirect => {
        if (!redirect) return;

        const popup = window.open('', '_blank');

        popup.location.href = redirect;

        this.cookieInterval = setInterval(() => {
          const cookie = getCookie('esignSSO');

          if (popup?.closed) {
            clearInterval(this.cookieInterval);
            this.setState({ loading: false });
          }

          if (cookie) {
            const { verified, code, message } = cookie;

            if (verified === true) {
              this.onPasswordConfirmed();
            } else {
              const messageLines = [
                'Oops',
                `${this.t('Errors.ErrorCode')}: ${code}`,
                this.t(`Errors.${code}`, { defaultValue: message }),
              ];

              ErrorNotification(messageLines.join('\n'));
            }

            popup.close();
            removeCookie('esignSSO');
          }
        }, 1000);
      });
  };

  render() {
    const { showModal, isSSO, ssoProvider, onClose, form: { getFieldDecorator } } = this.props;
    const { loading } = this.state;

    const sso = {
      azure: { title: 'Microsoft Azure', action: 'loginAzure' },
      okta: { title: 'Okta', action: 'loginOkta' },
    };

    const { title, action } = sso[ssoProvider] || {};

    return (
      <Modal
        title='E-Signature'
        visible={showModal}
        footer={null}
        centered
        destroyOnClose
        maskClosable
        onCancel={() => {
          if (!!onClose && onClose instanceof Function) {
            onClose();
          }
        }}
        zIndex={1002}
      >
        <div className={styles.container}>
          <div className={styles.content}>
            <div className={styles.subheader} style={{ marginTop: -14 }}>RegDesk eSignature</div>
            Please verify your identity
          </div>

          <div className={styles.liner} />

          <Form onSubmit={e => this.handleSubmit(e)}>
            <FormItem label='Your email'>
              {getFieldDecorator('email', {
                initialValue: '',
                rules: [
                  {
                    required: true,
                    message: 'Please, enter your email address',
                  },
                  {
                    type: 'email',
                    message: 'Email not valid',
                  },
                ],
              })(
                <Input
                  disabled={loading}
                  placeholder='Your email address'
                  autoFocus
                />
              )}
            </FormItem>

            <FormItem label='Password'>
              {getFieldDecorator('password', totalPasswordRules())(
                <Input.Password
                  disabled={loading}
                  placeholder='Enter password'
                  autoComplete='new-password'
                  type='password'
                />
              )}
            </FormItem>

            <FormItem style={{ marginBottom: 0 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button type='primary' htmlType='submit' disabled={loading} loading={loading}>
                  Confirm
                </Button>

                {isSSO && (
                  <Button
                    disabled={loading}
                    loading={loading}
                    style={{ color: '#fff', background: '#f5b518', borderRadius: 4, borderColor: '#f5b518' }}
                    onClick={() => this.onSsoConfirm(action)}
                  >
                    Confirm with {title}
                  </Button>
                )}
              </div>
            </FormItem>
          </Form>
        </div>
      </Modal>
    );
  }
}

export default withTranslation()(ESignatureConfirmPassword);
