import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import config from 'config';
import { connect } from 'react-redux';
import { Icon, message, Upload, Button, Tooltip } from 'antd';
import pdfImage from './doc/pdf.png';
import docImage from './doc/doc.png';
import zipImage from './doc/zip.png';
import actions from '../../actions';
import { getAutoVersion } from '../../utils/dmsUtil';

const checkNotValidMimeType = {
  'pdf': fileType => fileType !== 'application/pdf',
  'image': fileType => fileType.search('image') === -1,
  'audio': fileType => fileType.search('audio') === -1,
  'video': fileType => fileType.search('video') === -1,
  'docx': fileType => fileType !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'xlsx': fileType => fileType !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    || fileType !== 'application/vnd.ms-excel',
  'pptx': fileType => fileType !== 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'compressed': fileType => fileType !== 'application/zip'
    || fileType.search('compressed') === -1
    || fileType.search('zip') === -1
    || fileType.search('rar') === -1
    || fileType.search('octet-stream') === -1,
  'any': () => false
};

@connect(state => ({
  loading: state.dms.loading,
  applicationsLoading: state.applications.loading,
  trackingLoading: state.tracking.loading,
  role: state.account.role,
  dmsSettings: state.account.dmsSettings,
  parentDMSSettings: state.account.parentDMSSettings,
}))
export default class Uploader extends PureComponent {
  static defaultProps = {
    defaultDocs: [],
    fileType: 'any',
    disabled: false,
    listType: 'picture',
    showUploadList: true,
    uploadType: 'default',
    name: 'file',
    shouldResetList: false,
    multiple: false,
    textBtn: 'Upload',
    data: {},
    openImageNewTab: false,
    getOnlyIds: false,
    className: '',
    parentModule: 'applications',
    messageForHidden: '',
    previousVersion: '',
  };

  static propTypes = {
    defaultDocs: PropTypes.array,
    onChange: PropTypes.func,
    onBeforeUpload: PropTypes.func,
    fileType: PropTypes.string,
    maxFileSize: PropTypes.number,
    maxFiles: PropTypes.number,
    disabled: PropTypes.bool,
    listType: PropTypes.string,
    showUploadList: PropTypes.bool,
    uploadType: PropTypes.string,
    name: PropTypes.string,
    shouldResetList: PropTypes.bool,
    multiple: PropTypes.bool,
    openImageNewTab: PropTypes.bool,
    textBtn: PropTypes.string,
    data: PropTypes.object,
    className: PropTypes.string,
    parentModule: PropTypes.string,
    messageForHidden: PropTypes.string,
    previousVersion: PropTypes.string,
  };

  constructor(props) {
    super(props);
    this.url = `${config.apiServer}/api/dms/file/`;

    const { defaultDocs } = props;

    this.state = {
      docs: this.fill(defaultDocs),
      showModal: false
    };
  }

  componentWillReceiveProps(nextProps) {
    const { defaultDocs } = this.props;

    if (nextProps.defaultDocs.length > defaultDocs.length) {
      this.setState({ docs: this.fill(nextProps.defaultDocs) });
    }
  }

  componentWillUnmount() {
    const { parentModule } = this.props;

    if (parentModule) {
      actions[parentModule].change({ loading: false });
    } else {
      actions.dms.change({ loading: false });
    }
  }

  /**
   * Event change upload
   * @param file
   * @param fileList
   */
  onChange = ({ file, fileList }) => {
    const {
      fileType,
      maxFiles,
      maxFileSize,
      fileNameLengthLimit,
      parentModule,
      previousVersion,
      role,
      dmsSettings,
      parentDMSSettings,
    } = this.props;

    if (!file.status || file.status === 'removed') {
      return;
    }

    let error = '';

    if (previousVersion) {
      const settings = role === 'sub-client' && !!parentDMSSettings?.isOrganization ? parentDMSSettings : dmsSettings;

      const version = getAutoVersion({ versionNameRule: settings.rules?.documentVersion, previousVersion });

      if (version.error) {
        error = version.error;
      }
    }

    const fileName = file.name.split('.')[0];

    if (fileNameLengthLimit && fileName.length > fileNameLengthLimit) error = 'File name limit exceeded!';

    if (maxFiles && fileList.length > maxFiles) error = 'Maximum Files limit reached!';

    if (fileType) {
      const fileTypeArr = fileType.split(',');

      const fileTypeIsAllowed = fileTypeArr.some(type => checkNotValidMimeType[type] && !checkNotValidMimeType[type](file.type));

      if (!fileTypeIsAllowed) error = 'File Type not supported!';
    }

    if (maxFileSize && file.size / 1000 > maxFileSize) error = 'File size limit exceeded!';

    if (error) {
      message.error(error);

      if (parentModule) {
        actions[parentModule].change({ loading: false });
      } else {
        actions.dms.change({ loading: false });
      }

      return;
    }

    this.setState({
      docs: fileList.map(item => {
        if (item.response) {
          return { ...item, ...this.getUrl(item.type, item.response.idf) };
        }

        return item;
      }),
    });

    // show notification message only for newly added files
    if (file.percent === 0) {
      const messageInterval = setInterval(() => {
        if (file.status === 'done') {
          message.success(`${file.name} file successfully uploaded`);
          clearInterval(messageInterval);
        }

        if (file.status === 'error') {
          message.error(`${file.name} file upload error`);
          clearInterval(messageInterval);
        }
      }, 500);
    }

    const isUploading = fileList.some(item => item.status === 'uploading');

    if (!isUploading) {
      this.ping(fileList.filter(item => item.status === 'done'));
    }

    if (parentModule) {
      actions[parentModule].change({ loading: false });
    } else {
      actions.dms.change({ loading: false });
    }
  };

  /**
   * Remove file
   * @param file
   */
  onRemove = file => {
    const { docs } = this.state;
    const filteredDocs = docs.filter(item => item.uid !== file.uid);

    this.setState({ docs: filteredDocs });
    this.ping(filteredDocs);
  };

  /**
   * Check upload file
   * @param file
   * @param fileList
   * @returns {boolean}
   */
  onBeforeUpload = (file, fileList) => {
    const { onBeforeUpload } = this.props;

    if (onBeforeUpload) {
      return onBeforeUpload(file, fileList);
    }

    return true;
  };

  /**
   * Show popup for image and open new tab for any files
   * @param file
   */
  onPreview = file => {
    const { openImageNewTab } = this.props;

    if (file.status !== 'done') {
      return;
    }

    if (
      !openImageNewTab
      && (file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg')
    ) {
      actions.modal.show({
        show: true,
        cmp: 'previewImage',
        title: 'Preview image',
        data: { src: file.url || file.thumbUrl },
      });

      return;
    }

    window.open(file.url || file.thumbUrl, '_blank');
  };

  /**
   * Get url for file
   * @param type
   * @param idf
   */
  getUrl = (type, idf) => {
    const props = {
      thumbUrl: docImage,
      url: this.url + idf,
    };

    if (type === 'image/jpeg' || type === 'image/png' || type === 'image/jpg') {
      props.thumbUrl = this.url + idf;
    }

    if (type === 'application/pdf') {
      props.thumbUrl = pdfImage;
      props.icon = 'file-pdf';
    }

    if (type === 'application/zip') {
      props.thumbUrl = zipImage;
      props.icon = 'file-zip';
    }

    return props;
  };

  /**
   * Get icon
   * @returns {string}
   */
  getIcon = () => {
    const { fileType } = this.props;

    switch (fileType) {
      case 'image':
        return 'picture';
      case 'pdf':
        return 'file-pdf';
      case 'zip':
        return 'file-zip';
      default:
        return 'upload';
    }
  };

  /**
   * Fill data for uploader
   * @param defaultValue
   * @returns {*}
   */
  fill = defaultValue => {
    const { getOnlyIds, fileType } = this.props;
    const docs = defaultValue || [];

    return docs.map(item => {
      const idf = getOnlyIds ? item._id || item.idf || item.response && item.response.idf : item._id;
      const type = getOnlyIds ? (fileType === 'image' ? 'image/png' : 'doc') : (item.type || item.mimetype);
      const doc = { ...this.getUrl(type, idf) };

      doc.error = undefined;
      doc.lastModified = new Date();
      doc.name = item.name;
      doc.status = 'done';
      doc.type = type;
      doc.uid = idf;
      doc.idf = idf;

      return doc;
    });
  };

  /**
   * Ping to parent component
   * @param files
   */
  ping = files => {
    const { onChange, getOnlyIds, shouldResetList } = this.props;

    if (onChange) {
      if (getOnlyIds) {
        onChange(
          files.map(file => ({
            _id: file.idf || file.response && file.response.idf,
            name: file.name,
          }))
        );
      } else {
        onChange(
          files.map(file => ({
            _id: file.idf || file.response && file.response.idf,
            type: file.type,
            name: file.name,
          }))
        );
      }
    }

    if (shouldResetList) {
      this.setState({ docs: [] });
    }

    return false;
  };

  addFilesById = (filesIds) => {
    const { docs } = this.state;
    const { onChange } = this.props;

    if (onChange) {
      const newDocs = filesIds.map(({ _id, type, name }) => {
        const doc = { ...this.getUrl(type, _id) };

        doc.error = undefined;
        doc.lastModified = new Date();
        doc.name = name;
        doc.status = 'done';
        doc.type = type;
        doc.uid = _id;
        doc.idf = _id;

        return doc;
      });

      this.ping(newDocs.concat(docs));
    }
  };

  selectExistingFiles = () => {
    this.setState((state) => ({ ...state, showModal: true }));
  };

  render() {
    const {
      parentModule,
      loading,
      applicationsLoading,
      trackingLoading,
      style,
      invisible,
      onClick,
      onHover = () => { },
      messageForHidden,
      uploadType,
      multiple,
      name,
      data,
      disabled,
      listType,
      textBtn,
      children,
      shouldResetList,
      onlyListDocs,
      maxFiles,
      showUploadList
    } = this.props;

    const { docs } = this.state;
    const isLoading = parentModule && parentModule === 'applications' ? applicationsLoading : parentModule === 'tracking' ? trackingLoading : loading;

    if (uploadType === 'dragger') {
      return (
        <div style={style} onClick={onClick}>
          <Upload.Dragger
            withCredentials
            name={name}
            multiple={multiple}
            action={this.url}
            data={data}
            onChange={this.onChange}
            onRemove={this.onRemove}
            beforeUpload={this.onBeforeUpload}
            fileList={docs}
            disabled={disabled || isLoading}
            style={invisible ? { background: 'none', border: 'none' } : {}}
            openFileDialogOnClick={!invisible}
            showUploadList={!invisible}

          >
            {!invisible ? (
              <>
                <p className='ant-upload-drag-icon' style={{ marginTop: 30 }}>
                  <Icon type='cloud-upload' style={{ fontSize: '42px' }} />
                </p>

                <p style={{ fontSize: '20px' }}>Drag and drop file here</p>
                <p className='ant-upload-hint' style={{ marginTop: 20 }}>or</p>

                <Button
                  disabled={isLoading}
                  loading={isLoading}
                  type='primary'
                  style={{ margin: '10px 0 20px' }}
                >
                  Browse file
                </Button>
              </>
            ) : (
              <Tooltip placement='bottom' title={messageForHidden || ''}>
                <div
                  style={{ width: 160, height: 50 }}
                  onMouseEnter={() => onHover(true)}
                  onMouseLeave={() => onHover(false)}
                />
              </Tooltip>
            )}
          </Upload.Dragger>
        </div>
      );
    }

    let btn = (
      <a>
        <Icon type='upload' style={{ marginRight: 5 }} />
        {textBtn}
      </a>
    );

    if (listType === 'picture') {
      btn = (
        <div>
          <Button className='ant-upload-text'>
            <Icon type={this.getIcon()} style={{ fontSize: 20, color: '#999' }} />
            {textBtn}
          </Button>
        </div>
      );
    }

    if (children) {
      btn = children;
    }

    if (!shouldResetList && docs.length >= maxFiles || onlyListDocs) {
      btn = null;
    }

    return (
      <Upload
        action={this.url}
        listType={listType}
        onChange={this.onChange}
        onPreview={this.onPreview}
        beforeUpload={this.onBeforeUpload}
        onRemove={this.onRemove}
        showUploadList={showUploadList}
        withCredentials
        fileList={docs}
        disabled={disabled || isLoading}
        data={data}
        multiple={multiple}
        className='upload-list-inline'
      >
        {btn}
      </Upload>
    );
  }
}
