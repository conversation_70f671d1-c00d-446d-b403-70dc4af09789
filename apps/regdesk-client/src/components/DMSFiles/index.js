import React, { Component } from 'react';
import PropTypes from 'prop-types';
import config from 'config';
import { Card, Icon, Tooltip, Popconfirm, message } from 'antd';
import DMSAddVersion from '../Modal/DMSUpload';
import AutoUploadedDMSDocs from '../Modal/DMSLog';
import actions from '../../actions';
import utils from '../../utils';
import { getAutoVersion } from '../../utils/dmsUtil';
import { FULL_DATE_FORMAT } from '../../utils/date';

export default class DMSFiles extends Component {
  state = {
    newVersion: null,
    autoUploadedDMSDocs: undefined,
    autoUploadHovered: false
  };

  static defaultProps = {
    type: '',
    defaultDoc: [],
    loading: false,
    disabled: false,
    parentModule: 'applications',
    idq: '',
    dmsSettings: {},
    autoDMSDisabled: true,
    autoDMSConfirmation: false,
    actions: [],
    onChange: () => {},
    onRemoveFile: () => {},
  };

  static propTypes = {
    type: PropTypes.string,
    defaultDoc: PropTypes.array,
    loading: PropTypes.bool,
    disabled: PropTypes.bool,
    parentModule: PropTypes.string,
    idq: PropTypes.string,
    dmsSettings: PropTypes.object,
    autoDMSDisabled: PropTypes.bool,
    autoDMSConfirmation: PropTypes.bool,
    actions: PropTypes.array,
    onChange: PropTypes.func,
    onRemoveFile: PropTypes.func,
  };

  removeDoc = id => {
    const { defaultDoc, onChange, onDelete, idq } = this.props;
    const currentDocs = [...defaultDoc];
    const index = currentDocs.findIndex(d => d._id === id);

    if (index > -1) currentDocs.splice(index, 1);

    if (onDelete) {
      onDelete(currentDocs);
    } else {
      onChange(currentDocs, idq);
    }
  };

  updateDoc = async doc => {
    const { defaultDoc, onChange, onUpdateVersion, idq, parentModule } = this.props;

    actions[parentModule].change({ loading: true });

    return actions.dms.getLatestVersions({ id: doc.parent?._id }).then(data => {
      const currentVersions = defaultDoc.map(d => d._id);
      const latestVersion = data.container?.documents[data.container.documents.length - 1];
      const outdatedIndex = currentVersions.findIndex(d => d === doc._id);

      if (outdatedIndex > -1) {
        currentVersions[outdatedIndex] = latestVersion;

        if (onUpdateVersion) {
          onUpdateVersion(currentVersions);
          actions[parentModule].change({ loading: false });
        } else {
          onChange(currentVersions, idq);
          actions[parentModule].change({ loading: false });
        }
      } else {
        actions[parentModule].change({ loading: false });
        message.warn('Failed to find newer version');
      }
    });
  };

  uploadNewVersion = (data, doc) => {
    const { defaultDoc, idq, parentModule, onChange, onUpdateVersion } = this.props;

    if (data && data.id) {
      actions[parentModule].change({ loading: true });

      const currentVersions = defaultDoc.map(d => d._id);
      const outdatedIndex = currentVersions.findIndex(d => d === doc._id);

      if (outdatedIndex > -1) {
        currentVersions[outdatedIndex] = data.id;

        if (onUpdateVersion) {
          onUpdateVersion(currentVersions);
        } else {
          onChange(currentVersions, idq);
        }
      }
    }
  }

  uploadDoc = (doc, autoUploadedFiles) => {
    if (doc.viewOnly) return;

    const { parentModule, closeModal } = this.props;

    if (parentModule === 'tracking' || parentModule === 'standardsWorkflow') {
      this.setState({ newVersion: doc });
    } else {
      actions.modal.show({
        show: true,
        cmp: 'DMSAddVersion',
        width: 840,
        title: 'Upload New Document Version',
        maskClosable: false,
        data: {
          containerId: doc.parent?._id,
          multiple: true,
          parentModule,
          closeModal,
          autoUploadedFiles,
          onUpload: data => {
            if (data && data.id) {
              this.uploadNewVersion(data, doc);
            }
          },
        },
      });
    }
  };

  confirmAutoCreateDMS = (files, doc) => {
    const { dmsSettings, showAutoUploadedFiles } = this.props;

    const addVersionManually = dmsSettings?.rules?.documentVersion.search('Manual') > -1;

    if (addVersionManually) return this.uploadDoc(doc, files);

    const dmsDocsData = [];

    const version = getAutoVersion({ versionNameRule: dmsSettings.rules?.documentVersion, previousVersion: doc.version });

    if (version.error) {
      message.error(version.error);

      return undefined;
    }

    const dmsDoc = {
      _id: doc._id,
      version,
      parent: {
        name: doc.parent.name,
        tag: {
          name: doc.parent.tag?.name
        }
      },
      files: [...doc.files, ...files]
    };

    dmsDocsData.push(dmsDoc);

    if (showAutoUploadedFiles) {
      return this.setState({ autoUploadedDMSDocs: dmsDocsData });
    }

    actions.modal.show({
      show: true,
      width: 700,
      cmp: 'DMSLog',
      title: <span><Icon type='question-circle' style={{ marginRight: 5, color: 'rgb(250, 173, 20)' }} />Confirmation</span>,
      data: {
        confirmation: true,
        docs: dmsDocsData,
        onClose: actions.modal.hide,
        onConfirm: async(newDMSData) => {
          for (const dmsDoc of newDMSData) {
            delete dmsDoc._id;

            const newData = { containerId: doc.parent._id, files: dmsDoc.files, tagId: doc.parent.tag?._id, ...dmsDoc };

            await actions.dms.addDocument(newData).then((data) => {
              this.uploadNewVersion(data, doc);
            }).catch(() => {
              message.error(`Error creating document ${dmsDoc.parent.name}`);
            });
          }
          actions.modal.hide();
        }
      }
    });
  }

  render() {
    const { defaultDoc, loading, disabled, disableTooltip, parentModule, type, onChange, onUpdateVersion, idq, dmsSettings, showAutoUploadedFiles, onRemoveFile, autoDMSConfirmation, actions: previewActions } = this.props;
    const { newVersion, autoUploadedDMSDocs, autoUploadHovered } = this.state;
    const versionDisabled = dmsSettings.rules?.documentVersion?.search('Manual') > -1;

    if (!defaultDoc || defaultDoc.length === 0) return <></>;

    const output = defaultDoc.map(doc => {
      if (!doc || !doc.files) return;

      const isArchived = doc.parent?.tag?.name === 'Archive';
      const { version, expirationDate, referenceNumber } = doc || {};
      const tag = doc.parent.tag?.name;
      const name = doc.parent?.name;
      const description = `${version || 'no version'} | ${expirationDate ? utils.date.getUTC(expirationDate, FULL_DATE_FORMAT) : 'no date'
      } | ${referenceNumber || ''}`;

      const docs = doc.files.map(d => (
        <div key={d._id} style={{ marginTop: 7, display: 'flex', justifyContent: 'space-between', alignItems: 'center', overflowWrap: 'anywhere' }}>
          <a target='_blank' rel='noreferrer' href={`${config.apiServer}/api/dms/file/${d._id}`}>
            {d.name}
          </a>

          {autoDMSConfirmation && (
            <Tooltip placement='bottom' title='Remove file'>
              <Icon
                type='delete'
                style={{ color: '#41ADDD', cursor: 'pointer' }}
                onClick={() => onRemoveFile({ docId: doc._id, fileId: d._id })}
              />
            </Tooltip>
          )}
        </div>
      ));

      const userActions = [];

      const confirmModuleText = () => {
        switch (parentModule) {
          case 'standardsWorkflow':
            return 'standards workflow';
          case 'ccpTasks':
          case 'ccpProjects':
            return 'task';
          default:
            return parentModule.slice(-1) === 's'
              ? parentModule.slice(0, -1)
              : parentModule;
        }
      };

      if (loading) {
        userActions.push(<Icon type='loading' key='loading' />);
      } else if (disabled) {
        userActions.push(<Tooltip placement='bottom' title={disableTooltip || ''}><span>{`Cannot update ${confirmModuleText()}`}</span></Tooltip>);
      } else {
        if (!doc.viewOnly && !isArchived) {
          userActions.push(
            <Tooltip placement='bottom' title='Upload a new version'>
              <Icon
                style={autoUploadHovered
                  ? { color: '#1890ff', transition: 'color 0.3s' }
                  : { color: 'rgba(0, 0, 0, 0.45)', transition: 'color 0.3s' }}
                type='upload'
                key='upload'
                onClick={() => this.uploadDoc(doc)}
              />
            </Tooltip>
          );
        } else {
          userActions.push(
            <Tooltip placement='bottom' title={isArchived ? 'This document is archived and cannot be modified' : 'No Permission to Modify Document!'}>
              <Icon style={{ color: '#d9d9d9' }} type='upload' key='upload' />
            </Tooltip>
          );
        }

        if (doc.isNewVersionAvailable) {
          userActions.push(
            <Tooltip placement='bottom' title='Update to the most recent version'>
              <Popconfirm
                title='Update this document to the latest version?'
                onConfirm={() => this.updateDoc(doc)}
                onCancel={() => {}}
                okText='Yes'
                cancelText='No'
              >
                <Icon type='arrow-up' key='arrow-up' />
              </Popconfirm>
            </Tooltip>
          );
        } else {
          userActions.push(
            <Tooltip placement='bottom' title='Version is up to date'>
              <Icon style={{ color: '#d9d9d9' }} type='arrow-up' key='arrow-up' />
            </Tooltip>
          );
        }

        userActions.push(
          <Tooltip placement='bottom' title='Remove this document'>
            <Popconfirm
              title={`Remove this document from ${confirmModuleText()}?`}
              onConfirm={() => this.removeDoc(doc._id)}
              onCancel={() => {}}
              okText='Yes'
              cancelText='No'
            >
              <Icon type='delete' key='delete' />
            </Popconfirm>
          </Tooltip>
        );
      }

      if (showAutoUploadedFiles && autoUploadedDMSDocs?.length && autoUploadedDMSDocs[0]._id === doc._id) {
        return (
          <Card key={doc._id} title={<span><Icon type='question-circle' style={{ marginRight: 5, color: 'rgb(250, 173, 20)' }} />Confirmation</span>} style={{ width: '170%', marginLeft: '-57%' }}>
            {!versionDisabled && <AutoUploadedDMSDocs
              confirmation
              docs={autoUploadedDMSDocs}
              onClose={() => this.setState({ autoUploadedDMSDocs: undefined })}
              onConfirm={() => {
                for (const dmsDoc of autoUploadedDMSDocs) {
                  const newData = { containerId: doc.parent._id, files: dmsDoc.files, tagId: doc.parent.tag._id, version: dmsDoc.version };

                  actions.dms.addDocument(newData).then((data) => {
                    this.uploadNewVersion(data, doc);
                  }).catch(() => {
                    message.error('Error creating document');
                  });
                }
              }}
            />}
          </Card>
        );
      }

      return (
        <Card
          key={doc._id}
          size='small'
          title={`Tag: ${tag}`}
          actions={type !== 'log' && newVersion?._id !== doc._id ? userActions : previewActions}
          style={
            !newVersion
              ? { marginTop: 20, marginBottom: 20, width: '100%' }
              : { width: '170%', marginLeft: '-57%' }
          }
        >

          {!newVersion || newVersion._id !== doc._id ? (
            <>
              <p style={{ marginBottom: 10 }}>Name: {name}</p>
              <p style={{ marginBottom: 20 }}>{description}</p>
              {docs}
            </>
          ) : (
            <DMSAddVersion
              multiple
              containerId={newVersion.parent?._id}
              parentModule={parentModule}
              onCancel={() => this.setState({ newVersion: null })}
              onUpload={data => {
                this.setState({ newVersion: null });

                if (data && data.id) {
                  const currentVersions = defaultDoc.map(d => d._id);
                  const outdatedIndex = currentVersions.findIndex(d => d === newVersion._id);

                  if (outdatedIndex > -1) {
                    currentVersions[outdatedIndex] = data.id;

                    if (onUpdateVersion) {
                      onUpdateVersion(currentVersions);
                    } else {
                      onChange(currentVersions, idq);
                    }
                  }
                }
              }}
            />
          )}
        </Card>
      );
    });

    return output;
  }
}
