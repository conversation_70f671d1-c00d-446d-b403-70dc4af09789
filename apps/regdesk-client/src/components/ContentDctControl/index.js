import React, { useState, useEffect } from 'react';
import { Button, Icon, Modal, Empty, Input } from 'antd';

const { TextArea } = Input;

export default ({ item, addItem, changeInput, remove, changeItemHandler, onCloseModal, showModal, onEdit, onInsert, handleSubmit, input, data }) => {
  const [info, setInfo] = useState(data || {});

  useEffect(() => {
    setInfo(data);
  }, [data]);

  const itemsData = info.map((v, i) => ({ ...v, key: i }));

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'flex-end' }}>
        <TextArea
          rows={4}
          value={input}
          onChange={e => changeInput(e.target.value)}
          style={{ marginRight: 10, marginBottom: 0 }}
        />

        <Button onClick={addItem} type='secondary' disabled={!input?.length}>Add</Button>
      </div>

      <div style={{ marginTop: 20, height: 1, borderTop: '1px solid #e8e8e8', width: '100%' }} />
      {!itemsData.length && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}

      <div>
        {itemsData.map((v) => (
          <div key={`${v.name}_${v.key}`} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ wordWrap: 'break-word', maxWidth: 'calc(100% - 80px)', paddingRight: 10 }}>{v.name}</div>

            <div>
              <Icon type='plus-circle' onClick={() => onInsert(v.key)} />
              <Icon type='edit' style={{ marginLeft: 10, marginRight: 10 }} onClick={() => onEdit(v)} />
              <Icon type='minus-circle' onClick={() => remove(v.key)} />
            </div>
          </div>
        ))}
      </div>

      <div style={{ height: 1, borderTop: '1px solid #e8e8e8', width: '100%', marginBottom: 20 }} />

      <Modal
        title={item?.insertKey ? 'Add' : 'Edit'}
        visible={showModal}
        footer={null}
        maskClosable
        onCancel={onCloseModal}
        onClose={onCloseModal}
        width={500}
        zIndex={1001}
      >
        {showModal && (
          <div>
            <TextArea
              rows={4}
              value={item?.name}
              onChange={changeItemHandler}
            />

            <div style={{ marginTop: 20 }}>
              <Button onClick={onCloseModal}>Cancel</Button>

              <Button style={{ marginLeft: 8 }} type='primary' onClick={handleSubmit}>
                {Number.isInteger(item.key) ? 'Update' : 'Add'}
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};
