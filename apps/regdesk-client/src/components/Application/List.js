import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { Icon, List, Input } from 'antd';
import { withTranslation } from 'react-i18next';
import styles from './index.less';
import CountryFlag from '../CountryFlag';
import ProductName from '../Product/Name';
import api from '../../utils/api';
import UniDropdown, { Types as UniDropdownTypes } from '../UniDropdown';

class ListApplication extends PureComponent {
  static defaultProps = {
    defaultValue: '',
    defaultList: [],
    multiSelect: false,
    onSelect: () => {},
  };

  static propTypes = {
    defaultValue: PropTypes.string,
    multiSelect: PropTypes.bool,
    productId: PropTypes.string,
    products: PropTypes.array,
    onSelect: PropTypes.func,
  };

  constructor(props){
    super(props);

    this.state = {
      list: [],
      sortList: [],
      loading: true,
      searchVal: ''
    };
  }

  componentDidMount() {
    api.applications.get({ tracking: true }).then( ({ list }) => {
      this.setState({
        list,
        loading: false,
        pagination: {
          current: 1,
          pageSize: 10,
          total: list.length
        }
      });
    });
  }

  onClick = item => {
    this.props.onSelect(item);
  };

  searchItem = ({ target }) => {
    const { value: val } = target;

    this.setState({
      sortList: this.state.list.filter( item => {
        if (item.name){
          return item.name.toLowerCase().includes(val.toLowerCase());
        }

        return item.id.toLowerCase().includes(val.toLowerCase());
      }),
      searchVal: val
    });
  };

  render() {
    const { defaultValue, t } = this.props;
    const { pagination, loading, searchVal, sortList, list } = this.state;

    return (
      <div className={styles.container} style={{ marginBottom: 10 }}>
        <Input.Search
          style={{ margin: '10px 0' }}
          placeholder='Find application'
          size='large'
          onChange={this.searchItem}
        />
        <List
          pagination={{
            size: 'Pagination',
            onChange: nextPage => this.setState({ pagination: { ...pagination, current: nextPage } }),
            ...pagination
          }}
          loading={loading}
          dataSource={searchVal.length ? sortList : list}
          style={{ maxHeight: 'auto', overflow: 'auto' }}
          renderItem={({ _id: id, ...item }) => (
            <List.Item
              className={defaultValue === id && styles.active}
              onClick={() => this.onClick({ ...item, _id: id })}
              actions={[<Icon type='right' className={styles.icon} />]}
            >
              <List.Item.Meta title={item.name || item.id} />
              <SkuView sku={item.sku} t={t} />
              <div style={{ marginLeft: 48 }}>
                <CountryFlag style={{ marginRight: 10 }} countyId={item.countryId} key={item.createdAt} />
              </div>
            </List.Item>
          )}
        />
      </div>
    );
  }
}

export default withTranslation()(ListApplication);

const SkuView = ({ sku, t }) => {

  if ((sku && sku.length === 0) || !sku) {
    return <ProductName product={null} />;
  }

  if (sku.length === 1) {
    return <ProductName className={styles.productName} product={sku[0]} />;
  }

  return (
    <UniDropdown type={UniDropdownTypes.sku} data={sku} isQuerySnapshot>
      <a>
        {t('Glossary.SKUlist_one')}<Icon type='down' />
      </a>
    </UniDropdown>
  );
};
