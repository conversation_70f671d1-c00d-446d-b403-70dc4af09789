import React from 'react';
import config from 'config';
import { Button } from 'antd';
import actions from '../../../actions';


/**
 * Get Button for Doc
 * @param doc
 * @param props
 * @returns {*}
 * @constructor
 */
export default function ButtonDoc({ doc = {}, ...props }) {
  const { _id: id, name, text, link, type } = doc;
  const isImg = type === 'image/jpeg' || type === 'image/png' || type === 'image/jpg';
  let icon = 'file';

  if (isImg) {
    icon = 'picture';
  }

  if (type === 'application/pdf') {
    icon = 'file-pdf';
  }

  return (
    <Button
      type="dashed"
      icon={icon}
      style={{ marginRight: 10 }}
      onClick={() => {
        const url = `${config.apiServer}/api/doc/${id}`;
        const src = link || url;

        if (isImg) {
          actions.modal.show({
            cmp: 'previewImage',
            title: 'Preview image',
            data: { src },
          });
        } else {
          window.open(src, '_blank');
        }
      }}
      {...props}
    >
      {name || text}
    </Button>
  );
}
