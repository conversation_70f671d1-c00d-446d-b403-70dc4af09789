import React from 'react';
import { Icon } from 'antd';
import config from 'config';

/**
 * Get Link for Doc
 * @param docId
 * @param docName
 * @param link
 * @param props
 * @returns {*}
 * @constructor
 */
export default function LinkDoc({ docId, docName, link, ...props }) {
  return (
    <a
      onClick={e => {
        e.preventDefault();
        window.open(`${config.apiServer}/api/doc/${docId}`, '_blank');
      }}
      {...props}
    >
      { link ? <span style={{ marginRight: 5 }}><Icon type="link" style={{ marginRight: 5 }} />{ docName ? docName : 'Open' }</span> : <span>{ docName ? docName : docId }</span>}
    </a>
  )
}
