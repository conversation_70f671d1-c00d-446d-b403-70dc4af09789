import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { Icon, List, Input } from 'antd';
import { withTranslation } from 'react-i18next';
import styles from './index.less';
import CountryFlag from '../CountryFlag';
import api from '../../utils/api';
import UniDropdown, { Types as UniDropdownTypes } from '../UniDropdown';

class ListChecklist extends PureComponent {
  static defaultProps = {
    defaultValue: '',
    multiSelect: false,
    onSelect: () => {},
  };

  static propTypes = {
    defaultValue: PropTypes.string,
    multiSelect: PropTypes.bool,
    productId: PropTypes.string,
    products: PropTypes.array,
    onSelect: PropTypes.func,
  };

  constructor(props){
    super(props);

    this.state = {
      list: [],
      sortList: [],
      loading: true,
      searchVal: ''
    };
  }

  componentDidMount() {
    api.checklists.get({ tracking: true }).then( ({ list }) => {
      this.setState({
        list,
        loading: false,
        pagination: {
          current: 1,
          pageSize: 10,
          total: list.length
        }
      });
    });
  }

  onClick = item => {
    const { onSelect } = this.props;

    onSelect(item);
  };

  searchItem = ({ target }) => {
    const { value: val } = target;
    const { list } = this.state;

    this.setState({
      sortList: list.filter( item => {
        if (item.name){
          return item.name.toLowerCase().includes(val.toLowerCase());
        }

        return item.id.toLowerCase().includes(val.toLowerCase());
      }),
      searchVal: val
    });
  };

  render() {
    const { defaultValue, t } = this.props;
    const { loading, pagination, searchVal, sortList, list } = this.state;

    return (
      <div className={styles.container} style={{ marginBottom: 10 }}>
        <Input.Search
          style={{ margin: '10px 0' }}
          placeholder='Find checklist'
          size='large'
          onChange={this.searchItem}
        />

        <List
          pagination={{
            size: 'Pagination',
            onChange: nextPage => this.setState({ pagination: { ...pagination, current: nextPage } }),
            ...pagination
          }}
          loading={loading}
          dataSource={searchVal.length ? sortList : list}
          style={{ maxHeight: 'auto', overflow: 'auto' }}
          renderItem={({ _id: id, ...item }) => (
            <List.Item
              className={defaultValue === id && styles.active}
              onClick={() => this.onClick({ ...item, _id: id })}
              actions={[<Icon type='right' className={styles.icon} />]}
            >
              <List.Item.Meta title={item.name || item.id} />
              <SkuView id={id} key={id} t={t} />

              <div style={{ marginLeft: 48 }}>
                <CountryFlag style={{ marginRight: 10 }} countyId={item.countryId} key={item.createdAt} />
              </div>
            </List.Item>
          )}
        />
      </div>
    );
  }
}

export default withTranslation()(ListChecklist);

const SkuView = ({ id, t }) => (
  <UniDropdown type={UniDropdownTypes.sku} filter={{ checklistId: id }} forSelect={false}>
    <a>{t('Glossary.SKUlist_one')} <Icon type='down' /></a>
  </UniDropdown>
);
