.container {
  :global {
    .ant-list-item {
      padding-left: 6px;
      border-left: 4px solid transparent;

      &:hover {
        cursor: pointer;
        background-color: rgba(108,192,229,0.2);
        border-left: 4px solid #6CC0E5;
        border-bottom: 1px solid #ffffff;
      }
    }

    .ant-list-item-action {
      display: flex;
      align-items: center;
    }

    .ant-list-split .ant-list-item:last-child {
      border-bottom: none !important;
    }
  }

  .active {
    background-color: rgba(108,192,229,0.2);
    border-left: 4px solid #6CC0E5;
    border-bottom: 1px solid #ffffff !important;
  }

  .title {
    color: #41ADDD;
  }

  .sp {
    color: rgba(0, 0, 0, 0.65);
  }

  .description {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .class-1 {
    border-bottom: 2px solid #6CC0E5;
  }

  .class-2 {
    border-bottom: 2px solid #f4ce42;
  }

  .class-3 {
    border-bottom: 2px solid #f46241;
  }

  .icon {
    font-size: 20px;
    margin-right: 10px;
  }

  .content {
    display: flex;
    justify-content: flex-end;
    flex-direction: row;
    align-items: center;
    flex: 1;
    text-align: right;
    color: rgba(0,0,0,.45);
    margin-right: 15px;

    p {
      margin: 0;
      padding: 0;
    }
  }

  .productName {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    max-width: 100px;
  }
}

.listSku {
  max-height: 300px;
  overflow-y: auto;
}
