import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Input, Popover, Button, Checkbox, Row, Col, Icon, Badge, message, Tooltip } from 'antd';
import { withTranslation } from 'react-i18next';
import actions from '../../actions';

@connect(state => ({
  advancedSearchList: state.checklists.advancedSearchList,
  advancedSearchValue: state.checklists.advancedSearchValue,
  isAdvancedSearch: state.checklists.isAdvancedSearch,
  loading: state.checklists.loading,
}))

class SearchChecklist extends PureComponent {
  constructor(props) {
    super(props);

    this.options = [
      { key: 'name' },
      { key: 'type' },
      { key: 'requirementName' },
      { key: 'product' },
      { key: 'subtype' },
      { key: 'expiryDate', hasTooltip: true },
      { key: 'sku' },
      { key: 'legalManufacturerProduct' },
      { key: 'deadlineDate', hasTooltip: true },
      { key: 'skuDescription' },
      { key: 'legalManufacturerSKU' },
      { key: 'businessUnits' },
      { key: 'country' },
      { key: 'owner' },
      { key: 'divisions' },
      { key: 'productClass' },
      { key: 'comment' },
      { key: 'groups' }
    ];

    this.state = {
      searchValue: props.advancedSearchValue || '',
      searchList: props.advancedSearchList || [],
      indeterminate: false,
      checkAll: false,
      visible: false,
    };
  }

  componentWillReceiveProps(nextProps) {
    this.reloadSearch(nextProps);
  }

  reloadSearch = nextProps => {
    if (!nextProps.isAdvancedSearch) {
      this.setState({
        searchValue: '',
        searchList: [],
        indeterminate: false,
        checkAll: false,
        visible: false,
      });
    }
  };

  /**
   * Change one filter
   * @param searchList
   */
  onChange = searchList => {
    this.setState({
      searchList,
      indeterminate: !!searchList.length && searchList.length < this.options.length,
      checkAll: searchList.length === this.options.length,
    });
  };

  /**
   * Select all filters
   * @param e
   */
  onCheckAllChange = e => {
    this.setState({
      indeterminate: false,
      checkAll: e.target.checked,
      searchList: e.target.checked ? this.options.map(o => o.key) : [],
    });
  };

  /**
   * Visible Popover
   * @param visible
   */
  handleVisibleChange = visible => this.setState({ visible });

  /**
   * Reset Search
   */
  resetSearch = () => {
    this.setState({ visible: false, checkAll: false, indeterminate: false, searchList: [], searchValue: '' });
    actions.checklists.change({ advancedSearchList: [], advancedSearchValue: '' });
    actions.checklists.get({ pagination: {} });
    actions.checklists.getMap({ map: 'checklists' });
  };

  /**
   * Search
   * @returns {MessageType|null}
   */
  search = () => {
    const { searchValue, searchList } = this.state;

    if (!searchList.length && searchValue !== '') return message.info('Select at least one parameter');
    if (searchValue === '' && searchList.length) return message.info('Enter search string');

    actions.checklists.change({
      advancedSearchValue: searchValue,
      advancedSearchList: searchList,
      isAdvancedSearch: true,
    });
    actions.checklists.get({ pagination: {} });
    actions.checklists.getMap({ map: 'checklists' });
    this.setState({ visible: false });

    return null;
  };

  /**
   * Render advenced search
   * @returns {JSX.Element}
   * @private
   */
  _renderAdvencedSearch = () => {
    const {
      checkAll,
      searchList,
      searchValue,
      indeterminate
    } = this.state;

    const { t } = this.props;

    return (
      <div style={{ padding: 10 }}>
        <Row>
          <Col span={8}>
            <Input.Search
              value={searchValue}
              placeholder='Search...'
              onSearch={this.search}
              onChange={e => this.setState({ searchValue: e.target.value })}
              style={{ marginBottom: 10 }}
            />
          </Col>
        </Row>

        <div style={{ borderBottom: '1px solid #e8e8e8', margin: '10px 0' }}>Select parameters for search:</div>

        <Checkbox
          indeterminate={indeterminate}
          onChange={this.onCheckAllChange}
          checked={checkAll}
          style={{ margin: '10px 0' }}
        >
          Select all
        </Checkbox>

        <Checkbox.Group style={{ width: '100%', marginBottom: 15 }} value={searchList} onChange={this.onChange}>
          <Row>
            {this.options.map(({ key, hasTooltip }) => (
              <Col key={key} span={8}>
                <Checkbox value={key} style={{ width: '100%', padding: '10px 0', fontWeight: 500 }}>
                  {hasTooltip ? (
                    <Tooltip
                      placement='top'
                      title={t(`Checklists.AdvanсedSearch.${key}.tooltip`)}
                      overlayStyle={{ whiteSpace: 'pre-line' }}
                    >
                      {t(`Checklists.AdvanсedSearch.${key}.title`)}
                    </Tooltip>
                  ) : (
                    t(`Checklists.AdvanсedSearch.${key}.title`)
                  )}
                </Checkbox>
              </Col>
            ))}
          </Row>
        </Checkbox.Group>

        <Row type='flex' align='middle' justify='center'>
          <Col span={4}>
            <Button type='default' onClick={this.resetSearch}>
              Reset Search
            </Button>
          </Col>

          <Col span={4} offset={1}>
            <Button type='primary' onClick={this.search}>
              Search
            </Button>
          </Col>
        </Row>
      </div>
    );
  };

  /**
   * Render advenced aearch title
   * @returns {JSX.Element}
   * @private
   */
  _renderAdvencedSearchTitle = () => (
    <Row style={{ height: 40, fontSize: 16, padding: '0 10px' }} type='flex' align='middle' justify='space-between'>
      <Col>Advanced Search</Col>

      <Col>
        <Icon type='close' onClick={() => this.setState({ visible: false })} />
      </Col>
    </Row>
  );

  render() {
    const { advancedSearchValue, loading } = this.props;
    const { visible } = this.state;

    return (
      <Popover
        content={this._renderAdvencedSearch()}
        title={this._renderAdvencedSearchTitle()}
        trigger='click'
        visible={visible}
        onVisibleChange={this.handleVisibleChange}
        overlayStyle={{ maxWidth: '65%', left: '100px', padding: 10 }}
        placement='left'
      >
        {advancedSearchValue ? (
          <Badge dot>
            <Button disabled={loading} style={{ marginLeft: 10 }}>
              Advanced Search
            </Button>
          </Badge>
        ) : (
          <Button disabled={loading} style={{ marginLeft: 10 }}>
            Advanced Search
          </Button>
        )}
      </Popover>
    );
  }
}

export default withTranslation()(SearchChecklist);
