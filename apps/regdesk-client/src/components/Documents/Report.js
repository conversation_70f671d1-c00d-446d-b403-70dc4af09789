import React, { PureComponent } from 'react';
import { Icon, Progress, Table, Tooltip } from 'antd';
import config from 'config';
import actions from '../../actions';
import events from '../../utils/events';
import api from '../../utils/api';

export default class Documents extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      docs: [],
      loading: false,
      content: null,
      to: null,
      from: null,
      type: null,
      triggeredExports: new Set(), // Track documents that have already triggered exports
      pendingPdfExports: [], // Store PDF docs that need to be triggered once parameters are available
    };
  }

  componentDidMount() {
    const { state, module, onGetDocs } = this.props;
    const { reportType } = state;

    this.setState({ loading: true });

    events.on('FileCreatingProgress', this.updateCreatingProgress);

    const modules = {
      'products': api.products.generateReport,
      'tracking': api.tracking.generateReport,
      'checklists': api.checklists.generateReport,
      'standards': api.standards.generateReport,
      'legislations': api.legislation.generateReport,
      'mdr': api.mdrReports.generate,
      'phr': api.phrReports.generate,
    };

    if (modules[module]) {
      modules[module]({ reportType, body: { state } })
        .then(({ docs }) => {
          this.setState({ docs, loading: false });

          if (onGetDocs) {
            onGetDocs(docs);
          }

          actions.task.check();
        })
        .catch(() => this.setState({ loading: false }));
    }
  }

  componentDidUpdate(prevProps) {
    const { state, module } = this.props;
    const { reportType } = state;
    const prevReportType = prevProps.state?.reportType;
    const prevModule = prevProps.module;

    // 检查 reportType 或 module 是否发生变化
    if (reportType !== prevReportType || module !== prevModule) {
      console.log(`Report changed from ${prevModule}:${prevReportType} to ${module}:${reportType}, reloading...`);

      // 清理之前的状态
      this.setState({
        docs: [],
        loading: true,
        content: null,
        to: null,
        from: null,
        type: null,
        triggeredExports: new Set(),
        pendingPdfExports: [],
      });

      // 重新加载报告数据
      const modules = {
        'products': api.products.generateReport,
        'tracking': api.tracking.generateReport,
        'checklists': api.checklists.generateReport,
        'standards': api.standards.generateReport,
        'legislations': api.legislation.generateReport,
        'mdr': api.mdrReports.generate,
        'phr': api.phrReports.generate,
      };

      if (modules[module]) {
        modules[module]({ reportType, body: { state } })
          .then(({ docs }) => {
            this.setState({ docs, loading: false });

            const { onGetDocs } = this.props;

            if (onGetDocs) {
              onGetDocs(docs);
            }

            actions.task.check();
          })
          .catch(() => this.setState({ loading: false }));
      }
    }
  }

  componentWillUnmount() {
    const { docs } = this.state;
    const { onGetDocs } = this.props;

    events.off('FileCreatingProgress', this.updateCreatingProgress);

    docs.forEach((doc) => this.remove(doc._id));

    if (onGetDocs) {
      onGetDocs([]);
    }
  }

  getToParameterValue = (content, paramName) => {
    const iframeSrcMatch = content.match(/<iframe[^>]*src="([^"]*)"[^>]*>/);

    if (iframeSrcMatch) {
      const [ , src ] = iframeSrcMatch;
      const e = document.createElement('textarea');

      e.innerHTML = src;

      const urlObj = new URL(e.textContent);

      return urlObj.searchParams.get(paramName);
    }
    
    return null;
  }

  updateCreatingProgress = ({ progress, docId, content, filePath }) => {
    const { docs, content: stateContent, pendingPdfExports } = this.state;
    const newDocs = [...docs];
    const index = newDocs.findIndex(i => i._id === docId);

    let stateUpdate = {};
    let shouldTriggerPdfExports = false;
    let pdfDocsToTrigger = [];
    let hasNewParameters = false;
    let docsChanged = false;

    console.info('progress', progress);

    // Handle content and parameter extraction
    if (content) {
      const to = this.getToParameterValue(content, 'to');
      const from = this.getToParameterValue(content, 'from');
      const type = this.getToParameterValue(content, 'type');

      console.log('Extracted parameters:', { to, from, type });
      console.log('Current state content exists:', !!stateContent);

      // Only update content if it's different from current content
      // This prevents infinite refresh loops while still allowing report switching
      const isDifferentContent = content !== stateContent;

      if (isDifferentContent) {
        console.log('Content is different, updating iframe content');
        // Add content and parameters to state update
        stateUpdate = { ...stateUpdate, content, to, from, type };
        hasNewParameters = true;
      } else {
        console.log('Content is the same, skipping update to prevent refresh loop');
      }
    }

    if (index !== -1) {
      const wasNotComplete = newDocs[index].progress !== 100;
      const oldProgress = newDocs[index].progress;
      const oldFilePath = newDocs[index].filePath;

      newDocs[index].progress = progress;

      if (filePath) {
        newDocs[index].filePath = filePath;
      }

      // Only mark docs as changed if there's an actual change
      if (oldProgress !== progress || oldFilePath !== filePath) {
        docsChanged = true;
      }

      // Check if we should trigger PDF exports
      if (wasNotComplete && progress === 100 && !newDocs[index].name.endsWith('.pdf')) {
        shouldTriggerPdfExports = true;
        pdfDocsToTrigger = newDocs.filter(doc => doc.name.endsWith('.pdf'));
      }
    }

    // Only include docs in state update if they actually changed
    if (docsChanged) {
      stateUpdate.docs = newDocs;
    }

    // Only call setState if there are actual changes
    if (Object.keys(stateUpdate).length > 0) {
      this.setState(stateUpdate, () => {
        console.info(`Updated progress for doc ${docId}: ${progress}%`);

        // Handle PDF exports
        if (shouldTriggerPdfExports) {
          const { to, from, type } = this.state;
        
          if (to && from && type) {
            console.log('Triggering PDF exports with parameters:', { to, from, type });
            this.triggerPdfExports(pdfDocsToTrigger);
          } else {
            console.warn('Parameters not available yet, adding to pending queue:', pdfDocsToTrigger.map(d => d.name));

            // Add to pending queue
            this.setState(prevState => ({
              pendingPdfExports: [...prevState.pendingPdfExports, ...pdfDocsToTrigger]
            }));
          }
        }

        // If we just got new parameters, process any pending exports
        if (hasNewParameters) {
          const { to, from, type, pendingPdfExports: currentPending } = this.state;

          if (to && from && type && currentPending.length > 0) {
            console.log('Processing pending PDF exports with new parameters:', { to, from, type });
            this.triggerPdfExports(currentPending);
            // Clear the pending queue
            this.setState({ pendingPdfExports: [] });
          }
        }
      });
    } else {
      // Even if no state update, we might need to handle PDF exports
      if (shouldTriggerPdfExports) {
        const { to, from, type } = this.state;

        if (to && from && type) {
          console.log('Triggering PDF exports with parameters (no state update):', { to, from, type });
          this.triggerPdfExports(pdfDocsToTrigger);
        }
      }
    }
  };

  triggerPdfExports = (docs) => {
    const { triggeredExports, to, from, type } = this.state;
    
    console.log('triggerPdfExports called with state:', { to, from, type });
    console.log('triggeredExports:', triggeredExports);
    
    // Only proceed if we have the necessary parameters
    if (!to || !from || !type) {
      console.warn('Missing required parameters for PDF export:', { to, from, type });

      return;
    }

    docs.forEach((doc) => {
      console.log('Processing doc:', { name: doc.name, id: doc._id, progress: doc.progress, filePath: doc.filePath });
      
      // Check if this document should trigger a PDF export
      if (
        doc.name.endsWith('.pdf') // Is a PDF file
        && !doc.filePath // Doesn't have a generated file path yet
        && !triggeredExports.has(doc._id) // Haven't triggered export for this doc yet
      ) {
        console.log(`Triggering PDF export for ${doc.name}`);
        
        // Mark this document as triggered
        this.setState((prevState) => ({
          triggeredExports: new Set(prevState.triggeredExports).add(doc._id)
        }));

        // Trigger the Grafana PDF export
        const exportUrl = `${config.grafanaPdfDownloadServer}/exporter/${type}?to=${to}&from=${from}&filename=${doc.name}&pdfId=${doc._id}&callback_url=${config.apiServer}/api/report/progress`;
        
        console.log(`Export URL: ${exportUrl}`);
        
        // Directly call the export URL using fetch
        fetch(exportUrl, { 
          method: 'GET',
          mode: 'no-cors' // This allows cross-origin requests without CORS preflight
        })
          .then(() => {
            console.log(`PDF export triggered successfully for ${doc.name}`);
          })
          .catch((error) => {
            console.error(`Failed to trigger PDF export for ${doc.name}:`, error);

            // Remove from triggered set on error so it can be retried
            this.setState((prevState) => {
              const retryTriggeredExports = new Set(prevState.triggeredExports);

              retryTriggeredExports.delete(doc._id);

              return { triggeredExports: retryTriggeredExports };
            });
          });
      } else {
        console.log(`Skipping doc ${doc.name}:`, {
          isPdf: doc.name.endsWith('.pdf'),
          hasFilePath: !!doc.filePath,
          isComplete: doc.progress === 100,
          alreadyTriggered: triggeredExports.has(doc._id)
        });
      }
    });
  };

  remove = (id) => {
    const { docs } = this.state;

    this.setState({ loading: true });

    api.doc.deleteReport(id)
      .then(() => {
        const newDocs = docs.filter(i => i._id !== id);

        this.setState({ docs: newDocs, loading: false });
      })
      .catch(() => this.setState({ loading: false }));
  }

  getColumns = () => {
    const columns = [
      {
        title: 'Name',
        dataIndex: 'name',
        sorter: (a, b) => a.name.localeCompare(b.name),
        render: (name, { _id, progress, filePath }) => {
          let downloadUrl = `${config.apiServer}/api/doc/${_id}`;

          if (name.endsWith('.pdf') && filePath) {
            // Use the filePath directly if available, encode URI to handle spaces and special characters
            downloadUrl = `${config.grafanaPdfDownloadServer}/${encodeURI(filePath)}`;
          }

          return (
            <a disabled={progress !== 100} href={downloadUrl} target='_blank' rel='noreferrer'>
              {name}
            </a>
          );
        },
      },
      {
        title: 'Progress',
        dataIndex: 'progress',
        sorter: (a, b) => a.progress - b.progress,
        render: (progress) => {
          const isReady = progress === 100;
          const percent = isReady ? progress : Math.floor(progress);

          return (
            <Progress
              width={100}
              percent={percent}
              status={isReady ? 'success' : 'normal'}
              strokeColor={isReady ? undefined : '#bae7ff'}
              size='small'
            />
          );
        },
      },
      {
        title: 'Actions',
        dataIndex: '_id',
        render: (_id, { name, progress, filePath }) => {
          let downloadUrl = `${config.apiServer}/api/doc/${_id}`;

          if (name.endsWith('.pdf') && filePath) {
            // Use the filePath directly if available, encode URI to handle spaces and special characters
            downloadUrl = `${config.grafanaPdfDownloadServer}/${encodeURI(filePath)}`;
          }

          return (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-around' }}>
              <a disabled={progress !== 100} href={downloadUrl} target='_blank' rel='noreferrer'>
                <Tooltip placement='bottom' title='Download file'>
                  <Icon type='download' style={{ fontSize: 15, color: 'rgba(0, 0, 0, 0.65)' }} />
                </Tooltip>
              </a>
            </div>
          );
        }
      },
    ];

    return columns;
  };

  render() {
    const {
      docs,
      loading,
      content,
    } = this.state;

    return (
      <>
        <Table
          loading={loading}
          rowKey='_id'
          columns={this.getColumns()}
          dataSource={docs}
          pagination={false}
          style={{ marginBottom: '30px' }}
        />

        {(content || !docs.length)
          ? <div dangerouslySetInnerHTML={{ __html: content }} />
          : <div style={{ display: 'flex', justifyContent: 'center' }}><Icon type='loading' /></div>}
      </>
    );
  }

}
