import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { Icon, List } from 'antd';
import styles from './index.less';
import Separator from '../Separator';
import Search from './Search';

@connect(
  state => ({
    data: state.checklists.docNames,
    loading: state.checklists.loading
  })
)

export default class ListDocNames extends PureComponent {
  static defaultProps = {
    className: '',
    defaultValue: '',
    defaultList: [],
    onSelect: () => {},
    hideActions: false,
    hideDescription: false,
    hideTitle: false,
    multiSelect: false,
    showSeparatorBottom: false,
  };

  static propTypes = {
    className: PropTypes.string,
    multiSelect: PropTypes.bool,
    defaultValue: PropTypes.string,
    defaultList: PropTypes.array,
    onSelect: PropTypes.func,
    hideTitle: PropTypes.bool,
    hideActions: PropTypes.bool,
    hideDescription: PropTypes.bool,
    showSeparatorBottom: PropTypes.bool,
    maxHeight: PropTypes.number,
  };

  static Search = Search;

  state = {
    list: this.props.defaultList,
  };

  onClick = (name) => {
    if (this.props.multiSelect) {
      let newList = [...this.state.list];

      if (newList.includes(name)) newList = newList.filter(docName => name !== docName);
      else newList.push(name);

      this.setState({ list: newList });
      this.props.onSelect(newList);
    } else {
      this.props.onSelect(name);
    }
  };

  render() {
    return (
      <div className={classNames(styles.container, this.props.className)}>
        {
          !this.props.hideDescription && (
            <div className={styles.description}>
              { !this.props.hideTitle && <span>Please, type in a document name</span> }
              <Search style={this.props.hideTitle ? { width: '100%', marginTop: 5 } : {}} />
            </div>
          )
        }

        { !this.props.hideDescription && <Separator /> }

        <List
          size="large"
          pagination={{pageSize:5}}
          loading={this.props.loading}
          dataSource={this.props.data}
          style={{ maxHeight: this.props.maxHeight || 'auto', overflow: 'auto' }}
          renderItem={item => (
            <List.Item
              className={this.props.defaultValue === item || this.state.list.includes(item) ? styles.active : ''}
              actions={this.props.hideActions ? null : [<Icon onClick={() => this.onClick(item)} type="right" className={styles.icon} />]}
            >
              <List.Item.Meta
                onClick={() => this.onClick(item)}
                title={<div className={styles.title}>{item}</div>}
              />
            </List.Item>
          )}
        />

        { this.props.showSeparatorBottom && <Separator /> }
      </div>
    );
  }
}
