import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { Input } from 'antd';
import actions from '../../actions';

@connect(
  state => ({
    searchDocName: state.checklists.searchDocName,
  })
)

export default class SearchDocNames extends PureComponent {
  componentWillUnmount() {
    actions.checklists.change({ searchDocName: '' })
  };

  render() {
    const {
      searchDocName,
      style = {},
    } = this.props;

    return (
      <Input.Search
        style={{ width: 270, ...style }}
        value={searchDocName}
        placeholder="Search..."
        onSearch={value => actions.checklists.searchDocNames(value)}
        onChange={e => actions.checklists.searchDocNames(e.target.value)}
      />
    );
  }
}
