import React from 'react';
import { Layout } from 'antd';
import moment from 'moment';
import { useTranslation } from 'react-i18next';

import styles from './index.less';

export default ({ ...props }) => {
  const { t } = useTranslation();
  return (
    <Layout.Footer className={styles.footer} {...props}>
      © {moment().format('YYYY')} RegDesk, Inc. {t('Dashboard.AllRightsReserved')}
    </Layout.Footer>
  );
};
