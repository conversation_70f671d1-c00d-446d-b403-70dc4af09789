import React, { PureComponent } from 'react';
import moment from 'moment';
import { Avatar, Tooltip, Comment, Empty, Popconfirm, Button } from 'antd';
import PropTypes from 'prop-types';
import config from 'config';
import styles from './index.less';

class List extends PureComponent {
  constructor(props) {
    super(props);

    this.state = { loading: null };  
  }

  componentDidMount() {
    this.scrollToBottom();
  }

  componentDidUpdate(prevProps) {
    const { list } = this.props;

    if (list.length !== prevProps.list.length) {
      this.scrollToBottom();
    }
  }

  scrollToBottom = () => {
    this.chat.scrollTop = this.chat.scrollHeight;
  };

  resolveComment = commentId => {
    const { resolveComment } = this.props;

    this.setState({ loading: commentId });

    resolveComment(commentId)
      .then(() => this.setState({ loading: null }))
      .catch(() => this.setState({ loading: null }));
  };

  getActions = (resolvedBy, commentId, updateDate) => {
    const { loading } = this.state;
    const { disabled } = this.props;

    if (!commentId) return;

    if (resolvedBy) {
      return [<>
        <span className={styles.resolvedText}>Resolved by {resolvedBy}</span>
        <Tooltip title={moment(updateDate).getFullUTC()}>
          <span className={styles.resolvedTime}>{moment(updateDate).utc().fromNow()}</span>
        </Tooltip>
      </>];
    }

    return [
      <Popconfirm
        title='Resolve comment?'
        onConfirm={() => this.resolveComment(commentId)}
        okText='Confirm'
        cancelText='Cancel'
        disabled={disabled}
      >
        <Button type='primary' size='small' loading={loading === commentId} disabled={loading === commentId || disabled}>
          Resolve
        </Button>
      </Popconfirm>
    ];
  };

  render() {
    const { list, disabled, tooltipMsg } = this.props;

    return (
      <Tooltip placement='top' title={tooltipMsg}>
        <div
          className={styles.chat}
          ref={el => {
            this.chat = el;
          }}
          style={{
            maxHeight: disabled ? 300 : 400,
            padding: !disabled ? '12px 24px 24px 24px' : '0 15px',
            cursor: 'pointer',
          }}
        >
          {list.length === 0 ? (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          ) : (
            list.map(({ name, createdAt: createDate, updatedAt: updateDate, message: msg, user = {}, resolvedBy, _id }) => (
              <Comment
                key={_id}
                author={name}
                avatar={
                  <Avatar
                    alt='avatar'
                    icon='user'
                    src={user?.avatar && `${config.apiServer}/api/doc/${user.avatar}`}
                  />
                }
                content={<div className={styles.msg}>{msg}</div>}
                datetime={
                  <Tooltip title={moment(createDate).getFullUTC()}>
                    <span>{moment(createDate).utc().fromNow()}</span>
                  </Tooltip>
                }
                actions={this.getActions(resolvedBy, _id, updateDate)}
              />
            ))
          )}
        </div>
      </Tooltip>
    );
  }
}

List.defaultProps = {
  list: [],
  disabled: false,
  tooltipMsg: '',
};;

List.propTypes = {
  list: PropTypes.array,
  disabled: PropTypes.bool,
  tooltipMsg: PropTypes.string,
};

export default List;