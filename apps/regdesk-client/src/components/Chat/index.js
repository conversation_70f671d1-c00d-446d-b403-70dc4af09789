import React, { PureComponent } from 'react';
import config from 'config';
import { Form, Button, Input, Card, Tooltip, message, Icon, Skeleton, List, Avatar } from 'antd';
import PropTypes from 'prop-types';
import CommentsList from './List';
import styles from './index.less';
import api from '../../utils/api';

@Form.create()
class Chat extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      hideUsers: true,
      users: [],
      allMentionedUsers: new Set(),
    };  
  }

  componentDidMount() {
    api.company.getUsers().then(({ users }) => this.setState({ users }));
  }

  handleSubmit = (e) => {
    e.preventDefault();

    const { form, onSubmit, loading } = this.props;
    const { allMentionedUsers } = this.state;

    form.validateFieldsAndScroll((err, values) => {
      if (!err && !loading) {
        const messageText = values.message;
        const mentionedUsers = [];

        if (allMentionedUsers.size > 0) {
          [...allMentionedUsers.values()].forEach((name) => {
            if (messageText.indexOf(name) !== -1) {
              mentionedUsers.push(name);
            }
          });
        }

        Promise.resolve({})
          .then(() => onSubmit({ message: messageText, mentionedUsers }))
          .then(() => {
            form.setFieldsValue({ message: '' });
            message.success('Comment sent');
          });
      }
    });
  };

  changeHandler = (e) => {
    const { value } = e.target;
    const lastSymb = value.slice(-2);

    if (lastSymb === '@' || lastSymb === ' @' || lastSymb === '@@') {
      this.setState({ hideUsers: false });
    } else {
      this.setState({ hideUsers: true });
    }
  };

  render() {
    const {
      loading,
      form,
      history,
      disabled,
      disabledReply,
      title,
      showIcon,
      showSkeleton,
      hideTitle,
      resolveComment,
      tooltipMsg,
    } = this.props;
    const { hideUsers, users, allMentionedUsers } = this.state;
    const { getFieldDecorator } = form;
    const disableMessageReply = disabled || disabledReply;

    const titleCard = (
      <div>
        {showIcon && <Icon type='rocket' />}
        <span style={{ marginLeft: showIcon ? 10 : 0 }}>{title}</span>
      </div>
    );

    return (
      <Card
        title={!hideTitle ? titleCard : ''}
        style={{ marginBottom: !disabled ? 24 : 0 }}
        bodyStyle={{
          padding: 0,
          fontFamily: 'Georgia, Times, "Times New Roman", serif',
        }}
      >
        {showSkeleton ? (
          <div style={{ padding: !disabled ? '12px 24px 24px 24px' : '0 15px' }}>
            <Skeleton active />
          </div>
        ) : (
          <CommentsList
            list={history}
            disabled={disabled}
            resolveComment={(commentId) => resolveComment(commentId)}
            tooltipMsg={tooltipMsg}
          />
        )}

        {!disableMessageReply && (
          <div style={{ position: 'relative' }}>
            <Form onSubmit={this.handleSubmit} className={styles.form}>
              {getFieldDecorator('message', {
                rules: [
                  {
                    required: true,
                    message: 'Please! Enter message',
                  },
                ],
              })(
                <Input.TextArea
                  onChange={(e) => this.changeHandler(e)}
                  rows={2}
                  placeholder='Reply...'
                  autoComplete='off'
                  ref={node => (this.msg = node)}
                />
              )}

              <Tooltip placement='bottom' title='Send message'>
                <Button
                  icon='edit'
                  shape='circle'
                  size='large'
                  htmlType='submit'
                  disabled={loading}
                  loading={loading}
                  type='primary'
                  ghost
                />
              </Tooltip>

              {!hideUsers && (
                <List
                  style={{
                    position: 'absolute',
                    top: 70,
                    background: '#fff',
                    width: '100%',
                    left: 0,
                    borderRadius: 3,
                    border: '1px solid #d9d9d9',
                    zIndex: 10,
                  }}
                  itemLayout='horizontal'
                  dataSource={users}
                  renderItem={({ avatar, name, email }) => (
                    <List.Item
                      style={{ cursor: 'pointer', padding: '10px' }}
                      onClick={() => {
                        const msg = form.getFieldValue('message');

                        form.setFieldsValue({ message: `${msg}${name} ` });
                        allMentionedUsers.add(name);
                        this.setState({ hideUsers: true, allMentionedUsers });
                        this.msg.focus();
                      }}
                    >
                      <List.Item.Meta
                        style={{ alignItems: 'center' }}
                        avatar={
                          <Avatar
                            alt='avatar'
                            icon='user'
                            src={avatar && `${config.apiServer}/api/doc/${avatar}`}
                          />
                        }
                        title={name}
                        description={email}
                      />
                    </List.Item>
                  )}
                />
              )}
            </Form>
          </div>
        )}
      </Card>
    );
  }
}

Chat.defaultProps = {
  history: [],
  disabled: false,
  showIcon: false,
  loading: false,
  showSkeleton: false,
  title: 'Internal Review',
  hideTitle: false,
  onSubmit: () => {},
  tooltipMsg: '',
};

Chat.propTypes = {
  history: PropTypes.array,
  disabled: PropTypes.bool,
  onSubmit: PropTypes.func,
  hideTitle: PropTypes.bool,
  title: PropTypes.string,
  showIcon: PropTypes.bool,
  loading: PropTypes.bool,
  showSkeleton: PropTypes.bool,
  tooltipMsg: PropTypes.string,
};

export default Chat;