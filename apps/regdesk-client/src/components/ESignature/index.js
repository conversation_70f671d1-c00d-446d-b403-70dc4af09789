import { connect } from 'react-redux';
import SignatureCanvas from 'react-signature-canvas'
// import { Tabs, Tab } from 'react-bootstrap';
import { Row, Col, Button, Select, Modal, Tabs } from 'antd';
import React, { useState } from 'react';
import api from '../../utils/api';
import styles from './index.less'

export default class ESignature extends React.Component {
  state = {
    activeKey: '0',
    fullName: '',
    showModal: false,
    signData: null
  }

  closeModal = () => {
    this.setState({ showModal: false, fullName: '' })
  }

  showModal = (fullName = {}) => {
    this.setState({ showModal: true, fullName })
  }

  render() {
    const { fullName } = this.props;
    const { showModal, activeKey} = this.state;
    const isEdit = !!fullName;
    let signaturePad = React.createRef();

    const handleSignatureTabChange = async (activeKey) => {
      if (activeKey === '1') {
          const sign = await handleGetTypeInSignature(fullName, 'DancingScript-VariableFont_wght');
        //   setSignData(sign);
        this.setState({ signData: sign, activeKey: activeKey });
      } else {
        // clear signData
          this.setState({ signData: null, activeKey: activeKey});
      }
    }

    const handleTypeInSignatureFontChange = async (font) => {
      const sign = await handleGetTypeInSignature(fullName, font);
      this.setState({ signData: sign });
    }

    const clearHandwriting = async () => {
      signaturePad.clear();
    }

    const applyHandwriting = async () => {
    //   const { signChanged } = props;
      let sign;
      if (this.state.activeKey === '1') {
          sign = this.state.signData;
      } else {
        if (!signaturePad.isEmpty()) {
          sign = signaturePad.toDataURL();
          this.setState({ signData: sign });
        }
      }
      if (this.props.onSign) {
        this.props.onSign(sign)
      }
      this.closeModal();
    }

    const handleGetTypeInSignature = async (word, fontFamily) => {
        return await api.esign.getTypeInSignature({signature: word, fontFamily}).then((res) => {
            return res.signatureBase64
        });
    }

    const selectFonts = (activeKey === '1')?<Select
      required
      showSearch
      allowClear
      placeholder={'Select Fonts'}
      style={{width: 200}}
      optionFilterProp="children"
      className={styles.select}
      onChange={handleTypeInSignatureFontChange}
      disabled={activeKey === '0'}
    >
      <Select.Option value="DancingScript-VariableFont_wght">DancingScript</Select.Option>
      <Select.Option value="Handlee-Regular">Handlee</Select.Option>
      <Select.Option value="LilyScriptOne-Regular">LilyScriptOne</Select.Option>
      <Select.Option value="PinyonScript-Regular">PinyonScript</Select.Option>
      <Select.Option value="NanumPenScript-Regular">NanumPenScript</Select.Option>
      <Select.Option value="RougeScript-Regular">RougeScript</Select.Option>
      <Select.Option value="Caveat-VariableFont_wght">Caveat</Select.Option>
      <Select.Option value="IndieFlower-Regular">IndieFlower</Select.Option>
      <Select.Option value="Satisfy-Regular">Satisfy</Select.Option>
      <Select.Option value="GloriaHallelujah-Regular">GloriaHallelujah</Select.Option>
      <Select.Option value="GreatVibes-Regular">GreatVibes</Select.Option>
      <Select.Option value="Zeyada-Regular">Zeyada</Select.Option>
    </Select> : <Button
                      type="button"
                      onClick={clearHandwriting}
                  >Clear</Button>;

    return (
      <Modal
        title=''
        visible={showModal}
        footer={null}
        centered
        destroyOnClose
        maskClosable={false}
        onCancel={this.closeModal}
        onClose={this.closeModal}
        width="736px"
        height="450px"
        zIndex={1001}
      >
        {
          showModal && (
            <div>
            <Tabs
              className="outer-tabs"
              defaultActiveKey={this.state.activeKey}
              onChange={handleSignatureTabChange}
              id={'esign-tabs'}
              tabBarExtraContent={selectFonts}
            >
              <Tabs.TabPane eventKey='0' key='0' tab='Draw Signature'>
                <SignatureCanvas ref={(ref) => { signaturePad = ref }} penColor='black'
                  canvasProps={{width: 686, height: 220, className: styles.sigCanvas}} />
              </Tabs.TabPane>
              <Tabs.TabPane eventKey='1' key='1' tab='Type In Signature'>
                  <Row style={{height: 220, fontSize: 16, margin: '10px', className: styles.sigCanvas}} type="flex" align="middle" justify="space-between">
                      {this.state.signData && <img src={`${this.state.signData}`} />}
                  </Row>

              </Tabs.TabPane>
            </Tabs>
            <div className={styles.description}>
              By signing below, I acknowledge my electronic user ID and password combination act as an electronic signature and my electronic signature is the legally binding equivalent of my handwritten signature.
            </div>
              <div className={styles.footer}>
                  <Button
                      type="primary"
                      onClick={applyHandwriting}
                  >Apply Signature</Button>
              </div>
          </div>
          )
        }
      </Modal>
    );
  }
}
