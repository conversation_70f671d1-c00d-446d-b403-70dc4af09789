@main-offset: 40px;
@gutter-width: 8px;
@widget-title-width: 34px;

@main-header-width: 64px;
@main-header-margin: 1px;
@header-width: 72px;
@header-margin: 8px;
@offset-for-scroll: 80px;
@offset-for-help-widget: 20px;

@mobile-sm: 320;
@mobile: 576;
@tablet-sm: 768;
@tablet: 992;
@laptop: 1200;
@desktop: 1440;
@desktop-lg: 1920;

@min-mobile-sm: ~"(min-width: 320px)";
@min-mobile: ~"(min-width: 576px)";
@min-tablet-sm: ~"(min-width: 768px)";
@min-tablet: ~"(min-width: 992px)";
@min-laptop: ~"(min-width: 1200px)";
@min-desktop: ~"(min-width: 1440px)";
@min-desktop-lg: ~"(min-width: 1920px)";

@line-width: @gutter-width;
@line-start: 0;
@cell-width: 60px;

// colors
@color-white: #ffffff;
@color-black: #000000;
@color-grey: #d9d9d9;

@color-orange: #F49926;
@color-orange-hover: #FFAA3F;
@color-orange-pressed: #F16F12;
@color-orange-disabled: #F8C88C;

@color-header: @color-white;
@color-background: #ffffff;
@color-line: @color-background;
@color-cell: @color-white;
@color-blue-main: #005594;

// TODO: RD-2478
// #colors() {
//   white: #ffffff;
//   header: @color-white;
//   background: #f5f5f5;
//   line: @color-background;
//   cell: @color-white;
//   blue-main: #005594;
// }

.nav {
  background: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
  z-index: 50;
  padding-left: 30px;
  padding-right: 30px;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);

  .title {
    flex-grow: 1;
    margin: 0;
    margin-right: 16px;
    font-size: 2.4rem;
    text-align: center;
  }

}

.logo {
  height: 40px;
  width: 132px;
  background-image: url('../../assets/logo.png');
  background-size: contain;
  background-repeat: no-repeat;
}

.grid(@total-columns, @breakpoint) {
  @media @breakpoint {
    background:
      repeating-linear-gradient(to bottom,
        @color-line @line-start @line-width,
        transparent @line-width @line-width + @cell-width ),
      repeating-linear-gradient(to right,
        @color-line @line-start @line-width,
        @color-cell @line-width calc(100% / @total-columns - (@line-width / @total-columns)));
  }
}

.orangeButton {
  min-width: 174px;
  min-height: 46px;
  padding-left: 20px;
  padding-right: 20px;
  background: @color-orange;
  border: none;
  border-radius: 8px;
  color: @color-white;
  font-weight: bold;

  &:active {
    background: @color-orange-pressed;
  }

  &:hover,
  &:focus {
    background: @color-orange-hover;
  }

  &:disabled {
    background: @color-orange-disabled;
    color: @color-white;

    &:hover,
    &:focus {
      background: @color-orange-disabled;
      color: @color-white;
    }
  }
}

.orangeOpacityButton {
  min-height: 46px;
  padding-left: 20px;
  padding-right: 20px;
  border: 1px solid @color-orange;
  color: @color-orange !important;
  border-radius: 8px;
  font-weight: bold;

  &:active {
    background: @color-orange-pressed;
    color: @color-white !important;
  }

  &:hover,
  &:focus {
    border: 1px solid @color-orange;
  }

  &:disabled {
    border: 1px solid @color-orange;

    &:hover,
    &:focus {
      border: 1px solid @color-orange;
      color: @color-orange !important;
    }
  }
}

.kebabMenu {
  color: @color-black;

  &:active,
  &:hover,
  &:focus {
    color: @color-black;
  }
}

.selectButton {
  font-weight: normal;

  :global {
    .ant-select-selection {
      height: 46px;
      padding: 8px 46px 8px 20px;
      border: 1px solid @color-blue-main;
      color: @color-blue-main;
      font-size: 1.8rem;

      &__rendered {
        margin: 0;
      }

      .ant-select-arrow {
        top: calc(50% - 3px);
        right: 18px;
      }
    }
  }
}

.selectLabel {
  display: block;
  margin-bottom: 8px;
  color: @color-grey;
  font-size: 1.2rem;
  font-weight: lighter;
  line-height: 1;
  text-transform: uppercase;
}

.typeSelect {
  padding: 16px 8px;

  .typeSelectName {
    margin-left: 10px;
  }

  .typeSelectSelect {
    :global {
      .ant-select-selection {
        height: 48px;
        padding: 12px 46px 12px 20px;
        background-color: transparent;
        border: none;
        color: @color-blue-main;
        font-size: 1.8rem;

        &__rendered {
          margin: 0;
        }

        .ant-select-arrow {
          top: calc(50% + 1px);
          right: 18px;
          color: @color-blue-main;
          font-size: 1.6rem;
        }
      }
    }
  }

}

.typeDropdown {
  :global {
    .ant-select-dropdown-menu-item {
      padding-left: 20px;
      padding-right: 20px;
      font-size: 1.6rem;
      font-family: 'Helvetica Neue Light', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
    }

    .anticon {
      margin-right: 10px;
    }
  }
}

.customInput {
  margin-left: 0 !important;

  :global {
    .ant-input {
      font-size: 2.4rem;
      color: @color-black;
      border: none;
      padding: 0 !important;
    }

    .ant-input-suffix {
      top: calc(50% - 4px);
      left: -16px;
      right: auto;
      margin: 0;
      color: @color-blue-main;
      font-size: 1.3rem;
      transform: none;
    }
  }
}

.sigCanvas {
  margin: 10px 0;
  border: 1px solid @color-grey;
  border-radius: 4px;
}

// container
.container {
  margin: 1px auto 0;
  background-color: @color-background;
  min-height: 100vh;

  .content {
    padding: 10px 30px 5px;
    margin: 0 auto;
    max-width: 1200px;
    .header {
      font-size: 2.4rem;
    }
    .subheader {
      font-size: 1.8rem;
    }
    .sign {
      border: 1px solid @color-grey;
      border-radius: 4px;
      width: 300px;
      height: 100px;
    }
    .bold {
      font-weight: bold;
    }
    .avatarContent {
      width: 100%;
      height: 100px;

      .avatar {
        width: 100px;
        float: left;
      }
      .userDetail {
        float: left;
        margin-left: 20px;
        line-height: 30px;
      }
      .sign {
        float: right;
        width: 300px;
        height: 100px;
      }
    }
    .grid {
      width: 100%;
      height: 100px;
      .timestamp {
        width: 300px;
        float: left;
      }
      .audit {
        float: left;
      }
    }
    
    
  }
  
  .liner {
    height: 1px;
    margin: 0 30px;
    background: linear-gradient(90deg, #7F8080 0%, #6992A5 31.77%, #58A1C3 48.44%, #6DBDE1 64.58%, rgba(185, 224, 241, 0.71) 82.68%, rgba(212, 236, 246, 0.05) 100%);
  }

  
}

.description {
  padding-bottom: 30px;
}

.footer {
  text-align: right;
}