files:
   /etc/nginx/conf.d/proxy.conf:
     owner: root
     group: root
     mode: "000644"
     content: |
       upstream nodejs {
           server 127.0.0.1:8081;
           keepalive 256;
       }

       server {
           listen 8080;
           client_max_body_size 1024M;

           if ($http_x_forwarded_proto = 'http') {
               return 301 https://$host$request_uri;
           }

           if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2})") {
               set $year $1;
               set $month $2;
               set $day $3;
               set $hour $4;
           }
           access_log /var/log/nginx/healthd/application.log.$year-$month-$day-$hour healthd;
           access_log  /var/log/nginx/access.log  main;


           location / {
               proxy_pass  http://nodejs;
               proxy_set_header Upgrade $http_upgrade;
               proxy_set_header Connection "upgrade";
               proxy_http_version 1.1;
               proxy_set_header        Host            $host;
               proxy_set_header        X-Real-IP	$remote_addr;
               proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
           }

       gzip on;
       gzip_comp_level 4;
       gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;

       }

       http {
           client_max_body_size 1024M;
       }

       location / {
           client_max_body_size 1024M;
       }

   /opt/elasticbeanstalk/hooks/configdeploy/post/99_kill_default_nginx.sh:
     owner: root
     group: root
     mode: "000755"
     content: |
       #!/bin/bash -xe
       rm -f /etc/nginx/conf.d/00_elastic_beanstalk_proxy.conf
       service nginx stop
       service nginx start

container_commands:
  removeconfig:
    command: "rm -f /tmp/deployment/config/#etc#nginx#conf.d#00_elastic_beanstalk_proxy.conf /etc/nginx/conf.d/00_elastic_beanstalk_proxy.conf"
