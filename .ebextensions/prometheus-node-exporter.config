files:
    "/check_exporter":
      mode: "000755"
      owner: root
      group: root
      content: |
        #!/bin/bash
        if [ ! -f /usr/bin/node_exporter ]; then
          wget https://sysload.regdesk.co/download/prometheus-node-exporter/node_exporter -P /usr/bin
          chmod 000755 /usr/bin/node_exporter
        fi

    "/lib/systemd/system/prometheus-node-exporter.service":
      mode: "000755"
      owner: root
      group: root
      content: |
        [Unit]
          Description=Prometheus exporter for machine metrics
          Documentation=https://github.com/prometheus/node_exporter
        [Service]
          Restart=on-failure
          ExecStartPre=/check_exporter
          ExecStart=/usr/bin/node_exporter
          ExecReload=/bin/kill -HUP $MAINPID
          TimeoutStopSec=20s
          SendSIGKILL=no
        [Install]
          WantedBy=multi-user.target

services:
    sysvinit:
        prometheus-node-exporter:
          enabled: true
          ensureRunning: true

option_settings:
  aws:elb:listener:9100:
    ListenerProtocol: TCP
    InstancePort: 9100
    InstanceProtocol: TCP

Resources:
  sslSecurityGroupIngress: 
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: {"Fn::GetAtt" : ["AWSEBSecurityGroup", "GroupId"]}
      IpProtocol: tcp
      ToPort: 9100
      FromPort: 9100
      CidrIp:
        Fn::GetOptionSetting:
          OptionName: PrometheusIP
          DefaultValue: ************/32