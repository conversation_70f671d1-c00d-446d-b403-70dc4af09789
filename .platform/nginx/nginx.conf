#Elastic Beanstalk Nginx Configuration File

user                    nginx;
error_log               /var/log/nginx/error.log warn;
pid                     /var/run/nginx.pid;
worker_processes        auto;
worker_rlimit_nofile    200000;

events {
    worker_connections  1024;
}

http {
    include	  /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    include conf.d/[!99_grafana]*.conf;

    map $http_upgrade $connection_upgrade {
        default     "upgrade";
    }

    server {
	    listen        80 default_server;
        access_log    /var/log/nginx/access.log main;
        
        gzip on;
        gzip_static on;
        gzip_comp_level 9;
        gzip_proxied any;
        gzip_min_length 256;
        gzip_types application/javascript application/rss+xml application/vnd.ms-fontobject application/x-font application/x-font-opentype application/x-font-otf application/x-font-truetype application/x-font-ttf application/x-javascript application/xhtml+xml application/xml application/json font/opentype font/otf font/ttf image/svg+xml image/x-icon text/css text/javascript text/plain text/xml;
        gzip_vary on;
        proxy_set_header    X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_buffering off;
        
       	# Include the Elastic Beanstalk generated locations
        include conf.d/elasticbeanstalk/*.conf;
        
        client_header_timeout 4000s;
        client_body_timeout   4000s;
        keepalive_timeout     4000s;
        proxy_read_timeout 4000s;
        proxy_send_timeout 4000s;
        proxy_connect_timeout 3000s;

        # Grafana API endpoint with dynamic timeout for sysload-embed.regdesk.ai
        location /api/report/grafana {
            proxy_pass http://127.0.0.1:8080;
            keepalive_timeout     4000s;
            proxy_read_timeout 4000s;
            proxy_send_timeout 4000s;
            proxy_connect_timeout 3000s;

            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            add_header Access-Control-Allow-Origin "https://sysload-embed.regdesk.ai" always;
            add_header Access-Control-Allow-Credentials "true" always;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With" always;
            
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "https://sysload-embed.regdesk.ai";
                add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
                add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With";
                return 204;
            }
        }
    }
}