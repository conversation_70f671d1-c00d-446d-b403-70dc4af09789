client_header_timeout 4000s;
client_body_timeout   4000s;
keepalive_timeout     4000s;
proxy_read_timeout 4000s;
proxy_send_timeout 4000s;
proxy_connect_timeout 3000s;

# Grafana API endpoint with dynamic timeout for sysload-embed.regdesk.ai
location /api/report/grafana {
    proxy_pass http://127.0.0.1:8080;
    keepalive_timeout     4000s;
    proxy_read_timeout 4000s;
    proxy_send_timeout 4000s;
    proxy_connect_timeout 3000s;

    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    add_header Access-Control-Allow-Origin "https://sysload-embed.regdesk.ai" always;
    add_header Access-Control-Allow-Credentials "true" always;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With" always;
    
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin "https://sysload-embed.regdesk.ai";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With";
        return 204;
    }
}